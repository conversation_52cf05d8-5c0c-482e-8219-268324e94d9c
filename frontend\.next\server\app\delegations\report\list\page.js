(()=>{var e={};e.id=9726,e.ids=[9726],e.modules={997:(e,t,s)=>{"use strict";s.d(t,{k:()=>x});var r=s(60687),a=s(28946),i=s(11516),l=s(20620),n=s(36644);let o=(0,s(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var d=s(43210),c=s(68752),u=s(21342),m=s(3940),h=s(22482),p=s(22364);function x({className:e,csvData:t,enableCsv:s=!1,entityId:x,fileName:g,reportContentId:b,reportType:f,tableId:y}){let[j,v]=(0,d.useState)(!1),[N,w]=(0,d.useState)(!1),{showFormSuccess:k,showFormError:C}=(0,m.t6)(),R=async()=>{v(!0);try{let e=`/api/reports/${f}${x?`/${x}`:""}`,t=document.createElement("a");t.href=e,t.download=`${g}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),k({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),C(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{v(!1)}},A=async()=>{if(s){w(!0);try{if(t?.data&&t.headers)(0,p.og)(t.data,t.headers,`${g}.csv`);else if(y){let e=(0,p.tL)(y);(0,p.og)(e.data,e.headers,`${g}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");k({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),C(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{w(!1)}}},P=j||N;return(0,r.jsxs)("div",{className:(0,h.cn)("flex items-center gap-2 no-print",e),children:[(0,r.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,r.jsx)(a.A,{className:"size-4"})}),(0,r.jsxs)(u.rI,{children:[(0,r.jsx)(u.ty,{asChild:!0,children:(0,r.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:P,size:"icon",title:"Download Report",children:P?(0,r.jsx)(i.A,{className:"size-4 animate-spin"}):(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsxs)(u.SQ,{align:"end",children:[(0,r.jsxs)(u._2,{disabled:j,onClick:R,children:[j?(0,r.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(n.A,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download PDF"})]}),s&&(0,r.jsxs)(u._2,{disabled:N,onClick:A,children:[N?(0,r.jsx)(i.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(o,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4724:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["delegations",{children:["report",{children:["list",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48344)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\report\\list\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,78691)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\report\\list\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\report\\list\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/delegations/report/list/page",pathname:"/delegations/report/list",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>o,Hj:()=>d,XI:()=>l,nA:()=>u,nd:()=>c});var r=s(60687),a=s(43210),i=s(22482);let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{className:(0,i.cn)("w-full caption-bottom text-sm",e),ref:s,...t})}));l.displayName="Table";let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("thead",{className:(0,i.cn)("[&_tr]:border-b",e),ref:s,...t}));n.displayName="TableHeader";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tbody",{className:(0,i.cn)("[&_tr:last-child]:border-0",e),ref:s,...t}));o.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tfoot",{className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),ref:s,...t})).displayName="TableFooter";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tr",{className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),ref:s,...t}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("th",{className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),ref:s,...t}));c.displayName="TableHead";let u=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("td",{className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),ref:s,...t}));u.displayName="TableCell",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("caption",{className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),ref:s,...t})).displayName="TableCaption"},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14583:(e,t,s)=>{"use strict";s.d(t,{$o:()=>b,Eb:()=>p,Iu:()=>u,WA:()=>x,cU:()=>m,dK:()=>c,n$:()=>h});var r=s(60687),a=s(43967),i=s(74158),l=s(69795),n=s(43210),o=s(29523),d=s(22482);let c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:(0,d.cn)("flex justify-center",e),ref:s,...t}));c.displayName="Pagination";let u=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("ul",{className:(0,d.cn)("flex flex-row items-center gap-1",e),ref:s,...t}));u.displayName="PaginationContent";let m=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("li",{className:(0,d.cn)("",e),ref:s,...t}));m.displayName="PaginationItem";let h=n.forwardRef(({className:e,isActive:t,...s},a)=>(0,r.jsx)(o.$,{"aria-current":t?"page":void 0,className:(0,d.cn)("h-9 w-9",e),ref:a,size:"icon",variant:t?"outline":"ghost",...s}));h.displayName="PaginationLink";let p=n.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(o.$,{className:(0,d.cn)("h-9 w-9 gap-1",e),ref:s,size:"icon",variant:"ghost",...t,children:[(0,r.jsx)(a.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Previous page"})]}));p.displayName="PaginationPrevious";let x=n.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(o.$,{className:(0,d.cn)("h-9 w-9 gap-1",e),ref:s,size:"icon",variant:"ghost",...t,children:[(0,r.jsx)(i.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Next page"})]}));x.displayName="PaginationNext";let g=n.forwardRef(({className:e,...t},s)=>(0,r.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",e),ref:s,...t,children:[(0,r.jsx)(l.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"More pages"})]}));function b({className:e,currentPage:t,onPageChange:s,totalPages:a}){let i=(()=>{let e=[];e.push(1);let s=Math.max(2,t-1),r=Math.min(a-1,t+1);s>2&&e.push("ellipsis1");for(let t=s;t<=r;t++)e.push(t);return r<a-1&&e.push("ellipsis2"),a>1&&e.push(a),e})();return a<=1?null:(0,r.jsx)(c,{className:e,children:(0,r.jsxs)(u,{children:[(0,r.jsx)(m,{children:(0,r.jsx)(p,{"aria-disabled":1===t?"true":void 0,"aria-label":"Go to previous page",disabled:1===t,onClick:()=>s(t-1)})}),i.map((e,a)=>"ellipsis1"===e||"ellipsis2"===e?(0,r.jsx)(m,{children:(0,r.jsx)(g,{})},`ellipsis-${a}`):(0,r.jsx)(m,{children:(0,r.jsx)(h,{"aria-label":`Go to page ${e}`,isActive:t===e,onClick:()=>s(e),children:e})},`page-${e}`)),(0,r.jsx)(m,{children:(0,r.jsx)(x,{"aria-disabled":t===a?"true":void 0,"aria-label":"Go to next page",disabled:t===a,onClick:()=>s(t+1)})})]})})}g.displayName="PaginationEllipsis"},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>g,gC:()=>x,l6:()=>c,yv:()=>u});var r=s(60687),a=s(22670),i=s(61662),l=s(89743),n=s(58450),o=s(43210),d=s(22482);let c=a.bL;a.YJ;let u=a.WT,m=o.forwardRef(({children:e,className:t,...s},l)=>(0,r.jsxs)(a.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),ref:l,...s,children:[e,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]}));m.displayName=a.l9.displayName;let h=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:s,...t,children:(0,r.jsx)(l.A,{className:"size-4"})}));h.displayName=a.PP.displayName;let p=o.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),ref:s,...t,children:(0,r.jsx)(i.A,{className:"size-4"})}));p.displayName=a.wn.displayName;let x=o.forwardRef(({children:e,className:t,position:s="popper",...i},l)=>(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,ref:l,...i,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:e}),(0,r.jsx)(p,{})]})}));x.displayName=a.UC.displayName,o.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),ref:s,...t})).displayName=a.JU.displayName;let g=o.memo(o.forwardRef(({children:e,className:t,...s},i)=>{let l=o.useCallback(e=>{"function"==typeof i?i(e):i&&(i.current=e)},[i]);return(0,r.jsxs)(a.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),ref:l,...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:e})]})}));g.displayName=a.q7.displayName,o.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),ref:s,...t})).displayName=a.wv.displayName},15795:(e,t,s)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function a(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${t} (Role)`}return"Unknown Employee"}function i(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function l(e){return e.replaceAll("_"," ")}s.d(t,{DV:()=>a,fZ:()=>r,s:()=>i,vq:()=>l})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20454:(e,t,s)=>{Promise.resolve().then(s.bind(s,48344))},20620:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},26398:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29368:()=>{},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37859:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(60687),a=s(75699),i=s(58261);s(29368),s(62453);var l=s(89743),n=s(61662),o=s(41936),d=s(78726),c=s(92876),u=s(26398),m=s(48206),h=s(16189),p=s(43210),x=s(44493),g=s(22482);let b={Cancelled:{bg:"bg-gradient-to-br from-red-50 to-red-100",border:"border-red-200",text:"text-red-700"},Completed:{bg:"bg-gradient-to-br from-purple-50 to-purple-100",border:"border-purple-200",text:"text-purple-700"},Confirmed:{bg:"bg-gradient-to-br from-green-50 to-green-100",border:"border-green-200",text:"text-green-700"},In_Progress:{bg:"bg-gradient-to-br from-yellow-50 to-yellow-100",border:"border-yellow-200",text:"text-yellow-700"},No_details:{bg:"bg-gradient-to-br from-gray-50 to-gray-100",border:"border-gray-200",text:"text-gray-700"},Planned:{bg:"bg-gradient-to-br from-blue-50 to-blue-100",border:"border-blue-200",text:"text-blue-700"}},f=e=>e.replace("_"," ");function y({className:e,delegations:t}){let s=t.length,a=t.reduce((e,t)=>{let s=t.status;return e[s]=(e[s]||0)+1,e},{}),i=t.reduce((e,t)=>e+(t.delegates?.length||0),0),l=Object.entries(a).sort(([,e],[,t])=>t-e).map(([e])=>e);return(0,r.jsxs)("div",{className:(0,g.cn)("mt-6 mb-8",e),children:[(0,r.jsxs)("div",{className:"mb-6 grid grid-cols-2 gap-4 lg:grid-cols-4",children:[(0,r.jsx)(j,{className:"border-slate-200 bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegations",textColor:"text-slate-700",value:s,valueColor:"text-slate-800"}),(0,r.jsx)(j,{className:"border-indigo-200 bg-gradient-to-br from-indigo-50 to-indigo-100 shadow-sm transition-shadow hover:shadow-md",label:"Total Delegates",textColor:"text-indigo-700",value:i,valueColor:"text-indigo-800"}),l.slice(0,2).map(e=>{let t=b[e];return(0,r.jsx)(j,{className:(0,g.cn)(t.bg,t.border,"shadow-sm hover:shadow-md transition-shadow"),label:f(e),textColor:t.text,value:a[e],valueColor:t.text},e)})]}),l.length>2&&(0,r.jsx)("div",{className:"grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-6",children:l.slice(2).map(e=>{let t=b[e];return(0,r.jsx)(j,{className:(0,g.cn)(t.bg,t.border,"shadow-sm hover:shadow-md transition-shadow"),compact:!0,label:f(e),textColor:t.text,value:a[e],valueColor:t.text},e)})})]})}function j({className:e,compact:t=!1,label:s,textColor:a="text-gray-600",value:i,valueColor:l="text-gray-800"}){return(0,r.jsx)(x.Zp,{className:(0,g.cn)("overflow-hidden border transition-all duration-200",e),children:(0,r.jsxs)(x.Wu,{className:(0,g.cn)("text-center",t?"p-3":"p-4"),children:[(0,r.jsx)("div",{className:(0,g.cn)("font-bold",t?"text-xl mb-1":"text-3xl mb-2",l),children:i.toLocaleString()}),(0,r.jsx)("div",{className:(0,g.cn)("font-medium",t?"text-xs":"text-sm",a),children:s})]})})}var v=s(997),N=s(96834),w=s(29523),k=s(89667),C=s(52027),R=s(14583),A=s(15079),P=s(6211),M=s(63502),S=s(15795);let D=["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"],E=e=>{switch(e){case"Cancelled":return"bg-red-100 text-red-800 border-red-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}},z=e=>{if(!e)return"N/A";try{return(0,a.GP)((0,i.H)(e),"MMM d, yyyy")}catch{return"Invalid Date"}};function q(){return(0,r.jsx)(p.Suspense,{fallback:(0,r.jsx)("div",{className:"py-10 text-center",children:"Loading report..."}),children:(0,r.jsx)(O,{})})}function O(){(0,h.useSearchParams)();let{data:e,error:t,isLoading:s,refetch:a}=(0,M.BD)(),i=(0,p.useMemo)(()=>e||[],[e]),[b,f]=(0,p.useState)(""),[j,q]=(0,p.useState)(""),[O,F]=(0,p.useState)("all"),[_,L]=(0,p.useState)({}),[$,T]=(0,p.useState)(1),[I]=(0,p.useState)(10),[H,U]=(0,p.useState)("durationFrom"),[Q,G]=(0,p.useState)("asc"),Z=(0,p.useCallback)((e,t,s)=>[...e].sort((e,r)=>{let a,i;switch(t){case"delegates":a=e.delegates?.length||0,i=r.delegates?.length||0;break;case"durationFrom":a=new Date(e.durationFrom).getTime(),i=new Date(r.durationFrom).getTime();break;case"eventName":a=e.eventName.toLowerCase(),i=r.eventName.toLowerCase();break;case"location":a=e.location.toLowerCase(),i=r.location.toLowerCase();break;case"status":a=e.status,i=r.status;break;default:a=e[t],i=r[t]}return null==a||null==i?0:a<i?"asc"===s?-1:1:a>i?"asc"===s?1:-1:0}),[]),K=(0,p.useMemo)(()=>{let e=[...i];if(j){let t=j.toLowerCase();e=e.filter(e=>e.eventName.toLowerCase().includes(t)||e.location.toLowerCase().includes(t)||e.delegates?.some(e=>e.name.toLowerCase().includes(t))||e.notes?.toLowerCase().includes(t)||e.status.toLowerCase().includes(t))}return"all"!==O&&(e=e.filter(e=>e.status===O)),_.from&&(e=e.filter(e=>new Date(e.durationFrom)>=_.from)),_.to&&(e=e.filter(e=>new Date(e.durationFrom)<=_.to)),Z(e,H,Q)},[i,j,O,_,H,Q,Z]),V=(0,p.useCallback)(e=>{H===e?G("asc"===Q?"desc":"asc"):(U(e),G("asc"))},[H,Q]),W=(0,p.useCallback)(e=>H!==e?"none":"asc"===Q?"ascending":"descending",[H,Q]),B=(0,p.useCallback)(()=>{f(""),q(""),F("all"),L({}),T(1)},[]),J=$*I,X=J-I,Y=(0,p.useMemo)(()=>K.slice(X,J),[K,X,J]),ee=(0,p.useMemo)(()=>Math.ceil(K.length/I),[K.length,I]),et=(0,p.useCallback)(e=>{T(e)},[]),es=(0,p.useCallback)(e=>H!==e?null:"asc"===Q?(0,r.jsx)(l.A,{className:"ml-1 inline-block size-4"}):(0,r.jsx)(n.A,{className:"ml-1 inline-block size-4"}),[H,Q]);return s?(0,r.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,r.jsx)(C.jt,{count:5,variant:"table"})}):t?(0,r.jsxs)("div",{className:"mx-auto max-w-5xl p-4 text-red-500",children:["Error loading delegations: ",t.message,(0,r.jsx)(w.$,{className:"ml-2",onClick:()=>a(),children:"Retry"})]}):(0,r.jsxs)("div",{className:"delegation-report-container",children:[(0,r.jsx)("div",{className:"no-print mb-6 text-right",children:(0,r.jsx)(v.k,{enableCsv:K.length>0,fileName:`delegations-list-report-${new Date().toISOString().split("T")[0]}`,reportContentId:"#delegations-list-report-content",reportType:"delegations",tableId:"#delegations-table"})}),(0,r.jsxs)("div",{className:"report-content",id:"delegations-list-report-content",children:[(0,r.jsxs)("header",{className:"delegation-report-header",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"delegation-report-title",children:"Delegation List Report"}),(0,r.jsx)("div",{className:"no-print mx-auto h-1 w-24 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600"})]}),(0,r.jsx)("p",{className:"delegation-report-subtitle",children:b||"all"!==O?`Filtered by: ${"all"===O?"":`Status - ${(0,S.fZ)(O)}`}${b?("all"===O?"":" | ")+`Search - "${b}"`:""}`:"All Delegations"}),(0,r.jsxs)("p",{className:"delegation-report-date",children:["Generated: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,r.jsx)("div",{className:"no-print delegation-summary-grid",children:(0,r.jsx)(y,{delegations:K.map(e=>({...e,delegates:e.delegates||[],escortEmployeeIds:e.escorts?.map(e=>e.employeeId.toString())||[]}))})}),(0,r.jsxs)("div",{className:"print-only delegation-print-summary",children:[(0,r.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,r.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegations:"})," ",(0,r.jsx)("span",{className:"delegation-print-summary-value",children:K.length})]}),(0,r.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,r.jsx)("span",{className:"delegation-print-summary-label",children:"Total Delegates:"})," ",(0,r.jsx)("span",{className:"delegation-print-summary-value",children:K.reduce((e,t)=>e+(t.delegates?.length||0),0)})]}),D.map(e=>({count:K.filter(t=>t.status===e).length,status:e})).filter(e=>e.count>0).sort((e,t)=>t.count-e.count).slice(0,3).map(({count:e,status:t})=>(0,r.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,r.jsxs)("span",{className:"delegation-print-summary-label",children:[(0,S.fZ)(t),":"]})," ",(0,r.jsx)("span",{className:"delegation-print-summary-value",children:e})]},t)),"all"!==O&&(0,r.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,r.jsx)("span",{className:"delegation-print-summary-label",children:"Filtered by Status:"})," ",(0,r.jsx)("span",{className:"delegation-print-summary-value",children:(0,S.fZ)(O)})]}),b&&(0,r.jsxs)("div",{className:"delegation-print-summary-item",children:[(0,r.jsx)("span",{className:"delegation-print-summary-label",children:"Search Term:"})," ",(0,r.jsxs)("span",{className:"delegation-print-summary-value",children:['"',b,'"']})]})]})]}),(0,r.jsx)("div",{className:"no-print mb-8",children:(0,r.jsx)(x.Zp,{className:"border-0 bg-gradient-to-r from-slate-50 to-gray-50 shadow-lg",children:(0,r.jsxs)(x.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"delegation-filters",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"status-filter",children:"Filter by Status"}),(0,r.jsxs)(A.l6,{"aria-label":"Filter by status",onValueChange:F,value:O,children:[(0,r.jsx)(A.bq,{className:"w-full border-gray-300 bg-white focus:border-blue-500 focus:ring-blue-500",children:(0,r.jsx)(A.yv,{placeholder:"All Statuses"})}),(0,r.jsxs)(A.gC,{children:[(0,r.jsx)(A.eb,{value:"all",children:"All Statuses"}),D.map(e=>(0,r.jsx)(A.eb,{value:e,children:(0,S.fZ)(e)},e))]})]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"search-input",children:"Search Delegations"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(k.p,{"aria-label":"Search delegations",className:"border-gray-300 bg-white px-10 focus:border-blue-500 focus:ring-blue-500",id:"search-input",onChange:e=>f(e.target.value),placeholder:"Search by event, location, or delegate...",type:"text",value:b}),(0,r.jsx)(o.A,{"aria-hidden":"true",className:"absolute left-3 top-1/2 size-5 -translate-y-1/2 text-gray-400"}),b&&(0,r.jsxs)(w.$,{"aria-label":"Clear search",className:"absolute right-1 top-1/2 size-7 -translate-y-1/2",onClick:()=>f(""),size:"icon",variant:"ghost",children:[(0,r.jsx)(d.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Clear search"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"mb-2 block text-sm font-semibold text-gray-700",htmlFor:"date-range",children:"Date Range"}),(0,r.jsx)(k.p,{"aria-label":"Date range filter (coming soon)",className:"bg-gray-100 opacity-50",disabled:!0,id:"date-range",placeholder:"Date range filter coming soon",type:"text"})]})]}),(b||"all"!==O)&&(0,r.jsxs)("div",{className:"mt-6 flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4",children:[(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("span",{className:"font-semibold text-blue-800",children:"Active Filters:"}),b&&(0,r.jsxs)("span",{className:"ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-800",children:['Search: "',b,'"']}),"all"!==O&&(0,r.jsxs)("span",{className:"ml-2 rounded bg-green-100 px-2 py-1 text-xs text-green-800",children:["Status:"," ",(0,S.fZ)(O)]})]}),(0,r.jsx)(w.$,{"aria-label":"Reset all filters",className:"border-blue-300 text-blue-700 hover:bg-blue-100",onClick:B,size:"sm",variant:"outline",children:"Reset Filters"})]})]})})}),0===K.length?(0,r.jsx)("div",{className:"rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-slate-100 py-16 text-center",children:(0,r.jsxs)("div",{className:"mx-auto max-w-md",children:[(0,r.jsx)("div",{className:"mb-4 text-gray-400",children:(0,r.jsx)(c.A,{className:"mx-auto mb-4 size-16"})}),(0,r.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-700",children:"No delegations found"}),(0,r.jsx)("p",{className:"mb-4 text-gray-500",children:"No delegations match the current filter criteria."}),(0,r.jsx)(w.$,{"aria-label":"Reset filters to show all delegations",className:"mt-2",onClick:B,size:"lg",variant:"outline",children:"Reset Filters"})]})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"delegation-table-container",children:(0,r.jsxs)(P.XI,{className:"delegation-table",id:"delegations-table",children:[(0,r.jsx)(P.A0,{children:(0,r.jsxs)(P.Hj,{className:"border-b border-gray-200 bg-gradient-to-r from-slate-100 to-gray-100",children:[(0,r.jsxs)(P.nd,{"aria-label":"Sort by event name","aria-sort":W("eventName"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>V("eventName"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),V("eventName"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Event Name ",es("eventName")]}),(0,r.jsxs)(P.nd,{"aria-label":"Sort by location","aria-sort":W("location"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>V("location"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),V("location"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Location ",es("location")]}),(0,r.jsxs)(P.nd,{"aria-label":"Sort by duration","aria-sort":W("durationFrom"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>V("durationFrom"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),V("durationFrom"))},role:"columnheader",style:{width:"20%"},tabIndex:0,children:["Duration ",es("durationFrom")]}),(0,r.jsxs)(P.nd,{"aria-label":"Sort by status","aria-sort":W("status"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>V("status"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),V("status"))},role:"columnheader",style:{width:"10%"},tabIndex:0,children:["Status ",es("status")]}),(0,r.jsxs)(P.nd,{"aria-label":"Sort by number of delegates","aria-sort":W("delegates"),className:"cursor-pointer font-semibold text-gray-700 transition-colors hover:bg-slate-200",onClick:()=>V("delegates"),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),V("delegates"))},role:"columnheader",style:{width:"25%"},tabIndex:0,children:["Delegates ",es("delegates")]})]})}),(0,r.jsx)(P.BF,{className:"no-print",children:Y.map((e,t)=>(0,r.jsxs)(P.Hj,{className:(0,g.cn)("page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100",t%2==0?"bg-white":"bg-slate-50/30"),children:[(0,r.jsx)(P.nA,{className:"print-text-wrap p-4 font-medium",title:e.eventName,children:(0,r.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,r.jsx)(P.nA,{className:"print-text-wrap print-location-col p-4",title:e.location,children:(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(u.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,r.jsx)(P.nA,{className:"whitespace-nowrap p-4",children:(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:z(e.durationFrom)}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",z(e.durationTo)]})]})]})}),(0,r.jsx)(P.nA,{className:"p-4",children:(0,r.jsx)(N.E,{className:(0,g.cn)("text-xs py-1 px-2 font-medium",E(e.status)),children:(0,S.fZ)(e.status)})}),(0,r.jsx)(P.nA,{className:"print-text-wrap max-w-xs p-4",title:e.delegates?.map(e=>e.name).join(", "),children:(e.delegates?.length||0)>0?(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(m.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"no-print",children:3>=(e.delegates?.length||0)?(0,r.jsx)("div",{className:"space-y-1",children:e.delegates?.map((e,t)=>(0,r.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||t))}):(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-1 space-y-1",children:e.delegates?.slice(0,2).map((e,t)=>(0,r.jsx)("div",{className:"text-sm text-gray-700",children:e.name},e.id||t))}),(0,r.jsxs)("span",{className:"rounded bg-gray-100 px-2 py-1 text-xs text-gray-500",children:["+",(e.delegates?.length||0)-2," ","more"]})]})}),(0,r.jsx)("span",{className:"print-only",children:(0,r.jsx)("div",{className:"space-y-1",children:e.delegates?.map((e,t)=>(0,r.jsx)("div",{className:"text-sm",children:e.name},e.id||t))})})]})]}):(0,r.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id))}),(0,r.jsx)(P.BF,{className:"print-only",children:K.map((e,t)=>(0,r.jsxs)(P.Hj,{className:(0,g.cn)("page-break-inside-avoid",t%2==0?"bg-white":"bg-slate-50/30"),children:[(0,r.jsx)(P.nA,{className:"print-text-wrap font-medium",title:e.eventName,children:(0,r.jsx)("div",{className:"font-semibold text-gray-800",children:e.eventName})}),(0,r.jsx)(P.nA,{className:"print-text-wrap print-location-col",title:e.location,children:(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(u.A,{className:"mr-2 size-4 text-gray-400"}),e.location]})}),(0,r.jsx)(P.nA,{className:"",children:(0,r.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,r.jsx)(c.A,{className:"mr-2 size-4 text-gray-400"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:z(e.durationFrom)}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["to ",z(e.durationTo)]})]})]})}),(0,r.jsx)(P.nA,{className:"",children:(0,r.jsx)(N.E,{className:(0,g.cn)("text-xs py-1 px-2 font-medium",E(e.status)),children:(0,S.fZ)(e.status)})}),(0,r.jsx)(P.nA,{className:"print-text-wrap",title:e.delegates?.map(e=>e.name).join(", "),children:(e.delegates?.length||0)>0?(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(m.A,{className:"mr-2 mt-0.5 size-4 shrink-0 text-gray-400"}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"space-y-1",children:e.delegates?.map((e,t)=>(0,r.jsx)("div",{className:"text-sm",children:e.name},e.id||t))})})]}):(0,r.jsx)("span",{className:"text-sm text-gray-400",children:"No delegates"})})]},e.id))})]})}),K.length>I&&(0,r.jsx)("div",{className:"no-print mt-8 flex justify-center",children:(0,r.jsx)(R.$o,{currentPage:$,onPageChange:et,totalPages:ee})})]}),(0,r.jsx)("footer",{className:"delegation-report-footer",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{className:"font-medium",children:["Report generated on: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,r.jsx)("p",{className:"text-gray-400",children:"WorkHub - Delegation Management"}),(0,r.jsx)("p",{className:"print-only text-xs",children:"Confidential - For internal use only"})]})})]})]})}},43967:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48344:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\delegations\\\\report\\\\list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\delegations\\report\\list\\page.tsx","default")},54050:(e,t,s)=>{"use strict";s.d(t,{n:()=>c});var r=s(43210),a=s(65406),i=s(33465),l=s(35536),n=s(31212),o=class extends l.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,n.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,n.EN)(t.mutationKey)!==(0,n.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#a(),this.#i()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#a(){let e=this.#s?.state??(0,a.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},d=s(8693);function c(e,t){let s=(0,d.jE)(t),[a]=r.useState(()=>new o(s,e));r.useEffect(()=>{a.setOptions(e)},[a,e]);let l=r.useSyncExternalStore(r.useCallback(e=>a.subscribe(i.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),c=r.useCallback((e,t)=>{a.mutate(e,t).catch(n.lQ)},[a]);if(l.error&&(0,n.GU)(a.options.throwOnError,[l.error]))throw l.error;return{...l,mutate:c,mutateAsync:l.mutate}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62453:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69795:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78691:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>a});var r=s(37413);let a={title:"Delegation List Report"};function i({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90190:(e,t,s)=>{Promise.resolve().then(s.bind(s,37859))},91645:e=>{"use strict";e.exports=require("net")},92876:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},93425:(e,t,s)=>{"use strict";s.d(t,{E:()=>x});var r=s(43210),a=s(33465),i=s(5563),l=s(35536),n=s(31212);function o(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var d=class extends l.Q{#e;#l;#n;#o;#d;#c;#u;#m;#h=[];constructor(e,t,s){super(),this.#e=e,this.#o=s,this.#n=[],this.#d=[],this.#l=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#d.forEach(e=>{e.subscribe(t=>{this.#p(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#d.forEach(e=>{e.destroy()})}setQueries(e,t){this.#n=e,this.#o=t,a.jG.batch(()=>{let e=this.#d,t=this.#x(this.#n);this.#h=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),r=s.map(e=>e.getCurrentResult()),a=s.some((t,s)=>t!==e[s]);(e.length!==s.length||a)&&(this.#d=s,this.#l=r,this.hasListeners()&&(o(e,s).forEach(e=>{e.destroy()}),o(s,e).forEach(e=>{e.subscribe(t=>{this.#p(e,t)})}),this.#i()))})}getCurrentResult(){return this.#l}getQueries(){return this.#d.map(e=>e.getCurrentQuery())}getObservers(){return this.#d}getOptimisticResult(e,t){let s=this.#x(e),r=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#g(e??r,t),()=>this.#b(r,s)]}#b(e,t){return t.map((s,r)=>{let a=e[r];return s.defaultedQueryOptions.notifyOnChangeProps?a:s.observer.trackResult(a,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#g(e,t){return t?(this.#c&&this.#l===this.#m&&t===this.#u||(this.#u=t,this.#m=this.#l,this.#c=(0,n.BH)(this.#c,t(e))),this.#c):e}#x(e){let t=new Map(this.#d.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),a=t.get(r.queryHash);a?s.push({defaultedQueryOptions:r,observer:a}):s.push({defaultedQueryOptions:r,observer:new i.$(this.#e,r)})}),s}#p(e,t){let s=this.#d.indexOf(e);-1!==s&&(this.#l=function(e,t,s){let r=e.slice(0);return r[t]=s,r}(this.#l,s,t),this.#i())}#i(){if(this.hasListeners()){let e=this.#c,t=this.#b(this.#l,this.#h);e!==this.#g(t,this.#o?.combine)&&a.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#l)})})}}},c=s(8693),u=s(24903),m=s(18228),h=s(16142),p=s(76935);function x({queries:e,...t},s){let l=(0,c.jE)(s),o=(0,u.w)(),x=(0,m.h)(),g=r.useMemo(()=>e.map(e=>{let t=l.defaultQueryOptions(e);return t._optimisticResults=o?"isRestoring":"optimistic",t}),[e,l,o]);g.forEach(e=>{(0,p.jv)(e),(0,h.LJ)(e,x)}),(0,h.wZ)(x);let[b]=r.useState(()=>new d(l,g,t)),[f,y,j]=b.getOptimisticResult(g,t.combine),v=!o&&!1!==t.subscribed;r.useSyncExternalStore(r.useCallback(e=>v?b.subscribe(a.jG.batchCalls(e)):n.lQ,[b,v]),()=>b.getCurrentResult(),()=>b.getCurrentResult()),r.useEffect(()=>{b.setQueries(g,t)},[g,t,b]);let N=f.some((e,t)=>(0,p.EU)(g[t],e))?f.flatMap((e,t)=>{let s=g[t];if(s){let t=new i.$(l,s);if((0,p.EU)(s,e))return(0,p.iL)(s,t,x);(0,p.nE)(e,o)&&(0,p.iL)(s,t,x)}return[]}):[];if(N.length>0)throw Promise.all(N);let w=f.find((e,t)=>{let s=g[t];return s&&(0,h.$1)({result:e,errorResetBoundary:x,throwOnError:s.throwOnError,query:l.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(w?.error)throw w.error;return y(j())}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,211,1658,8390,2670,101,7055,3502,5782],()=>s(4724));module.exports=r})();