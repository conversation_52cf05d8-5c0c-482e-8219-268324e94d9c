(()=>{var e={};e.id=8147,e.ids=[8147],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38094:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=r(65239),t=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["tasks",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88989)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/tasks/page",pathname:"/tasks",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88989:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\tasks\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\tasks\\page.tsx","default")},90415:(e,s,r)=>{Promise.resolve().then(r.bind(r,88989))},90462:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>em});var a=r(60687);let t=(0,r(82614).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var l=r(35265),i=r(58369),n=r(85814),d=r.n(n),o=r(43210),c=r(95668),m=r(37392),x=r(29385);let u={entityType:"task",title:"Task Dashboard",description:"Oversee all tasks, assignments, and progress.",viewModes:["cards","table","list"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},h=({className:e=""})=>{let{layout:s,monitoring:r,setViewMode:t,setGridColumns:l,toggleCompactMode:i,setMonitoringEnabled:n,setRefreshInterval:d,toggleAutoRefresh:o,resetSettings:c}=(0,x.fX)("task")();return(0,a.jsx)(m.s,{config:u,entityType:"task",layout:s,monitoring:r,setViewMode:t,setGridColumns:l,toggleCompactMode:i,setMonitoringEnabled:n,setRefreshInterval:d,toggleAutoRefresh:o,resetSettings:c,className:e})};var g=r(15036),p=r(58595),j=r(72963),f=r(3662),b=r(97025),v=r(88514),N=r(48206),y=r(92876),k=r(41936),w=r(90586),C=r(78726),A=r(96834),z=r(29523),P=r(89667),E=r(80013),$=r(35950),M=r(67146),R=r(40988),S=r(56896),I=r(26373),q=r(22482),D=r(75699);let L=[{value:"Pending",label:"Pending",icon:g.A,color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"Assigned",label:"Assigned",icon:p.A,color:"text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800"},{value:"In_Progress",label:"In Progress",icon:j.A,color:"text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-900/20 dark:border-purple-800"},{value:"Completed",label:"Completed",icon:f.A,color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Cancelled",label:"Cancelled",icon:b.A,color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],T=[{value:"Low",label:"Low Priority",color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Medium",label:"Medium Priority",color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"High",label:"High Priority",color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],_=({onFiltersChange:e,className:s,initialFilters:r={},employeesList:t=[]})=>{let[l,i]=(0,o.useState)({search:"",status:[],priority:[],assignee:[],dateRange:{},...r}),[n,d]=(0,o.useState)(!1),c=s=>{let r={...l,...s};i(r),e?.(r)},m=()=>{let s={search:"",status:[],priority:[],assignee:[],dateRange:{}};i(s),e?.(s)},x=e=>{c({status:l.status.includes(e)?l.status.filter(s=>s!==e):[...l.status,e]})},u=e=>{c({priority:l.priority.includes(e)?l.priority.filter(s=>s!==e):[...l.priority,e]})},h=e=>{c({assignee:l.assignee.includes(e)?l.assignee.filter(s=>s!==e):[...l.assignee,e]})},g=e=>{c({dateRange:{from:e?.from??void 0,to:e?.to??void 0}})},j=+!!l.search+l.status.length+l.priority.length+l.assignee.length+(l.dateRange.from||l.dateRange.to?1:0);return(0,a.jsxs)("div",{className:(0,q.cn)("flex flex-col gap-4",s),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(k.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(P.p,{placeholder:"Search tasks...",value:l.search,onChange:e=>c({search:e.target.value}),className:"pl-10"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(f.A,{className:"size-4"}),"Status",l.status.length>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:l.status.length})]})}),(0,a.jsx)(R.hl,{className:"w-56 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Task Status"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>c({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)($.w,{}),(0,a.jsx)("div",{className:"space-y-2",children:L.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.S,{id:`status-${e.value}`,checked:l.status.includes(e.value),onCheckedChange:()=>x(e.value)}),(0,a.jsxs)(E.J,{htmlFor:`status-${e.value}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(s,{className:"size-3"}),(0,a.jsx)(A.E,{variant:"outline",className:(0,q.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(v.A,{className:"size-4"}),"Priority",l.priority.length>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:l.priority.length})]})}),(0,a.jsx)(R.hl,{className:"w-48 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Priority Level"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>c({priority:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)($.w,{}),(0,a.jsx)("div",{className:"space-y-2",children:T.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.S,{id:`priority-${e.value}`,checked:l.priority.includes(e.value),onCheckedChange:()=>u(e.value)}),(0,a.jsx)(E.J,{htmlFor:`priority-${e.value}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:(0,a.jsx)(A.E,{variant:"outline",className:(0,q.cn)("text-xs border",e.color),children:e.label})})]},e.value))})]})})]}),{}),t&&t.length>0&&(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(N.A,{className:"size-4"}),"Assignee",l.assignee.length>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:l.assignee.length})]})}),(0,a.jsx)(R.hl,{className:"w-64 p-3",align:"start",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Employee"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>c({assignee:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)($.w,{}),(0,a.jsxs)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.S,{id:"assignee-unassigned",checked:l.assignee.includes("unassigned"),onCheckedChange:()=>h("unassigned")}),(0,a.jsxs)(E.J,{htmlFor:"assignee-unassigned",className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(p.A,{className:"size-3 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Unassigned"})]})]}),t?.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S.S,{id:`assignee-${e.id}`,checked:l.assignee.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsxs)(E.J,{htmlFor:`assignee-${e.id}`,className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,a.jsx)(p.A,{className:"size-3"}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.role})]})]})]},e.id))]})]})})]}),{}),(0,a.jsx)(()=>(0,a.jsxs)(R.AM,{children:[(0,a.jsx)(R.Wv,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(y.A,{className:"size-4"}),"Date Range",(l.dateRange.from||l.dateRange.to)&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,a.jsx)(R.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Task Date Range"}),(0,a.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>c({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,a.jsx)(I.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:g,numberOfMonths:2,className:"rounded-md border-0"}),(0,a.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:l.dateRange.from&&!l.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)(M.cj,{open:n,onOpenChange:d,children:[(0,a.jsx)(M.CG,{asChild:!0,children:(0,a.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,a.jsx)(w.A,{className:"size-4"}),"Filters",j>0&&(0,a.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:j})]})}),(0,a.jsxs)(M.h,{side:"bottom",className:"h-[80vh]",children:[(0,a.jsxs)(M.Fm,{children:[(0,a.jsx)(M.qp,{children:"Filter Tasks"}),(0,a.jsx)(M.Qs,{children:"Refine your task list with advanced filters"})]}),(0,a.jsxs)("div",{className:"grid gap-6 py-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(E.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:L.map(e=>{let s=e.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(S.S,{id:`mobile-status-${e.value}`,checked:l.status.includes(e.value),onCheckedChange:()=>x(e.value)}),(0,a.jsxs)(E.J,{htmlFor:`mobile-status-${e.value}`,className:"flex items-center gap-1 cursor-pointer text-xs flex-1",children:[(0,a.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(E.J,{className:"text-sm font-medium",children:"Priority"}),(0,a.jsx)("div",{className:"grid gap-2",children:T.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(S.S,{id:`mobile-priority-${e.value}`,checked:l.priority.includes(e.value),onCheckedChange:()=>u(e.value)}),(0,a.jsx)(E.J,{htmlFor:`mobile-priority-${e.value}`,className:"cursor-pointer text-sm flex-1",children:e.label})]},e.value))})]}),t&&t.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(E.J,{className:"text-sm font-medium",children:"Assignee"}),(0,a.jsxs)("div",{className:"grid gap-2 max-h-48 overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(S.S,{id:"mobile-assignee-unassigned",checked:l.assignee.includes("unassigned"),onCheckedChange:()=>h("unassigned")}),(0,a.jsx)(E.J,{htmlFor:"mobile-assignee-unassigned",className:"cursor-pointer text-sm flex-1",children:"Unassigned"})]}),t.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,a.jsx)(S.S,{id:`mobile-assignee-${e.id}`,checked:l.assignee.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsx)(E.J,{htmlFor:`mobile-assignee-${e.id}`,className:"cursor-pointer text-sm flex-1",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.role})]})})]},e.id))]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(E.J,{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsx)("div",{className:"border rounded-md p-3",children:(0,a.jsx)(I.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:g,numberOfMonths:1,className:"rounded-md border-0"})})]}),(0,a.jsx)(z.$,{variant:"outline",onClick:m,className:"w-full",children:"Clear All Filters"})]})]})]})}),j>0&&(0,a.jsxs)(z.$,{variant:"ghost",size:"sm",onClick:m,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,a.jsx)(C.A,{className:"size-3"}),"Clear (",j,")"]})]}),j>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[l.search&&(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:['Search: "',l.search,'"',(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>c({search:""}),children:(0,a.jsx)(C.A,{className:"size-3"})})]}),l.status.map(e=>{let s=L.find(s=>s.value===e);return s?(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>x(e),children:(0,a.jsx)(C.A,{className:"size-3"})})]},e):null}),l.priority.map(e=>{let s=T.find(s=>s.value===e);return s?(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u(e),children:(0,a.jsx)(C.A,{className:"size-3"})})]},e):null}),l.assignee.map(e=>{let s=t?.find(s=>s.id===e),r="unassigned"===e?"Unassigned":s?.name||"Unknown";return(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:["Assignee: ",r,(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>h(e),children:(0,a.jsx)(C.A,{className:"size-3"})})]},e)}),(l.dateRange.from||l.dateRange.to)&&(0,a.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",l.dateRange.from?(0,D.GP)(l.dateRange.from,"MMM d"):"?"," ","-"," ",l.dateRange.to?(0,D.GP)(l.dateRange.to,"MMM d, yyyy"):"?",(0,a.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>c({dateRange:{}}),children:(0,a.jsx)(C.A,{className:"size-3"})})]})]})]})};var F=r(58261),G=r(26398),H=r(24920),J=r(14975),W=r(8760),V=r(68752),O=r(44493),U=r(15795);let B=e=>{switch(e){case"Assigned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20";case"Pending":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},X=e=>{switch(e){case"High":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Low":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Medium":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},Y=e=>{if(!e)return"N/A";try{return(0,D.GP)((0,F.H)(e),"MMM d, yyyy HH:mm")}catch{return"Invalid Date"}};function Z({task:e}){let s=!!e.staffEmployeeId,r=!!e.driverEmployeeId,t=!!e.vehicleId;return(0,a.jsxs)(O.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,a.jsxs)(O.aR,{className:"p-5",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,a.jsx)(O.ZB,{className:"line-clamp-2 text-lg font-semibold text-primary",title:e.description,children:e.description}),(0,a.jsxs)("div",{className:"flex shrink-0 flex-col items-end gap-1",children:[(0,a.jsx)(A.E,{className:(0,q.cn)("text-xs py-1 px-2 font-semibold",B(e.status)),children:e.status}),(0,a.jsxs)(A.E,{className:(0,q.cn)("text-xs py-1 px-2 font-semibold",X(e.priority)),children:[e.priority," Priority"]})]})]}),(0,a.jsxs)(O.BT,{className:"flex items-center pt-1 text-sm text-muted-foreground",children:[(0,a.jsx)(G.A,{className:"mr-1.5 size-4 shrink-0 text-accent"}),e.location]})]}),(0,a.jsxs)(O.Wu,{className:"flex grow flex-col p-5",children:[(0,a.jsx)($.w,{className:"my-3 bg-border/50"}),(0,a.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Start: "}),(0,a.jsx)("strong",{className:"font-semibold",children:Y(e.dateTime)})]})]}),e.deadline&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Deadline: "}),(0,a.jsx)("strong",{className:"font-semibold",children:Y(e.deadline)})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,a.jsxs)("strong",{className:"font-semibold",children:[e.estimatedDuration," mins"]})]})]}),s&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Staff: "}),(0,a.jsx)("strong",{className:"font-semibold",children:e.staffEmployee?(0,U.DV)(e.staffEmployee):`ID: ${e.staffEmployeeId}`})]})]}),r&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Driver: "}),(0,a.jsx)("strong",{className:"font-semibold",children:e.driverEmployee?(0,U.DV)(e.driverEmployee):`ID: ${e.driverEmployeeId}`})]})]}),t&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(H.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Vehicle: "}),(0,a.jsx)("strong",{className:"font-semibold",children:e.vehicle?`${e.vehicle.make} ${e.vehicle.model} (${e.vehicle.licensePlate||`ID: ${e.vehicle.id}`})`:`ID: ${e.vehicleId}`})]})]}),!s&&"Completed"!==e.status&&"Cancelled"!==e.status&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(J.A,{className:"mr-2.5 size-4 shrink-0 text-destructive"}),(0,a.jsx)("strong",{className:"font-semibold text-destructive",children:"No Staff Assigned"})]})]}),e.notes&&(0,a.jsx)("p",{className:"mt-3 line-clamp-2 border-t border-dashed border-border/50 pt-2 text-xs text-muted-foreground",title:e.notes,children:e.notes})]}),(0,a.jsx)(O.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,a.jsx)(V.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,a.jsx)(W.A,{className:"size-4"}),children:(0,a.jsx)(d(),{href:`/tasks/${e.id}`,children:"View Details"})})})]})}var K=r(15209);let Q=({className:e="",compactMode:s,tasks:r,gridColumns:t=3,viewMode:l})=>{switch(l){case"list":return(0,a.jsx)("div",{className:(0,q.cn)("flex flex-col",s?"gap-2":"gap-4",e),children:r.map(e=>(0,a.jsx)(Z,{task:e},e.id))});case"table":return(0,a.jsx)(K.z,{className:e,tasks:r});default:return(0,a.jsx)("div",{className:(0,q.cn)("grid grid-cols-1 gap-6",`md:grid-cols-2 lg:grid-cols-${t}`,s&&"gap-3",e),children:r.map(e=>(0,a.jsx)(Z,{task:e},e.id))})}};var ee=r(69981),es=r(12662),er=r(63503),ea=r(52027),et=r(48041),el=r(19599),ei=r(73227),en=r(72273),ed=r(83144);function eo(){return(0,a.jsxs)("div",{className:"flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-md",children:[(0,a.jsxs)("div",{className:"flex grow flex-col p-5",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)(ea.jt,{className:"mb-1 h-7 w-3/5 bg-muted/50",count:1,variant:"default"}),(0,a.jsx)(ea.jt,{className:"mb-1 h-5 w-1/4 rounded-full bg-muted/50",count:1,variant:"default"})]}),(0,a.jsx)(ea.jt,{className:"mb-3 h-4 w-1/2 bg-muted/50",count:1,variant:"default"}),(0,a.jsx)(ea.jt,{className:"my-3 h-px w-full bg-border/50",count:1,variant:"default"}),(0,a.jsx)("div",{className:"grow space-y-2.5",children:Array.from({length:3}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(ea.jt,{className:"mr-2.5 size-5 rounded-full bg-muted/50",count:1,variant:"default"}),(0,a.jsx)(ea.jt,{className:"h-5 w-2/3 bg-muted/50",count:1,variant:"default"})]},s))})]}),(0,a.jsx)("div",{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,a.jsx)(ea.jt,{className:"h-10 w-full bg-muted/50",count:1,variant:"default"})})]})}let ec=()=>{let{layout:e}=(0,x.fX)("task")(),{data:s=[],error:r,isLoading:n,refetch:c}=(0,ei.si)(),{data:m=[],error:u,isLoading:g,refetch:p}=(0,el.nR)(),{data:j=[],error:f,isLoading:b,refetch:v}=(0,en.T$)(),[N,y]=(0,o.useState)(""),[k,w]=(0,o.useState)("all"),[C,A]=(0,o.useState)("all"),[z,P]=(0,o.useState)("all"),[E,$]=(0,o.useState)({}),M=(0,o.useMemo)(()=>m.map(e=>({id:String(e.id),name:e.fullName??e.name,role:e.role})),[m]),R=(0,o.useMemo)(()=>{let e=[...s.map(e=>(0,ed.R)(e,m,j))],r=N.toLowerCase();return"all"!==k&&(e=e.filter(e=>e.status===k)),"all"!==C&&(e=e.filter(e=>e.priority===C)),"all"!==z&&(e=e.filter(e=>(e.staffEmployeeId&&String(e.staffEmployeeId)===z||e.driverEmployeeId&&String(e.driverEmployeeId)===z)??("unassigned"===z&&!e.staffEmployeeId&&!e.driverEmployeeId))),r&&(e=e.filter(e=>{let s=e.staffEmployeeId?M.find(s=>s.id===String(e.staffEmployeeId)):null,a=e.driverEmployeeId?M.find(s=>s.id===String(e.driverEmployeeId)):null;return(e.description.toLowerCase().includes(r)||e.location.toLowerCase().includes(r)||e.notes?.toLowerCase().includes(r))??s?.name.toLowerCase().includes(r)??a?.name.toLowerCase().includes(r)})),(E.from||E.to)&&(e=e.filter(e=>{let s=new Date(e.createdAt);return E.from&&E.to?s>=E.from&&s<=E.to:E.from?s>=E.from:!E.to||s<=E.to})),e},[N,s,m,j,k,C,z,M,E.from,E.to]),S=(0,o.useCallback)(async()=>{await Promise.all([c(),p(),v()])},[c,p,v]),I=N||"all"!==k||"all"!==C||"all"!==z;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(es.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,a.jsx)(et.z,{description:"Oversee all tasks, assignments, and progress.",icon:t,title:"Manage Tasks",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(V.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(l.A,{className:"size-4"}),children:(0,a.jsx)(d(),{href:"/tasks/add",children:"Add New Task"})}),(0,a.jsx)(ee.M,{getReportUrl:()=>{let e=new URLSearchParams({employee:z,priority:C,searchTerm:N,status:k}).toString();return`/tasks/report?${e}`},isList:!0}),(0,a.jsxs)(er.lG,{children:[(0,a.jsx)(er.zM,{asChild:!0,children:(0,a.jsx)(V.r,{actionType:"secondary",icon:(0,a.jsx)(i.A,{className:"size-4"}),children:"Settings"})}),(0,a.jsxs)(er.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsx)(er.L3,{children:"Dashboard Settings"}),(0,a.jsx)(er.rr,{children:"Customize how tasks are displayed and managed."}),(0,a.jsx)(h,{})]})]})]})}),(0,a.jsx)(_,{employeesList:M,initialFilters:{assignee:"all"===z?[]:[z],dateRange:E,priority:"all"===C?[]:[C],search:N,status:"all"===k?[]:[k]},onFiltersChange:e=>{y(e.search),w(e.status.length>0?e.status[0]:"all"),A(e.priority.length>0?e.priority[0]:"all"),P(e.assignee.length>0?e.assignee[0]:"all"),$(e.dateRange)}}),(0,a.jsx)(ea.gO,{data:R,emptyComponent:(0,a.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,a.jsx)(t,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:I?"No Tasks Match Your Filters":"No Tasks Created Yet"}),(0,a.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:I?"Try adjusting your search or filter criteria.":"It looks like you haven't created any tasks yet. Get started by adding one."}),!I&&(0,a.jsx)(V.r,{actionType:"primary",asChild:!0,icon:(0,a.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,a.jsx)(d(),{href:"/tasks/add",children:"Create Your First Task"})})]}),error:r?.message??u?.message??f?.message??null,isLoading:n||g||b,loadingComponent:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)(eo,{},s))}),onRetry:S,children:s=>(0,a.jsx)(Q,{compactMode:e.compactMode,gridColumns:e.gridColumns,tasks:s,viewMode:"calendar"===e.viewMode?"cards":e.viewMode})})]})};function em(){return(0,a.jsx)(c.A,{children:(0,a.jsx)(ec,{})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98663:(e,s,r)=>{Promise.resolve().then(r.bind(r,90462))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[4447,211,1658,8390,2670,4897,6362,6805,2890,101,7055,9599,5009,9637,3439,9922,5563,4867],()=>r(38094));module.exports=a})();