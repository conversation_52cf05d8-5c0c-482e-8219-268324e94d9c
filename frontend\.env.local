# Localhost Environment Configuration
# Use this configuration for standard localhost development
NODE_ENV=development

# API Configuration - Localhost
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
NEXT_PUBLIC_WS_URL=ws://localhost:3001

# Backend URL for server-side requests
BACKEND_URL=http://localhost:3001

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o

# Security Configuration
NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC="http://localhost:3001"

# Development Settings
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_DEV_TOOLS_ENABLED=true
NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=true

# Docker Configuration
NEXT_PUBLIC_DOCKER_ENV=false
