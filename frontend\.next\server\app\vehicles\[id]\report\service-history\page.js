(()=>{var e={};e.id=5298,e.ids=[5298],e.modules={997:(e,t,s)=>{"use strict";s.d(t,{k:()=>f});var r=s(60687),i=s(28946),n=s(11516),o=s(20620),a=s(36644);let l=(0,s(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var d=s(43210),c=s(68752),u=s(21342),h=s(3940),p=s(22482),m=s(22364);function f({className:e,csvData:t,enableCsv:s=!1,entityId:f,fileName:x,reportContentId:v,reportType:y,tableId:j}){let[b,g]=(0,d.useState)(!1),[_,S]=(0,d.useState)(!1),{showFormSuccess:w,showFormError:N}=(0,h.t6)(),k=async()=>{g(!0);try{let e=`/api/reports/${y}${f?`/${f}`:""}`,t=document.createElement("a");t.href=e,t.download=`${x}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),w({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),N(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{g(!1)}},C=async()=>{if(s){S(!0);try{if(t?.data&&t.headers)(0,m.og)(t.data,t.headers,`${x}.csv`);else if(j){let e=(0,m.tL)(j);(0,m.og)(e.data,e.headers,`${x}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");w({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),N(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{S(!1)}}},R=b||_;return(0,r.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,r.jsx)(c.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,r.jsx)(i.A,{className:"size-4"})}),(0,r.jsxs)(u.rI,{children:[(0,r.jsx)(u.ty,{asChild:!0,children:(0,r.jsx)(c.r,{actionType:"secondary","aria-label":"Download report",disabled:R,size:"icon",title:"Download Report",children:R?(0,r.jsx)(n.A,{className:"size-4 animate-spin"}):(0,r.jsx)(o.A,{className:"size-4"})})}),(0,r.jsxs)(u.SQ,{align:"end",children:[(0,r.jsxs)(u._2,{disabled:b,onClick:k,children:[b?(0,r.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(a.A,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download PDF"})]}),s&&(0,r.jsxs)(u._2,{disabled:_,onClick:C,children:[_?(0,r.jsx)(n.A,{className:"mr-2 size-4 animate-spin"}):(0,r.jsx)(l,{className:"mr-2 size-4"}),(0,r.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},54008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(60687),i=s(76180),n=s.n(i),o=s(44610),a=s(24920),l=s(30474),d=s(85814),c=s.n(d),u=s(16189),h=s(43210),p=s(95668),m=s(997),f=s(24847),x=s(68752),v=s(29523),y=s(44493),j=s(52027),b=s(48041),g=s(63213),_=s(72273),S=s(2775),w=s(48184);function N(){let e=(0,u.useParams)(),{loading:t,session:s,user:i}=(0,g.useAuthContext)(),d=!!i&&!!s?.access_token,N=e?.id,{data:k,error:C,isLoading:R,refetch:P}=(0,_.W_)(Number(N),{enabled:d}),{data:z=[],error:A,isLoading:O,refetch:M}=(0,S.xH)(Number(N),{enabled:d&&!!k?.id}),F=(0,h.useCallback)(()=>{P(),M()},[P,M]),q=C||A;return t||R||O?(0,r.jsx)(p.A,{children:(0,r.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(j.jt,{count:1,variant:"card"}),(0,r.jsx)(j.jt,{count:5,variant:"table"})]})})}):q||!k?(0,r.jsx)(p.A,{children:(0,r.jsx)("div",{className:"mx-auto max-w-5xl p-4",children:(0,r.jsx)(y.Zp,{className:"shadow-md",children:(0,r.jsxs)(y.Wu,{className:"p-6",children:[(0,r.jsx)("h2",{className:"mb-2 text-xl font-semibold text-red-600",children:"Error"}),(0,r.jsx)("p",{className:"mb-4 text-gray-700",children:(0,w.u1)(q)||"Vehicle not found"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(v.$,{onClick:F,children:"Try Again"}),(0,r.jsx)(v.$,{asChild:!0,variant:"outline",children:(0,r.jsx)(c(),{href:"/vehicles",children:"Back to Vehicles"})})]})]})})})}):(0,r.jsx)(p.A,{children:(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86 print-container mx-auto max-w-5xl space-y-6 p-4",children:[(0,r.jsx)(b.z,{description:`${k.make} ${k.model} (${k.year})`,icon:o.A,title:"Vehicle Service History",children:(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86 no-print flex gap-2",children:[(0,r.jsx)(x.r,{actionType:"tertiary",asChild:!0,icon:(0,r.jsx)(a.A,{className:"size-4"}),children:(0,r.jsx)(c(),{href:`/vehicles/${N}`,children:"View Vehicle"})}),(0,r.jsx)(m.k,{enableCsv:z.length>0,entityId:N,fileName:`vehicle-service-history-${k.make}-${k.model}`,reportContentId:"#vehicle-service-history-content",reportType:"vehicle-service-history",tableId:"#service-history-table"})]})}),(0,r.jsxs)("div",{id:"vehicle-service-history-content",className:"jsx-690e25f84e8ddf86 report-content",children:[(0,r.jsx)(y.Zp,{className:"card-print mb-6 shadow-md",children:(0,r.jsx)(y.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86 col-span-1 md:col-span-2",children:[(0,r.jsx)("h2",{className:"jsx-690e25f84e8ddf86 mb-4 text-xl font-semibold text-gray-800",children:"Vehicle Details"}),(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86 grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Make:"})," ",k.make]}),(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Model:"})," ",k.model]}),(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Year:"})," ",k.year]}),k.licensePlate&&(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Plate Number:"})," ",k.licensePlate]}),k.color&&(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Color:"})," ",k.color]}),(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("strong",{className:"jsx-690e25f84e8ddf86",children:"Initial Odometer:"})," ",null!==k.initialOdometer&&void 0!==k.initialOdometer?`${k.initialOdometer.toLocaleString()} miles`:"Not recorded"]}),(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Current Odometer:"})," ",z.length>0?`${Math.max(...z.map(e=>e.odometer)).toLocaleString()} miles`:null!==k.initialOdometer&&void 0!==k.initialOdometer?`${k.initialOdometer.toLocaleString()} miles`:"No odometer data available"]}),(0,r.jsxs)("div",{className:"jsx-690e25f84e8ddf86 col-span-2",children:[(0,r.jsx)("span",{className:"jsx-690e25f84e8ddf86 font-medium",children:"Last Updated:"})," ",z.length>0?new Date(Math.max(...z.map(e=>new Date(e.date).getTime()))).toLocaleDateString():"No service records"]})]})]}),k.imageUrl&&(0,r.jsx)("div",{className:"jsx-690e25f84e8ddf86 no-print col-span-1",children:(0,r.jsx)("div",{className:"jsx-690e25f84e8ddf86 relative aspect-[4/3] w-full overflow-hidden rounded",children:(0,r.jsx)(l.default,{alt:`${k.make} ${k.model}`,fill:!0,sizes:"(max-width: 768px) 100vw, 300px",src:k.imageUrl,style:{objectFit:"cover"}})})})]})})}),(0,r.jsxs)("header",{className:"jsx-690e25f84e8ddf86 print-only mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,r.jsx)("h1",{className:"jsx-690e25f84e8ddf86 text-3xl font-bold text-gray-800",children:"Vehicle Service History Report"}),(0,r.jsxs)("p",{className:"jsx-690e25f84e8ddf86 text-md text-gray-600",children:[k.make," ",k.model," (",k.year,")",k.licensePlate&&` - ${k.licensePlate}`]})]}),(0,r.jsx)(f.R,{error:null,isLoading:!1,onRetry:F,records:z,showVehicleInfo:!1,vehicleSpecific:!0}),(0,r.jsxs)("footer",{className:"jsx-690e25f84e8ddf86 mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,r.jsxs)("p",{className:"jsx-690e25f84e8ddf86",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,r.jsx)("p",{className:"jsx-690e25f84e8ddf86",children:"WorkHub - Vehicle Service Management"})]})]}),(0,r.jsx)(n(),{id:"690e25f84e8ddf86",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}},54050:(e,t,s)=>{"use strict";s.d(t,{n:()=>c});var r=s(43210),i=s(65406),n=s(33465),o=s(35536),a=s(31212),l=class extends o.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,a.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.EN)(t.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#n()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#i(){let e=this.#s?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},d=s(8693);function c(e,t){let s=(0,d.jE)(t),[i]=r.useState(()=>new l(s,e));r.useEffect(()=>{i.setOptions(e)},[i,e]);let o=r.useSyncExternalStore(r.useCallback(e=>i.subscribe(n.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=r.useCallback((e,t)=>{i.mutate(e,t).catch(a.lQ)},[i]);if(o.error&&(0,a.GU)(i.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:c,mutateAsync:o.mutate}}},55347:(e,t,s)=>{Promise.resolve().then(s.bind(s,54008))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63762:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),i=s(48088),n=s(88170),o=s.n(n),a=s(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(t,l);let d={children:["",{children:["vehicles",{children:["[id]",{children:["report",{children:["service-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93906)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\service-history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82196)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\service-history\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/vehicles/[id]/report/service-history/page",pathname:"/vehicles/[id]/report/service-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},75913:(e,t,s)=>{"use strict";s(56397);var r=s(43210),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(r),n="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,i=t.optimizeForSpeed,a=void 0===i?n:i;l(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",l("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){return l(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},s.replaceRule=function(e,t){this._optimizeForSpeed;var s=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},s.deleteRule=function(e){this._serverSheet.deleteRule(e)},s.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},s.cssRules=function(){return this._serverSheet.cssRules},s.makeStyleTag=function(e,t,s){t&&l(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return s?i.insertBefore(r,s):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},c={};function u(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return c[r]||(c[r]="jsx-"+d(e+"-"+s)),c[r]}function h(e,t){var s=e+(t=t.replace(/\/style/gi,"\\/style"));return c[s]||(c[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[s]}var p=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,i=t.optimizeForSpeed,n=void 0!==i&&i;this._sheet=r||new a({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),r&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var s=this.getIdAndRules(e),r=s.styleId,i=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var n=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=n,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var i=u(r,s);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return h(i,e)}):[h(i,t)]}}return{styleId:u(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=r.createContext(null);m.displayName="StyleSheetContext";i.default.useInsertionEffect||i.default.useLayoutEffect;var f=void 0;function x(e){var t=f||r.useContext(m);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},76180:(e,t,s)=>{"use strict";e.exports=s(75913).style},78335:()=>{},78907:(e,t,s)=>{Promise.resolve().then(s.bind(s,93906))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82196:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>i});var r=s(37413);let i={title:"Vehicle Report"};function n({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93906:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\vehicles\\\\[id]\\\\report\\\\service-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\service-history\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,211,1658,8390,2670,4897,474,101,7055,5782,5009,9637,4423],()=>s(63762));module.exports=r})();