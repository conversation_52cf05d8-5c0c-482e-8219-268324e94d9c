exports.id=4101,exports.ids=[4101],exports.modules={824:(e,s,t)=>{"use strict";t.d(s,{e:()=>H});var a=t(60687),l=t(57601),r=t(69559),i=t(76311),n=t(71273),c=t(43210),o=t(96834),d=t(29523),m=t(44493),x=t(22482),h=t(51358),p=t(20659),u=t(58369),g=t(57207),j=t(54052);let y=({widget:e,onConfigure:s,onDelete:t})=>{let{attributes:l,listeners:i,setNodeRef:n,transform:c,transition:o,isDragging:j}=(0,h.gl)({id:e.id,data:{type:"widget",widget:e}}),y={transform:c?`translate3d(${c.x}px, ${c.y}px, 0)`:void 0,transition:o};return(0,a.jsx)("div",{ref:n,style:y,className:(0,x.cn)("group relative",j&&"opacity-50"),children:(0,a.jsx)(m.Zp,{className:"border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors",children:(0,a.jsxs)(m.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{...l,...i,className:"cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded",children:(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"})}),(0,a.jsx)("h4",{className:"text-sm font-medium",children:e.title})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 min-h-[120px] flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(r.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsxs)("div",{className:"text-sm",children:[e.type," Widget"]}),(0,a.jsx)("div",{className:"text-xs mt-1",children:e.config&&Object.keys(e.config).length>0?"Configured":"Click settings to configure"})]})}),(0,a.jsxs)("div",{className:"mt-3 flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",e.type]}),(0,a.jsxs)("span",{children:["Span: ",e.span]})]})]})})})},v=({widgets:e,columns:s,onWidgetConfigure:t,onWidgetDelete:i,className:n=""})=>{let{isOver:c,setNodeRef:o}=(0,l.zM)({id:"drop-zone"});return(0,a.jsx)(m.Zp,{className:(0,x.cn)("min-h-[400px]",n),children:(0,a.jsxs)(m.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Report Canvas"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.length," widget",1!==e.length?"s":""]})]}),(0,a.jsx)("div",{ref:o,className:(0,x.cn)("min-h-[300px] border-2 border-dashed rounded-lg p-4 transition-colors",c?"border-blue-400 bg-blue-50":"border-gray-200",0===e.length&&"flex items-center justify-center"),children:0===e.length?(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(r.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h4",{className:"text-lg font-medium mb-2",children:"Start Building Your Report"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:"Drag widgets from the palette on the left to create your custom report"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-xs",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Drop widgets here"})]})]}):(0,a.jsx)(h.gB,{items:e.map(e=>e.id),strategy:h._G,children:(0,a.jsx)("div",{className:(0,x.cn)("grid gap-4",{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4"}[s]||"grid-cols-2"),children:e.sort((e,s)=>(e.position||0)-(s.position||0)).map(e=>(0,a.jsx)(y,{widget:e,onConfigure:()=>t(e),onDelete:()=>i(e.id)},e.id))})})}),c&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Drop widget here to add to report"})]})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Layout: ",s," column",1!==s?"s":""]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Drag widgets to reorder • Click settings to configure"})]})})]})})};var f=t(27605),N=t(63442),b=t(9275),w=t(63503),k=t(71669),C=t(89667),A=t(15079),S=t(56896);let T=b.z.object({title:b.z.string().min(1,"Title is required"),span:b.z.string(),showTitle:b.z.boolean().default(!0),showBorder:b.z.boolean().default(!0),refreshInterval:b.z.number().min(0).max(1440).optional(),height:b.z.number().min(200).max(800).optional(),chartType:b.z.string().optional(),xAxisField:b.z.string().optional(),yAxisField:b.z.string().optional(),colorScheme:b.z.string().optional(),showLegend:b.z.boolean().optional(),showGrid:b.z.boolean().optional(),pageSize:b.z.number().min(5).max(100).optional(),sortable:b.z.boolean().optional(),filterable:b.z.boolean().optional(),exportable:b.z.boolean().optional(),metricType:b.z.string().optional(),aggregationType:b.z.string().optional(),comparisonPeriod:b.z.string().optional()}),D=[{value:"col-span-1",label:"1 Column"},{value:"col-span-2",label:"2 Columns"},{value:"col-span-3",label:"3 Columns"},{value:"col-span-4",label:"4 Columns"},{value:"col-span-full",label:"Full Width"}],R=[{value:"bar",label:"Bar Chart"},{value:"line",label:"Line Chart"},{value:"pie",label:"Pie Chart"},{value:"area",label:"Area Chart"},{value:"scatter",label:"Scatter Plot"}],E=[{value:"default",label:"Default"},{value:"blue",label:"Blue"},{value:"green",label:"Green"},{value:"red",label:"Red"},{value:"purple",label:"Purple"},{value:"orange",label:"Orange"}],$=({widget:e,onSave:s,onCancel:t})=>{let l=(0,f.mN)({resolver:(0,N.u)(T),defaultValues:{title:e.title,span:e.span,showTitle:e.config?.showTitle??!0,showBorder:e.config?.showBorder??!0,refreshInterval:e.config?.refreshInterval||60,height:e.config?.height||300,chartType:e.config?.chartType||"bar",xAxisField:e.config?.xAxisField||"",yAxisField:e.config?.yAxisField||"",colorScheme:e.config?.colorScheme||"default",showLegend:e.config?.showLegend??!0,showGrid:e.config?.showGrid??!0,pageSize:e.config?.pageSize||10,sortable:e.config?.sortable??!0,filterable:e.config?.filterable??!0,exportable:e.config?.exportable??!0,metricType:e.config?.metricType||"count",aggregationType:e.config?.aggregationType||"sum",comparisonPeriod:e.config?.comparisonPeriod||"previous-month"}}),r=["bar-chart","pie-chart","line-chart"].includes(e.type),i="data-table"===e.type;return["analytics","metrics"].includes(e.type),(0,a.jsx)(w.lG,{open:!0,onOpenChange:t,children:(0,a.jsxs)(w.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(w.c7,{children:[(0,a.jsxs)(w.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Configure Widget: ",e.title]}),(0,a.jsx)(w.rr,{children:"Customize the appearance and behavior of this widget."})]}),(0,a.jsx)(k.lV,{...l,children:(0,a.jsxs)("form",{onSubmit:l.handleSubmit(t=>{s({...e,title:t.title,span:t.span,config:{...e.config,...t}})}),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Basic Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(k.zB,{control:l.control,name:"title",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Widget Title"}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(C.p,{placeholder:"Enter widget title",...e})}),(0,a.jsx)(k.C5,{})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"span",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Width"}),(0,a.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(k.MJ,{children:(0,a.jsx)(A.bq,{children:(0,a.jsx)(A.yv,{placeholder:"Select width"})})}),(0,a.jsx)(A.gC,{children:D.map(e=>(0,a.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(k.C5,{})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(k.zB,{control:l.control,name:"height",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Height (px)"}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(C.p,{type:"number",min:"200",max:"800",...e,onChange:s=>e.onChange(parseInt(s.target.value)||300)})}),(0,a.jsx)(k.C5,{})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"refreshInterval",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Refresh Interval (minutes)"}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(C.p,{type:"number",min:"0",max:"1440",...e,onChange:s=>e.onChange(parseInt(s.target.value)||60)})}),(0,a.jsx)(k.C5,{})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(k.zB,{control:l.control,name:"showTitle",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Show Title"}),(0,a.jsx)(k.Rr,{children:"Display widget title"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value,onCheckedChange:e.onChange})})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"showBorder",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Show Border"}),(0,a.jsx)(k.Rr,{children:"Display widget border"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value,onCheckedChange:e.onChange})})]})})]})]}),r&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Chart Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(k.zB,{control:l.control,name:"chartType",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Chart Type"}),(0,a.jsxs)(A.l6,{onValueChange:e.onChange,value:e.value||"",children:[(0,a.jsx)(k.MJ,{children:(0,a.jsx)(A.bq,{children:(0,a.jsx)(A.yv,{placeholder:"Select chart type"})})}),(0,a.jsx)(A.gC,{children:R.map(e=>(0,a.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(k.C5,{})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"colorScheme",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Color Scheme"}),(0,a.jsxs)(A.l6,{onValueChange:e.onChange,value:e.value||"",children:[(0,a.jsx)(k.MJ,{children:(0,a.jsx)(A.bq,{children:(0,a.jsx)(A.yv,{placeholder:"Select color scheme"})})}),(0,a.jsx)(A.gC,{children:E.map(e=>(0,a.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(k.C5,{})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(k.zB,{control:l.control,name:"showLegend",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Show Legend"}),(0,a.jsx)(k.Rr,{children:"Display chart legend"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value||!1,onCheckedChange:e.onChange})})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"showGrid",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Show Grid"}),(0,a.jsx)(k.Rr,{children:"Display chart grid lines"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value||!1,onCheckedChange:e.onChange})})]})})]})]}),i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Table Settings"}),(0,a.jsx)(k.zB,{control:l.control,name:"pageSize",render:({field:e})=>(0,a.jsxs)(k.eI,{children:[(0,a.jsx)(k.lR,{children:"Page Size"}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(C.p,{type:"number",min:"5",max:"100",...e,onChange:s=>e.onChange(parseInt(s.target.value)||10)})}),(0,a.jsx)(k.Rr,{children:"Number of rows per page"}),(0,a.jsx)(k.C5,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)(k.zB,{control:l.control,name:"sortable",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Sortable"}),(0,a.jsx)(k.Rr,{children:"Enable column sorting"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value||!1,onCheckedChange:e.onChange})})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"filterable",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Filterable"}),(0,a.jsx)(k.Rr,{children:"Enable column filters"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value||!1,onCheckedChange:e.onChange})})]})}),(0,a.jsx)(k.zB,{control:l.control,name:"exportable",render:({field:e})=>(0,a.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(k.lR,{children:"Exportable"}),(0,a.jsx)(k.Rr,{children:"Enable data export"})]}),(0,a.jsx)(k.MJ,{children:(0,a.jsx)(S.S,{checked:e.value||!1,onCheckedChange:e.onChange})})]})})]})]}),(0,a.jsxs)(w.Es,{children:[(0,a.jsx)(d.$,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),(0,a.jsx)(d.$,{type:"submit",children:"Save Configuration"})]})]})})]})})};var P=t(8751),M=t(27629),z=t(22853),F=t(3341),V=t(3746),I=t(48206),L=t(24920),O=t(92865),W=t(36644),B=t(14328),Z=t(26622),U=t(36558);let G=({widget:e,isDisabled:s=!1})=>{let{attributes:t,listeners:r,setNodeRef:i,transform:n,isDragging:c}=(0,l.PM)({id:e.id,data:{type:"widget-type",widgetType:e.id},disabled:s}),d=n?{transform:`translate3d(${n.x}px, ${n.y}px, 0)`}:void 0;return(0,a.jsxs)("div",{ref:i,style:d,...r,...t,className:(0,x.cn)("p-3 border rounded-lg cursor-grab active:cursor-grabbing transition-all",c&&"opacity-50",s?"opacity-50 cursor-not-allowed":"hover:shadow-md hover:border-blue-300",!s&&"bg-white"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[e.icon,(0,a.jsx)("span",{className:"text-sm font-medium",children:e.name})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.description}),(0,a.jsx)(o.E,{variant:"outline",className:"text-xs",children:e.category})]})},_=[{id:"analytics",name:"Analytics Widget",description:"Key metrics and performance indicators",icon:(0,a.jsx)(P.A,{className:"h-4 w-4"}),category:"Analytics",supportedDataSources:["delegations","tasks","vehicles","employees","cross-entity"]},{id:"metrics",name:"Metrics Widget",description:"Display key performance metrics",icon:(0,a.jsx)(M.A,{className:"h-4 w-4"}),category:"Analytics",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"bar-chart",name:"Bar Chart",description:"Compare values across categories",icon:(0,a.jsx)(M.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"pie-chart",name:"Pie Chart",description:"Show proportional data distribution",icon:(0,a.jsx)(z.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"line-chart",name:"Line Chart",description:"Display trends over time",icon:(0,a.jsx)(F.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"data-table",name:"Data Table",description:"Tabular data with sorting and filtering",icon:(0,a.jsx)(V.A,{className:"h-4 w-4"}),category:"Data",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"employee-performance",name:"Employee Performance",description:"Employee performance metrics and charts",icon:(0,a.jsx)(I.A,{className:"h-4 w-4"}),category:"Employee",supportedDataSources:["employees","cross-entity"]},{id:"vehicle-utilization",name:"Vehicle Utilization",description:"Vehicle usage and utilization metrics",icon:(0,a.jsx)(L.A,{className:"h-4 w-4"}),category:"Vehicle",supportedDataSources:["vehicles","cross-entity"]},{id:"task-status",name:"Task Status",description:"Task completion and status tracking",icon:(0,a.jsx)(O.A,{className:"h-4 w-4"}),category:"Task",supportedDataSources:["tasks","cross-entity"]},{id:"delegation-overview",name:"Delegation Overview",description:"Delegation status and distribution",icon:(0,a.jsx)(W.A,{className:"h-4 w-4"}),category:"Delegation",supportedDataSources:["delegations","cross-entity"]},{id:"correlation",name:"Correlation Analysis",description:"Cross-entity relationships and correlations",icon:(0,a.jsx)(B.A,{className:"h-4 w-4"}),category:"Analysis",supportedDataSources:["cross-entity"]},{id:"timeline",name:"Timeline Widget",description:"Events and activities over time",icon:(0,a.jsx)(Z.A,{className:"h-4 w-4"}),category:"Timeline",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"cost-analysis",name:"Cost Analysis",description:"Financial metrics and cost tracking",icon:(0,a.jsx)(U.A,{className:"h-4 w-4"}),category:"Financial",supportedDataSources:["vehicles","employees","cross-entity"]}],J=({dataSource:e,className:s=""})=>{let t=_.filter(s=>s.supportedDataSources.includes(e)||s.supportedDataSources.includes("cross-entity")),l=t.reduce((e,s)=>(e[s.category]||(e[s.category]=[]),e[s.category].push(s),e),{});return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsxs)(m.aR,{className:"pb-3",children:[(0,a.jsx)(m.ZB,{className:"text-lg",children:"Widget Palette"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Drag widgets to the report canvas to build your custom report"}),(0,a.jsxs)(o.E,{variant:"secondary",className:"text-xs w-fit",children:[t.length," widgets available"]})]}),(0,a.jsxs)(m.Wu,{className:"space-y-4",children:[Object.entries(l).map(([s,t])=>(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2 text-gray-700",children:s}),(0,a.jsx)("div",{className:"space-y-2",children:t.map(s=>(0,a.jsx)(G,{widget:s,isDisabled:!s.supportedDataSources.includes(e)&&!s.supportedDataSources.includes("cross-entity")},s.id))})]},s)),0===t.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,a.jsx)("div",{className:"text-sm",children:"No widgets available for this data source"})})]})]})},H=({className:e="",onPreview:s,onSave:t,reportType:h})=>{let[p,u]=(0,c.useState)(h?.widgetConfigs||[]),[g,j]=(0,c.useState)(null),[y,f]=(0,c.useState)(!1),[N,b]=(0,c.useState)(null),[w,k]=(0,c.useState)({dataSource:h?.dataSource||"delegations",description:h?.description||"",filters:h?.filters||[],layout:{columns:2,spacing:4},name:h?.name||"New Report",widgets:p});return(0,a.jsxs)("div",{className:(0,x.cn)("space-y-6",e),children:[(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"size-5"}),"Report Builder"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(o.E,{className:"text-xs",variant:"secondary",children:[p.length," widgets"]}),(0,a.jsxs)(d.$,{onClick:()=>{s&&s(w)},size:"sm",variant:"outline",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),"Preview"]}),(0,a.jsxs)(d.$,{onClick:()=>{t&&t(w)},size:"sm",children:[(0,a.jsx)(n.A,{className:"mr-2 size-4"}),"Save Report"]})]})]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Report Name"}),(0,a.jsx)("input",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>k(s=>({...s,name:e.target.value})),type:"text",value:w.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Data Source"}),(0,a.jsxs)("select",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>k(s=>({...s,dataSource:e.target.value})),value:w.dataSource,children:[(0,a.jsx)("option",{value:"delegations",children:"Delegations"}),(0,a.jsx)("option",{value:"tasks",children:"Tasks"}),(0,a.jsx)("option",{value:"vehicles",children:"Vehicles"}),(0,a.jsx)("option",{value:"employees",children:"Employees"}),(0,a.jsx)("option",{value:"cross-entity",children:"Cross-Entity"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Layout Columns"}),(0,a.jsxs)("select",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>k(s=>({...s,layout:{...s.layout,columns:Number.parseInt(e.target.value)}})),value:w.layout.columns,children:[(0,a.jsx)("option",{value:1,children:"1 Column"}),(0,a.jsx)("option",{value:2,children:"2 Columns"}),(0,a.jsx)("option",{value:3,children:"3 Columns"}),(0,a.jsx)("option",{value:4,children:"4 Columns"})]})]})]})})]}),(0,a.jsxs)(l.Mp,{onDragEnd:e=>{let{active:s,over:t}=e;if(!t)return void b(null);if("drop-zone"===t.id&&s.data.current?.type==="widget-type"){let e=s.data.current.widgetType,t={config:{},id:`widget-${Date.now()}`,position:p.length,span:"col-span-1",title:`${e} Widget`,type:e};u(e=>[...e,t]),k(e=>({...e,widgets:[...e.widgets,t]}))}if(t.id!==s.id&&s.data.current?.type==="widget"){let e=p.findIndex(e=>e.id===s.id),a=p.findIndex(e=>e.id===t.id);if(-1!==e&&-1!==a){let s=[...p],[t]=s.splice(e,1);t&&s.splice(a,0,t);let l=s.map((e,s)=>({...e,position:s}));u(l),k(e=>({...e,widgets:l}))}}b(null)},onDragStart:e=>{b(e.active.id)},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(J,{dataSource:w.dataSource})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(v,{columns:w.layout.columns,onWidgetConfigure:e=>{j(e),f(!0)},onWidgetDelete:e=>{let s=p.filter(s=>s.id!==e);u(s),k(e=>({...e,widgets:s}))},widgets:p})})]}),(0,a.jsx)(l.Hd,{children:N?(0,a.jsx)("div",{className:"rounded-lg border bg-white p-4 shadow-lg",children:(0,a.jsx)("div",{className:"text-sm font-medium",children:N.replace("-"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())})}):null})]}),y&&g&&(0,a.jsx)($,{onCancel:()=>{f(!1),j(null)},onSave:e=>{let s=p.map(s=>s.id===e.id?e:s);u(s),k(e=>({...e,widgets:s})),f(!1),j(null)},widget:g})]})}},7889:(e,s,t)=>{"use strict";t.d(s,{GW:()=>n});var a=t(60687);t(43210);var l=t(95668),r=t(22482);let i=({children:e,className:s="",config:t})=>(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:(0,r.cn)("min-h-screen bg-background",s),children:(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:e})})})}),n=({children:e,className:s="",config:t})=>(0,a.jsx)(i,{config:t,className:s,children:(0,a.jsx)("div",{className:"space-y-8",children:e})})},10053:(e,s,t)=>{"use strict";t.d(s,{st:()=>ss,j3:()=>L,Sp:()=>J,BR:()=>eQ,tp:()=>e4,AN:()=>e6,Wf:()=>si,AP:()=>X,xr:()=>ew,Iq:()=>ep,B:()=>eu,Uq:()=>el,Ny:()=>eP,SV:()=>eJ,EO:()=>eB,VH:()=>eF});var a=t(60687),l=t(43210),r=t.n(l),i=t(48482),n=t(53892),c=t(69206),o=t(25679),d=t(38246),m=t(44493),x=t(30259),h=t(8342);let p=e=>({Planned:"#3b82f6",Confirmed:"#10b981",In_Progress:"#f59e0b",Completed:"#22c55e",Cancelled:"#ef4444",No_details:"#6b7280"})[e]||"#6b7280",u=e=>{let s={totalCount:e.totalCount||0,statusDistribution:e.statusDistribution?.map(e=>({status:e.status,count:e.count||0,percentage:e.percentage||0,color:p(e.status)}))||[],trendData:e.trendData?.map(e=>({date:e.date,created:e.created||0,completed:e.completed||0,inProgress:e.inProgress||0}))||[],locationMetrics:e.locationMetrics||[],summary:{totalDelegations:e.summary?.totalDelegations||0,activeDelegations:e.summary?.activeDelegations||0,completedDelegations:e.summary?.completedDelegations||0,totalDelegates:e.summary?.totalDelegates||0,averageDuration:e.summary?.averageDuration||0,completionRate:e.summary?.completionRate||0},delegations:[]};return e.serviceHistory&&(s.serviceHistory=v(e.serviceHistory)),e.serviceCosts&&"object"==typeof e.serviceCosts&&!Array.isArray(e.serviceCosts)&&(s.serviceCosts=f(e.serviceCosts)),e.taskData&&"object"==typeof e.taskData&&!Array.isArray(e.taskData)&&(s.taskData=N(e.taskData)),s.delegations=e.delegations?.map(e=>({id:(e.id||0).toString(),delegationId:e.delegationId||e.id?.toString()||"",customerName:e.customerName||e.customer?.name||"Unknown Customer",vehicleModel:e.vehicleModel||e.vehicle?.model||"Unknown Vehicle",licensePlate:e.licensePlate||e.vehicle?.licensePlate||"Unknown",status:e.status,assignedEmployee:e.driverEmployee?.name||e.staffEmployee?.name||"Unassigned",location:e.location||"",createdAt:e.createdAt||"",completedAt:e.completedAt}))||[],s},g=e=>({totalTasks:e.totalTasks||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,inProgressTasks:e.inProgressTasks||0,overdueTasks:e.overdueTasks||0,averageCompletionTime:e.averageCompletionTime||0,tasksByPriority:e.tasksByPriority?.map(e=>({priority:e.priority,count:e.count||0}))||[],tasksByStatus:e.tasksByStatus?.map(e=>({status:e.status,count:e.count||0}))||[]}),j=e=>e?.map(e=>({date:e.date,created:e.created||0,completed:e.completed||0,inProgress:e.inProgress||0}))||[],y=e=>e?.map(e=>({location:e.location||"",delegationCount:e.delegationCount||e.delegationsCount||e.count||0,averageDuration:e.averageDuration||0,completionRate:e.completionRate||0}))||[],v=e=>({id:e.id||"",vehicleId:e.vehicleId||0,vehicleName:e.vehicleName||"",serviceType:e.serviceType,status:e.status,scheduledDate:e.scheduledDate||"",completedDate:e.completedDate,cost:e.cost||0,description:e.description||"",relatedDelegationId:e.relatedDelegationId,relatedTaskId:e.relatedTaskId}),f=e=>({totalCost:e.totalCost||0,averageCostPerService:e.averageCostPerService||0,costByType:e.costByType?.map(e=>({type:e.type,cost:e.cost||0,count:e.count||0}))||[],monthlyTrend:e.monthlyTrend?.map(e=>({month:e.month||"",cost:e.cost||0}))||[]}),N=e=>({totalTasks:e.totalTasks||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,overdueTasks:e.overdueTasks||0,averageCompletionTime:e.averageCompletionTime||0,tasksByPriority:e.tasksByPriority?.map(e=>({priority:e.priority,count:e.count||0}))||[]});class b{constructor(e="/api/reporting"){this.baseUrl=e}async getCrossEntityAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.includeCrossEntityCorrelations&&t.append("includeCrossEntityCorrelations","true");let a=await h.uE.get(`/reporting/cross-entity/analytics?${t.toString()}`);return a.data||a}catch(e){throw console.error("Error fetching cross-entity analytics:",e),Error(`Failed to load cross-entity analytics: ${e instanceof Error?e.message:"Unknown error"}`)}}async getDelegationAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.includeServiceHistory&&t.append("includeServiceHistory","true"),e.includeTaskData&&t.append("includeTaskData","true");let a=await h.uE.get(`/reporting/delegations/analytics?${t.toString()}`);return u(a.data||a)}catch(e){throw console.error("Error fetching delegation analytics:",e),Error(`Failed to load delegation analytics: ${e instanceof Error?e.message:"Unknown error"}`)}}async getDelegations(e,s){try{let t=this.buildQueryParams(e),a=new URLSearchParams(t);a.append("page",s.page.toString()),a.append("pageSize",s.pageSize.toString());let l=await h.uE.get(`/reporting/delegations?${a.toString()}`);return l.data||l}catch(e){throw console.error("Error fetching delegations:",e),Error(`Failed to load delegations: ${e instanceof Error?e.message:"Unknown error"}`)}}async getEmployeeAnalytics(e){try{let s=this.buildQueryParams(e),t=await h.uE.get(`/reporting/employee/analytics?${s}`);return t.data||t}catch(e){throw console.error("Error fetching employee analytics:",e),Error(`Failed to load employee analytics: ${e instanceof Error?e.message:"Unknown error"}`)}}async getLocationMetrics(e){try{let s=this.buildQueryParams(e),t=await h.uE.get(`/reporting/locations/metrics?${s}`);return y(t.data||t)}catch(e){throw console.error("Error fetching location metrics:",e),Error(`Failed to load location metrics: ${e instanceof Error?e.message:"Unknown error"}`)}}async getServiceCostSummary(e){try{let s=this.buildQueryParams(e);return(await h.uE.get(`/reporting/services/costs?${s}`)).data||{averageCostPerService:0,costByType:[],monthlyTrend:[],totalCost:0}}catch(e){throw console.error("Error fetching service costs:",e),Error(`Failed to load service costs: ${e instanceof Error?e.message:"Unknown error"}`)}}async getServiceHistory(e){try{let s=this.buildQueryParams(e);return(await h.uE.get(`/reporting/services/history?${s}`)).data||[]}catch(e){throw console.error("Error fetching service history:",e),Error(`Failed to load service history: ${e instanceof Error?e.message:"Unknown error"}`)}}async getTaskAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.taskStatus&&e.taskStatus.length>0&&t.append("taskStatus",e.taskStatus.join(",")),e.taskPriority&&e.taskPriority.length>0&&t.append("taskPriority",e.taskPriority.join(","));let a=await h.uE.get(`/reporting/tasks/analytics?${t.toString()}`);return a.data||a}catch(e){throw console.error("Error fetching task analytics:",e),Error(`Failed to load task analytics: ${e instanceof Error?e.message:"Unknown error"}`)}}async getTaskMetrics(e){try{let s=e?.length?`delegationIds=${e.join(",")}`:"",t=await h.uE.get(`/reporting/tasks/metrics?${s}`);return g(t.data||t)}catch(e){throw console.error("Error fetching task metrics:",e),Error(`Failed to load task metrics: ${e instanceof Error?e.message:"Unknown error"}`)}}async getTrendData(e){try{let s=this.buildQueryParams(e),t=await h.uE.get(`/reporting/trends?${s}`);return j(t.data||t)}catch(e){throw console.error("Error fetching trend data:",e),Error(`Failed to load trend data: ${e instanceof Error?e.message:"Unknown error"}`)}}async getVehicleAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.vehicles&&e.vehicles.length>0&&t.append("vehicles",e.vehicles.join(",")),e.serviceTypes&&e.serviceTypes.length>0&&t.append("serviceTypes",e.serviceTypes.join(",")),e.serviceStatus&&e.serviceStatus.length>0&&t.append("serviceStatus",e.serviceStatus.join(","));let a=`/reporting/vehicles/analytics${t.toString()?`?${t.toString()}`:""}`,l=await h.uE.get(a);return l.data||l}catch(e){throw console.error("Error fetching vehicle analytics:",e),e}}appendArrayParams(e,s,t){t&&t.length>0&&e.append(s,t.join(","))}buildQueryParams(e){let s=new URLSearchParams;try{let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);if(isNaN(t.getTime())||isNaN(a.getTime()))throw TypeError("Invalid date range provided");s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}catch(a){console.error("Error processing date range:",a);let e=new Date,t=new Date(Date.now()-2592e6);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",e.toISOString().split("T")[0]||e.toISOString())}return this.appendArrayParams(s,"status",e.status),this.appendArrayParams(s,"locations",e.locations),this.appendArrayParams(s,"employees",e.employees),this.appendArrayParams(s,"vehicles",e.vehicles),e.taskStatus&&this.appendArrayParams(s,"taskStatus",e.taskStatus),e.taskPriority&&this.appendArrayParams(s,"taskPriority",e.taskPriority),e.serviceTypes&&this.appendArrayParams(s,"serviceTypes",e.serviceTypes),e.serviceStatus&&this.appendArrayParams(s,"serviceStatus",e.serviceStatus),e.costRange&&(s.append("minCost",e.costRange.min.toString()),s.append("maxCost",e.costRange.max.toString())),s.toString()}}let w=new b;var k=t(95594);let C=(e,s,t)=>{let a=(0,k.Sk)(["delegations",e,s],()=>w.getDelegationAnalytics(e),{placeholderData:e=>e,showErrorToast:!0,...t}),r=(0,l.useMemo)(()=>{if(!a.data?.delegations)return;let e=a.data.delegations,t=(s.page-1)*s.pageSize,l=t+s.pageSize;return{data:e.slice(t,l).map(e=>({id:e.id,delegationId:e.id.toString(),customerName:e.title,vehicleModel:"N/A",licensePlate:"N/A",status:e.status,assignedEmployee:e.assignedTo,location:e.location,createdAt:e.createdAt,completedAt:e.completedAt||null})),meta:{total:e.length,page:s.page,pageSize:s.pageSize,totalPages:Math.ceil(e.length/s.pageSize)}}},[a.data?.delegations,s.page,s.pageSize]);return{...a,data:r}};t(23133);var A=t(43612);let S={all:["reporting"],analytics:()=>[...S.all,"analytics"],delegationAnalytics:e=>[...S.analytics(),"delegations",e],taskMetrics:e=>[...S.all,"tasks","metrics",e],trends:e=>[...S.all,"trends",e],locationMetrics:e=>[...S.all,"locations","metrics",e],serviceHistory:e=>[...S.all,"services","history",e],serviceCosts:e=>[...S.all,"services","costs",e],taskAnalytics:e=>[...S.analytics(),"tasks",e],vehicleAnalytics:e=>[...S.analytics(),"vehicles",e],employeeAnalytics:e=>[...S.analytics(),"employees",e],crossEntityAnalytics:e=>[...S.analytics(),"cross-entity",e]},T=(e,s)=>{let t=(0,l.useMemo)(()=>S.delegationAnalytics(e),[e]),a=(0,l.useCallback)(()=>w.getDelegationAnalytics(e),[e]);return(0,A.I)({queryKey:t,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnMount:!0,...s})},D=(e,s)=>{let t=(0,l.useMemo)(()=>S.trends(e),[e]),a=(0,l.useCallback)(()=>w.getTrendData(e),[e]);return(0,A.I)({queryKey:t,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,...s})},R=(e,s)=>{let t=(0,l.useMemo)(()=>S.locationMetrics(e),[e]),a=(0,l.useCallback)(()=>w.getLocationMetrics(e),[e]);return(0,A.I)({queryKey:t,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,...s})},E=(e,s)=>{let t=(0,l.useMemo)(()=>S.taskAnalytics(e),[e]),a=(0,l.useCallback)(()=>w.getTaskAnalytics(e),[e]);return(0,A.I)({queryKey:t,queryFn:a,staleTime:18e4,gcTime:48e4,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,enabled:!!e.includeTaskData,...s})};var $=t(84122),P=t(85726),M=t(91821),z=t(27629),F=t(3662);let V=({payload:e})=>e&&0!==e.length?(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-6 pt-4 border-t border-border",children:e.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:e.value}),e.payload&&(0,a.jsxs)("span",{className:"text-muted-foreground",children:["(",e.payload.count,")"]})]},`legend-${s}`))}):null,I=({active:e,payload:s})=>{if(e&&s&&s.length){let e=s[0],t=s[0].payload.total||100,l=t>0?Math.round(e.value/t*100):0;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2",children:e.name}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Count:"}),(0,a.jsx)("span",{className:"font-bold text-lg",style:{color:e.color},children:e.value})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Percentage:"}),(0,a.jsxs)("span",{className:"font-medium text-primary",children:[l,"%"]})]})]})]})}return null},L=()=>{let{data:e,isLoading:s,error:t}=T((0,$.N8)());if(s)return(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsx)(P.E,{className:"h-7 w-48"})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(P.E,{className:"h-[400px] w-full rounded-lg"})})]});if(t)return(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(z.A,{className:"h-6 w-6"}),"Delegation Status"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsxs)(M.Fc,{variant:"destructive",children:[(0,a.jsx)(M.XL,{children:"Error"}),(0,a.jsx)(M.TN,{children:t.message})]})})]});let l=e?.statusDistribution??[],r=l.reduce((e,s)=>e+s.count,0),x=l.map(e=>({...e,total:r})),h=["#22c55e","#f59e0b","#ef4444","#3b82f6","#8b5cf6"],p=x.map((e,s)=>({...e,color:e.color||h[s%h.length]}));return(0,a.jsxs)(m.Zp,{children:[(0,a.jsxs)(m.aR,{className:"pb-6",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(z.A,{className:"h-6 w-6 text-primary"}),"Delegation Status"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(F.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Distribution of delegation statuses"})]})]}),(0,a.jsxs)(m.Wu,{className:"pt-6",children:[(0,a.jsx)(i.u,{width:"100%",height:300,children:(0,a.jsxs)(n.r,{children:[(0,a.jsx)(c.F,{data:p,cx:"50%",cy:"50%",labelLine:!1,outerRadius:100,innerRadius:40,fill:"#8884d8",dataKey:"count",nameKey:"status",stroke:"#fff",strokeWidth:2,children:p.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))}),(0,a.jsx)(d.m,{content:(0,a.jsx)(I,{})})]})}),(0,a.jsx)(V,{payload:p.map(e=>({value:e.status,color:e.color,payload:e}))}),r>0&&(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Delegations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:r})]}),p.length>0&&p[0]&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Most Common Status:"}),(0,a.jsx)("span",{className:"font-semibold",style:{color:p[0].color},children:p[0].status})]})]})]})]})};var O=t(61678),W=t(85168),B=t(27747),Z=t(9920),U=t(57359),G=t(50326),_=t(8751);let J=()=>{let{data:e,isLoading:s,error:t}=D((0,$.N8)());return s?(0,a.jsx)(P.E,{className:"h-[350px] w-full"}):t?(0,a.jsxs)(M.Fc,{variant:"destructive",children:[(0,a.jsx)(M.XL,{children:"Error"}),(0,a.jsx)(M.TN,{children:t.message})]}):(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"h-5 w-5"}),"Delegation Trends"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(i.u,{width:"100%",height:300,children:(0,a.jsxs)(O.b,{data:e||[],children:[(0,a.jsx)(W.d,{strokeDasharray:"3 3"}),(0,a.jsx)(B.W,{dataKey:"date"}),(0,a.jsx)(Z.h,{}),(0,a.jsx)(d.m,{}),(0,a.jsx)(U.s,{}),(0,a.jsx)(G.N,{type:"monotone",dataKey:"created",stroke:"#8884d8",name:"Created"}),(0,a.jsx)(G.N,{type:"monotone",dataKey:"completed",stroke:"#82ca9d",name:"Completed"}),(0,a.jsx)(G.N,{type:"monotone",dataKey:"inProgress",stroke:"#ffc658",name:"In Progress"})]})})})]})};var H=t(2041),K=t(90812),q=t(26398);let Q=({active:e,payload:s,label:t})=>{if(e&&s&&s.length){let e=s[0].payload,l=s[0].payload.total||100,r=l>0?Math.round(e.delegationCount/l*100):0;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:t}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Delegations:"}),(0,a.jsx)("span",{className:"font-bold text-blue-600 dark:text-blue-400 text-lg",children:e.delegationCount})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Percentage:"}),(0,a.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[r,"%"]})]}),void 0!==e.completionRate&&(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Completion Rate:"}),(0,a.jsxs)("span",{className:"font-medium text-green-600 dark:text-green-400",children:[e.completionRate,"%"]})]}),e.averageResponseTime&&(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avg Response:"}),(0,a.jsxs)("span",{className:"font-medium text-yellow-600 dark:text-yellow-400",children:[e.averageResponseTime,"h"]})]})]})]})}return null},X=()=>{let{data:e,isLoading:s,error:t}=R((0,$.N8)());if(s)return(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsx)(P.E,{className:"h-7 w-48"})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(P.E,{className:"h-[350px] w-full rounded-lg"})})]});if(t)return(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(q.A,{className:"h-6 w-6"}),"Location Distribution"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsxs)(M.Fc,{variant:"destructive",children:[(0,a.jsx)(M.XL,{children:"Error"}),(0,a.jsx)(M.TN,{children:t.message})]})})]});let l=e?.reduce((e,s)=>e+s.delegationCount,0)||0,r=e?.map(e=>({...e,total:l}))||[];return(0,a.jsxs)(m.Zp,{children:[(0,a.jsxs)(m.aR,{className:"pb-6",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(q.A,{className:"h-6 w-6 text-primary"}),"Location Distribution"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(_.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Delegation distribution across locations"})]})]}),(0,a.jsxs)(m.Wu,{className:"pt-6",children:[(0,a.jsx)(i.u,{width:"100%",height:350,children:(0,a.jsxs)(H.E,{data:r,layout:"vertical",margin:{top:8,right:40,left:20,bottom:8},children:[(0,a.jsx)(W.d,{strokeDasharray:"3 3",stroke:"#f1f5f9",horizontal:!0,vertical:!1}),(0,a.jsx)(B.W,{type:"number",fontSize:12,tick:{fill:"#64748b"},axisLine:{stroke:"#e2e8f0"},tickLine:{stroke:"#e2e8f0"}}),(0,a.jsx)(Z.h,{type:"category",dataKey:"location",width:140,fontSize:12,tick:{fill:"#374151",fontSize:12},axisLine:{stroke:"#e2e8f0"},tickLine:{stroke:"#e2e8f0"},orientation:"left"}),(0,a.jsx)(d.m,{content:(0,a.jsx)(Q,{})}),(0,a.jsx)(K.y,{dataKey:"delegationCount",fill:"#3b82f6",name:"Delegations",radius:[0,6,6,0],stroke:"#2563eb",strokeWidth:1})]})}),l>0&&(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Delegations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:l})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Active Locations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:r.length})]})]})]})]})};var Y=t(29523),ee=t(92865),es=t(14975),et=t(15036),ea=t(48206);let el=({className:e="",data:s=[],height:t=300,interactive:r=!0,showLegend:x=!0})=>{let h=(0,l.useMemo)(()=>s.map(e=>({color:e.color||ei(e.status),name:er(e.status),percentage:Math.round(e.percentage),status:e.status,value:e.count})),[s]),p=(0,l.useMemo)(()=>h.reduce((e,s)=>e+s.value,0),[h]);return s&&0!==s.length?(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsxs)(m.aR,{children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"size-5"}),"Task Status Distribution"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total Tasks: ",p]})]}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(i.u,{height:t,width:"100%",children:(0,a.jsxs)(n.r,{children:[(0,a.jsx)(c.F,{cx:"50%",cy:"50%",data:h,dataKey:"value",fill:"#8884d8",label:({name:e,percentage:s})=>`${e}: ${s}%`,labelLine:!1,outerRadius:Math.min(.3*t,100),children:h.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))}),r&&(0,a.jsx)(d.m,{content:(0,a.jsx)(({active:e,payload:s})=>{if(e&&s?.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg dark:bg-gray-800",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.value})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},{})}),x&&(0,a.jsx)(U.s,{content:(0,a.jsx)(({payload:e})=>x&&e?(0,a.jsx)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:e.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"size-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.value," (",e.payload.percentage,"%)"]})]},s))}):null,{})})]})})})]}):(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"size-5"}),"Task Status Distribution"]})}),(0,a.jsx)(m.Wu,{className:"flex items-center justify-center",style:{height:t},children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(z.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No task status data available"})]})})]})},er=e=>({Assigned:"Assigned",Cancelled:"Cancelled",Completed:"Completed",In_Progress:"In Progress",Pending:"Pending"})[e]||e,ei=e=>({Assigned:"#3b82f6",Cancelled:"#ef4444",Completed:"#10b981",In_Progress:"#8b5cf6",Pending:"#f59e0b"})[e]||"#6b7280";var en=t(88514);let ec=({data:e=[],className:s="",showLegend:t=!0,interactive:r=!0,height:n=300})=>{let c=(0,l.useMemo)(()=>e.map(e=>({name:eo(e.priority),value:e.count,color:e.color||ed(e.priority),percentage:Math.round(e.percentage),priority:e.priority})),[e]),x=(0,l.useMemo)(()=>c.reduce((e,s)=>e+s.value,0),[c]);return e&&0!==e.length?(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsxs)(m.aR,{children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(en.A,{className:"h-5 w-5"}),"Task Priority Distribution"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total Tasks: ",x]})]}),(0,a.jsxs)(m.Wu,{children:[(0,a.jsx)(i.u,{width:"100%",height:n,children:(0,a.jsxs)(H.E,{data:c,margin:{top:20,right:30,left:20,bottom:5},children:[(0,a.jsx)(W.d,{strokeDasharray:"3 3",className:"opacity-30"}),(0,a.jsx)(B.W,{dataKey:"name",tick:{fontSize:12},className:"text-muted-foreground"}),(0,a.jsx)(Z.h,{tick:{fontSize:12},className:"text-muted-foreground"}),r&&(0,a.jsx)(d.m,{content:(0,a.jsx)(({active:e,payload:s,label:t})=>{if(e&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:t}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.value})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},{})}),(0,a.jsx)(K.y,{dataKey:"value",radius:[4,4,0,0],fill:"#8884d8",children:c.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))})]})}),t&&(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-4",children:c.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.name,": ",e.value," (",e.percentage,"%)"]})]},s))})]})]}):(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(en.A,{className:"h-5 w-5"}),"Task Priority Distribution"]})}),(0,a.jsx)(m.Wu,{className:"flex items-center justify-center",style:{height:n},children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(en.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No task priority data available"})]})})]})},eo=e=>({Low:"Low",Medium:"Medium",High:"High"})[e]||e,ed=e=>({Low:"#10b981",Medium:"#f59e0b",High:"#ef4444"})[e]||"#6b7280";var em=t(96834),ex=t(23562),eh=t(20620);let ep=({data:e=[],className:s="",showExportOptions:t=!0,maxDisplayItems:r=10})=>{let i=(0,l.useMemo)(()=>e.sort((e,s)=>s.completionRate-e.completionRate).slice(0,r),[e,r]),n=(0,l.useMemo)(()=>{if(!e||0===e.length)return{totalEmployees:0,totalAssignedTasks:0,totalCompletedTasks:0,averageCompletionRate:0,averageCompletionTime:0};let s=e.reduce((e,s)=>e+s.assignedTasks,0),t=e.reduce((e,s)=>e+s.completedTasks,0),a=e.reduce((e,s)=>e+s.completionRate,0)/e.length,l=e.reduce((e,s)=>e+s.averageCompletionTime,0)/e.length;return{totalEmployees:e.length,totalAssignedTasks:s,totalCompletedTasks:t,averageCompletionRate:Math.round(a),averageCompletionTime:Math.round(10*l)/10}},[e]),c=async()=>{try{if(!e||0===e.length)return void console.warn("No data to export");let s=e.map(e=>({"Employee Name":e.employeeName,"Assigned Tasks":e.assignedTasks,"Completed Tasks":e.completedTasks,"Completion Rate (%)":Math.round(e.completionRate),"Average Completion Time (days)":e.averageCompletionTime}));if(0===s.length)return void console.warn("No data to export");let t=[Object.keys(s[0]).join(","),...s.map(e=>Object.values(e).join(","))].join("\n"),a=new Blob([t],{type:"text/csv"}),l=window.URL.createObjectURL(a),r=document.createElement("a");r.href=l,r.download=`task-assignment-metrics-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(l),document.body.removeChild(r)}catch(e){console.error("Export failed:",e)}},o=e=>e>=90?{variant:"default",label:"Excellent"}:e>=75?{variant:"secondary",label:"Good"}:e>=60?{variant:"outline",label:"Average"}:{variant:"destructive",label:"Needs Improvement"};return e&&0!==e.length?(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Task Assignment Metrics"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Employee task performance and completion rates"})]}),t&&(0,a.jsxs)(Y.$,{variant:"outline",size:"sm",onClick:c,className:"h-8",children:[(0,a.jsx)(eh.A,{className:"h-3 w-3 mr-1"}),"Export CSV"]})]}),(0,a.jsxs)(m.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-blue-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:n.totalEmployees})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-blue-700",children:"Employees"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-purple-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(_.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:n.totalAssignedTasks})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-purple-700",children:"Assigned"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-green-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(F.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-green-600",children:n.totalCompletedTasks})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-700",children:"Completed"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-emerald-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(_.A,{className:"h-5 w-5 text-emerald-600"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-emerald-600",children:[n.averageCompletionRate,"%"]})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-emerald-700",children:"Avg. Rate"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-orange-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-orange-600",children:[n.averageCompletionTime,"d"]})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-orange-700",children:"Avg. Time"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-muted-foreground",children:["Employee Performance (",i.length," of ",e.length,")"]}),(0,a.jsx)("div",{className:"space-y-3",children:i.map((e,s)=>{let t=o(e.completionRate);return(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between p-4 border rounded-lg bg-muted/20 gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 mb-3",children:[(0,a.jsx)("h5",{className:"font-medium truncate",children:e.employeeName}),(0,a.jsx)(em.E,{variant:t.variant,className:"text-xs w-fit",children:t.label})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Assigned"}),(0,a.jsx)("span",{className:"font-medium text-base",children:e.assignedTasks})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Completed"}),(0,a.jsx)("span",{className:"font-medium text-base",children:e.completedTasks})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Rate"}),(0,a.jsxs)("span",{className:"font-medium text-base",children:[Math.round(e.completionRate),"%"]})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Avg. Time"}),(0,a.jsxs)("span",{className:"font-medium text-base",children:[e.averageCompletionTime,"d"]})]})]})]}),(0,a.jsxs)("div",{className:"w-full md:w-32 flex flex-col items-center",children:[(0,a.jsx)(ex.k,{value:e.completionRate,className:"h-3 w-full"}),(0,a.jsxs)("p",{className:"text-sm font-medium mt-2 text-center",children:[Math.round(e.completionRate),"%"]})]})]},e.employeeId)})}),e.length>r&&(0,a.jsx)("div",{className:"text-center pt-2",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing top ",r," employees by completion rate"]})})]})]})]}):(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Task Assignment Metrics"]})}),(0,a.jsx)(m.Wu,{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(ea.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No task assignment data available"})]})})]})},eu=({className:e="",compact:s=!1,showCharts:t=!0})=>{let{data:r,isLoading:i,error:n}=E((0,$.N8)(),{enabled:!0,staleTime:12e4}),c=(0,l.useMemo)(()=>r?{totalTasks:r.totalCount||0,completedTasks:r.statusDistribution?.find(e=>"Completed"===e.status)?.count||0,completionRate:Math.round(100*(r.completionRate||0)),overdueTasks:r.overdueCount||0,averageCompletionTime:r.averageCompletionTime||0,assignedEmployees:r.assignmentMetrics?.length||0}:null,[r]);return i?(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{className:"pb-6",children:(0,a.jsx)(P.E,{className:"h-7 w-48"})}),(0,a.jsxs)(m.Wu,{className:"space-y-8",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,s)=>(0,a.jsx)(P.E,{className:"h-32 rounded-lg"},s))}),t&&!s&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsx)(P.E,{className:"h-80 rounded-lg"}),(0,a.jsx)(P.E,{className:"h-80 rounded-lg"})]})]})]}):n?(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(ee.A,{className:"h-6 w-6"}),"Task Analytics"]})}),(0,a.jsx)(m.Wu,{className:"pt-6",children:(0,a.jsxs)(M.Fc,{variant:"destructive",children:[(0,a.jsx)(es.A,{className:"h-4 w-4"}),(0,a.jsx)(M.XL,{children:"Error Loading Task Analytics"}),(0,a.jsx)(M.TN,{children:n.message||"Failed to load task analytics data"})]})})]}):(0,a.jsxs)(m.Zp,{className:`${e} overflow-hidden`,children:[(0,a.jsx)(m.aR,{className:"pb-8",children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(ee.A,{className:"h-6 w-6 text-primary"}),"Task Analytics"]})}),(0,a.jsxs)(m.Wu,{className:"px-8 pb-8 space-y-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6",children:(0,a.jsx)(ee.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-3",children:c?.totalTasks||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Total Tasks"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-green-100 dark:bg-green-900/30 mb-6",children:(0,a.jsx)(F.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-3",children:c?.completedTasks||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Completed"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-6",children:(0,a.jsx)(_.A,{className:"h-8 w-8 text-emerald-600"})}),(0,a.jsxs)("div",{className:"text-4xl font-bold text-emerald-600 mb-3",children:[c?.completionRate||0,"%"]}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Completion Rate"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-red-100 dark:bg-red-900/30 mb-6",children:(0,a.jsx)(es.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-red-600 mb-3",children:c?.overdueTasks||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-6",children:(0,a.jsx)(et.A,{className:"h-8 w-8 text-yellow-600"})}),(0,a.jsxs)("div",{className:"text-4xl font-bold text-yellow-600 mb-3",children:[c?.averageCompletionTime||0,"d"]}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Avg. Completion"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-6",children:(0,a.jsx)(ea.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-purple-600 mb-3",children:c?.assignedEmployees||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Assigned Staff"})]})]}),t&&!s&&r&&(0,a.jsxs)("div",{className:"space-y-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)(m.Zp,{className:"border-2",children:[(0,a.jsx)(m.aR,{className:"pb-6",children:(0,a.jsx)(m.ZB,{className:"text-lg",children:"Status Distribution"})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(el,{data:r.statusDistribution,className:"h-80 w-full",showLegend:!0,interactive:!0})})]}),(0,a.jsxs)(m.Zp,{className:"border-2",children:[(0,a.jsx)(m.aR,{className:"pb-6",children:(0,a.jsx)(m.ZB,{className:"text-lg",children:"Priority Distribution"})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(ec,{data:r.priorityDistribution,className:"h-80 w-full",showLegend:!0,interactive:!0})})]})]}),(0,a.jsx)(ep,{data:r.assignmentMetrics,className:"w-full"})]}),s&&r&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary",children:[c?.completionRate,"%"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"completion rate"})]}),(0,a.jsxs)(Y.$,{variant:"ghost",size:"sm",className:"gap-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4"}),"View Details"]})]})]})]})};var eg=t(56090),ej=t(93772),ey=t(75699),ev=t(16189),ef=t(76311),eN=t(6211),eb=t(3389);let ew=()=>{let e=(0,$.N8)(),[s,t]=r().useState({page:1,pageSize:10}),l=(0,ev.useRouter)(),{toast:i}=(0,eb.dj)(),{data:n,error:c,isLoading:o}=C(e,s),d=e=>{l.push(`/delegations/${e.id}`)},x=[{accessorKey:"delegationId",header:"Delegation ID"},{accessorKey:"customerName",header:"Customer"},{accessorKey:"vehicleModel",header:"Vehicle"},{accessorKey:"status",cell:({row:e})=>(0,a.jsx)(em.E,{children:e.original.status}),header:"Status"},{accessorKey:"location",header:"Location"},{accessorKey:"createdAt",cell:({row:e})=>(0,ey.GP)(new Date(e.original.createdAt),"PPpp"),header:"Date"},{cell:({row:e})=>(0,a.jsxs)(Y.$,{size:"sm",variant:"outline",onClick:()=>d(e.original),className:"flex items-center gap-1",children:[(0,a.jsx)(ef.A,{className:"h-3 w-3"}),"View"]}),id:"actions",header:"Actions"}],h=(0,eg.N4)({columns:x,data:n?.data??[],getCoreRowModel:(0,ej.HT)(),manualPagination:!0,rowCount:n?.meta.total??0});return c?(0,a.jsxs)(M.Fc,{variant:"destructive",children:[(0,a.jsx)(M.XL,{children:"Error"}),(0,a.jsx)(M.TN,{children:c.message})]}):(0,a.jsxs)(m.Zp,{children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(m.ZB,{children:"Detailed Report"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(Y.$,{variant:"outline",size:"sm",onClick:()=>{if(!n?.data.length)return void i({title:"No Data",description:"No data available to export.",variant:"destructive"});let e=new Blob([["ID,Customer,Vehicle,Status,Location,Created At,Completed At",...n.data.map(e=>[e.delegationId,e.customerName,e.vehicleModel,e.status,e.location,e.createdAt,e.completedAt||"N/A"].join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`delegation-report-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s),i({title:"Export Successful",description:"Delegation data has been exported to CSV."})},disabled:!n?.data.length,className:"flex items-center gap-1",children:[(0,a.jsx)(eh.A,{className:"h-3 w-3"}),"Export"]})})]})}),(0,a.jsxs)(m.Wu,{children:[(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(eN.XI,{children:[(0,a.jsx)(eN.A0,{children:h.getHeaderGroups().map(e=>(0,a.jsx)(eN.Hj,{children:e.headers.map(e=>(0,a.jsx)(eN.nd,{children:(0,eg.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(eN.BF,{children:o?Array.from({length:s.pageSize}).map((e,s)=>(0,a.jsx)(eN.Hj,{children:x.map((e,t)=>(0,a.jsx)(eN.nA,{children:(0,a.jsx)(P.E,{className:"h-6 w-full"})},`loading-cell-${s}-${e.id??t}`))},`loading-row-${s}`)):h.getRowModel().rows.map(e=>(0,a.jsx)(eN.Hj,{children:e.getVisibleCells().map(e=>(0,a.jsx)(eN.nA,{children:(0,eg.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Page ",n?.meta.page," of ",n?.meta.totalPages," (",n?.meta.total," total records)"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(Y.$,{disabled:1===s.page,onClick:()=>t(e=>({...e,page:e.page-1})),size:"sm",variant:"outline",children:"Previous"}),(0,a.jsx)(Y.$,{disabled:s.page>=(n?.meta.totalPages??1),onClick:()=>t(e=>({...e,page:e.page+1})),size:"sm",variant:"outline",children:"Next"})]})]})]})]})};t(35950),t(63502);var ek=t(22482);t(73227);var eC=t(24920),eA=t(69795),eS=t(36558),eT=t(29333);let eD=e=>(0,x.Sk)(["vehicle-analytics",e],async()=>{let s=new URLSearchParams;if(e?.dateRange){let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}e?.vehicles&&e.vehicles.forEach(e=>s.append("vehicles",e.toString())),e?.serviceTypes&&e.serviceTypes.forEach(e=>s.append("serviceTypes",e)),e?.serviceStatus&&e.serviceStatus.forEach(e=>s.append("serviceStatus",e));let t=`/reporting/vehicles/analytics${s.toString()?`?${s.toString()}`:""}`,a=await h.uE.get(t);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3});var eR=t(39883),eE=t(17016);let e$=({label:e,value:s,icon:t,trend:l,variant:r="default"})=>(0,a.jsxs)("div",{className:(0,ek.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[r]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e})]}),l&&(0,a.jsxs)(em.E,{variant:l.isPositive?"default":"destructive",className:"text-xs",children:[l.isPositive?"+":"",l.value,"%"]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:s})})]}),eP=({filters:e,className:s="",showExportOptions:t=!0,compact:l=!1})=>{let{data:r,isLoading:i,error:n}=eD(e);if(i)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5"}),"Vehicle Analytics"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(n)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5"}),"Vehicle Analytics"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:n})})]});let c=r?.totalCount||0,o=r?.utilizationMetrics?.filter(e=>e.utilizationRate>0).length||0,d=c>0?o/c*100:0,x=r?.costAnalysis?.totalCost||0,h=r?.costAnalysis?.averageCostPerService||0;return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5"}),"Vehicle Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{variant:"secondary",className:"text-xs",children:[c," vehicles"]}),t&&(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eh.A,{className:"h-4 w-4"})}),(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eA.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(e$,{label:"Total Vehicles",value:c,icon:(0,a.jsx)(eC.A,{className:"h-4 w-4"})}),(0,a.jsx)(e$,{label:"Utilization Rate",value:`${Math.round(d)}%`,icon:(0,a.jsx)(_.A,{className:"h-4 w-4"}),variant:d<70?"warning":"success"}),(0,a.jsx)(e$,{label:"Total Service Cost",value:`$${x.toLocaleString()}`,icon:(0,a.jsx)(eS.A,{className:"h-4 w-4"})}),(0,a.jsx)(e$,{label:"Avg. Cost/Service",value:`$${Math.round(h)}`,icon:(0,a.jsx)(eT.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Active Vehicles"}),(0,a.jsx)(eC.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-blue-900",children:o})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"Maintenance Due"}),(0,a.jsx)(es.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-green-900",children:r?.maintenanceSchedule?.filter(e=>new Date(e.nextMaintenanceDate)<=new Date).length||0})]}),(0,a.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-orange-700",children:"Avg. Service Time"}),(0,a.jsx)(et.A,{className:"h-4 w-4 text-orange-600"})]}),(0,a.jsxs)("span",{className:"text-xl font-bold text-orange-900",children:[r?.serviceHistory&&r.serviceHistory.length>0?Math.round(r.serviceHistory.reduce((e,s)=>e+(s.cost||0),0)/r.serviceHistory.length):0,"h"]})]})]}),!l&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)(Y.$,{variant:"outline",size:"sm",children:"View Details"})]})})]})]})},eM=({active:e,payload:s,label:t})=>{if(e&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.vehicleName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Utilization:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.utilizationRate,"%"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Active Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.activeDelegations})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Total Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.totalDelegations})]})]})}return null},ez=e=>e>=80?"#ef4444":e>=60?"#f59e0b":e>=40?"#10b981":e>=20?"#3b82f6":"#6b7280",eF=({data:e,filters:s,className:t="",showLegend:r=!0,interactive:n=!0,height:c=300})=>{let{data:x,isLoading:h,error:p}=eD(s),u=e||x?.utilizationMetrics,g=(0,l.useMemo)(()=>u?u.map(e=>({vehicleName:e.vehicleName,utilizationRate:Math.round(e.utilizationRate),activeDelegations:e.activeDelegations,totalDelegations:e.totalDelegations,color:ez(e.utilizationRate),displayName:e.vehicleName.length>15?`${e.vehicleName.substring(0,12)}...`:e.vehicleName})).sort((e,s)=>s.utilizationRate-e.utilizationRate).slice(0,10):[],[u]);if(h)return(0,a.jsxs)(m.Zp,{className:t,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5"}),"Vehicle Utilization"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(p)return(0,a.jsxs)(m.Zp,{className:t,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5"}),"Vehicle Utilization"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:p})})]});let j=g.length>0?Math.round(g.reduce((e,s)=>e+s.utilizationRate,0)/g.length):0,y=g.filter(e=>e.utilizationRate>=80).length;return(0,a.jsxs)(m.Zp,{className:t,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5"}),"Vehicle Utilization"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{variant:"secondary",className:"text-xs",children:["Avg: ",j,"%"]}),y>0&&(0,a.jsxs)(em.E,{variant:"destructive",className:"text-xs",children:[y," overutilized"]})]})]})}),(0,a.jsx)(m.Wu,{children:0===g.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(eC.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No vehicle utilization data available"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.u,{width:"100%",height:c,children:(0,a.jsxs)(H.E,{data:g,margin:{top:20,right:30,left:20,bottom:60},children:[(0,a.jsx)(W.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(B.W,{dataKey:"displayName",angle:-45,textAnchor:"end",height:60,fontSize:12}),(0,a.jsx)(Z.h,{domain:[0,100],tickFormatter:e=>`${e}%`,fontSize:12}),n&&(0,a.jsx)(d.m,{content:(0,a.jsx)(eM,{})}),(0,a.jsx)(K.y,{dataKey:"utilizationRate",radius:[4,4,0,0],name:"Utilization Rate",children:g.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))})]})}),r&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded"}),(0,a.jsx)("span",{children:"Overutilized (80%+)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-orange-500 rounded"}),(0,a.jsx)("span",{children:"High (60-79%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,a.jsx)("span",{children:"Good (40-59%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,a.jsx)("span",{children:"Moderate (20-39%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-500 rounded"}),(0,a.jsx)("span",{children:"Low (0-19%)"})]})]})]})})]})};var eV=t(75564),eI=t(35533),eL=t(90327),eO=t(26622);let eW=({compact:e=!1,item:s})=>{let t=new Date(s.nextMaintenanceDate),l=new Date,r=(0,eV.Y)(t,l),i=(0,eI.d)(t,l)&&(0,eV.Y)(t,(0,eL.f)(l,7)),n=()=>r?(0,a.jsx)(em.E,{variant:"destructive",children:"Overdue"}):i?(0,a.jsx)(em.E,{className:"bg-orange-100 text-orange-800",variant:"secondary",children:"Due Soon"}):(0,a.jsx)(em.E,{variant:"outline",children:"Scheduled"});return(0,a.jsxs)("div",{className:(0,ek.cn)("flex items-center justify-between p-3 border rounded-lg",r&&"border-red-200 bg-red-50",i&&"border-orange-200 bg-orange-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[r?(0,a.jsx)(es.A,{className:"size-4 text-red-600"}):i?(0,a.jsx)(et.A,{className:"size-4 text-orange-600"}):(0,a.jsx)(eO.A,{className:"size-4 text-blue-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:s.maintenanceType}),!e&&n()]}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(eC.A,{className:"size-3"}),s.vehicleName]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(eO.A,{className:"size-3"}),(0,ey.GP)(t,"MMM dd, yyyy")]}),s.estimatedCost&&(0,a.jsxs)("span",{children:["$",s.estimatedCost.toLocaleString()]})]})]})]}),e&&(0,a.jsx)("div",{className:"text-right",children:n()})]})},eB=({className:e="",compact:s=!1,filters:t,maxItems:l=5})=>{let{data:r,error:i,isLoading:n}=eD(t),c=r?.maintenanceSchedule||[];if(n)return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eT.A,{className:"size-5"}),"Vehicle Maintenance"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(i)return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eT.A,{className:"size-5"}),"Vehicle Maintenance"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:i})})]});let o=new Date,d=c.filter(e=>(0,eV.Y)(new Date(e.nextMaintenanceDate),o)),x=c.filter(e=>(0,eI.d)(new Date(e.nextMaintenanceDate),o)&&(0,eV.Y)(new Date(e.nextMaintenanceDate),(0,eL.f)(o,7))),h=c.sort((e,s)=>{let t=new Date(e.nextMaintenanceDate),a=new Date(s.nextMaintenanceDate),l=(0,eV.Y)(t,o),r=(0,eV.Y)(a,o);return l&&!r?-1:!l&&r?1:t.getTime()-a.getTime()}).slice(0,l);return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eT.A,{className:"size-5"}),"Vehicle Maintenance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[d.length>0&&(0,a.jsxs)(em.E,{className:"text-xs",variant:"destructive",children:[d.length," overdue"]}),x.length>0&&(0,a.jsxs)(em.E,{className:"bg-orange-100 text-xs text-orange-800",variant:"secondary",children:[x.length," due soon"]}),(0,a.jsx)(Y.$,{size:"sm",variant:"ghost",children:(0,a.jsx)(eA.A,{className:"size-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-red-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-700",children:d.length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-orange-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-700",children:x.length}),(0,a.jsx)("div",{className:"text-xs text-orange-600",children:"Due Soon"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-green-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:0}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Completed"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===h.length?(0,a.jsxs)("div",{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(eT.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No maintenance items scheduled"})]}):h.map((e,t)=>(0,a.jsx)(eW,{compact:s,item:e},`${e.vehicleId}-${e.maintenanceType}-${t}`))}),!s&&c.length>l&&(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsxs)(Y.$,{className:"w-full",size:"sm",variant:"outline",children:["View All Maintenance (",c.length," total)"]})})]})]})};var eZ=t(81950),eU=t(22853);let eG=({label:e,value:s,icon:t,trend:l,variant:r="default"})=>(0,a.jsxs)("div",{className:(0,ek.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[r]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e})]}),l&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[l.isPositive?(0,a.jsx)(_.A,{className:"h-3 w-3 text-green-600"}):(0,a.jsx)(eZ.A,{className:"h-3 w-3 text-red-600"}),(0,a.jsxs)(em.E,{variant:l.isPositive?"default":"destructive",className:"text-xs",children:[l.isPositive?"+":"",l.value,"%"]})]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:s})})]}),e_=({active:e,payload:s})=>{if(e&&s&&s.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Cost:"," ",(0,a.jsxs)("span",{className:"font-medium",children:["$",e.value.toLocaleString()]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},eJ=({filters:e,className:s="",showTrend:t=!0,compact:r=!1})=>{let{data:x,isLoading:h,error:p}=eD(e),u=x?.costAnalysis,g=(0,l.useMemo)(()=>{if(!u?.costByType)return[];let e=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6"];return u.costByType.map((s,t)=>({name:s.type,value:s.cost,percentage:Math.round(s.cost/u.totalCost*100),color:e[t%e.length]}))},[u]),j=(0,l.useMemo)(()=>u?.monthlyTrend?u.monthlyTrend.map((e,s)=>({month:(0,ey.GP)(new Date(e.month),"MMM"),cost:e.cost,services:s+1})):[],[u]);if(h)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5"}),"Cost Analytics"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(p)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5"}),"Cost Analytics"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:p})})]});let y=u?.totalCost||0,v=u?.averageCostPerService||0,f=y>0?Math.min(y/1e4*100,100):0,N=12*y;return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5"}),"Cost Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{variant:"secondary",className:"text-xs",children:["$",y.toLocaleString()," total"]}),f>90&&(0,a.jsx)(em.E,{variant:"destructive",className:"text-xs",children:"Budget Alert"}),(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eA.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(eG,{label:"Total Cost",value:`$${y.toLocaleString()}`,icon:(0,a.jsx)(eS.A,{className:"h-4 w-4"})}),(0,a.jsx)(eG,{label:"Avg/Service",value:`$${Math.round(v)}`,icon:(0,a.jsx)(z.A,{className:"h-4 w-4"})}),(0,a.jsx)(eG,{label:"Budget Used",value:`${Math.round(f)}%`,icon:(0,a.jsx)(eU.A,{className:"h-4 w-4"}),variant:f>90?"destructive":f>75?"warning":"success"}),(0,a.jsx)(eG,{label:"Projected Annual",value:`$${Math.round(N).toLocaleString()}`,icon:(0,a.jsx)(_.A,{className:"h-4 w-4"})})]}),!r&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Cost by Service Type"}),g.length>0?(0,a.jsx)(i.u,{width:"100%",height:200,children:(0,a.jsxs)(n.r,{children:[(0,a.jsx)(c.F,{data:g,cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:({name:e,percentage:s})=>`${e}: ${s}%`,labelLine:!1,children:g.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))}),(0,a.jsx)(d.m,{content:(0,a.jsx)(e_,{})})]})}):(0,a.jsx)("div",{className:"h-48 flex items-center justify-center text-gray-500",children:"No cost breakdown data available"})]}),t&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Monthly Cost Trend"}),j.length>0?(0,a.jsx)(i.u,{width:"100%",height:200,children:(0,a.jsxs)(O.b,{data:j,children:[(0,a.jsx)(W.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(B.W,{dataKey:"month",fontSize:12}),(0,a.jsx)(Z.h,{tickFormatter:e=>`$${e}`,fontSize:12}),(0,a.jsx)(d.m,{formatter:e=>[`$${e.toLocaleString()}`,"Cost"],labelFormatter:e=>`Month: ${e}`}),(0,a.jsx)(G.N,{type:"monotone",dataKey:"cost",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4}})]})}):(0,a.jsx)("div",{className:"h-48 flex items-center justify-center text-gray-500",children:"No trend data available"})]})]}),f>90&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Budget Alert"})]}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:["You've used ",Math.round(f),"% of your maintenance budget. Consider reviewing upcoming expenses."]})]})]})]})};var eH=t(29094);let eK=e=>(0,x.Sk)(["employee-analytics",e],async()=>{let s=new URLSearchParams;if(e?.dateRange){let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}e?.employees&&e.employees.forEach(e=>s.append("employees",e.toString())),e?.locations&&e.locations.forEach(e=>s.append("locations",e)),e?.includeEmployeeMetrics&&s.append("includeEmployeeMetrics","true");let t=`/reporting/employees/analytics${s.toString()?`?${s.toString()}`:""}`,a=await h.uE.get(t);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),eq=({label:e,value:s,icon:t,trend:l,variant:r="default"})=>(0,a.jsxs)("div",{className:(0,ek.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[r]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e})]}),l&&(0,a.jsxs)(em.E,{variant:l.isPositive?"default":"destructive",className:"text-xs",children:[l.isPositive?"+":"",l.value,"%"]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:s})})]}),eQ=({filters:e,className:s="",showExportOptions:t=!0,compact:l=!1})=>{let{data:r,isLoading:i,error:n}=eK(e);if(i)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Employee Analytics"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(n)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Employee Analytics"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:n})})]});let c=r?.totalCount||0,o=r?.performanceMetrics?.filter(e=>e.completedDelegations>0||e.completedTasks>0).length||0,d=r?.performanceMetrics?.length&&r.performanceMetrics.length>0?Math.round(r.performanceMetrics.reduce((e,s)=>e+s.averageRating,0)/r.performanceMetrics.length):0,x=r?.taskAssignments?.reduce((e,s)=>e+s.completedTasks,0)||0,h=r?.taskAssignments?.reduce((e,s)=>e+s.overdueTasksCount,0)||0,p=r?.availabilityMetrics?.length&&r.availabilityMetrics.length>0?Math.round(r.availabilityMetrics.reduce((e,s)=>e+s.utilizationRate,0)/r.availabilityMetrics.length):0;return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Employee Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{variant:"secondary",className:"text-xs",children:[c," employees"]}),h>0&&(0,a.jsxs)(em.E,{variant:"destructive",className:"text-xs",children:[h," overdue"]}),t&&(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eh.A,{className:"h-4 w-4"})}),(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eA.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(eq,{label:"Total Employees",value:c,icon:(0,a.jsx)(ea.A,{className:"h-4 w-4"})}),(0,a.jsx)(eq,{label:"Active Employees",value:o,icon:(0,a.jsx)(eH.A,{className:"h-4 w-4"}),variant:o<.8*c?"warning":"success"}),(0,a.jsx)(eq,{label:"Avg Performance",value:`${d}/10`,icon:(0,a.jsx)(_.A,{className:"h-4 w-4"}),variant:d<7?"warning":"success"}),(0,a.jsx)(eq,{label:"Utilization Rate",value:`${p}%`,icon:(0,a.jsx)(et.A,{className:"h-4 w-4"}),variant:p<70?"warning":p>90?"destructive":"success"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Completed Tasks"}),(0,a.jsx)(F.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-blue-900",children:x})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-red-700",children:"Overdue Tasks"}),(0,a.jsx)(es.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-red-900",children:h})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"On-Time Rate"}),(0,a.jsx)(et.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)("span",{className:"text-xl font-bold text-green-900",children:[r?.performanceMetrics?.length&&r.performanceMetrics.length>0?Math.round(r.performanceMetrics.reduce((e,s)=>e+s.onTimePerformance,0)/r.performanceMetrics.length):0,"%"]})]})]}),!l&&r?.workloadDistribution&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Workload Distribution"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-700",children:r.workloadDistribution.filter(e=>"Underutilized"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Underutilized"})]}),(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-700",children:r.workloadDistribution.filter(e=>"Optimal"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Optimal"})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-red-700",children:r.workloadDistribution.filter(e=>"Overloaded"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overloaded"})]})]})]}),!l&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)(Y.$,{variant:"outline",size:"sm",children:"View Details"})]})})]})]})};var eX=t(43619),eY=t(32584);let e0=({active:e,label:s,payload:t})=>{if(e&&t?.length){let e=t[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.employeeName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Performance Score:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.averageRating,"/10"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Completed Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.completedDelegations})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Completed Tasks:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.completedTasks})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["On-Time Rate:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.onTimePerformance,"%"]})]})]})}return null},e2=e=>e>=9?"#10b981":e>=8?"#3b82f6":e>=7?"#f59e0b":e>=6?"#ef4444":"#6b7280",e4=({className:e="",data:s,filters:t,height:r=300,interactive:n=!0,maxEmployees:c=10,showLegend:x=!0})=>{let{data:h,error:p,isLoading:u}=eK(t),g=s||h?.performanceMetrics,j=(0,l.useMemo)(()=>g?g.map(e=>({averageRating:e.averageRating,color:e2(e.averageRating),completedDelegations:e.completedDelegations,completedTasks:e.completedTasks,displayName:e.employeeName.length>12?`${e.employeeName.slice(0,9)}...`:e.employeeName,employeeName:e.employeeName,onTimePerformance:e.onTimePerformance,overallScore:Math.round(.4*e.averageRating+.01*e.onTimePerformance*3+.3*e.workloadScore),workloadScore:e.workloadScore})).sort((e,s)=>s.overallScore-e.overallScore).slice(0,c):[],[g,c]);if(u)return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"size-5"}),"Employee Performance"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(p)return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"size-5"}),"Employee Performance"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:p})})]});let y=j.length>0?Math.round(j.reduce((e,s)=>e+s.averageRating,0)/j.length*10)/10:0,v=j.filter(e=>e.averageRating>=8).length;return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"size-5"}),"Employee Performance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{className:"text-xs",variant:"secondary",children:["Avg: ",y,"/10"]}),v>0&&(0,a.jsxs)(em.E,{className:"text-xs",variant:"default",children:[(0,a.jsx)(eX.A,{className:"mr-1 size-3"}),v," top performers"]})]})]})}),(0,a.jsx)(m.Wu,{children:0===j.length?(0,a.jsx)("div",{className:"flex h-64 items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(ea.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No employee performance data available"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.u,{height:r,width:"100%",children:(0,a.jsxs)(H.E,{data:j,margin:{bottom:60,left:20,right:30,top:20},children:[(0,a.jsx)(W.d,{stroke:"#f0f0f0",strokeDasharray:"3 3"}),(0,a.jsx)(B.W,{angle:-45,dataKey:"displayName",fontSize:12,height:60,textAnchor:"end"}),(0,a.jsx)(Z.h,{domain:[0,10],fontSize:12,tickFormatter:e=>`${e}/10`}),n&&(0,a.jsx)(d.m,{content:(0,a.jsx)(e0,{})}),(0,a.jsx)(K.y,{dataKey:"averageRating",name:"Performance Score",radius:[4,4,0,0],children:j.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))})]})}),x&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-green-500"}),(0,a.jsx)("span",{children:"Excellent (9-10)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-blue-500"}),(0,a.jsx)("span",{children:"Good (8-8.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-orange-500"}),(0,a.jsx)("span",{children:"Average (7-7.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-red-500"}),(0,a.jsx)("span",{children:"Below Average (6-6.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-gray-500"}),(0,a.jsx)("span",{children:"Poor (<6)"})]})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Top Performers"}),(0,a.jsx)("div",{className:"space-y-2",children:j.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded bg-gray-50 p-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(em.E,{className:"flex size-6 items-center justify-center p-0 text-xs",variant:"outline",children:s+1}),(0,a.jsx)(eY.eu,{className:"size-6",children:(0,a.jsx)(eY.q5,{className:"text-xs",children:e.employeeName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.employeeName})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-bold",children:[e.averageRating,"/10"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.onTimePerformance,"% on-time"]})]})]},e.employeeName))})]})]})})]})},e1=({item:e,compact:s=!1})=>{let t=e=>{switch(e){case"Underutilized":return"text-blue-600 bg-blue-100";case"Optimal":return"text-green-600 bg-green-100";case"Overloaded":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}};return"Overloaded"===e.status||e.status,(0,a.jsxs)("div",{className:(0,ek.cn)("flex items-center justify-between p-3 border rounded-lg","Overloaded"===e.status&&"border-red-200 bg-red-50","Optimal"===e.status&&"border-green-200 bg-green-50","Underutilized"===e.status&&"border-blue-200 bg-blue-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,a.jsx)(eY.eu,{className:"h-8 w-8",children:(0,a.jsx)(eY.q5,{className:"text-xs",children:e.employeeName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.employeeName}),!s&&(0,a.jsx)(em.E,{className:(0,ek.cn)("text-xs",t(e.status)),children:e.status})]}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Workload: ",e.currentWorkload,"/",e.capacity]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[e.workloadPercentage,"%"]})]}),(0,a.jsx)(ex.k,{value:e.workloadPercentage,className:"h-2 mt-1",style:{"--progress-background":"Overloaded"===e.status?"#ef4444":"Optimal"===e.status?"#10b981":"#3b82f6"}})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"Underutilized":return(0,a.jsx)(et.A,{className:"h-4 w-4 text-blue-600"});case"Optimal":return(0,a.jsx)(F.A,{className:"h-4 w-4 text-green-600"});case"Overloaded":return(0,a.jsx)(es.A,{className:"h-4 w-4 text-red-600"});default:return(0,a.jsx)(ea.A,{className:"h-4 w-4 text-gray-600"})}})(e.status),s&&(0,a.jsxs)(em.E,{className:(0,ek.cn)("text-xs",t(e.status)),children:[e.workloadPercentage,"%"]})]})]})},e6=({filters:e,className:s="",compact:t=!1,maxItems:l=8})=>{let{data:r,isLoading:i,error:n}=eK(e),c=r?.workloadDistribution||[];if(i)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Employee Workload"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(n)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Employee Workload"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:n})})]});let o=c.filter(e=>"Overloaded"===e.status),d=c.filter(e=>"Underutilized"===e.status),x=c.filter(e=>"Optimal"===e.status),h=[...o,...x,...d].slice(0,l),p=c.length>0?Math.round(c.reduce((e,s)=>e+s.workloadPercentage,0)/c.length):0;return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5"}),"Employee Workload"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{variant:"secondary",className:"text-xs",children:["Avg: ",p,"%"]}),o.length>0&&(0,a.jsxs)(em.E,{variant:"destructive",className:"text-xs",children:[o.length," overloaded"]}),(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eA.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:d.length}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Underutilized"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:x.length}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Optimal"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-700",children:o.length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overloaded"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===h.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(ea.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No workload data available"})]}):h.map((e,s)=>(0,a.jsx)(e1,{item:e,compact:t},`${e.employeeId}-${s}`))}),o.length>0&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Workload Alert"})]}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:[o.length," employee",o.length>1?"s are":" is"," overloaded. Consider redistributing tasks or adjusting schedules."]})]}),!t&&c.length>l&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)(Y.$,{variant:"outline",size:"sm",className:"w-full",children:["View All Employees (",c.length," total)"]})})]})]})};var e3=t(14328),e5=t(86940),e8=t(28274);let e9=e=>(0,x.Sk)(["cross-entity-analytics",e],async()=>{let s=new URLSearchParams;if(e?.dateRange){let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}e?.employees&&e.employees.forEach(e=>s.append("employees",e.toString())),e?.vehicles&&e.vehicles.forEach(e=>s.append("vehicles",e.toString())),e?.locations&&e.locations.forEach(e=>s.append("locations",e));let t=`/reporting/cross-entity/analytics${s.toString()?`?${s.toString()}`:""}`,a=await h.uE.get(t);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),e7=({active:e,payload:s})=>{if(e&&s?.length){let e=s[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["X-Axis: ",(0,a.jsx)("span",{className:"font-medium",children:e.x})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Y-Axis: ",(0,a.jsx)("span",{className:"font-medium",children:e.y})]}),e.correlation&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Correlation: ",(0,a.jsx)("span",{className:"font-medium",children:e.correlation})]})]})}return null},se=({description:e,icon:s,title:t,value:l})=>(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[s,(0,a.jsx)("span",{className:"text-sm font-medium",children:t})]}),(0,a.jsx)(em.E,{className:(0,ek.cn)("text-xs",(e=>{let s=Math.abs(e);return s>=.8?"text-green-600 bg-green-100":s>=.6?"text-blue-600 bg-blue-100":s>=.4?"text-orange-600 bg-orange-100":"text-gray-600 bg-gray-100"})(l)),children:(e=>{let s=Math.abs(e);return s>=.8?"Strong":s>=.6?"Moderate":s>=.4?"Weak":"Very Weak"})(l)})]}),(0,a.jsx)("div",{className:"mb-1 text-2xl font-bold",children:l.toFixed(3)}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:e})]}),ss=({className:e="",correlationType:s="all",filters:t,interactive:r=!0})=>{let{data:n,error:c,isLoading:x}=e9(t),h=(0,l.useMemo)(()=>{if(!n?.correlations)return[];switch(s){case"employee-vehicle":return n.correlations.employeeVehicle?.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:`${e.employeeName} - ${e.vehicleName}`,x:e.employeePerformance,y:e.vehicleUtilization}))||[];case"performance-workload":return n.correlations.performanceWorkload?.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.employeeName,x:e.workloadPercentage,y:e.performanceScore}))||[];case"task-delegation":return n.correlations.taskDelegation?.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.taskType,x:e.taskComplexity,y:e.delegationSuccess}))||[];default:return n.correlations.overall?.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.entityName,x:e.xValue,y:e.yValue}))||[]}},[n,s]);if(x)return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(e3.A,{className:"size-5"}),"Cross-Entity Correlations"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(c)return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(e3.A,{className:"size-5"}),"Cross-Entity Correlations"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:c})})]});let p=n?.metrics??{employeeVehicle:0,overallEfficiency:0,performanceWorkload:0,taskDelegation:0};return(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(e3.A,{className:"size-5"}),"Cross-Entity Correlations"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{className:"text-xs",variant:"secondary",children:[h.length," relationships"]}),(0,a.jsx)(Y.$,{size:"sm",variant:"ghost",children:(0,a.jsx)(eA.A,{className:"size-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,a.jsx)(se,{description:"Performance vs Vehicle Usage",icon:(0,a.jsx)(ea.A,{className:"size-4"}),title:"Employee-Vehicle",value:p.employeeVehicle||0}),(0,a.jsx)(se,{description:"Task Complexity vs Success",icon:(0,a.jsx)(ee.A,{className:"size-4"}),title:"Task-Delegation",value:p.taskDelegation||0}),(0,a.jsx)(se,{description:"Workload vs Performance",icon:(0,a.jsx)(_.A,{className:"size-4"}),title:"Performance-Workload",value:p.performanceWorkload||0}),(0,a.jsx)(se,{description:"System-wide Correlation",icon:(0,a.jsx)(e3.A,{className:"size-4"}),title:"Overall Efficiency",value:p.overallEfficiency||0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-3 text-sm font-medium",children:"Relationship Visualization"}),h.length>0?(0,a.jsx)(i.u,{height:300,width:"100%",children:(0,a.jsxs)(e5.t,{data:h,margin:{bottom:20,left:20,right:30,top:20},children:[(0,a.jsx)(W.d,{stroke:"#f0f0f0",strokeDasharray:"3 3"}),(0,a.jsx)(B.W,{dataKey:"x",fontSize:12,name:"X-Axis",type:"number"}),(0,a.jsx)(Z.h,{dataKey:"y",fontSize:12,name:"Y-Axis",type:"number"}),r&&(0,a.jsx)(d.m,{content:(0,a.jsx)(e7,{})}),(0,a.jsx)(e8.X,{dataKey:"y",fill:"#3b82f6",children:h.map((e,s)=>(0,a.jsx)(o.f,{fill:e.color},`cell-${s}`))})]})}):(0,a.jsx)("div",{className:"flex h-64 items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(e3.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No correlation data available"})]})})]}),n?.insights&&n.insights.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Key Insights"}),(0,a.jsx)("div",{className:"space-y-2",children:n.insights.slice(0,3).map((e,s)=>(0,a.jsx)("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(_.A,{className:"mt-0.5 size-4 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-800",children:e.title}),(0,a.jsx)("p",{className:"mt-1 text-xs text-blue-700",children:e.description})]})]})},s))})]})]})]})};var st=t(36644),sa=t(8760);let sl=({entity:e,size:s="md"})=>(0,a.jsxs)("div",{className:(0,ek.cn)("border rounded-lg flex items-center gap-2",(e=>{switch(e){case"employee":return"bg-blue-100 text-blue-700 border-blue-200";case"vehicle":return"bg-green-100 text-green-700 border-green-200";case"task":return"bg-orange-100 text-orange-700 border-orange-200";case"delegation":return"bg-purple-100 text-purple-700 border-purple-200";default:return"bg-gray-100 text-gray-700 border-gray-200"}})(e.type),{sm:"p-2 text-xs",md:"p-3 text-sm",lg:"p-4 text-base"}[s]),children:[(e=>{switch(e){case"employee":return(0,a.jsx)(ea.A,{className:"h-4 w-4"});case"vehicle":return(0,a.jsx)(eC.A,{className:"h-4 w-4"});case"task":return(0,a.jsx)(ee.A,{className:"h-4 w-4"});case"delegation":return(0,a.jsx)(st.A,{className:"h-4 w-4"});default:return(0,a.jsx)(e3.A,{className:"h-4 w-4"})}})(e.type),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"font-medium truncate",children:e.name}),(0,a.jsxs)("div",{className:"text-xs opacity-75",children:[e.connections," connections"]})]}),(0,a.jsxs)(em.E,{variant:"outline",className:"text-xs",children:[Math.round(100*e.strength),"%"]})]}),sr=({from:e,to:s,strength:t,type:l})=>(0,a.jsx)("div",{className:(0,ek.cn)("p-3 border rounded-lg",(e=>e>=.8?"border-green-500 bg-green-50":e>=.6?"border-blue-500 bg-blue-50":e>=.4?"border-orange-500 bg-orange-50":"border-gray-500 bg-gray-50")(t)),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[(0,a.jsx)(sl,{entity:e,size:"sm"}),(0,a.jsx)(sa.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)(sl,{entity:s,size:"sm"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)(em.E,{variant:"outline",className:"text-xs",children:[Math.round(100*t),"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:l})]})]})}),si=({filters:e,className:s="",maxConnections:t=10,showDetails:r=!0})=>{let{data:i,isLoading:n,error:c}=e9(e),{entities:o,connections:d}=(0,l.useMemo)(()=>{if(!i?.network)return{entities:[],connections:[]};let e=i.network.nodes?.map(e=>({id:e.id,name:e.name,type:e.type,connections:e.connectionCount||0,strength:e.strength||0}))||[],s=i.network.edges?.slice(0,t).map(s=>({from:e.find(e=>e.id===s.from)||e[0],to:e.find(e=>e.id===s.to)||e[0],strength:s.weight||0,type:s.type||"related"}))||[];return{entities:e,connections:s}},[i,t]);if(n)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(e3.A,{className:"h-5 w-5"}),"Entity Relationship Network"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eR.g,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(c)return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(e3.A,{className:"h-5 w-5"}),"Entity Relationship Network"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(eE.n,{error:c})})]});let x=o.length,h=d.length,p=d.length>0?d.reduce((e,s)=>e+s.strength,0)/d.length:0,u=o.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{});return(0,a.jsxs)(m.Zp,{className:s,children:[(0,a.jsx)(m.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(e3.A,{className:"h-5 w-5"}),"Entity Relationship Network"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(em.E,{variant:"secondary",className:"text-xs",children:[x," entities"]}),(0,a.jsxs)(em.E,{variant:"outline",className:"text-xs",children:[h," connections"]}),(0,a.jsx)(Y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(eA.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(m.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:u.employee||0}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Employees"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:u.vehicle||0}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Vehicles"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-700",children:u.task||0}),(0,a.jsx)("div",{className:"text-xs text-orange-600",children:"Tasks"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-700",children:u.delegation||0}),(0,a.jsx)("div",{className:"text-xs text-purple-600",children:"Delegations"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Most Connected Entities"}),(0,a.jsx)("div",{className:"space-y-2",children:o.sort((e,s)=>s.connections-e.connections).slice(0,5).map((e,s)=>(0,a.jsx)(sl,{entity:e},e.id))})]}),r&&d.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Strongest Connections"}),(0,a.jsx)("div",{className:"space-y-3",children:d.sort((e,s)=>s.strength-e.strength).slice(0,5).filter(e=>e.from&&e.to).map((e,s)=>(0,a.jsx)(sr,{from:e.from,to:e.to,strength:e.strength,type:e.type},`${e.from.id}-${e.to.id}-${s}`))})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Network Health"}),(0,a.jsx)(em.E,{variant:p>.7?"default":p>.4?"secondary":"destructive",className:"text-xs",children:p>.7?"Strong":p>.4?"Moderate":"Weak"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["Average connection strength:"," ",Math.round(100*p),"%"]})]})]})]})}},12045:(e,s,t)=>{"use strict";t.d(s,{W3:()=>m,kv:()=>o,e5:()=>d,fD:()=>x});var a=t(43210),l=t(25758),r=t(93778);class i{constructor(e){this.apiClient=e}async generateAggregateReport(e){try{let s=await this.apiClient.request({url:`/api/reporting/reports/aggregate/${e.entityType}`,method:"POST",data:{filters:e.filters,template:e.template||"default",format:"json",options:e.options||{}}}),t=s.data.data||s.data;return{data:t?.aggregate||t,metadata:s.data.metadata||t?.metadata||{id:`aggregate_${e.entityType}_${Date.now()}`,type:"aggregate",entityType:e.entityType,format:e.format||"json",template:e.template||"default",generatedAt:new Date().toISOString(),generatedBy:"system",filters:e.filters,options:e.options}}}catch(s){throw console.error("Failed to generate aggregate report:",s),Error(`Failed to generate ${e.entityType} aggregate report`)}}async generateIndividualReport(e){try{let s=await this.apiClient.request({url:`/api/reporting/reports/individual/${e.entityType}/${e.entityId}`,method:"POST",data:{template:e.template||"default",format:"json",options:e.options||{}}});return{data:s.data.data||s.data,metadata:s.data.metadata||{id:`individual_${e.entityType}_${e.entityId}_${Date.now()}`,type:"individual",entityType:e.entityType,entityId:e.entityId,format:e.format||"json",template:e.template||"default",generatedAt:new Date().toISOString(),generatedBy:"system",options:e.options}}}catch(s){throw console.error("Failed to generate individual report:",s),Error(`Failed to generate ${e.entityType} individual report`)}}async generateComprehensiveReport(e){try{let s=await this.apiClient.request({url:"/api/reporting/reports/generate",method:"POST",data:{entityTypes:e.entityTypes,filters:e.filters,template:e.template||"comprehensive",format:"json",options:e.options||{}}});return{data:s.data.data||s.data,metadata:s.data.metadata||{id:`comprehensive_${Date.now()}`,type:"comprehensive",entityTypes:e.entityTypes,format:e.format||"json",template:e.template||"comprehensive",generatedAt:new Date().toISOString(),generatedBy:"system",filters:e.filters,options:e.options}}}catch(e){throw console.error("Failed to generate comprehensive report:",e),Error("Failed to generate comprehensive report")}}}class n{async request(e){throw Error("API client not initialized. Use createReportGenerationService with proper API client.")}}let c=e=>new i(e);new i(new n);let o=()=>{let[e,s]=(0,a.useState)(!1),[l,i]=(0,a.useState)(null),{client:n}=(0,r.a8)(),o=c({request:async e=>({data:await n.request(e)})}),d=(0,a.useCallback)(async e=>{s(!0),i(null);try{return await o.generateComprehensiveReport(e)}catch(e){throw i(e instanceof Error?e.message:"Failed to generate report"),e}finally{s(!1)}},[o]),m=(0,a.useCallback)(async e=>{s(!0),i(null);try{return await o.generateIndividualReport(e)}catch(e){throw i(e instanceof Error?e.message:"Failed to generate individual report"),e}finally{s(!1)}},[o]);return{generateComprehensiveReport:d,generateIndividualReport:m,generateAggregateReport:(0,a.useCallback)(async e=>{s(!0),i(null);try{return await o.generateAggregateReport(e)}catch(e){throw i(e instanceof Error?e.message:"Failed to generate aggregate report"),e}finally{s(!1)}},[o]),exportReport:(0,a.useCallback)(async(e,s,a,l,r)=>{try{let{useExport:i}=await Promise.resolve().then(t.bind(t,91625)),{exportReportToPDF:n,exportReportToExcel:c,exportToCSV:o}=i(r||"report");switch(s){case"pdf":await n(e,a,l||`${a.charAt(0).toUpperCase()+a.slice(1)} Report`,r);break;case"excel":c(e,a,r);break;case"csv":let d=Array.isArray(e.data)?e.data:[e.data||e];o(d,{filename:r||"report"});break;default:throw Error(`Unsupported export format: ${s}`)}}catch(e){throw console.error("Export failed:",e),e}},[]),isGenerating:e,error:l}},d=e=>{let{client:s}=(0,r.a8)(),t=new URLSearchParams;e?.type&&t.append("type",e.type),e?.entityType&&t.append("entityType",e.entityType);let a=(0,l.Sk)(["report-history",e],async()=>await s.get(`/api/reporting/reports/history?${t.toString()}`),{cacheDuration:12e4,enableRetry:!0});return{reports:a.data?.reports||[],pagination:a.data?.pagination,isLoading:a.isLoading,error:a.error,refetch:a.refetch}},m=()=>{let[e,s]=(0,a.useState)(!1),[t,l]=(0,a.useState)(null),{client:i}=(0,r.a8)();return{downloadReport:(0,a.useCallback)(async e=>{s(!0),l(null);try{let s=await i.get(`/api/reporting/reports/${e}/download`);console.log("Download result:",s),alert("Download functionality will be implemented with file storage")}catch(e){throw l(e instanceof Error?e.message:"Failed to download report"),e}finally{s(!1)}},[i]),isDownloading:e,downloadError:t}},x=()=>{let{client:e}=(0,r.a8)(),s=(0,l.Sk)(["report-templates"],async()=>{let s=await e.get("/api/reporting/reports/templates");return Array.isArray(s)?s:s&&Array.isArray(s.data)?s.data:(console.warn("Report templates API returned unexpected format:",s),[])},{cacheDuration:6e5,enableRetry:!0});return{templates:Array.isArray(s.data)?s.data:[],isLoading:s.isLoading,error:s.error,refetch:s.refetch}}},17016:(e,s,t)=>{"use strict";t.d(s,{n:()=>c});var a=t(60687);t(43210);var l=t(14975),r=t(77368),i=t(91821),n=t(29523);function c({error:e,title:s="Error",onRetry:t,className:c,showRetry:o=!0,retryText:d="Try Again"}){if(!e)return null;let m=e instanceof Error?e.message:String(e);return(0,a.jsxs)(i.Fc,{variant:"destructive",className:c,children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)(i.XL,{children:s}),(0,a.jsxs)(i.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-3",children:m}),o&&t&&(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:t,className:"h-8",children:[(0,a.jsx)(r.A,{className:"h-3 w-3 mr-2"}),d]})]})]})}},17066:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{ReportingDashboard:()=>$});var l=t(60687),r=t(27629),i=t(8751),n=t(92865),c=t(24920),o=t(48206),d=t(14328),m=t(36644),x=t(58369),h=t(3746),p=t(90586),u=t(77368),g=t(20620),j=t(43210),y=t.n(j),v=t(7889),f=t(96834),N=t(29523),b=t(85763),w=t(54567),k=t(824),C=t(27069),A=t(90101),S=t(37783),T=t(41536),D=t(85599),R=t(10053),E=e([w]);w=(E.then?(await E)():E)[0];let $=({className:e=""})=>{let[s,t]=y().useState("overview"),[a,j]=y().useState(!1),E={costRange:{max:1e4,min:0},dateRange:{from:new Date(Date.now()-2592e6),to:new Date},employees:[],includeServiceHistory:!1,includeTaskData:!1,locations:[],serviceStatus:[],serviceTypes:[],status:[],vehicles:[]},$=[{description:"High-level metrics and key performance indicators",icon:(0,l.jsx)(r.A,{className:"size-4"}),id:"overview",label:"Overview"},{description:"Detailed analytics and trend analysis",icon:(0,l.jsx)(i.A,{className:"size-4"}),id:"analytics",label:"Analytics"},{description:"Task metrics and performance analysis",icon:(0,l.jsx)(n.A,{className:"size-4"}),id:"tasks",label:"Tasks"},{description:"Vehicle utilization and maintenance analytics",icon:(0,l.jsx)(c.A,{className:"size-4"}),id:"vehicles",label:"Vehicles"},{description:"Employee performance and workload analysis",icon:(0,l.jsx)(o.A,{className:"size-4"}),id:"employees",label:"Employees"},{description:"Cross-entity relationships and correlations",icon:(0,l.jsx)(d.A,{className:"size-4"}),id:"correlations",label:"Correlations"},{description:"Generate comprehensive data reports for all entities",icon:(0,l.jsx)(m.A,{className:"size-4"}),id:"generation",label:"Generate Reports"},{description:"Manage report types and build custom reports",icon:(0,l.jsx)(x.A,{className:"size-4"}),id:"management",label:"Management"},{description:"Raw delegation data in tabular format",icon:(0,l.jsx)(h.A,{className:"size-4"}),id:"data",label:"Data"}],P=e=>{switch(e){case"overview":return[{component:R.j3,id:"status",span:"col-span-12 lg:col-span-4"},{component:R.B,id:"tasks",span:"col-span-12 lg:col-span-8"},{component:R.Sp,id:"trend",span:"col-span-12 lg:col-span-8"},{component:R.AP,id:"location",span:"col-span-12 lg:col-span-4"}];case"analytics":return[{component:R.Sp,id:"trend",span:"col-span-12 lg:col-span-8"},{component:R.AP,id:"location",span:"col-span-12 lg:col-span-4"},{component:R.B,id:"tasks",span:"col-span-12 lg:col-span-6"},{component:R.j3,id:"status",span:"col-span-12 lg:col-span-6"}];case"correlations":return[{component:R.st,id:"cross-entity-correlations",span:"col-span-12"},{component:R.Wf,id:"entity-relationships",span:"col-span-12 lg:col-span-6"},{component:R.B,id:"task-correlations",span:"col-span-12 lg:col-span-6"}];case"tasks":return[{component:R.B,id:"task-metrics",span:"col-span-12"},{component:R.Iq,id:"task-assignments",span:"col-span-12 lg:col-span-6"},{component:R.Uq,id:"task-status-chart",span:"col-span-12 lg:col-span-6"}];case"vehicles":return[{component:R.Ny,id:"vehicle-analytics",span:"col-span-12 lg:col-span-6"},{component:R.VH,id:"vehicle-utilization",span:"col-span-12 lg:col-span-6"},{component:R.EO,id:"vehicle-maintenance",span:"col-span-12 lg:col-span-6"},{component:R.SV,id:"vehicle-costs",span:"col-span-12 lg:col-span-6"}];case"employees":return[{component:R.BR,id:"employee-analytics",span:"col-span-12 lg:col-span-8"},{component:R.AN,id:"employee-workload",span:"col-span-12 lg:col-span-4"},{component:R.tp,id:"employee-performance",span:"col-span-12"}];case"generation":return[{component:()=>(0,l.jsx)(w.c,{}),id:"report-generation",span:"col-span-12"}];case"management":return[{component:()=>(0,l.jsx)(C.i,{}),id:"report-type-manager",span:"col-span-12 lg:col-span-6"},{component:()=>(0,l.jsx)(k.e,{}),id:"report-builder",span:"col-span-12 lg:col-span-6"}];case"data":return[{component:()=>(0,l.jsx)(A._L,{}),id:"data-table",span:"col-span-12"}];default:return[]}},M=async()=>{try{let e={activeTab:s,filters:E,metadata:{exportedBy:"Reporting Dashboard",version:"2.0.0"},timestamp:new Date().toISOString()},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=globalThis.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download=`dashboard-export-${new Date().toISOString().split("T")[0]}.json`,document.body.append(l),l.click(),l.remove(),globalThis.URL.revokeObjectURL(a)}catch(e){console.error("Export failed:",e)}},z=()=>{let e=P(s);return(0,l.jsx)("div",{className:"grid grid-cols-12 gap-6",children:e.map(({component:e,id:t,span:a})=>(0,l.jsx)("div",{className:a,children:(0,l.jsx)(e,{})},`${s}-${t}`))})};return(0,l.jsx)(v.GW,{className:e,config:S.Z,children:(0,l.jsx)(D.n,{actions:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)(N.$,{className:a?"bg-primary text-primary-foreground":"",onClick:()=>j(!a),size:"sm",variant:"outline",children:[(0,l.jsx)(p.A,{className:"mr-2 size-4"}),"Filters"]}),(0,l.jsxs)(N.$,{onClick:()=>{globalThis.location.reload()},size:"sm",variant:"outline",children:[(0,l.jsx)(u.A,{className:"mr-2 size-4"}),"Refresh"]}),(0,l.jsxs)(N.$,{onClick:M,size:"sm",variant:"outline",children:[(0,l.jsx)(g.A,{className:"mr-2 size-4"}),"Export"]})]}),description:"Interactive dashboard with real-time metrics and insights",filters:a?(0,l.jsx)(T.y,{}):void 0,title:"Reporting Dashboard",children:(0,l.jsxs)("div",{className:"space-y-8",children:[(()=>{let e=[];return(E.status.length>0&&e.push(`${E.status.length} status`),E.locations.length>0&&e.push(`${E.locations.length} locations`),E.employees.length>0&&e.push(`${E.employees.length} employees`),E.vehicles.length>0&&e.push(`${E.vehicles.length} vehicles`),0===e.length)?null:(0,l.jsxs)("div",{className:"flex items-center gap-3 text-sm text-muted-foreground",children:[(0,l.jsx)("span",{children:"Active filters:"}),e.map((e,s)=>(0,l.jsx)(f.E,{className:"text-xs",variant:"secondary",children:e},`filter-${s}-${e}`))]})})(),(0,l.jsxs)(b.tU,{className:"w-full",onValueChange:t,value:s,children:[(0,l.jsx)(b.j7,{className:"grid w-full grid-cols-9 h-12",children:$.map(e=>(0,l.jsxs)(b.Xi,{className:"flex items-center gap-2 text-sm font-medium",value:e.id,children:[e.icon,(0,l.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id))}),$.map(e=>(0,l.jsxs)(b.av,{className:"space-y-8",value:e.id,children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("h2",{className:"text-2xl font-semibold tracking-tight flex items-center gap-3",children:[e.icon,e.label]}),(0,l.jsx)("p",{className:"text-muted-foreground",children:e.description})]}),z()]},e.id))]})]})})})};a()}catch(e){a(e)}})},19484:(e,s,t)=>{Promise.resolve().then(t.bind(t,12662))},24164:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{p:()=>d});var l=t(60687),r=t(43210),i=t.n(r),n=t(972),c=e([n]);let o=(n=(c.then?(await c)():c)[0]).StyleSheet.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#059669",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#ecfdf5",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#f0fdf4",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),d=({data:e,reportTitle:s,metadata:t})=>{let a=i().useMemo(()=>{if(!e)return{};let s=e?.data||e;return s&&"object"==typeof s?{totalCount:s.totalCount||0,completedTasks:s.completedTasks||0,pendingTasks:s.pendingTasks||0,overdueCount:s.overdueCount||0,completionRate:s.completionRate||0,averageCompletionTime:s.averageCompletionTime||0,statusDistribution:Array.isArray(s.statusDistribution)?s.statusDistribution:[],priorityDistribution:Array.isArray(s.priorityDistribution)?s.priorityDistribution:[],assignmentMetrics:s.assignmentMetrics||{},...s}:{}},[e]),r=t||{};return(0,l.jsx)(n.Document,{children:(0,l.jsxs)(n.Page,{size:"A4",style:o.page,children:[(0,l.jsx)(n.Text,{style:o.header,children:s||"Task Report"}),(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Task Summary"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Total Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.totalCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Completed Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.completedTasks||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Pending Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.pendingTasks||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Overdue Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.overdueCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Completion Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.completionRate?.toFixed(2)||0,"%"]})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Completion Time:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.averageCompletionTime?.toFixed(2)||0," hours"]})]})]}),a.statusDistribution&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Status Distribution"}),(0,l.jsxs)(n.View,{style:o.table,children:[(0,l.jsxs)(n.View,{style:o.tableHeader,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:"Status"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Count"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Percentage"})]}),a.statusDistribution.map((e,s)=>(0,l.jsxs)(n.View,{style:o.tableRow,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.status||e?._id||"Unknown"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.count||e?._count?.status||0}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.percentage?`${Number(e.percentage).toFixed(1)}%`:"N/A"})]},`status-${s}`))]})]}),a.priorityDistribution&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Priority Distribution"}),(0,l.jsxs)(n.View,{style:o.table,children:[(0,l.jsxs)(n.View,{style:o.tableHeader,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:"Priority"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Count"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Percentage"})]}),a.priorityDistribution.map((e,s)=>(0,l.jsxs)(n.View,{style:o.tableRow,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.priority||e?._id||"Unknown"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.count||e?._count?.priority||0}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.percentage?`${Number(e.percentage).toFixed(1)}%`:"N/A"})]},`priority-${s}`))]})]}),a.assignmentMetrics&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Assignment Metrics"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Assigned Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.assignmentMetrics.assignedCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Unassigned Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.assignmentMetrics.unassignedCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Assignment Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.assignmentMetrics.assignmentRate?.toFixed(2)||0,"%"]})]})]}),r&&Object.keys(r).length>0&&(0,l.jsxs)(n.View,{style:o.metadata,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Report Information"}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Report ID: ",r.id||"N/A"]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Type: ",r.type||"N/A"]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Entity Type: ",r.entityType||"N/A"]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Generated:"," ",r.generatedAt?new Date(r.generatedAt).toLocaleString():"N/A"]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Generated By: ",r.generatedBy||"N/A"]})]})]})})};a()}catch(e){a(e)}})},26373:(e,s,t)=>{"use strict";t.d(s,{V:()=>o});var a=t(60687),l=t(43967),r=t(74158);t(43210);var i=t(16488),n=t(29523),c=t(22482);function o({className:e,classNames:s,showOutsideDays:t=!0,...o}){return(0,a.jsx)(i.hv,{className:(0,c.cn)("p-3",e),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...s},components:{IconLeft:({className:e,...s})=>(0,a.jsx)(l.A,{className:(0,c.cn)("h-4 w-4",e),...s}),IconRight:({className:e,...s})=>(0,a.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",e),...s})},showOutsideDays:t,...o})}o.displayName="Calendar"},27069:(e,s,t)=>{"use strict";t.d(s,{i:()=>J});var a=t(60687),l=t(43210),r=t(44493),i=t(29523),n=t(96834),c=t(36644),o=t(54052),d=t(58369),m=t(22482),x=t(8693),h=t(54050),p=t(30259),u=t(8342);let g=()=>{let e=(0,x.jE)(),s=(0,p.Sk)(["report-types"],async()=>{let e=await u.uE.get("/reporting/report-types");return e.data?.data||[]},{cacheDuration:3e5,enableRetry:!0}),t=(0,h.n)({mutationFn:async e=>{let s=await u.uE.post("/reporting/report-types",e);return s.data?.data||s.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),a=(0,h.n)({mutationFn:async({id:e,...s})=>{let t=await u.uE.put(`/reporting/report-types/${e}`,s);return t.data?.data||t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),l=(0,h.n)({mutationFn:async e=>{await u.uE.delete(`/reporting/report-types/${e}`)},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),r=(0,h.n)({mutationFn:async e=>{let s=await u.uE.post(`/reporting/report-types/${e}/duplicate`);return s.data?.data||s.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),i=(0,h.n)({mutationFn:async({id:e,isActive:s})=>{let t=await u.uE.patch(`/reporting/report-types/${e}/toggle-active`,{isActive:s});return t.data?.data||t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}});return{createReportType:t,data:s.data,deleteReportType:l,duplicateReportType:r,error:s.error,isLoading:s.isLoading,refetch:s.refetch,toggleReportTypeActive:i,updateReportType:a}};var j=t(39883),y=t(17016),v=t(27605),f=t(63442),N=t(9275),b=t(63503),w=t(71669),k=t(89667),C=t(34729),A=t(15079),S=t(56896);let T=N.z.object({name:N.z.string().min(1,"Name is required").max(100,"Name must be less than 100 characters"),description:N.z.string().optional(),category:N.z.string().min(1,"Category is required"),dataSource:N.z.string().min(1,"Data source is required"),widgets:N.z.array(N.z.string()).min(1,"At least one widget is required"),filters:N.z.array(N.z.string()).optional(),isActive:N.z.boolean().default(!0),isPublic:N.z.boolean().default(!1),refreshInterval:N.z.number().min(1).max(1440).optional(),tags:N.z.array(N.z.string()).optional()}),D=[{value:"analytics",label:"Analytics Widget"},{value:"chart",label:"Chart Widget"},{value:"table",label:"Data Table"},{value:"metrics",label:"Metrics Widget"},{value:"correlation",label:"Correlation Widget"}],R=[{value:"delegations",label:"Delegations"},{value:"tasks",label:"Tasks"},{value:"vehicles",label:"Vehicles"},{value:"employees",label:"Employees"},{value:"cross-entity",label:"Cross-Entity"}],E=[{value:"operational",label:"Operational"},{value:"performance",label:"Performance"},{value:"financial",label:"Financial"},{value:"compliance",label:"Compliance"},{value:"analytics",label:"Analytics"}],$=({reportType:e,onSubmit:s,onCancel:t,isLoading:l=!1})=>{let r=(0,v.mN)({resolver:(0,f.u)(T),defaultValues:{name:e?.name||"",description:e?.description||"",category:e?.category||"",dataSource:e?.dataSource||"",widgets:e?.widgets||[],filters:e?.filters||[],isActive:e?.isActive??!0,isPublic:e?.isPublic??!1,refreshInterval:e?.refreshInterval||60,tags:e?.tags||[]}}),n=async e=>{try{await s(e)}catch(e){console.error("Form submission error:",e)}};return(0,a.jsx)(b.lG,{open:!0,onOpenChange:t,children:(0,a.jsxs)(b.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(b.c7,{children:[(0,a.jsxs)(b.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),e?"Edit Report Type":"Create Report Type"]}),(0,a.jsx)(b.rr,{children:e?"Update the report type configuration and settings.":"Create a new report type with custom widgets and data sources."})]}),(0,a.jsx)(w.lV,{...r,children:(0,a.jsxs)("form",{onSubmit:r.handleSubmit(n),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"}),(0,a.jsx)(w.zB,{control:r.control,name:"name",render:({field:e})=>(0,a.jsxs)(w.eI,{children:[(0,a.jsx)(w.lR,{children:"Name *"}),(0,a.jsx)(w.MJ,{children:(0,a.jsx)(k.p,{placeholder:"Enter report type name",...e})}),(0,a.jsx)(w.C5,{})]})}),(0,a.jsx)(w.zB,{control:r.control,name:"description",render:({field:e})=>(0,a.jsxs)(w.eI,{children:[(0,a.jsx)(w.lR,{children:"Description"}),(0,a.jsx)(w.MJ,{children:(0,a.jsx)(C.T,{placeholder:"Describe what this report type shows",rows:3,...e})}),(0,a.jsx)(w.C5,{})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(w.zB,{control:r.control,name:"category",render:({field:e})=>(0,a.jsxs)(w.eI,{children:[(0,a.jsx)(w.lR,{children:"Category *"}),(0,a.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(w.MJ,{children:(0,a.jsx)(A.bq,{children:(0,a.jsx)(A.yv,{placeholder:"Select category"})})}),(0,a.jsx)(A.gC,{children:E.map(e=>(0,a.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(w.C5,{})]})}),(0,a.jsx)(w.zB,{control:r.control,name:"dataSource",render:({field:e})=>(0,a.jsxs)(w.eI,{children:[(0,a.jsx)(w.lR,{children:"Data Source *"}),(0,a.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(w.MJ,{children:(0,a.jsx)(A.bq,{children:(0,a.jsx)(A.yv,{placeholder:"Select data source"})})}),(0,a.jsx)(A.gC,{children:R.map(e=>(0,a.jsx)(A.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(w.C5,{})]})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Widget Configuration"}),(0,a.jsx)(w.zB,{control:r.control,name:"widgets",render:({field:e})=>(0,a.jsxs)(w.eI,{children:[(0,a.jsx)(w.lR,{children:"Widgets *"}),(0,a.jsx)(w.Rr,{children:"Select the widgets to include in this report type"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:D.map(s=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.S,{id:s.value,checked:e.value?.includes(s.value),onCheckedChange:t=>{let a=e.value||[];t?e.onChange([...a,s.value]):e.onChange(a.filter(e=>e!==s.value))}}),(0,a.jsx)("label",{htmlFor:s.value,className:"text-sm",children:s.label})]},s.value))}),(0,a.jsx)(w.C5,{})]})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(w.zB,{control:r.control,name:"isActive",render:({field:e})=>(0,a.jsxs)(w.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(w.lR,{children:"Active"}),(0,a.jsx)(w.Rr,{children:"Enable this report type for use"})]}),(0,a.jsx)(w.MJ,{children:(0,a.jsx)(S.S,{checked:e.value,onCheckedChange:e.onChange})})]})}),(0,a.jsx)(w.zB,{control:r.control,name:"isPublic",render:({field:e})=>(0,a.jsxs)(w.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(w.lR,{children:"Public"}),(0,a.jsx)(w.Rr,{children:"Make available to all users"})]}),(0,a.jsx)(w.MJ,{children:(0,a.jsx)(S.S,{checked:e.value,onCheckedChange:e.onChange})})]})})]}),(0,a.jsx)(w.zB,{control:r.control,name:"refreshInterval",render:({field:e})=>(0,a.jsxs)(w.eI,{children:[(0,a.jsx)(w.lR,{children:"Refresh Interval (minutes)"}),(0,a.jsx)(w.MJ,{children:(0,a.jsx)(k.p,{type:"number",min:"1",max:"1440",placeholder:"60",...e,onChange:s=>e.onChange(parseInt(s.target.value)||60)})}),(0,a.jsx)(w.Rr,{children:"How often the report data should refresh (1-1440 minutes)"}),(0,a.jsx)(w.C5,{})]})})]}),(0,a.jsxs)(b.Es,{children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),(0,a.jsx)(i.$,{type:"submit",disabled:l,children:l?"Saving...":e?"Update":"Create"})]})]})})]})})};var P=t(21342),M=t(48206),z=t(76311),F=t(69795),V=t(19422),I=t(35137),L=t(92477),O=t(16032),W=t(57207),B=t(15036),Z=t(75699);let U=e=>{switch(e.toLowerCase()){case"operational":return"bg-blue-100 text-blue-800";case"performance":return"bg-green-100 text-green-800";case"financial":return"bg-yellow-100 text-yellow-800";case"compliance":return"bg-red-100 text-red-800";case"analytics":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},G=e=>{switch(e.toLowerCase()){case"delegations":case"tasks":case"vehicles":default:return(0,a.jsx)(c.A,{className:"h-4 w-4"});case"employees":return(0,a.jsx)(M.A,{className:"h-4 w-4"})}},_=({reportType:e,onSelect:s,onEdit:t,onDelete:l,onDuplicate:c,onToggleActive:o,className:d="",showActions:x=!0})=>{let h=(e,s)=>{e.stopPropagation(),s()};return(0,a.jsxs)(r.Zp,{className:(0,m.cn)("cursor-pointer transition-all duration-200 hover:shadow-md",!e.isActive&&"opacity-60",d),onClick:()=>{s&&s(e)},children:[(0,a.jsx)(r.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)(r.ZB,{className:"text-lg flex items-center gap-2",children:[G(e.dataSource),e.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsx)(n.E,{className:(0,m.cn)("text-xs",U(e.category)),children:e.category}),!e.isActive&&(0,a.jsx)(n.E,{variant:"secondary",className:"text-xs",children:"Inactive"}),e.isPublic&&(0,a.jsxs)(n.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(z.A,{className:"h-3 w-3 mr-1"}),"Public"]})]})]}),x&&(0,a.jsxs)(P.rI,{children:[(0,a.jsx)(P.ty,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:e=>e.stopPropagation(),children:(0,a.jsx)(F.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(P.SQ,{align:"end",children:[s&&(0,a.jsxs)(P._2,{onClick:t=>h(t,()=>s(e)),children:[(0,a.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Use Report Type"]}),t&&(0,a.jsxs)(P._2,{onClick:s=>h(s,()=>t(e)),children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),c&&(0,a.jsxs)(P._2,{onClick:s=>h(s,()=>c(e)),children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),o&&(0,a.jsx)(P._2,{onClick:s=>h(s,()=>o(e)),children:e.isActive?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Deactivate"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Activate"]})}),l&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(P.mB,{}),(0,a.jsxs)(P._2,{onClick:s=>h(s,()=>l(e)),className:"text-red-600",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-2",children:["Widgets (",e.widgets?.length||0,")"]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.widgets?.slice(0,3).map((e,s)=>(0,a.jsx)(n.E,{variant:"outline",className:"text-xs",children:e},s)),(e.widgets?.length||0)>3&&(0,a.jsxs)(n.E,{variant:"outline",className:"text-xs",children:["+",(e.widgets?.length||0)-3," more"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[G(e.dataSource),(0,a.jsx)("span",{children:e.dataSource})]}),e.refreshInterval&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(B.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[e.refreshInterval,"m"]})]})]}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,s)=>(0,a.jsx)(n.E,{variant:"secondary",className:"text-xs",children:e},s)),e.tags.length>3&&(0,a.jsxs)(n.E,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,a.jsx)("div",{className:"pt-2 border-t text-xs text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:["Created: ",e.createdAt?(0,Z.GP)(new Date(e.createdAt),"MMM dd, yyyy"):"Unknown"]}),e.updatedAt&&(0,a.jsxs)("span",{children:["Updated: ",(0,Z.GP)(new Date(e.updatedAt),"MMM dd")]})]})})]})]})},J=({className:e="",onReportTypeSelect:s,allowEdit:t=!0,allowDelete:x=!0})=>{let[h,p]=(0,l.useState)(!1),[u,v]=(0,l.useState)(null),[f,N]=(0,l.useState)(""),{data:b,isLoading:w,error:k,createReportType:C,updateReportType:A,deleteReportType:S,duplicateReportType:T}=g(),D=async e=>{try{let s={name:e.name,category:e.category,dataSource:e.dataSource,widgets:e.widgets,isActive:e.isActive,isPublic:e.isPublic,...e.description&&{description:e.description},...e.filters&&{filters:e.filters},...e.refreshInterval&&{refreshInterval:e.refreshInterval},...e.tags&&{tags:e.tags}};u?await A.mutateAsync({id:u.id,...s}):await C.mutateAsync(s),p(!1),v(null)}catch(e){console.error("Failed to save report type:",e)}},R=e=>{v(e),p(!0)},E=async e=>{if(window.confirm(`Are you sure you want to delete "${e.name}"?`))try{await S.mutateAsync(e.id)}catch(e){console.error("Failed to delete report type:",e)}},P=async e=>{try{await T.mutateAsync(e.id)}catch(e){console.error("Failed to duplicate report type:",e)}},M=(0,l.useMemo)(()=>b?.filter(e=>e.name.toLowerCase().includes(f.toLowerCase())||e.description?.toLowerCase().includes(f.toLowerCase()))||[],[b,f]);return w?(0,a.jsxs)(r.Zp,{className:e,children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Report Type Manager"]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)(j.g,{isLoading:!0,data:null,error:null,children:()=>null})})]}):k?(0,a.jsxs)(r.Zp,{className:e,children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Report Type Manager"]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)(y.n,{error:k})})]}):(0,a.jsxs)("div",{className:(0,m.cn)("space-y-6",e),children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Report Type Manager"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(n.E,{variant:"secondary",className:"text-xs",children:[M.length," types"]}),(0,a.jsxs)(i.$,{onClick:()=>p(!0),size:"sm",className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),"New Report Type"]})]})]})}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"text",placeholder:"Search report types...",value:f,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]}),0===M.length?(0,a.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Report Types Found"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:f?"No report types match your search.":"Get started by creating your first report type."}),(0,a.jsxs)(i.$,{onClick:()=>p(!0),children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Create Report Type"]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:M.map(e=>(0,a.jsx)(_,{reportType:e,...s&&{onSelect:s},...t&&{onEdit:R},...x&&{onDelete:E},onDuplicate:P},e.id))})]})]}),h&&(0,a.jsx)($,{reportType:u,onSubmit:D,onCancel:()=>{p(!1),v(null)},isLoading:C.isPending||A.isPending})]})}},33052:(e,s,t)=>{Promise.resolve().then(t.bind(t,87432))},34729:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var a=t(60687),l=t(43210),r=t(22482);let i=l.forwardRef(({className:e,...s},t)=>(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));i.displayName="Textarea"},37783:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var a=t(60687);t(43210);var l=t(10053),r=t(27629),i=t(3746);let n={entityType:"reporting",title:"Reporting Dashboard",description:"Comprehensive reporting and analytics dashboard",viewModes:["grid","list"],defaultViewMode:"grid",tabs:[{id:"overview",label:"Overview",icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),description:"High-level analytics and status distribution.",widgets:[{id:"delegation-status",component:l.j3,span:"lg:col-span-1"},{id:"delegation-trend",component:l.Sp,span:"lg:col-span-2"},{id:"location-distribution",component:l.AP,span:"lg:col-span-2"},{id:"task-metrics",component:l.B,span:"lg:col-span-1"}]},{id:"details",label:"Detailed Report",icon:(0,a.jsx)(i.A,{className:"h-4 w-4"}),description:"In-depth data table with filtering and sorting.",widgets:[{id:"reporting-table",component:l.xr,span:"lg:col-span-3 xl:col-span-4"}]}]};n.tabs.flatMap(e=>e.widgets),Date.now(),Date.now(),new Date().getFullYear()},39883:(e,s,t)=>{"use strict";t.d(s,{g:()=>a.gO});var a=t(52027)},40988:(e,s,t)=>{"use strict";t.d(s,{AM:()=>n,Wv:()=>c,hl:()=>o});var a=t(60687),l=t(40599),r=t(43210),i=t(22482);let n=l.bL,c=l.l9;l.bm;let o=r.forwardRef(({align:e="center",className:s,sideOffset:t=4,...r},n)=>(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{align:e,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",s),ref:n,sideOffset:t,...r})}));o.displayName=l.UC.displayName},41536:(e,s,t)=>{"use strict";t.d(s,{y:()=>O});var a=t(60687),l=t(43210),r=t.n(l),i=t(44493),n=t(29523),c=t(96834),o=t(35950),d=t(80013),m=t(26373),x=t(40988),h=t(22482),p=t(75699),u=t(26622),g=t(84122);let j=({compact:e=!1,className:s=""})=>{let t=(0,g.N8)(),{setDateRange:l}=(0,g.o1)(),{validationErrors:i}=(0,g.Kx)(),[c,o]=r().useState(!1),j=e=>{e?.from&&e?.to&&(l(e.from,e.to),o(!1))},y=[{label:"Last 7 days",getValue:()=>({from:new Date(Date.now()-6048e5),to:new Date})},{label:"Last 30 days",getValue:()=>({from:new Date(Date.now()-2592e6),to:new Date})},{label:"Last 90 days",getValue:()=>({from:new Date(Date.now()-7776e6),to:new Date})},{label:"This year",getValue:()=>({from:new Date(new Date().getFullYear(),0,1),to:new Date})}],v=e=>{l(e.from,e.to),o(!1)},f=()=>{let{from:s,to:a}=t.dateRange;return s&&a?e?`${(0,p.GP)(s,"MMM d")} - ${(0,p.GP)(a,"MMM d")}`:`${(0,p.GP)(s,"MMM d, yyyy")} - ${(0,p.GP)(a,"MMM d, yyyy")}`:"Select date range"},N=i.dateRange;return e?(0,a.jsxs)("div",{className:(0,h.cn)("space-y-1",s),children:[(0,a.jsx)(d.J,{className:"text-xs font-medium",children:"Date Range"}),(0,a.jsxs)(x.AM,{open:c,onOpenChange:o,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-start text-left font-normal",!t.dateRange.from&&"text-muted-foreground",N&&"border-red-500"),children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),f()]})}),(0,a.jsx)(x.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-3",children:y.map(e=>(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>v(e.getValue()),className:"text-xs",children:e.label},e.label))}),(0,a.jsx)(m.V,{mode:"range",selected:{from:t.dateRange.from,to:t.dateRange.to},onSelect:j,numberOfMonths:1,className:"rounded-md border"})]})})]}),N&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:N})]}):(0,a.jsxs)("div",{className:(0,h.cn)("space-y-2",s),children:[(0,a.jsx)(d.J,{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsxs)(x.AM,{open:c,onOpenChange:o,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-start text-left font-normal",!t.dateRange.from&&"text-muted-foreground",N&&"border-red-500"),children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),f()]})}),(0,a.jsx)(x.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Quick Ranges"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:y.map(e=>(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>v(e.getValue()),className:"text-sm",children:e.label},e.label))})]}),(0,a.jsx)(m.V,{mode:"range",selected:{from:t.dateRange.from,to:t.dateRange.to},onSelect:j,numberOfMonths:2,className:"rounded-md border"})]})})]}),N&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:N})]})};var y=t(56896),v=t(61662),f=t(78726);let N=({compact:e=!1,className:s=""})=>{let t=(0,g.N8)(),{setStatus:l}=(0,g.o1)(),{validationErrors:i}=(0,g.Kx)(),[o,m]=r().useState(!1),p=[{value:"DRAFT",label:"Draft",color:"bg-gray-100 text-gray-800"},{value:"PENDING",label:"Pending",color:"bg-yellow-100 text-yellow-800"},{value:"APPROVED",label:"Approved",color:"bg-blue-100 text-blue-800"},{value:"IN_PROGRESS",label:"In Progress",color:"bg-purple-100 text-purple-800"},{value:"COMPLETED",label:"Completed",color:"bg-green-100 text-green-800"},{value:"CANCELLED",label:"Cancelled",color:"bg-red-100 text-red-800"}],u=e=>{let s=t.status;s.includes(e)?l(s.filter(s=>s!==e)):l([...s,e])},j=()=>{l(p.map(e=>e.value))},N=()=>{l([])},b=()=>{let e=t.status.length;if(0===e)return"All statuses";if(1===e){let e=p.find(e=>e.value===t.status[0]);return e?.label||"Unknown"}return`${e} statuses`},w=i.status;return e?(0,a.jsxs)("div",{className:(0,h.cn)("space-y-1",s),children:[(0,a.jsx)(d.J,{className:"text-xs font-medium",children:"Status"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",w&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:b()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-56 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:j,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:N,children:"None"})]}),(0,a.jsx)("div",{className:"space-y-2",children:p.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.S,{id:`status-${e.value}`,checked:t.status.includes(e.value),onCheckedChange:()=>u(e.value)}),(0,a.jsx)(d.J,{htmlFor:`status-${e.value}`,className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsx)(c.E,{variant:"secondary",className:(0,h.cn)("text-xs",e.color),children:e.label})})]},e.value))})]})})]}),w&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:w})]}):(0,a.jsxs)("div",{className:(0,h.cn)("space-y-2",s),children:[(0,a.jsx)(d.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",w&&"border-red-500"),children:[(0,a.jsx)("span",{children:b()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Statuses"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:j,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:N,children:"None"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:p.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.S,{id:`status-${e.value}`,checked:t.status.includes(e.value),onCheckedChange:()=>u(e.value)}),(0,a.jsx)(d.J,{htmlFor:`status-${e.value}`,className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsx)(c.E,{variant:"secondary",className:(0,h.cn)("text-sm",e.color),children:e.label})})]},e.value))})]})})]}),t.status.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:t.status.map(e=>{let s=p.find(s=>s.value===e);return s?(0,a.jsxs)(c.E,{variant:"secondary",className:(0,h.cn)("text-xs pr-1",s.color),children:[s.label,(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u(e),children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]},e):null})}),w&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:w})]})};var b=t(89667),w=t(41936),k=t(26398);let C=({compact:e=!1,className:s=""})=>{let t=(0,g.N8)(),{setLocations:l}=(0,g.o1)(),{validationErrors:i}=(0,g.Kx)(),[o,m]=r().useState(!1),[p,u]=r().useState(""),j=["New York, NY","Los Angeles, CA","Chicago, IL","Houston, TX","Phoenix, AZ","Philadelphia, PA","San Antonio, TX","San Diego, CA","Dallas, TX","San Jose, CA","Austin, TX","Jacksonville, FL","Fort Worth, TX","Columbus, OH","Charlotte, NC","San Francisco, CA","Indianapolis, IN","Seattle, WA","Denver, CO","Washington, DC"].filter(e=>e.toLowerCase().includes(p.toLowerCase())),N=e=>{let s=t.locations;s.includes(e)?l(s.filter(s=>s!==e)):l([...s,e])},C=()=>{l(j)},A=()=>{l([])},S=()=>{let e=t.locations.length;return 0===e?"All locations":1===e?t.locations[0]:`${e} locations`},T=i.locations;return e?(0,a.jsxs)("div",{className:(0,h.cn)("space-y-1",s),children:[(0,a.jsx)(d.J,{className:"text-xs font-medium",children:"Location"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",T&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:S()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search locations...",value:p,onChange:e=>u(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:C,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:A,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:j.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.S,{id:`location-${e}`,checked:t.locations.includes(e),onCheckedChange:()=>N(e)}),(0,a.jsxs)(d.J,{htmlFor:`location-${e}`,className:"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-3 w-3 text-muted-foreground"}),e]})]},e))})]})})]}),T&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:T})]}):(0,a.jsxs)("div",{className:(0,h.cn)("space-y-2",s),children:[(0,a.jsx)(d.J,{className:"text-sm font-medium",children:"Locations"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",T&&"border-red-500"),children:[(0,a.jsx)("span",{children:S()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Locations"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:C,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:A,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search locations...",value:p,onChange:e=>u(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:j.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.S,{id:`location-${e}`,checked:t.locations.includes(e),onCheckedChange:()=>N(e)}),(0,a.jsxs)(d.J,{htmlFor:`location-${e}`,className:"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"}),e]})]},e))})]})})]}),t.locations.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.locations.slice(0,3).map(e=>(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs pr-1",children:[e.length>15?`${e.slice(0,15)}...`:e,(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>N(e),children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]},e)),t.locations.length>3&&(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["+",t.locations.length-3," more"]})]}),T&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:T})]})};var A=t(58595);let S=({compact:e=!1,className:s=""})=>{let t=(0,g.N8)(),{setEmployees:l}=(0,g.o1)(),{validationErrors:i}=(0,g.Kx)(),[o,m]=r().useState(!1),[p,u]=r().useState(""),j=[{id:1,name:"John Smith",department:"Operations"},{id:2,name:"Sarah Johnson",department:"Sales"},{id:3,name:"Mike Davis",department:"Engineering"},{id:4,name:"Emily Brown",department:"Marketing"},{id:5,name:"David Wilson",department:"Operations"},{id:6,name:"Lisa Anderson",department:"HR"},{id:7,name:"Tom Miller",department:"Finance"},{id:8,name:"Anna Garcia",department:"Sales"},{id:9,name:"Chris Taylor",department:"Engineering"},{id:10,name:"Jessica Lee",department:"Marketing"}],N=j.filter(e=>e.name.toLowerCase().includes(p.toLowerCase())||e.department.toLowerCase().includes(p.toLowerCase())),k=e=>{let s=t.employees;s.includes(e)?l(s.filter(s=>s!==e)):l([...s,e])},C=()=>{l(N.map(e=>e.id))},S=()=>{l([])},T=()=>{let e=t.employees.length;if(0===e)return"All employees";if(1===e){let e=j.find(e=>e.id===t.employees[0]);return e?.name||"Unknown"}return`${e} employees`},D=i.employees;return e?(0,a.jsxs)("div",{className:(0,h.cn)("space-y-1",s),children:[(0,a.jsx)(d.J,{className:"text-xs font-medium",children:"Employee"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",D&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:T()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search employees...",value:p,onChange:e=>u(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:C,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:S,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.S,{id:`employee-${e.id}`,checked:t.employees.includes(e.id),onCheckedChange:()=>k(e.id)}),(0,a.jsx)(d.J,{htmlFor:`employee-${e.id}`,className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"h-3 w-3 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.department})]})]})})]},e.id))})]})})]}),D&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:D})]}):(0,a.jsxs)("div",{className:(0,h.cn)("space-y-2",s),children:[(0,a.jsx)(d.J,{className:"text-sm font-medium",children:"Employees"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",D&&"border-red-500"),children:[(0,a.jsx)("span",{children:T()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Employees"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:C,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:S,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search employees...",value:p,onChange:e=>u(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.S,{id:`employee-${e.id}`,checked:t.employees.includes(e.id),onCheckedChange:()=>k(e.id)}),(0,a.jsx)(d.J,{htmlFor:`employee-${e.id}`,className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.department})]})]})})]},e.id))})]})})]}),t.employees.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.employees.slice(0,3).map(e=>{let s=j.find(s=>s.id===e);return s?(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs pr-1",children:[s.name,(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>k(e),children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]},e):null}),t.employees.length>3&&(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["+",t.employees.length-3," more"]})]}),D&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:D})]})};var T=t(24920);let D=({compact:e=!1,className:s=""})=>{let t=(0,g.N8)(),{setVehicles:l}=(0,g.o1)(),{validationErrors:i}=(0,g.Kx)(),[o,m]=r().useState(!1),[p,u]=r().useState(""),j=[{id:1,make:"Toyota",model:"Camry",year:2022,licensePlate:"ABC-123"},{id:2,make:"Honda",model:"Accord",year:2021,licensePlate:"DEF-456"},{id:3,make:"Ford",model:"F-150",year:2023,licensePlate:"GHI-789"},{id:4,make:"Chevrolet",model:"Silverado",year:2022,licensePlate:"JKL-012"},{id:5,make:"BMW",model:"X5",year:2021,licensePlate:"MNO-345"},{id:6,make:"Mercedes",model:"E-Class",year:2023,licensePlate:"PQR-678"},{id:7,make:"Audi",model:"A4",year:2022,licensePlate:"STU-901"},{id:8,make:"Nissan",model:"Altima",year:2021,licensePlate:"VWX-234"}],N=j.filter(e=>e.make.toLowerCase().includes(p.toLowerCase())||e.model.toLowerCase().includes(p.toLowerCase())||e.licensePlate.toLowerCase().includes(p.toLowerCase())),k=e=>{let s=t.vehicles;s.includes(e)?l(s.filter(s=>s!==e)):l([...s,e])},C=()=>{l(N.map(e=>e.id))},A=()=>{l([])},S=()=>{let e=t.vehicles.length;if(0===e)return"All vehicles";if(1===e){let e=j.find(e=>e.id===t.vehicles[0]);return e?`${e.make} ${e.model}`:"Unknown"}return`${e} vehicles`},D=i.vehicles;return e?(0,a.jsxs)("div",{className:(0,h.cn)("space-y-1",s),children:[(0,a.jsx)(d.J,{className:"text-xs font-medium",children:"Vehicle"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",D&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:S()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(w.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search vehicles...",value:p,onChange:e=>u(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:C,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:A,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.S,{id:`vehicle-${e.id}`,checked:t.vehicles.includes(e.id),onCheckedChange:()=>k(e.id)}),(0,a.jsx)(d.J,{htmlFor:`vehicle-${e.id}`,className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"h-3 w-3 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.make," ",e.model]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.licensePlate})]})]})})]},e.id))})]})})]}),D&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:D})]}):(0,a.jsxs)("div",{className:(0,h.cn)("space-y-2",s),children:[(0,a.jsx)(d.J,{className:"text-sm font-medium",children:"Vehicles"}),(0,a.jsxs)(x.AM,{open:o,onOpenChange:m,children:[(0,a.jsx)(x.Wv,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",className:(0,h.cn)("w-full justify-between text-left font-normal",D&&"border-red-500"),children:[(0,a.jsx)("span",{children:S()}),(0,a.jsx)(v.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(x.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Vehicles"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:C,children:"All"}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:A,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{placeholder:"Search vehicles...",value:p,onChange:e=>u(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(y.S,{id:`vehicle-${e.id}`,checked:t.vehicles.includes(e.id),onCheckedChange:()=>k(e.id)}),(0,a.jsx)(d.J,{htmlFor:`vehicle-${e.id}`,className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.make," ",e.model," (",e.year,")"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.licensePlate})]})]})})]},e.id))})]})})]}),t.vehicles.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.vehicles.slice(0,3).map(e=>{let s=j.find(s=>s.id===e);return s?(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs pr-1",children:[s.make," ",s.model,(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>k(e),children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]},e):null}),t.vehicles.length>3&&(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["+",t.vehicles.length-3," more"]})]}),D&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:D})]})};var R=t(63503),E=t(21342),$=t(71273),P=t(916),M=t(57207);let z=()=>{let[e,s]=r().useState({}),[t,a]=r().useState(0),l=r().useCallback(()=>{try{let e=localStorage.getItem("reporting-filter-presets"),t=e?JSON.parse(e):{};s(t)}catch{s({})}},[]);return r().useEffect(()=>{l()},[l,t]),{presets:e,refreshPresets:r().useCallback(()=>{a(e=>e+1)},[])}},F=({className:e=""})=>{let{applyPreset:s,saveAsPreset:t,deletePreset:l}=(0,g.v4)(),i=(0,g.N8)(),{presets:c,refreshPresets:o}=z(),[m,x]=r().useState(!1),[p,u]=r().useState(""),j=r().useCallback(()=>{p.trim()&&(t(p.trim()),u(""),x(!1),o())},[p,t,o]),y=r().useCallback(e=>{s(e)},[s]),f=r().useCallback(e=>{l(e),o()},[l,o]),N=r().useCallback(e=>{let s=[];return e.status?.length>0&&s.push(`${e.status.length} status`),e.locations?.length>0&&s.push(`${e.locations.length} locations`),e.employees?.length>0&&s.push(`${e.employees.length} employees`),e.vehicles?.length>0&&s.push(`${e.vehicles.length} vehicles`),s.length>0?s.join(", "):"No filters"},[]),w=[{name:"Last 30 Days",description:"All delegations from the last 30 days",action:()=>{console.log("Applying 30-day preset",{dateRange:{from:new Date(Date.now()-2592e6),to:new Date},status:[],locations:[],employees:[],vehicles:[]})}},{name:"Active Delegations",description:"In progress and approved delegations",action:()=>{console.log("Applying active delegations preset")}},{name:"Completed This Month",description:"Completed delegations from current month",action:()=>{console.log("Applying completed this month preset")}}],k=Object.keys(c);return(0,a.jsxs)("div",{className:(0,h.cn)("space-y-3",e),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(d.J,{className:"text-sm font-medium",children:"Filter Presets"}),(0,a.jsxs)(R.lG,{open:m,onOpenChange:x,children:[(0,a.jsx)(R.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-8",children:[(0,a.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Save"]})}),(0,a.jsxs)(R.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(R.c7,{children:[(0,a.jsx)(R.L3,{children:"Save Filter Preset"}),(0,a.jsx)(R.rr,{children:"Save your current filter settings as a preset for quick access later."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"preset-name",children:"Preset Name"}),(0,a.jsx)(b.p,{id:"preset-name",placeholder:"Enter preset name...",value:p,onChange:e=>u(e.target.value),onKeyDown:e=>{"Enter"===e.key&&j()}})]}),(0,a.jsxs)("div",{className:"p-3 bg-muted rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Current filters:"}),(0,a.jsx)("p",{className:"text-sm",children:N(i)})]})]}),(0,a.jsxs)(R.Es,{children:[(0,a.jsx)(n.$,{variant:"outline",onClick:()=>x(!1),children:"Cancel"}),(0,a.jsx)(n.$,{onClick:j,disabled:!p.trim(),children:"Save Preset"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Quick Presets"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:w.map(e=>(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:e.action,className:"h-8 text-xs",children:[(0,a.jsx)(P.A,{className:"h-3 w-3 mr-1"}),e.name]},e.name))})]}),k.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Saved Presets"}),(0,a.jsx)("div",{className:"space-y-1",children:k.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg border bg-card",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:N(c[e])})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>y(e),className:"h-8 px-2 text-xs",children:"Apply"}),(0,a.jsxs)(E.rI,{children:[(0,a.jsx)(E.ty,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(E.SQ,{align:"end",children:[(0,a.jsx)(E._2,{onClick:()=>y(e),children:"Apply Preset"}),(0,a.jsx)(E.mB,{}),(0,a.jsxs)(E._2,{onClick:()=>f(e),className:"text-red-600",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]},e))})]})]})};var V=t(90586),I=t(1305),L=t(58450);let O=({className:e="",showPresets:s=!0,compact:t=!1,includeServiceFilters:l=!1,includeTaskFilters:d=!1})=>{let m=(0,g.N8)(),{resetFilters:x,applyFilters:h,revertChanges:p}=(0,g.o1)(),{isFilterPanelOpen:u,hasUnsavedChanges:y,setFilterPanelOpen:v}=(0,g.FR)(),{validationErrors:b,isValid:w}=(0,g.Kx)(),k=()=>{x()},A=()=>{w&&h()},T=r().useMemo(()=>{let e=0;return m.status.length>0&&e++,m.locations.length>0&&e++,m.employees.length>0&&e++,m.vehicles.length>0&&e++,l&&(m.serviceTypes?.length&&e++,m.serviceStatus?.length&&e++,m.costRange&&e++),d&&m.includeTaskData&&e++,e},[m,l,d]),R=r().useMemo(()=>{let e=[];return m.status.length>0&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Status: ",m.status.length]},"status")),m.locations.length>0&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Locations: ",m.locations.length]},"locations")),m.employees.length>0&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Employees: ",m.employees.length]},"employees")),m.vehicles.length>0&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Vehicles: ",m.vehicles.length]},"vehicles")),l&&(m.serviceTypes?.length&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Service Types: ",m.serviceTypes.length]},"serviceTypes")),m.serviceStatus?.length&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Service Status: ",m.serviceStatus.length]},"serviceStatus")),m.costRange&&e.push((0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["Cost: $",m.costRange.min," - $",m.costRange.max]},"costRange"))),d&&m.includeTaskData&&e.push((0,a.jsx)(c.E,{variant:"secondary",className:"text-xs",children:"Task Data Included"},"taskData")),e},[m,l,d]),E=()=>{let e=Object.values(b);return 0===e.length?null:(0,a.jsx)("div",{className:"space-y-1",children:e.map((e,s)=>(0,a.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:e},s))})};return t?(0,a.jsxs)("div",{className:`space-y-4 ${e}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(V.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filters"}),T>0&&(0,a.jsx)(c.E,{variant:"default",className:"text-xs",children:T})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:k,children:(0,a.jsx)(I.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"default",size:"sm",onClick:A,disabled:!w||!y,children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsx)(j,{compact:!0}),(0,a.jsx)(N,{compact:!0}),(0,a.jsx)(C,{compact:!0}),(0,a.jsx)(S,{compact:!0}),(0,a.jsx)(D,{compact:!0})]}),E()]}):(0,a.jsxs)(i.Zp,{className:e,children:[(0,a.jsxs)(i.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(V.A,{className:"h-5 w-5"}),"Reporting Filters",T>0&&(0,a.jsxs)(c.E,{variant:"default",className:"text-xs",children:[T," active"]})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{v(!1)},className:"h-8 w-8 p-0",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]}),R.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:R})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6",children:[s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F,{}),(0,a.jsx)(o.w,{})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(j,{}),(0,a.jsx)(N,{}),(0,a.jsx)(C,{}),(0,a.jsx)(S,{}),(0,a.jsx)(D,{})]}),E(),(0,a.jsx)(o.w,{}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n.$,{variant:"outline",onClick:k,className:"flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"h-4 w-4"}),"Reset All"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[y&&(0,a.jsx)(n.$,{variant:"ghost",onClick:()=>{p()},children:"Revert"}),(0,a.jsxs)(n.$,{onClick:A,disabled:!w||!y,className:"flex items-center gap-2",children:[(0,a.jsx)(L.A,{className:"h-4 w-4"}),"Apply Filters"]})]})]})]})]})}},42024:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{D:()=>$});var l=t(60687),r=t(25928),i=t(80489),n=t(36644),c=t(24920),o=t(48206),d=t(27629),m=t(8751),x=t(90586),h=t(26622),p=t(20620),u=t(43210),g=t(91821),j=t(96834),y=t(29523),v=t(44493),f=t(56896),N=t(80013),b=t(79519),w=t(15079),k=t(49278),C=t(84547),A=t(91625),S=t(12045),T=e([A]);A=(T.then?(await T)():T)[0];let D=[{color:"bg-blue-100 text-blue-800",description:"Aggregate delegation analytics and trends",icon:i.A,id:"delegations",metrics:["Total Count","Completion Rate","Average Duration","Status Distribution"],name:"Delegations"},{color:"bg-green-100 text-green-800",description:"Task performance and completion analytics",icon:n.A,id:"tasks",metrics:["Total Tasks","Completion Rate","Average Time","Priority Distribution"],name:"Tasks"},{color:"bg-orange-100 text-orange-800",description:"Vehicle utilization and maintenance analytics",icon:c.A,id:"vehicles",metrics:["Fleet Size","Utilization Rate","Maintenance Costs","Performance Metrics"],name:"Vehicles"},{color:"bg-purple-100 text-purple-800",description:"Employee performance and workload analytics",icon:o.A,id:"employees",metrics:["Total Employees","Performance Scores","Workload Distribution","Availability"],name:"Employees"}],R=[{description:"Formatted analytics report",id:"pdf",name:"PDF"},{description:"Spreadsheet with charts",id:"excel",name:"Excel"},{description:"Raw data export",id:"csv",name:"CSV"}],E={delegations:[{id:"status",name:"Status",options:["Active","Completed","Pending","Cancelled"],type:"select"},{id:"priority",name:"Priority",options:["High","Medium","Low"],type:"select"},{id:"location",name:"Location",type:"text"}],employees:[{id:"department",name:"Department",type:"text"},{id:"position",name:"Position",type:"text"},{id:"status",name:"Status",options:["Active","Inactive","On Leave"],type:"select"}],tasks:[{id:"status",name:"Status",options:["Pending","In Progress","Completed","Cancelled"],type:"select"},{id:"priority",name:"Priority",options:["High","Medium","Low"],type:"select"},{id:"assignee",name:"Assignee",type:"text"}],vehicles:[{id:"status",name:"Status",options:["Active","Maintenance","Inactive"],type:"select"},{id:"type",name:"Vehicle Type",type:"text"},{id:"location",name:"Location",type:"text"}]},$=({defaultEntityType:e="delegations",onReportGenerated:s})=>{let[t,a]=(0,u.useState)(e),[i,n]=(0,u.useState)("default"),[c,o]=(0,u.useState)("pdf"),[T,$]=(0,u.useState)({from:(0,r.e)(new Date,29),to:new Date}),[P,M]=(0,u.useState)({}),[z,F]=(0,u.useState)(!0),[V,I]=(0,u.useState)(!0),{error:L,generateAggregateReport:O,isGenerating:W}=(0,S.kv)(),{isLoading:B,templates:Z}=(0,S.fD)(),{exportReportToExcel:U,exportReportToPDF:G,exportToCSV:_}=(0,A.useExport)(),J=(e,s)=>{M(t=>({...t,[e]:s}))},H=async()=>{try{let e,a={...P,...T&&{dateRange:{from:T.from.toISOString(),to:T.to.toISOString()}}};try{e=await O({entityType:t,filters:a,format:"json",options:{includeCharts:z,includeTrends:V},template:i})}catch(s){console.warn("API call failed, providing fallback data:",s),e={data:{priorityDistribution:[],records:[],statusDistribution:[],summary:{generatedAt:new Date().toISOString(),message:`Unable to fetch ${t} data from server. This is a sample report with fallback data.`,note:"Please check your connection and try again."},totalCount:0},metadata:{entityType:t,generatedAt:new Date().toISOString(),generatedBy:"System (Fallback)",id:`fallback_${Date.now()}`,note:"Generated with fallback data due to API unavailability",status:"fallback"}}}if(!e)throw Error("No report data received from server");e.data||(e.data={priorityDistribution:[],records:[],statusDistribution:[],summary:{generatedAt:new Date().toISOString(),message:"No data available for the selected criteria"},totalCount:0});let l=`${t.charAt(0).toUpperCase()+t.slice(1)} Analytics Report`,r=`${t}-analytics-${new Date().toISOString().split("T")[0]}`;try{switch(k.JP.show({description:`Creating ${c.toUpperCase()} report for ${t}...`,duration:2e3,title:"Generating Report"}),c){case"csv":{let s=Array.isArray(e.data)?e.data:[e.data];_(s,{filename:r}),console.log("CSV export completed successfully");break}case"excel":U(e,t,r),console.log("Excel export completed successfully");break;case"pdf":console.log("Starting PDF export with data:",e),await G(e,t,l,r),console.log("PDF export completed successfully");break;default:throw Error(`Unsupported export format: ${c}`)}k.JP.success("Report Generated Successfully",`${c.toUpperCase()} report has been generated and downloaded. ID: ${e.metadata?.id||"N/A"}`)}catch(e){throw console.error(`Failed to export ${c} report:`,e),k.JP.error("Export Failed",`Report was generated but ${c.toUpperCase()} export failed: ${e.message||"Unknown export error"}`),Error(`Report generated successfully but export failed: ${e.message||"Unknown export error"}`)}s?.(e)}catch(e){throw console.error("Failed to generate aggregate report:",e),e}},K=D.find(e=>e.id===t),q=Z?.filter(e=>e.entityTypes.includes(t))||[],Q=E[t]||[];return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Aggregate Report Generator"}),(0,l.jsx)("p",{className:"mt-1 text-gray-600",children:"Generate analytics reports with aggregated data and insights"})]}),(0,l.jsxs)(j.E,{className:"flex items-center gap-2",variant:"outline",children:[(0,l.jsx)(d.A,{className:"size-4"}),"Aggregate Report"]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,l.jsxs)("div",{className:"space-y-6 lg:col-span-2",children:[(0,l.jsxs)(v.Zp,{children:[(0,l.jsx)(v.aR,{children:(0,l.jsxs)(v.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(m.A,{className:"size-5"}),"Entity Type & Analytics"]})}),(0,l.jsx)(v.Wu,{className:"space-y-4",children:(0,l.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:D.map(e=>{let s=e.icon,r=t===e.id;return(0,l.jsx)("div",{className:`
                        cursor-pointer rounded-lg border p-4 transition-all
                        ${r?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}
                      `,onClick:()=>a(e.id),children:(0,l.jsxs)("div",{className:"flex items-start gap-3",children:[(0,l.jsx)(s,{className:"mt-1 size-6 text-gray-600"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,l.jsx)("span",{className:"font-medium",children:e.name}),(0,l.jsx)(j.E,{className:e.color,variant:"secondary",children:"Analytics"})]}),(0,l.jsx)("p",{className:"mb-3 text-sm text-gray-600",children:e.description}),(0,l.jsx)("div",{className:"space-y-1",children:e.metrics.map(e=>(0,l.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,l.jsx)("div",{className:"size-1 rounded-full bg-gray-400"}),e]},e))})]})]})},e.id)})})})]}),(0,l.jsxs)(v.Zp,{children:[(0,l.jsx)(v.aR,{children:(0,l.jsxs)(v.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"size-5"}),"Filters & Date Range"]})}),(0,l.jsxs)(v.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)(N.J,{className:"mb-2 flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"size-4"}),"Date Range"]}),(0,l.jsx)(C.U,{onChange:$,placeholder:"Select date range for analytics",value:T})]}),Q.length>0&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{className:"text-sm font-medium",children:"Entity Filters"}),(0,l.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:Q.map(e=>(0,l.jsxs)("div",{children:[(0,l.jsx)(N.J,{className:"mb-1 block text-sm",children:e.name}),"select"===e.type?(0,l.jsxs)(w.l6,{onValueChange:s=>{J(e.id,"all"===s?"":s)},value:P[e.id]||"all",children:[(0,l.jsx)(w.bq,{className:"h-8",children:(0,l.jsx)(w.yv,{placeholder:`Select ${e.name.toLowerCase()}`})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsxs)(w.eb,{value:"all",children:["All ",e.name,"s"]}),e.options?.map(e=>(0,l.jsx)(w.eb,{value:e,children:e},e))]})]}):(0,l.jsx)("input",{className:"h-8 w-full rounded-md border border-gray-300 px-3 text-sm",onChange:s=>J(e.id,s.target.value),placeholder:`Filter by ${e.name.toLowerCase()}`,type:"text",value:P[e.id]||""})]},e.id))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)(N.J,{className:"text-sm font-medium",children:"Report Options"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(f.S,{checked:z,id:"includeCharts",onCheckedChange:e=>F(!0===e)}),(0,l.jsx)(N.J,{className:"text-sm",htmlFor:"includeCharts",children:"Include Charts & Visualizations"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(f.S,{checked:V,id:"includeTrends",onCheckedChange:e=>I(!0===e)}),(0,l.jsx)(N.J,{className:"text-sm",htmlFor:"includeTrends",children:"Include Trend Analysis"})]})]})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(v.Zp,{children:[(0,l.jsx)(v.aR,{children:(0,l.jsx)(v.ZB,{children:"Report Configuration"})}),(0,l.jsxs)(v.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(N.J,{className:"mb-2 block",children:"Template"}),B?(0,l.jsx)(b.k,{}):(0,l.jsxs)(w.l6,{onValueChange:n,value:i,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{placeholder:"Select template"})}),(0,l.jsxs)(w.gC,{children:[(0,l.jsx)(w.eb,{value:"default",children:"Default Analytics"}),q.map(e=>(0,l.jsx)(w.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(N.J,{className:"mb-2 block",children:"Export Format"}),(0,l.jsxs)(w.l6,{onValueChange:o,value:c,children:[(0,l.jsx)(w.bq,{children:(0,l.jsx)(w.yv,{})}),(0,l.jsx)(w.gC,{children:R.map(e=>(0,l.jsx)(w.eb,{value:e.id,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:e.name}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]}),(0,l.jsxs)("div",{className:"space-y-2 rounded-lg bg-gray-50 p-3",children:[(0,l.jsx)("div",{className:"flex items-center gap-2",children:K&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(K.icon,{className:"size-4"}),(0,l.jsxs)("span",{className:"text-sm font-medium",children:[K.name," Analytics"]})]})}),T&&(0,l.jsxs)("p",{className:"text-xs text-gray-600",children:[T.from.toLocaleDateString()," -"," ",T.to.toLocaleDateString()]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(P).filter(([e,s])=>s).map(([e,s])=>(0,l.jsxs)(j.E,{className:"text-xs",variant:"outline",children:[e,": ",s]},e))})]})]})]}),(0,l.jsx)(v.Zp,{children:(0,l.jsxs)(v.Wu,{className:"pt-6",children:[L&&(0,l.jsx)(g.Fc,{className:"mb-4",variant:"destructive",children:(0,l.jsx)(g.TN,{children:L})}),(0,l.jsx)(y.$,{className:"w-full",disabled:W,onClick:H,size:"lg",children:W?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(b.k,{className:"mr-2 size-4"}),"Generating Analytics..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(p.A,{className:"mr-2 size-4"}),"Generate Aggregate Report"]})}),(0,l.jsx)("p",{className:"mt-2 text-center text-xs text-gray-500",children:"Report will include aggregated analytics and insights"})]})})]})]})]})};a()}catch(e){a(e)}})},45173:(e,s,t)=>{Promise.resolve().then(t.bind(t,53980))},53980:(e,s,t)=>{"use strict";t.d(s,{ReportingDashboard:()=>l});var a=t(12907);let l=(0,a.registerClientReference)(function(){throw Error("Attempted to call ReportingDashboard() from the server but ReportingDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\components\\features\\reporting\\dashboard\\ReportingDashboard.tsx","ReportingDashboard");(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\components\\\\features\\\\reporting\\\\dashboard\\\\ReportingDashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\components\\features\\reporting\\dashboard\\ReportingDashboard.tsx","default")},54501:(e,s,t)=>{"use strict";t.d(s,{_:()=>D});var a=t(60687),l=t(43210),r=t(44493),i=t(29523),n=t(56896),c=t(15079),o=t(80013),d=t(34729),m=t(89667),x=t(96834),h=t(35950),p=t(80489),u=t(36644),g=t(24920),j=t(48206),y=t(17612),v=t(58369),f=t(90586),N=t(26622),b=t(20620),w=t(12045),k=t(84547),C=t(79519),A=t(91821);let S=[{id:"delegations",name:"Delegations",icon:p.A,description:"Delegation assignments and status tracking",color:"bg-blue-100 text-blue-800"},{id:"tasks",name:"Tasks",icon:u.A,description:"Task completion and performance metrics",color:"bg-green-100 text-green-800"},{id:"vehicles",name:"Vehicles",icon:g.A,description:"Vehicle utilization and maintenance data",color:"bg-orange-100 text-orange-800"},{id:"employees",name:"Employees",icon:j.A,description:"Employee performance and workload analysis",color:"bg-purple-100 text-purple-800"}],T=[{id:"pdf",name:"PDF",description:"Formatted document for printing"},{id:"excel",name:"Excel",description:"Spreadsheet with multiple sheets"},{id:"csv",name:"CSV",description:"Comma-separated values for data analysis"}],D=()=>{let[e,s]=(0,l.useState)(["delegations"]),[t,p]=(0,l.useState)("comprehensive"),[g,j]=(0,l.useState)("pdf"),[D,R]=(0,l.useState)(null),[E,$]=(0,l.useState)({}),[P,M]=(0,l.useState)(""),[z,F]=(0,l.useState)(""),{generateComprehensiveReport:V,isGenerating:I,error:L}=(0,w.kv)(),{templates:O,isLoading:W}=(0,w.fD)(),B=e=>{s(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},Z=async()=>{if(0===e.length)return;let s={entityTypes:e,template:t,format:g,filters:{...E,...D&&{dateRange:{from:D.from.toISOString(),to:D.to.toISOString()}}},options:{name:P||`Report ${new Date().toLocaleDateString()}`,description:z,includeCharts:!0,includeSummary:!0}};try{await V(s)}catch(e){console.error("Failed to generate report:",e)}},U=Array.isArray(O)?O.find(e=>e.id===t):null;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Data Report Generator"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate comprehensive reports for delegations, tasks, vehicles, and employees"})]}),(0,a.jsxs)(x.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Report Builder"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),"Select Data Sources"]})}),(0,a.jsx)(r.Wu,{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:S.map(s=>{let t=s.icon,l=e.includes(s.id);return(0,a.jsx)("div",{className:`
                        border rounded-lg p-4 cursor-pointer transition-all
                        ${l?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}
                      `,onClick:()=>B(s.id),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(n.S,{checked:l,onCheckedChange:()=>B(s.id)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(t,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{className:"font-medium",children:s.name}),(0,a.jsx)(x.E,{className:s.color,variant:"secondary",children:s.id})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:s.description})]})]})},s.id)})})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Report Template"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[W?(0,a.jsx)(C.k,{}):(0,a.jsxs)(c.l6,{value:t,onValueChange:p,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select a template"})}),(0,a.jsx)(c.gC,{children:Array.isArray(O)&&O.map(e=>(0,a.jsx)(c.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]}),U&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:U.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:U.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:U.sections?.map(e=>(0,a.jsx)(x.E,{variant:"outline",className:"text-xs",children:e},e))})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Filters & Options"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(o.J,{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),"Date Range"]}),(0,a.jsx)(k.U,{value:D,onChange:R,placeholder:"Select date range for data"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(c.l6,{value:g,onValueChange:j,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{})}),(0,a.jsx)(c.gC,{children:T.map(e=>(0,a.jsx)(c.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"reportName",className:"mb-2 block",children:"Report Name"}),(0,a.jsx)(m.p,{id:"reportName",value:P,onChange:e=>M(e.target.value),placeholder:"Enter custom report name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"reportDescription",className:"mb-2 block",children:"Description"}),(0,a.jsx)(d.T,{id:"reportDescription",value:z,onChange:e=>F(e.target.value),placeholder:"Optional description for the report",rows:3})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Report Summary"})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Selected Entities"}),(0,a.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:e.map(e=>{let s=S.find(s=>s.id===e);return s?(0,a.jsx)(x.E,{className:s.color,variant:"secondary",children:s.name},e):null})})]}),(0,a.jsx)(h.w,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Template"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:U?.name||"None selected"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Format"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:T.find(e=>e.id===g)?.name})]}),D&&(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Date Range"}),(0,a.jsxs)("p",{className:"mt-1 text-sm",children:[D.from.toLocaleDateString()," -"," ",D.to.toLocaleDateString()]})]})]})]}),(0,a.jsx)(r.Zp,{children:(0,a.jsxs)(r.Wu,{className:"pt-6",children:[L&&(0,a.jsx)(A.Fc,{className:"mb-4",variant:"destructive",children:(0,a.jsx)(A.TN,{children:L})}),(0,a.jsx)(i.$,{onClick:Z,disabled:0===e.length||I,className:"w-full",size:"lg",children:I?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(C.k,{className:"mr-2 h-4 w-4"}),"Generating Report..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Generate Report"]})}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:"Report will be generated and available for download"})]})})]})]})]})}},54567:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{c:()=>k});var l=t(60687),r=t(43210),i=t(44493),n=t(85763),c=t(96834),o=t(29523),d=t(36644),m=t(58595),x=t(27629),h=t(44610),p=t(20620),u=t(58369),g=t(99196),j=t(54501),y=t(66958),v=t(42024),f=t(65602),N=t(91821),b=e([v]);v=(b.then?(await b)():b)[0];let w=[{id:"comprehensive",label:"Comprehensive Reports",icon:d.A,description:"Generate reports across multiple entity types",component:j._},{id:"individual",label:"Individual Reports",icon:m.A,description:"Generate detailed reports for specific entities",component:y.l},{id:"aggregate",label:"Aggregate Analytics",icon:x.A,description:"Generate analytics reports with aggregated data",component:v.D},{id:"history",label:"Report History",icon:h.A,description:"View and manage generated reports",component:f.r}],k=()=>{let[e,s]=(0,r.useState)("comprehensive"),[t,a]=(0,r.useState)([]),j=e=>{console.log("Report generated:",e);let s=e?.data?.metadata||e?.metadata;if(!s){console.error("No metadata found in report result:",e),alert("Report generated, but metadata is missing");return}let t={...e,metadata:s};a(e=>[t,...e.slice(0,4)]),alert(`Report generated successfully! ID: ${s.id}`)},y=e=>{console.log("Report selected:",e)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Data Report Generation"}),(0,l.jsx)("p",{className:"text-gray-600 mt-2",children:"Generate comprehensive reports for delegations, tasks, vehicles, and employees"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)(c.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,l.jsx)(p.A,{className:"h-4 w-4"}),"Report Builder"]}),(0,l.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Settings"]})]})]}),t.length>0&&(0,l.jsxs)(i.Zp,{children:[(0,l.jsx)(i.aR,{children:(0,l.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,l.jsx)(g.A,{className:"h-5 w-5"}),"Recent Activity"]})}),(0,l.jsx)(i.Wu,{children:(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:t.map((e,s)=>{let t=e?.metadata;return t?(0,l.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)(c.E,{variant:"outline",className:"text-xs",children:t.type||"Unknown"}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:t.generatedAt?new Date(t.generatedAt).toLocaleTimeString():"Unknown time"})]}),(0,l.jsx)("p",{className:"text-sm font-medium truncate",children:t.id||"No ID"}),(0,l.jsx)("p",{className:"text-xs text-gray-600",children:t.entityTypes?.join(", ")||t.entityType||"Unknown entity"})]},s):null})})})]}),(0,l.jsxs)(N.Fc,{children:[(0,l.jsx)(g.A,{className:"h-4 w-4"}),(0,l.jsxs)(N.TN,{children:[(0,l.jsx)("strong",{children:"Report Generation System:"})," Generate individual entity reports, aggregate analytics, or comprehensive reports across multiple data sources. All reports support PDF, Excel, and CSV export formats."]})]}),(0,l.jsxs)(n.tU,{value:e,onValueChange:s,className:"space-y-6",children:[(0,l.jsx)(i.Zp,{children:(0,l.jsxs)(i.Wu,{className:"pt-6",children:[(0,l.jsx)(n.j7,{className:"grid w-full grid-cols-4",children:w.map(e=>{let s=e.icon;return(0,l.jsxs)(n.Xi,{value:e.id,className:"flex items-center gap-2 data-[state=active]:bg-blue-50",children:[(0,l.jsx)(s,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id)})}),(0,l.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:w.map(s=>{if(s.id!==e)return null;let t=s.icon;return(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)(t,{className:"h-5 w-5 text-gray-600"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-gray-900",children:s.label}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:s.description})]})]},s.id)})})]})}),w.map(e=>{let s=e.component;return(0,l.jsx)(n.av,{value:e.id,className:"space-y-6",children:(0,l.jsx)(s,{onReportGenerated:j,onReportSelect:y})},e.id)})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsx)(i.aR,{children:(0,l.jsx)(i.ZB,{children:"Report Generation Guide"})}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(d.A,{className:"h-4 w-4 text-blue-600"}),(0,l.jsx)("span",{className:"font-medium",children:"Comprehensive"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Generate reports that include data from multiple entity types with cross-entity analytics."})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(m.A,{className:"h-4 w-4 text-green-600"}),(0,l.jsx)("span",{className:"font-medium",children:"Individual"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Create detailed reports for specific delegations, tasks, vehicles, or employees."})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4 text-orange-600"}),(0,l.jsx)("span",{className:"font-medium",children:"Aggregate"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Generate analytics reports with aggregated metrics and trend analysis."})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(h.A,{className:"h-4 w-4 text-purple-600"}),(0,l.jsx)("span",{className:"font-medium",children:"History"})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"View, download, and manage all your previously generated reports."})]})]})})]})]})};a()}catch(e){a(e)}})},65602:(e,s,t)=>{"use strict";t.d(s,{r:()=>A});var a=t(60687),l=t(1950),r=t(77368),i=t(44610),n=t(90586),c=t(41936),o=t(36644),d=t(26622),m=t(76311),x=t(20620),h=t(43210),p=t(91821),u=t(96834),g=t(29523),j=t(44493),y=t(89667),v=t(79519),f=t(15079),N=t(12045);let b=[{label:"All Types",value:""},{label:"Comprehensive",value:"comprehensive"},{label:"Individual",value:"individual"},{label:"Aggregate",value:"aggregate"}],w=[{label:"All Entities",value:""},{label:"Delegations",value:"delegations"},{label:"Tasks",value:"tasks"},{label:"Vehicles",value:"vehicles"},{label:"Employees",value:"employees"}],k={completed:{color:"bg-green-100 text-green-800",label:"Completed"},failed:{color:"bg-red-100 text-red-800",label:"Failed"},processing:{color:"bg-yellow-100 text-yellow-800",label:"Processing"}},C={csv:{color:"bg-blue-100 text-blue-800",label:"CSV"},excel:{color:"bg-green-100 text-green-800",label:"Excel"},pdf:{color:"bg-red-100 text-red-800",label:"PDF"}},A=({onReportSelect:e})=>{let[s,t]=(0,h.useState)(""),[A,S]=(0,h.useState)(""),[T,D]=(0,h.useState)(""),{error:R,isLoading:E,pagination:$,refetch:P,reports:M}=(0,N.e5)({...s&&{type:s},...A&&{entityType:A}}),{downloadError:z,downloadReport:F,isDownloading:V}=(0,N.W3)(),I=async e=>{try{await F(e)}catch(e){console.error("Failed to download report:",e)}},L=M.filter(e=>{if(!T)return!0;let s=T.toLowerCase();return e.id.toLowerCase().includes(s)||e.type.toLowerCase().includes(s)||e.entityType?.toLowerCase().includes(s)||e.entityTypes?.some(e=>e.toLowerCase().includes(s))}),O=e=>"individual"===e.type||"aggregate"===e.type?e.entityType:e.entityTypes?e.entityTypes.join(", "):"N/A";return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Report History"}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:"View and manage your generated reports"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(g.$,{disabled:E,onClick:()=>P(),size:"sm",variant:"outline",children:[(0,a.jsx)(r.A,{className:`mr-2 size-4 ${E?"animate-spin":""}`}),"Refresh"]}),(0,a.jsxs)(u.E,{className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(i.A,{className:"size-4"}),M.length," Reports"]})]})]}),(0,a.jsxs)(j.Zp,{children:[(0,a.jsx)(j.aR,{children:(0,a.jsxs)(j.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"size-5"}),"Filters"]})}),(0,a.jsx)(j.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400"}),(0,a.jsx)(y.p,{className:"pl-10",onChange:e=>D(e.target.value),placeholder:"Search reports...",value:T})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(f.l6,{onValueChange:t,value:s,children:[(0,a.jsx)(f.bq,{children:(0,a.jsx)(f.yv,{placeholder:"Report Type"})}),(0,a.jsx)(f.gC,{children:b.map(e=>(0,a.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(f.l6,{onValueChange:S,value:A,children:[(0,a.jsx)(f.bq,{children:(0,a.jsx)(f.yv,{placeholder:"Entity Type"})}),(0,a.jsx)(f.gC,{children:w.map(e=>(0,a.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)("div",{children:(0,a.jsx)(g.$,{className:"w-full",onClick:()=>{t(""),S(""),D("")},variant:"outline",children:"Clear Filters"})})]})})]}),(R||z)&&(0,a.jsx)(p.Fc,{variant:"destructive",children:(0,a.jsx)(p.TN,{children:String(R||z)})}),(0,a.jsxs)(j.Zp,{children:[(0,a.jsx)(j.aR,{children:(0,a.jsx)(j.ZB,{children:"Generated Reports"})}),(0,a.jsx)(j.Wu,{children:E?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(v.k,{className:"size-8"})}):0===L.length?(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(o.A,{className:"mx-auto mb-4 size-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Reports Found"}),(0,a.jsx)("p",{className:"text-gray-600",children:0===M.length?"No reports have been generated yet.":"No reports match your current filters."})]}):(0,a.jsx)("div",{className:"space-y-4",children:L.map(s=>{let t=k[s.status],r=C[s.format];return(0,a.jsx)("div",{className:"rounded-lg border p-4 transition-colors hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:s.id}),(0,a.jsx)(u.E,{className:t?.color,variant:"secondary",children:t?.label||s.status}),(0,a.jsx)(u.E,{className:r?.color,variant:"secondary",children:r?.label||s.format}),(0,a.jsx)(u.E,{variant:"outline",children:s.type})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm text-gray-600 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Entity Types:"}),(0,a.jsx)("p",{className:"mt-1",children:O(s)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Generated:"}),(0,a.jsxs)("p",{className:"mt-1 flex items-center gap-1",children:[(0,a.jsx)(d.A,{className:"size-3"}),(0,l.m)(new Date(s.generatedAt),{addSuffix:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"File Size:"}),(0,a.jsx)("p",{className:"mt-1",children:s.fileSize||"N/A"})]})]}),"individual"===s.type&&s.entityId&&(0,a.jsxs)("div",{className:"mt-2 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"font-medium",children:"Entity ID:"})," ",s.entityId]})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center gap-2",children:[e&&(0,a.jsxs)(g.$,{onClick:()=>e(s),size:"sm",variant:"outline",children:[(0,a.jsx)(m.A,{className:"mr-1 size-4"}),"View"]}),"completed"===s.status&&(0,a.jsxs)(g.$,{disabled:V,onClick:()=>I(s.id),size:"sm",variant:"outline",children:[V?(0,a.jsx)(v.k,{className:"mr-1 size-4"}):(0,a.jsx)(x.A,{className:"mr-1 size-4"}),"Download"]})]})]})},s.id)})})})]}),$&&$.totalPages>1&&(0,a.jsx)(j.Zp,{children:(0,a.jsx)(j.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",($.page-1)*$.limit+1," to"," ",Math.min($.page*$.limit,$.total)," ","of ",$.total," reports"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.$,{disabled:$.page<=1,size:"sm",variant:"outline",children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",$.page," of ",$.totalPages]}),(0,a.jsx)(g.$,{disabled:$.page>=$.totalPages,size:"sm",variant:"outline",children:"Next"})]})]})})})]})}},66958:(e,s,t)=>{"use strict";t.d(s,{l:()=>w});var a=t(60687),l=t(43210),r=t(44493),i=t(29523),n=t(15079),c=t(89667),o=t(80013),d=t(96834),m=t(80489),x=t(36644),h=t(24920),p=t(48206),u=t(58595),g=t(41936),j=t(20620),y=t(12045),v=t(79519),f=t(91821);let N=[{id:"delegations",name:"Delegation",icon:m.A,placeholder:"Enter delegation ID",description:"Generate detailed report for a specific delegation"},{id:"tasks",name:"Task",icon:x.A,placeholder:"Enter task ID",description:"Generate detailed report for a specific task"},{id:"vehicles",name:"Vehicle",icon:h.A,placeholder:"Enter vehicle ID or license plate",description:"Generate detailed report for a specific vehicle"},{id:"employees",name:"Employee",icon:p.A,placeholder:"Enter employee ID or email",description:"Generate detailed report for a specific employee"}],b=[{id:"pdf",name:"PDF",description:"Formatted document for printing"},{id:"excel",name:"Excel",description:"Spreadsheet format"},{id:"csv",name:"CSV",description:"Data export format"}],w=({defaultEntityType:e="delegations",defaultEntityId:s="",onReportGenerated:t})=>{let[m,x]=(0,l.useState)(e),[h,p]=(0,l.useState)(s),[w,k]=(0,l.useState)("default"),[C,A]=(0,l.useState)("pdf"),{generateIndividualReport:S,isGenerating:T,error:D}=(0,y.kv)(),{templates:R,isLoading:E}=(0,y.fD)(),$=async()=>{if(h.trim())try{let e=await S({entityType:m,entityId:h.trim(),template:w,format:C});t?.(e)}catch(e){console.error("Failed to generate individual report:",e)}},P=N.find(e=>e.id===m),M=R?.filter(e=>e.entityTypes.includes(m))||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Individual Report Generator"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate detailed reports for specific entities"})]}),(0,a.jsxs)(d.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Individual Report"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),"Entity Selection"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"mb-2 block",children:"Entity Type"}),(0,a.jsxs)(n.l6,{value:m,onValueChange:x,children:[(0,a.jsx)(n.bq,{children:(0,a.jsx)(n.yv,{})}),(0,a.jsx)(n.gC,{children:N.map(e=>{let s=e.icon;return(0,a.jsx)(n.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})]})},e.id)})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(o.J,{htmlFor:"entityId",className:"mb-2 block",children:[P?.name," ID"]}),(0,a.jsx)(c.p,{id:"entityId",value:h,onChange:e=>p(e.target.value),placeholder:P?.placeholder,className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:P?.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"mb-2 block",children:"Report Template"}),E?(0,a.jsx)(v.k,{}):(0,a.jsxs)(n.l6,{value:w,onValueChange:k,children:[(0,a.jsx)(n.bq,{children:(0,a.jsx)(n.yv,{placeholder:"Select a template"})}),(0,a.jsxs)(n.gC,{children:[(0,a.jsx)(n.eb,{value:"default",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Default Template"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Standard individual report format"})]})}),M.map(e=>(0,a.jsx)(n.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(n.l6,{value:C,onValueChange:A,children:[(0,a.jsx)(n.bq,{children:(0,a.jsx)(n.yv,{})}),(0,a.jsx)(n.gC,{children:b.map(e=>(0,a.jsx)(n.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Report Preview"})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Entity Type"}),(0,a.jsx)("div",{className:"flex items-center gap-2 mt-1",children:P&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(P.icon,{className:"h-4 w-4 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm",children:P.name})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Entity ID"}),(0,a.jsx)("p",{className:"mt-1 text-sm font-mono bg-white px-2 py-1 rounded border",children:h||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Template"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:M.find(e=>e.id===w)?.name||"Default Template"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{className:"text-sm font-medium text-gray-600",children:"Format"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:b.find(e=>e.id===C)?.name})]})]}),D&&(0,a.jsx)(f.Fc,{variant:"destructive",children:(0,a.jsx)(f.TN,{children:D})}),(0,a.jsx)(i.$,{onClick:$,disabled:!h.trim()||T,className:"w-full",size:"lg",children:T?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.k,{className:"mr-2 h-4 w-4"}),"Generating Report..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Generate Individual Report"]})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 text-center",children:["Report will include detailed information about the selected"," ",P?.name.toLowerCase()]})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Quick Actions"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:N.map(e=>{let s=e.icon;return(0,a.jsxs)(i.$,{variant:"outline",className:"h-auto p-4 flex flex-col items-center gap-2",onClick:()=>{x(e.id),p("")},children:[(0,a.jsx)(s,{className:"h-6 w-6"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.name," Report"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Generate ",e.name.toLowerCase()," report"]})]})]},e.id)})})})]})]})}},71669:(e,s,t)=>{"use strict";t.d(s,{C5:()=>y,MJ:()=>g,Rr:()=>j,eI:()=>p,lR:()=>u,lV:()=>o,zB:()=>m});var a=t(60687),l=t(43210),r=t(8730),i=t(27605),n=t(22482),c=t(80013);let o=i.Op,d=l.createContext({}),m=({...e})=>(0,a.jsx)(d.Provider,{value:{name:e.name},children:(0,a.jsx)(i.xI,{...e})}),x=()=>{let e=l.useContext(d),s=l.useContext(h),{getFieldState:t,formState:a}=(0,i.xW)(),r=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...r}},h=l.createContext({}),p=l.forwardRef(({className:e,...s},t)=>{let r=l.useId();return(0,a.jsx)(h.Provider,{value:{id:r},children:(0,a.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",e),...s})})});p.displayName="FormItem";let u=l.forwardRef(({className:e,...s},t)=>{let{error:l,formItemId:r}=x();return(0,a.jsx)(c.J,{ref:t,className:(0,n.cn)(l&&"text-destructive",e),htmlFor:r,...s})});u.displayName="FormLabel";let g=l.forwardRef(({...e},s)=>{let{error:t,formItemId:l,formDescriptionId:i,formMessageId:n}=x();return(0,a.jsx)(r.DX,{ref:s,id:l,"aria-describedby":t?`${i} ${n}`:`${i}`,"aria-invalid":!!t,...e})});g.displayName="FormControl";let j=l.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:l}=x();return(0,a.jsx)("p",{ref:t,id:l,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})});j.displayName="FormDescription";let y=l.forwardRef(({className:e,children:s,...t},l)=>{let{error:r,formMessageId:i}=x(),c=r?String(r?.message??""):s;return c?(0,a.jsx)("p",{ref:l,id:i,className:(0,n.cn)("text-sm font-medium text-destructive",e),...t,children:c}):null});y.displayName="FormMessage"},73227:(e,s,t)=>{"use strict";t.d(s,{ZY:()=>f,AK:()=>b,b7:()=>y,xo:()=>v,si:()=>j,K:()=>N});var a=t(93425),l=t(8693),r=t(54050),i=t(43210),n=t(46349),c=t(49603),o=t(83144),d=t(57930);let m={all:["tasks"],detail:e=>["tasks",e]},x=e=>({enabled:!!e,queryFn:()=>c.taskApiService.getById(e),queryKey:m.detail(e),staleTime:3e5}),h=()=>({queryFn:()=>c.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),p=()=>({queryFn:()=>c.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),u=e=>[x(e),h(),p()];var g=t(38118);let j=e=>(0,n.GK)([...m.all],async()=>(await c.taskApiService.getAll()).data,"task",{staleTime:0,...e}),y=e=>(0,n.GK)([...m.detail(e)],async()=>await c.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),v=e=>{let[s,t,l]=(0,a.E)({queries:u(e)}),r=(0,i.useMemo)(()=>{if(s?.data&&t?.data&&l?.data)try{let e=d.J.fromApi(s.data),a=Array.isArray(t.data)?t.data:[],r=Array.isArray(l.data)?l.data:[];return(0,o.R)(e,a,r)}catch(e){throw console.error("Error enriching task data:",e),e}},[s?.data,t?.data,l?.data]),n=(0,i.useCallback)(()=>{s?.refetch(),t?.refetch(),l?.refetch()},[s?.refetch,t?.refetch,l?.refetch]);return{data:r,error:s?.error||t?.error||l?.error,isError:s?.isError||t?.isError||l?.isError,isLoading:s?.isLoading||t?.isLoading||l?.isLoading,isPending:s?.isPending||t?.isPending||l?.isPending,refetch:n}},f=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let s=d.J.toCreateRequest(e);return await c.taskApiService.create(s)},onError:(s,t,a)=>{a?.previousTasks&&e.setQueryData(m.all,a.previousTasks),console.error("Failed to create task:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:m.all});let t=e.getQueryData(m.all);return e.setQueryData(m.all,(e=[])=>{let t="optimistic-"+Date.now().toString(),a=new Date().toISOString();return[...e,{createdAt:a,dateTime:s.dateTime??null,deadline:s.deadline??null,description:s.description,driverEmployee:null,driverEmployeeId:s.driverEmployeeId??null,estimatedDuration:s.estimatedDuration??null,id:t,location:s.location??null,notes:s.notes??null,priority:s.priority,requiredSkills:s.requiredSkills??null,staffEmployee:null,staffEmployeeId:s.staffEmployeeId??null,status:s.status||"Pending",subtasks:s.subtasks?.map(e=>({completed:e.completed||!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:t,title:e.title}))||[],updatedAt:a,vehicle:null,vehicleId:s.vehicleId??null}]}),{previousTasks:t}},onSettled:()=>{e.invalidateQueries({queryKey:m.all})}})},N=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async({data:e,id:s})=>{let t=d.J.toUpdateRequest(e);return await c.taskApiService.update(s,t)},onError:(s,t,a)=>{a?.previousTask&&e.setQueryData(m.detail(t.id),a.previousTask),a?.previousTasksList&&e.setQueryData(m.all,a.previousTasksList),console.error("Failed to update task:",s)},onMutate:async({data:s,id:t})=>{await e.cancelQueries({queryKey:m.all}),await e.cancelQueries({queryKey:m.detail(t)});let a=e.getQueryData(m.detail(t)),l=e.getQueryData(m.all);return e.setQueryData(m.detail(t),e=>{if(!e)return e;let a=new Date().toISOString();return{...e,dateTime:void 0!==s.dateTime?s.dateTime:e.dateTime,deadline:(0,g.d$)(void 0!==s.deadline?s.deadline:e.deadline),description:s.description??e.description,driverEmployeeId:(0,g.d$)(void 0!==s.driverEmployeeId?s.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==s.estimatedDuration?s.estimatedDuration:e.estimatedDuration,location:void 0!==s.location?s.location:e.location,notes:(0,g.d$)(void 0!==s.notes?s.notes:e.notes),priority:s.priority??e.priority,requiredSkills:void 0!==s.requiredSkills?s.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==s.staffEmployeeId?s.staffEmployeeId:e.staffEmployeeId,status:s.status??e.status,subtasks:s.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:t,title:e.title}))||e.subtasks||[],updatedAt:a,vehicleId:(0,g.d$)(void 0!==s.vehicleId?s.vehicleId:e.vehicleId)}}),e.setQueryData(m.all,(e=[])=>e.map(e=>{if(e.id===t){let a=new Date().toISOString(),l=s.subtasks?.map(e=>({completed:e.completed??!1,id:`optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2,7)}`,taskId:t,title:e.title}))||e.subtasks||[];return{...e,dateTime:void 0!==s.dateTime?s.dateTime:e.dateTime,deadline:(0,g.d$)(void 0!==s.deadline?s.deadline:e.deadline),description:s.description??e.description,driverEmployeeId:(0,g.d$)(void 0!==s.driverEmployeeId?s.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==s.estimatedDuration?s.estimatedDuration:e.estimatedDuration,location:void 0!==s.location?s.location:e.location,notes:(0,g.d$)(void 0!==s.notes?s.notes:e.notes),priority:s.priority??e.priority,requiredSkills:void 0!==s.requiredSkills?s.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==s.staffEmployeeId?s.staffEmployeeId:e.staffEmployeeId,status:s.status??e.status,subtasks:l,updatedAt:a,vehicleId:(0,g.d$)(void 0!==s.vehicleId?s.vehicleId:e.vehicleId)}}return e})),{previousTask:a,previousTasksList:l}},onSettled:(s,t,a)=>{e.invalidateQueries({queryKey:m.detail(a.id)}),e.invalidateQueries({queryKey:m.all})}})},b=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>(await c.taskApiService.delete(e),e),onError:(s,t,a)=>{a?.previousTasksList&&e.setQueryData(m.all,a.previousTasksList),console.error("Failed to delete task:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:m.all}),await e.cancelQueries({queryKey:m.detail(s)});let t=e.getQueryData(m.all);return e.setQueryData(m.all,(e=[])=>e.filter(e=>e.id!==s)),e.removeQueries({queryKey:m.detail(s)}),{previousTasksList:t}},onSettled:()=>{e.invalidateQueries({queryKey:m.all})}})}},79519:(e,s,t)=>{"use strict";t.d(s,{k:()=>r});var a=t(60687);t(43210);var l=t(22482);let r=({className:e,size:s="md"})=>(0,a.jsx)("div",{className:(0,l.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[s],e)})},83144:(e,s,t)=>{"use strict";t.d(s,{R:()=>l});class a{static enrich(e,s,t){let{employeeMap:a,vehicleMap:l}=this.createLookupMaps(s,t),r=this.enrichStaffEmployee(e,a);return r=this.enrichDriverEmployee(r,a),r=this.enrichVehicle(r,l)}static createLookupMaps(e,s){let t=Array.isArray(e)?e:[],a=Array.isArray(s)?s:[];return{employeeMap:new Map(t.map(e=>[e.id,e])),vehicleMap:new Map(a.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,s){if(!e.driverEmployeeId)return e;let t=e.driverEmployee??s.get(e.driverEmployeeId)??null;return{...e,driverEmployee:t}}static enrichStaffEmployee(e,s){if(!e.staffEmployeeId)return e;let t=e.staffEmployee??s.get(e.staffEmployeeId)??null;return{...e,staffEmployee:t}}static enrichVehicle(e,s){if(!e.vehicleId)return e;let t=e.vehicle??s.get(e.vehicleId)??null;return{...e,vehicle:t}}}let l=(e,s,t)=>a.enrich(e,s,t)},83559:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{W:()=>d});var l=t(60687),r=t(43210),i=t.n(r),n=t(972),c=e([n]);let o=(n=(c.then?(await c)():c)[0]).StyleSheet.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#7c3aed",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#faf5ff",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#faf5ff",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),d=({data:e,reportTitle:s,metadata:t})=>{let a=i().useMemo(()=>{if(!e)return{};let s=e?.data||e;return s&&"object"==typeof s?{totalCount:s.totalCount||0,activeCount:s.activeCount||0,onLeaveCount:s.onLeaveCount||0,averagePerformanceScore:s.averagePerformanceScore||0,satisfactionRate:s.satisfactionRate||0,performanceMetrics:s.performanceMetrics||{},departmentDistribution:Array.isArray(s.departmentDistribution)?s.departmentDistribution:[],taskAssignments:s.taskAssignments||{},workloadDistribution:Array.isArray(s.workloadDistribution)?s.workloadDistribution:[],availabilityMetrics:s.availabilityMetrics||{},...s}:{}},[e]);return(0,l.jsx)(n.Document,{children:(0,l.jsxs)(n.Page,{size:"A4",style:o.page,children:[(0,l.jsx)(n.Text,{style:o.header,children:s||"Employee Report"}),(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Employee Summary"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Total Employees:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.totalCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Active Employees:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.activeCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"On Leave:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.onLeaveCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Performance Score:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.averagePerformanceScore?.toFixed(2)||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Employee Satisfaction Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.satisfactionRate?.toFixed(2)||0,"%"]})]})]}),a.performanceMetrics&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Performance Metrics"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"High Performers:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.performanceMetrics.highPerformers||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Performers:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.performanceMetrics.averagePerformers||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Low Performers:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.performanceMetrics.lowPerformers||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Performance Improvement Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.performanceMetrics.improvementRate?.toFixed(2)||0,"%"]})]})]}),a.departmentDistribution&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Department Distribution"}),(0,l.jsxs)(n.View,{style:o.table,children:[(0,l.jsxs)(n.View,{style:o.tableHeader,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:"Department"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Count"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Percentage"})]}),a.departmentDistribution.map((e,s)=>(0,l.jsxs)(n.View,{style:o.tableRow,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.department||e?._id||"Unknown"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.count||e?._count?.department||0}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.percentage?`${Number(e.percentage).toFixed(1)}%`:"N/A"})]},`dept-${s}`))]})]}),a.taskAssignments&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Task Assignment Metrics"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Total Tasks Assigned:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.taskAssignments.totalAssigned||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Completed Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.taskAssignments.completed||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Pending Tasks:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.taskAssignments.pending||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Task Completion Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.taskAssignments.completionRate?.toFixed(2)||0,"%"]})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Tasks per Employee:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.taskAssignments.averagePerEmployee?.toFixed(1)||0})]})]}),a.workloadDistribution&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Workload Distribution"}),(0,l.jsxs)(n.View,{style:o.table,children:[(0,l.jsxs)(n.View,{style:o.tableHeader,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:"Workload Level"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Employee Count"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Percentage"})]}),a.workloadDistribution.map((e,s)=>(0,l.jsxs)(n.View,{style:o.tableRow,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.level||e?._id||"Unknown"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.count||e?._count?.level||0}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.percentage?`${Number(e.percentage).toFixed(1)}%`:"N/A"})]},`workload-${s}`))]})]}),a.availabilityMetrics&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Availability Metrics"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Available Employees:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.availabilityMetrics.available||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"On Assignment:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.availabilityMetrics.onAssignment||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"On Leave:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.availabilityMetrics.onLeave||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Availability Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.availabilityMetrics.availabilityRate?.toFixed(2)||0,"%"]})]})]}),t&&(0,l.jsxs)(n.View,{style:o.metadata,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Report Information"}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Report ID: ",t.id]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Type: ",t.type]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Entity Type: ",t.entityType]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Generated: ",new Date(t.generatedAt).toLocaleString()]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Generated By: ",t.generatedBy]})]})]})})};a()}catch(e){a(e)}})},84122:(e,s,t)=>{"use strict";t.d(s,{FR:()=>g,Kx:()=>j,N8:()=>p,o1:()=>u,v4:()=>y});var a=t(26787),l=t(59350),r=t(27127);let i=()=>{let e=new Date;return{costRange:{max:1e4,min:0},dateRange:{from:new Date(e.getTime()-2592e6),to:e},employees:[],includeServiceHistory:!1,includeTaskData:!1,locations:[],serviceStatus:[],serviceTypes:[],status:[],vehicles:[]}};new Date().getTime();let n=e=>{let s={};return e.dateRange&&(e.dateRange.from>e.dateRange.to&&(s.dateRange="Start date must be before end date"),Math.abs(e.dateRange.to.getTime()-e.dateRange.from.getTime())/864e5>365&&(s.dateRange="Date range cannot exceed 365 days")),e.status.length>10&&(s.status="Too many statuses selected (maximum 10)"),e.locations.length>50&&(s.locations="Too many locations selected (maximum 50)"),e.employees.length>100&&(s.employees="Too many employees selected (maximum 100)"),e.vehicles.length>100&&(s.vehicles="Too many vehicles selected (maximum 100)"),e.serviceTypes&&e.serviceTypes.length>20&&(s.serviceTypes="Too many service types selected (maximum 20)"),e.serviceStatus&&e.serviceStatus.length>10&&(s.serviceStatus="Too many service statuses selected (maximum 10)"),e.costRange&&(e.costRange.min<0&&(s.costRange="Minimum cost cannot be negative"),e.costRange.min>=e.costRange.max&&(s.costRange="Minimum cost must be less than maximum cost"),e.costRange.max>1e6&&(s.costRange="Maximum cost cannot exceed $1,000,000")),s},c=(0,a.v)()((0,l.lt)((0,l.eh)((0,l.Zr)((e,s)=>({applyFilters:()=>{let{filters:t,isValid:a}=s();a&&e({hasUnsavedChanges:!1,lastAppliedFilters:{...t}})},applyPreset:s=>{try{let t=localStorage.getItem("reporting-filter-presets"),a=(t?JSON.parse(t):{})[s];a&&e(e=>({filters:{...a},hasUnsavedChanges:!0,lastAppliedFilters:e.lastAppliedFilters}))}catch(e){console.error("Failed to apply preset:",e)}},clearValidationErrors:()=>{e({isValid:!0,validationErrors:{}})},deletePreset:e=>{try{let s=localStorage.getItem("reporting-filter-presets"),t=s?JSON.parse(s):{};delete t[e],localStorage.setItem("reporting-filter-presets",JSON.stringify(t))}catch(e){console.error("Failed to delete preset:",e)}},filters:i(),getPresets:()=>{try{let e=localStorage.getItem("reporting-filter-presets");return e?JSON.parse(e):{}}catch{return{}}},hasUnsavedChanges:!1,isFilterPanelOpen:!1,isValid:!0,lastAppliedFilters:i(),resetFilters:()=>{e({filters:i(),hasUnsavedChanges:!0,isValid:!0,validationErrors:{}})},revertChanges:()=>{let{lastAppliedFilters:t}=s();e({filters:{...t},hasUnsavedChanges:!1,isValid:!0,validationErrors:{}})},saveAsPreset:e=>{try{let{filters:t}=s(),a=localStorage.getItem("reporting-filter-presets"),l=a?JSON.parse(a):{};l[e]={...t},localStorage.setItem("reporting-filter-presets",JSON.stringify(l))}catch(e){console.error("Failed to save preset:",e)}},setCostRange:(s,t)=>{e(e=>{let a={...e.filters,costRange:{max:t,min:s}},l=n(a);return{filters:a,hasUnsavedChanges:!0,isValid:0===Object.keys(l).length,validationErrors:l}})},setDateRange:(s,t)=>{e(e=>{let a={...e.filters,dateRange:{from:s,to:t}},l=n(a);return{filters:a,hasUnsavedChanges:!0,isValid:0===Object.keys(l).length,validationErrors:l}})},setEmployees:s=>{e(e=>{let t={...e.filters,employees:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setFilterPanelOpen:s=>{e({isFilterPanelOpen:s})},setFilters:s=>{e(e=>{let t={...e.filters,...s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setIncludeServiceHistory:s=>{e(e=>{let t={...e.filters,includeServiceHistory:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setIncludeTaskData:s=>{e(e=>{let t={...e.filters,includeTaskData:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setLocations:s=>{e(e=>{let t={...e.filters,locations:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setServiceStatus:s=>{e(e=>{let t={...e.filters,serviceStatus:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setServiceTypes:s=>{e(e=>{let t={...e.filters,serviceTypes:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setStatus:s=>{e(e=>{let t={...e.filters,status:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setTaskPriorities:s=>{e(e=>{let t={...e.filters,taskPriorities:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setTaskStatus:s=>{e(e=>{let t={...e.filters,taskStatus:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setVehicles:s=>{e(e=>{let t={...e.filters,vehicles:s},a=n(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},toggleFilterPanel:()=>{e(e=>({isFilterPanelOpen:!e.isFilterPanelOpen}))},validateFilters:()=>{let{filters:t}=s(),a=n(t),l=0===Object.keys(a).length;return e({isValid:l,validationErrors:a}),l},validationErrors:{}}),{name:"reporting-filters-storage",partialize:e=>({filters:e.filters,lastAppliedFilters:e.lastAppliedFilters}),storage:(0,l.KU)(()=>localStorage)})),{name:"reporting-filters-store"})),o=e=>e.filters,d=e=>({applyFilters:e.applyFilters,resetFilters:e.resetFilters,revertChanges:e.revertChanges,setCostRange:e.setCostRange,setDateRange:e.setDateRange,setEmployees:e.setEmployees,setFilters:e.setFilters,setIncludeServiceHistory:e.setIncludeServiceHistory,setIncludeTaskData:e.setIncludeTaskData,setLocations:e.setLocations,setServiceStatus:e.setServiceStatus,setServiceTypes:e.setServiceTypes,setStatus:e.setStatus,setTaskPriorities:e.setTaskPriorities,setTaskStatus:e.setTaskStatus,setVehicles:e.setVehicles}),m=e=>({hasUnsavedChanges:e.hasUnsavedChanges,isFilterPanelOpen:e.isFilterPanelOpen,setFilterPanelOpen:e.setFilterPanelOpen,toggleFilterPanel:e.toggleFilterPanel}),x=e=>({clearValidationErrors:e.clearValidationErrors,isValid:e.isValid,validateFilters:e.validateFilters,validationErrors:e.validationErrors}),h=e=>({applyPreset:e.applyPreset,deletePreset:e.deletePreset,getPresets:e.getPresets,saveAsPreset:e.saveAsPreset}),p=()=>c((0,r.k)(o)),u=()=>c((0,r.k)(d)),g=()=>c((0,r.k)(m)),j=()=>c((0,r.k)(x)),y=()=>c((0,r.k)(h))},84547:(e,s,t)=>{"use strict";t.d(s,{U:()=>T});var a=t(60687),l=t(43210),r=t(29523),i=t(26373),n=t(40988),c=t(96834),o=t(91821),d=t(26622),m=t(78726),x=t(14975),h=t(3662),p=t(25928),u=t(29224),g=t(42102),j=t(46127),y=t(98192),v=t(95519),f=t(91551),N=t(47141),b=t(35533),w=t(75564),k=t(75699),C=t(76288),A=t(22482);let S=[{label:"Today",getValue:()=>({from:new Date,to:new Date})},{label:"Yesterday",getValue:()=>{let e=(0,p.e)(new Date,1);return{from:e,to:e}}},{label:"Last 3 days",getValue:()=>({from:(0,p.e)(new Date,2),to:new Date})},{label:"Last 7 days",getValue:()=>({from:(0,p.e)(new Date,6),to:new Date})},{label:"Last 2 weeks",getValue:()=>({from:(0,u.k)(new Date,2),to:new Date})},{label:"Last 30 days",getValue:()=>({from:(0,p.e)(new Date,29),to:new Date})},{label:"This week",getValue:()=>({from:(0,p.e)(new Date,new Date().getDay()),to:new Date})},{label:"This month",getValue:()=>({from:(0,g.w)(new Date),to:(0,j.p)(new Date)})},{label:"Last month",getValue:()=>{let e=(0,y.a)(new Date,1);return{from:(0,g.w)(e),to:(0,j.p)(e)}}},{label:"Last 3 months",getValue:()=>({from:(0,y.a)(new Date,3),to:new Date})},{label:"This year",getValue:()=>({from:(0,v.D)(new Date),to:(0,f.Q)(new Date)})}],T=({value:e,onChange:s,placeholder:t="Select date range",className:p,disabled:u=!1,maxDays:g=365,minDays:j=1,maxDate:y=new Date,minDate:v=new Date(2020,0,1),showValidation:f=!0})=>{let[T,D]=(0,l.useState)(!1),[R,E]=(0,l.useState)(e||null),$=(0,l.useMemo)(()=>{if(!R)return{isValid:!0};let{from:e,to:s}=R;if(!(0,N.f)(e)||!(0,N.f)(s))return{isValid:!1,message:"Invalid date selected",type:"error"};if((0,b.d)(e,s))return{isValid:!1,message:"Start date must be before end date",type:"error"};if(v&&(0,w.Y)(e,v))return{isValid:!1,message:`Start date cannot be before ${(0,k.GP)(v,"MMM dd, yyyy")}`,type:"error"};if(y&&(0,b.d)(s,y))return{isValid:!1,message:`End date cannot be after ${(0,k.GP)(y,"MMM dd, yyyy")}`,type:"error"};let t=(0,C.c)(s,e)+1;return t<j?{isValid:!1,message:`Date range must be at least ${j} day${j>1?"s":""}`,type:"error"}:t>g?{isValid:!1,message:`Date range cannot exceed ${g} days`,type:"error"}:t>90?{isValid:!0,message:`Large date range (${t} days) may affect performance`,type:"warning"}:{isValid:!0,message:`${t} day${t>1?"s":""} selected`,type:"info"}},[R,g,j,y,v]);(0,l.useEffect)(()=>{E(e||null)},[e]);let P=e=>{let t=e.getValue();E(t),M(t).isValid&&(s?.(t),D(!1))},M=e=>{if(!e)return{isValid:!0};let{from:s,to:t}=e;return(0,C.c)(t,s)+1>g?{isValid:!1,message:`Date range cannot exceed ${g} days`,type:"error"}:{isValid:!0}};return(0,a.jsx)("div",{className:(0,A.cn)("relative",p),children:(0,a.jsxs)(n.AM,{open:T,onOpenChange:D,children:[(0,a.jsx)(n.Wv,{asChild:!0,children:(0,a.jsxs)(r.$,{variant:"outline",className:(0,A.cn)("w-full justify-start text-left font-normal",!e&&"text-muted-foreground"),disabled:u,children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4"}),e?e.from.toDateString()===e.to.toDateString()?(0,k.GP)(e.from,"MMM dd, yyyy"):`${(0,k.GP)(e.from,"MMM dd, yyyy")} - ${(0,k.GP)(e.to,"MMM dd, yyyy")}`:t,e&&(0,a.jsxs)(c.E,{variant:"secondary",className:"ml-auto",children:[Math.ceil((e.to.getTime()-e.from.getTime())/864e5)+1," ","days"]})]})}),(0,a.jsx)(n.hl,{className:"w-auto max-w-4xl p-0",align:"start",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"border-r p-4 space-y-2 min-w-[160px]",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-gray-900 mb-3",children:"Quick Select"}),S.map(e=>(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-xs h-8 px-2",onClick:()=>P(e),children:e.label},e.label)),e&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"border-t pt-2 mt-3",children:(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-sm text-red-600 hover:text-red-700",onClick:()=>{E(null),s?.(null),D(!1)},children:[(0,a.jsx)(m.A,{className:"mr-2 h-3 w-3"}),"Clear"]})})})]}),(0,a.jsxs)("div",{className:"p-4 min-w-[600px]",children:[(0,a.jsx)(i.V,{mode:"range",selected:R||void 0,onSelect:e=>{if(e&&e.from){let t={from:e.from,to:e.to||e.from};E(t),M(t).isValid&&t.from&&t.to&&(s?.(t),D(!1))}else E(null)},numberOfMonths:2,className:"rounded-md border-0",disabled:[...v?[{before:v}]:[],...y?[{after:y}]:[]],showOutsideDays:!0,fixedWeeks:!0}),R&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h5",{className:"font-medium text-sm text-gray-900 mb-2",children:"Selected Range"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:["From: ",(0,k.GP)(R.from,"MMM dd, yyyy")]}),(0,a.jsxs)("div",{children:["To: ",(0,k.GP)(R.to,"MMM dd, yyyy")]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.ceil((R.to.getTime()-R.from.getTime())/864e5)+1," ","days"]})]}),f&&$.message&&(0,a.jsx)(o.Fc,{className:(0,A.cn)("mt-3 py-2 px-3","error"===$.type&&"border-red-200 bg-red-50","warning"===$.type&&"border-yellow-200 bg-yellow-50","info"===$.type&&"border-blue-200 bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["error"===$.type&&(0,a.jsx)(x.A,{className:"h-3 w-3 text-red-600"}),"warning"===$.type&&(0,a.jsx)(x.A,{className:"h-3 w-3 text-yellow-600"}),"info"===$.type&&(0,a.jsx)(h.A,{className:"h-3 w-3 text-blue-600"}),(0,a.jsx)(o.TN,{className:(0,A.cn)("text-xs","error"===$.type&&"text-red-700","warning"===$.type&&"text-yellow-700","info"===$.type&&"text-blue-700"),children:$.message})]})})]})]})]})})]})})}},85599:(e,s,t)=>{"use strict";t.d(s,{n:()=>l});var a=t(60687);t(43210);let l=({title:e,description:s,children:t,actions:l,filters:r})=>(0,a.jsxs)("div",{className:"flex flex-col h-full p-4 md:p-6 lg:p-8 gap-6",children:[(0,a.jsxs)("header",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100",children:e}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:s})]}),l&&(0,a.jsx)("div",{className:"flex-shrink-0",children:l})]}),r&&(0,a.jsx)("aside",{children:r}),(0,a.jsx)("main",{className:"flex-1",children:t})]})},85706:(e,s,t)=>{Promise.resolve().then(t.bind(t,17066))},90101:(e,s,t)=>{"use strict";t.d(s,{_L:()=>w});var a=t(60687),l=t(43210),r=t.n(l),i=t(6211),n=t(21342),c=t(29523),o=t(89667),d=t(96834),m=t(44493),x=t(56090),h=t(93772),p=t(73227),u=t(75699),g=t(72322),j=t(69795),y=t(76311),v=t(35137),f=t(92865),N=t(20620),b=t(90586);let w=({className:e="",showExportOptions:s=!0,maxRows:t=100})=>{let[w,S]=r().useState([]),[T,D]=r().useState([]),[R,E]=r().useState({}),[$,P]=r().useState({}),{data:M,isLoading:z,error:F}=(0,p.si)({staleTime:12e4}),V=(0,l.useMemo)(()=>M?M.slice(0,t).map(e=>({id:e.id,title:e.description,status:e.status,priority:e.priority,assignedTo:e.staffEmployee?.name||e.driverEmployee?.name||"Unassigned",dueDate:e.deadline?(0,u.GP)(new Date(e.deadline),"yyyy-MM-dd"):"No deadline",createdAt:(0,u.GP)(new Date(e.createdAt),"yyyy-MM-dd"),completedAt:"Completed"===e.status?(0,u.GP)(new Date(e.updatedAt),"yyyy-MM-dd"):void 0,estimatedHours:e.estimatedDuration||0,actualHours:e.estimatedDuration||0})):[],[M,t]),I=(0,l.useMemo)(()=>[{accessorKey:"title",header:({column:e})=>(0,a.jsxs)(c.$,{variant:"ghost",onClick:()=>e.toggleSorting("asc"===e.getIsSorted()),className:"h-8 px-2",children:["Task Title",(0,a.jsx)(g.A,{className:"ml-2 h-4 w-4"})]}),cell:({row:e})=>(0,a.jsx)("div",{className:"font-medium",children:e.getValue("title")})},{accessorKey:"status",header:({column:e})=>(0,a.jsxs)(c.$,{variant:"ghost",onClick:()=>e.toggleSorting("asc"===e.getIsSorted()),className:"h-8 px-2",children:["Status",(0,a.jsx)(g.A,{className:"ml-2 h-4 w-4"})]}),cell:({row:e})=>{let s=e.getValue("status");return(0,a.jsx)(d.E,{variant:k(s),children:A(s)})}},{accessorKey:"priority",header:({column:e})=>(0,a.jsxs)(c.$,{variant:"ghost",onClick:()=>e.toggleSorting("asc"===e.getIsSorted()),className:"h-8 px-2",children:["Priority",(0,a.jsx)(g.A,{className:"ml-2 h-4 w-4"})]}),cell:({row:e})=>{let s=e.getValue("priority");return(0,a.jsx)(d.E,{variant:C(s),children:s})}},{accessorKey:"assignedTo",header:({column:e})=>(0,a.jsxs)(c.$,{variant:"ghost",onClick:()=>e.toggleSorting("asc"===e.getIsSorted()),className:"h-8 px-2",children:["Assigned To",(0,a.jsx)(g.A,{className:"ml-2 h-4 w-4"})]}),cell:({row:e})=>(0,a.jsx)("div",{className:"text-sm",children:e.getValue("assignedTo")})},{accessorKey:"dueDate",header:({column:e})=>(0,a.jsxs)(c.$,{variant:"ghost",onClick:()=>e.toggleSorting("asc"===e.getIsSorted()),className:"h-8 px-2",children:["Due Date",(0,a.jsx)(g.A,{className:"ml-2 h-4 w-4"})]}),cell:({row:e})=>(0,a.jsx)("div",{className:"text-sm",children:e.getValue("dueDate")})},{accessorKey:"estimatedHours",header:"Est. Hours",cell:({row:e})=>(0,a.jsxs)("div",{className:"text-sm text-center",children:[e.getValue("estimatedHours"),"h"]})},{accessorKey:"actualHours",header:"Actual Hours",cell:({row:e})=>(0,a.jsxs)("div",{className:"text-sm text-center",children:[e.getValue("actualHours"),"h"]})},{id:"actions",header:"Actions",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)(n.rI,{children:[(0,a.jsx)(n.ty,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)(j.A,{className:"h-4 w-4"})]})}),(0,a.jsxs)(n.SQ,{align:"end",children:[(0,a.jsxs)(n.hO,{onClick:()=>console.log("View task:",s.id),children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),(0,a.jsxs)(n.hO,{onClick:()=>console.log("Edit task:",s.id),children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Edit Task"]}),(0,a.jsx)(n.hO,{onClick:()=>navigator.clipboard.writeText(s.id),children:"Copy Task ID"})]})]})}}],[]),L=(0,x.N4)({data:V,columns:I,onSortingChange:S,onColumnFiltersChange:D,getCoreRowModel:(0,h.HT)(),getPaginationRowModel:(0,h.kW)(),getSortedRowModel:(0,h.h5)(),getFilteredRowModel:(0,h.hM)(),onColumnVisibilityChange:E,onRowSelectionChange:P,state:{sorting:w,columnFilters:T,columnVisibility:R,rowSelection:$}}),O=async()=>{try{if(!V||0===V.length)return void console.warn("No data to export");let e=V.map(e=>({"Task ID":e.id,Title:e.title,Status:e.status,Priority:e.priority,"Assigned To":e.assignedTo,"Due Date":e.dueDate,"Created Date":e.createdAt,"Completed Date":e.completedAt||"","Estimated Hours":e.estimatedHours||"","Actual Hours":e.actualHours||""}));if(0===e.length)return void console.warn("No data to export");let s=[Object.keys(e[0]).join(","),...e.map(e=>Object.values(e).join(","))].join("\n"),t=new Blob([s],{type:"text/csv"}),a=window.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download=`task-report-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l)}catch(e){console.error("Export failed:",e)}};return z?(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Task Report"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse"}),(0,a.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("div",{className:"h-12 bg-muted rounded animate-pulse"},s))})]})})]}):F?(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Task Report"]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Failed to load task data"}),(0,a.jsx)("p",{className:"text-sm",children:F.message})]})})]}):(0,a.jsxs)(m.Zp,{className:e,children:[(0,a.jsxs)(m.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(m.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Task Report"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[V.length," tasks found"]})]}),s&&(0,a.jsxs)(c.$,{variant:"outline",size:"sm",onClick:O,className:"h-8",children:[(0,a.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"Export CSV"]})]}),(0,a.jsxs)(m.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(o.p,{placeholder:"Filter tasks...",value:L.getColumn("title")?.getFilterValue()??"",onChange:e=>L.getColumn("title")?.setFilterValue(e.target.value),className:"max-w-sm"})}),(0,a.jsxs)(n.rI,{children:[(0,a.jsx)(n.ty,{asChild:!0,children:(0,a.jsxs)(c.$,{variant:"outline",className:"ml-auto",children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Columns"]})}),(0,a.jsx)(n.SQ,{align:"end",children:L.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,a.jsx)(n.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:s=>e.toggleVisibility(!!s),children:e.id},e.id))})]})]}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(i.XI,{children:[(0,a.jsx)(i.A0,{children:L.getHeaderGroups().map(e=>(0,a.jsx)(i.Hj,{children:e.headers.map(e=>(0,a.jsx)(i.nd,{children:e.isPlaceholder?null:(0,x.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(i.BF,{children:L.getRowModel().rows?.length?L.getRowModel().rows.map(e=>(0,a.jsx)(i.Hj,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,a.jsx)(i.nA,{children:(0,x.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(i.Hj,{children:(0,a.jsx)(i.nA,{colSpan:I.length,className:"h-24 text-center",children:"No tasks found."})})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[L.getFilteredSelectedRowModel().rows.length," of"," ",L.getFilteredRowModel().rows.length," row(s) selected."]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>L.previousPage(),disabled:!L.getCanPreviousPage(),children:"Previous"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>L.nextPage(),disabled:!L.getCanNextPage(),children:"Next"})]})]})]})]})},k=e=>{switch(e){case"Completed":return"default";case"In_Progress":return"secondary";case"Cancelled":return"destructive";default:return"outline"}},C=e=>{switch(e){case"High":return"destructive";case"Medium":return"secondary";case"Low":return"outline";default:return"default"}},A=e=>"In_Progress"===e?"In Progress":e},91625:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{useExport:()=>h});var l=t(60687),r=t(972),i=t(43210),n=t(33103),c=t(95613),o=t(83559),d=t(24164),m=t(96813),x=e([r,c,o,d,m]);[r,c,o,d,m]=x.then?(await x)():x;let h=(e="report")=>{let[s,t]=(0,i.useState)(!1),[a,x]=(0,i.useState)(null),h=(0,i.useCallback)(async(s,a,i)=>{t(!0),x(null);try{let t=await (0,r.pdf)((0,l.jsx)(c.w,{data:s,filters:a,reportDate:new Date().toLocaleString(),reportTitle:i.filename||e})).toBlob(),n=URL.createObjectURL(t),o=document.createElement("a");o.href=n,o.setAttribute("download",`${i.filename||e}.pdf`),document.body.append(o),o.click(),o.remove(),URL.revokeObjectURL(n)}catch(e){console.error("PDF export failed:",e),x(e.message||"Failed to export to PDF.")}finally{t(!1)}},[e]),p=(0,i.useCallback)((s,a)=>{t(!0),x(null);try{let t=n.Wp.json_to_sheet(s),l=n.Wp.book_new();n.Wp.book_append_sheet(l,t,"Report Data"),n._h(l,`${a.filename||e}.xlsx`)}catch(e){console.error("Excel export failed:",e),x(e.message||"Failed to export to Excel.")}finally{t(!1)}},[e]),u=(0,i.useCallback)((s,a)=>{t(!0),x(null);try{let t=Object.keys(s[0]||{}).join(","),l=s.map(e=>Object.values(e).map(e=>`"${String(e).replaceAll('"','""')}"`).join(",")),r=[t,...l].join("\n"),i=new Blob([r],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let s=URL.createObjectURL(i);n.setAttribute("href",s),n.setAttribute("download",`${a.filename||e}.csv`),n.style.visibility="hidden",document.body.append(n),n.click(),n.remove()}}catch(e){console.error("CSV export failed:",e),x(e.message||"Failed to export to CSV.")}finally{t(!1)}},[e]),g=(0,i.useCallback)(async(e,s,a)=>{t(!0),x(null);try{let t=await (0,r.pdf)((0,l.jsx)(c.w,{data:e,filters:s,includeCharts:!0,includeServiceHistory:!!s.includeServiceHistory,reportDate:new Date().toLocaleString(),reportTitle:a||"Dashboard Report"})).toBlob(),i=URL.createObjectURL(t),n=document.createElement("a");n.href=i,n.setAttribute("download",`${a||"dashboard-report"}.pdf`),document.body.append(n),n.click(),n.remove(),URL.revokeObjectURL(i)}catch(e){console.error("Dashboard PDF export failed:",e),x(e.message||"Failed to export dashboard to PDF.")}finally{t(!1)}},[]),j=(0,i.useCallback)(async(e,s,a)=>{t(!0),x(null);try{let t=await (0,r.pdf)((0,l.jsx)(()=>(0,l.jsx)(r.Document,{children:(0,l.jsxs)(r.Page,{orientation:"landscape",size:"A4",style:{padding:30},children:[(0,l.jsxs)(r.View,{style:{marginBottom:20},children:[(0,l.jsx)(r.Text,{style:{fontSize:20,fontWeight:"bold"},children:s}),(0,l.jsxs)(r.Text,{style:{fontSize:10,marginTop:5},children:["Generated on: ",new Date().toLocaleDateString()]})]}),(0,l.jsx)(r.View,{style:{alignItems:"center",flex:1,justifyContent:"center"},children:(0,l.jsxs)(r.Text,{style:{fontSize:14},children:["Chart data: ",JSON.stringify(e,null,2)]})})]})}),{})).toBlob(),i=URL.createObjectURL(t),n=document.createElement("a");n.href=i,n.setAttribute("download",a||`${s.toLowerCase().replaceAll(/\s+/g,"-")}.pdf`),document.body.append(n),n.click(),n.remove(),URL.revokeObjectURL(i)}catch(e){console.error("Chart PDF export failed:",e),x(e.message||"Failed to export chart to PDF.")}finally{t(!1)}},[]),y=(0,i.useCallback)(async(e,s,a,i)=>{t(!0),x(null);let n=e=>{if(null==e)return null;if(Array.isArray(e))return e.map(n);if("object"==typeof e&&e.constructor===Object){let s={};for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"__proto__"!==t&&"constructor"!==t){let a=n(e[t]);void 0!==a&&(s[t]=a)}return s}return"function"==typeof e||"symbol"==typeof e?null:e};try{let t,x;if(!e)throw Error("No report data provided for PDF export");let h=e?.data||e||{},p=n(h),u={data:{totalCount:p?.totalCount||0,summary:p?.summary||{message:"No data available"},records:p?.records||[],statusDistribution:p?.statusDistribution||[],priorityDistribution:p?.priorityDistribution||[],...p},metadata:e?.metadata||{entityType:s,format:"pdf",generatedAt:new Date().toISOString(),generatedBy:"system",id:`${s}_${Date.now()}`,type:"aggregate"}};if(0===Object.keys(u.data).length)throw Error("After sanitization, no valid data is available for PDF export.");let g=a||`${s} Report`,j=u.metadata||{};switch(s){case"delegations":{let e={dateRange:{from:new Date,to:new Date},employees:[],locations:[],status:[],vehicles:[]};t=(0,l.jsx)(c.w,{data:u,filters:e,reportDate:new Date().toLocaleString(),reportTitle:g});break}case"employees":t=(0,l.jsx)(o.W,{data:u,metadata:j,reportTitle:g});break;case"tasks":t=(0,l.jsx)(d.p,{data:u,metadata:j,reportTitle:g});break;case"vehicles":t=(0,l.jsx)(m.w,{data:u,metadata:j,reportTitle:g});break;default:throw Error(`Unsupported entity type: ${s}. Supported types are: delegations, tasks, vehicles, employees`)}console.log("Generating PDF with SANITIZED data:",u),console.log("Entity Type:",s),console.log("Raw data structure:",e),console.log("Normalized data structure:",u),console.log("PDF Component:",t),console.log("Entity type:",s),console.log("Report title:",g);try{console.log("Starting PDF blob generation..."),x=await (0,r.pdf)(t).toBlob(),console.log("PDF blob generated successfully:",{size:x.size,type:x.type})}catch(e){throw console.error("PDF generation failed:",e),console.error("PDF error stack:",e.stack),Error(`PDF generation failed: ${e.message??"Unknown PDF error"}`)}if(!x||0===x.size)throw Error("Generated PDF blob is empty or invalid");console.log("Creating download link...");let y=URL.createObjectURL(x),v=document.createElement("a");v.href=y;let f=`${i||`${s}-report`}.pdf`;v.setAttribute("download",f),console.log("Triggering download for:",f),document.body.append(v),v.click(),v.remove(),URL.revokeObjectURL(y),console.log("Download triggered successfully")}catch(e){console.error("Report PDF export failed:",e),x(e.message||"Failed to export report to PDF.")}finally{t(!1)}},[]),v=(0,i.useCallback)((e,s,a)=>{t(!0),x(null);try{let t=n.Wp.book_new(),l=e.data||e;if(l.summary||l.totalCount){let e=[];if(l.totalCount&&e.push({Metric:"Total Count",Value:l.totalCount}),l.summary)for(let[s,t]of Object.entries(l.summary))e.push({Metric:s.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),Value:t});let s=n.Wp.json_to_sheet(e);n.Wp.book_append_sheet(t,s,"Summary")}if(l.statusDistribution){let e=n.Wp.json_to_sheet(l.statusDistribution);n.Wp.book_append_sheet(t,e,"Status Distribution")}if(l.priorityDistribution){let e=n.Wp.json_to_sheet(l.priorityDistribution);n.Wp.book_append_sheet(t,e,"Priority Distribution")}if(l.locationMetrics){let e=n.Wp.json_to_sheet(l.locationMetrics);n.Wp.book_append_sheet(t,e,"Location Metrics")}if(l.maintenanceMetrics){let e=n.Wp.json_to_sheet([l.maintenanceMetrics]);n.Wp.book_append_sheet(t,e,"Maintenance Metrics")}if(l.performanceMetrics){let e=n.Wp.json_to_sheet([l.performanceMetrics]);n.Wp.book_append_sheet(t,e,"Performance Metrics")}n._h(t,`${a||`${s}-report`}.xlsx`)}catch(e){console.error("Report Excel export failed:",e),x(e.message||"Failed to export report to Excel.")}finally{t(!1)}},[]);return{exportChartToPDF:j,exportDashboardToPDF:g,exportError:a,exportReportToExcel:v,exportReportToPDF:y,exportToCSV:u,exportToExcel:p,exportToPDF:h,isExporting:s}};a()}catch(e){a(e)}})},95613:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{w:()=>d});var l=t(60687),r=t(972),i=t(43210),n=t.n(i),c=e([r]);let o=(r=(c.then?(await c)():c)[0]).StyleSheet.create({header:{color:"#333",fontSize:24,marginBottom:10,textAlign:"center"},page:{backgroundColor:"#E4E4E4",flexDirection:"column",padding:30},section:{flexGrow:1,margin:10,padding:10},subheader:{color:"#555",fontSize:16,marginBottom:5},text:{fontSize:12,marginBottom:3}}),d=({data:e,filters:s,reportDate:t,reportTitle:a})=>{let{metadata:i,reportData:c,summary:d,totalCount:m}=n().useMemo(()=>{if(!e)return{metadata:{},reportData:{},summary:{message:"No data available"},totalCount:0};let s=e?.data??e??{},t=e?.metadata??{},a="object"!=typeof s||null===s||Array.isArray(s)?{}:s;return{metadata:t,reportData:a,summary:a.summary??{message:"No summary available"},totalCount:a.totalCount??(Array.isArray(a)?a.length:0)}},[e]),x=s??{},h=x?.dateRange??{},p=x?.status,u=t??new Date().toLocaleString();return(0,l.jsx)(r.Document,{children:(0,l.jsx)(r.Page,{size:"A4",style:o.page,children:(0,l.jsxs)(r.View,{style:o.section,children:[(0,l.jsx)(r.Text,{style:o.header,children:a??"Delegation Report"}),(0,l.jsxs)(r.Text,{style:o.text,children:["Report Generated: ",u]}),(0,l.jsxs)(r.Text,{style:o.text,children:["Total Delegations: ",m]}),d&&Object.keys(d).length>0&&(0,l.jsxs)(r.View,{style:{marginTop:15},children:[(0,l.jsx)(r.Text,{style:o.subheader,children:"Summary:"}),Object.entries(d).map(([e,s])=>(0,l.jsxs)(r.Text,{style:o.text,children:[e.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),": ",String(s??"N/A")]},e))]}),x&&Object.keys(x).length>0&&(0,l.jsxs)(r.View,{style:{marginTop:20},children:[(0,l.jsx)(r.Text,{style:o.subheader,children:"Filters Applied:"}),(0,l.jsxs)(r.Text,{style:o.text,children:["Date Range:"," ",h.from?.toLocaleDateString?.()??"N/A"," -"," ",h.to?.toLocaleDateString?.()??"N/A",(0,l.jsxs)(r.Text,{style:o.text,children:["Status: ",p?.join?.(", ")??"All"]})]})]}),i.id&&(0,l.jsxs)(r.View,{style:{marginTop:15},children:[(0,l.jsx)(r.Text,{style:o.subheader,children:"Report Details:"}),(0,l.jsxs)(r.Text,{style:o.text,children:["Report ID: ",i.id]}),(0,l.jsxs)(r.Text,{style:o.text,children:["Generated At:"," ",i.generatedAt?new Date(i.generatedAt).toLocaleString():"N/A"]})]})]})})})};a()}catch(e){a(e)}})},95863:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>r});var a=t(37413),l=t(87432);let r={description:"Comprehensive reporting and analytics dashboard",title:"Reports - WorkHub"};function i({children:e}){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,a.jsx)(l.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Reports & Analytics"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"Comprehensive delegation analytics and reporting"})]})})]}),(0,a.jsx)("div",{className:"min-h-[600px]",children:e})]})}},96813:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{w:()=>d});var l=t(60687),r=t(43210),i=t.n(r),n=t(972),c=e([n]);let o=(n=(c.then?(await c)():c)[0]).StyleSheet.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#dc2626",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#fef2f2",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#fef2f2",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),d=({data:e,reportTitle:s,metadata:t})=>{let a=i().useMemo(()=>{if(!e)return{};let s=e?.data||e;return s&&"object"==typeof s?{totalCount:s.totalCount||0,activeCount:s.activeCount||0,maintenanceCount:s.maintenanceCount||0,outOfServiceCount:s.outOfServiceCount||0,utilizationRate:s.utilizationRate||0,averageMileage:s.averageMileage||0,statusDistribution:Array.isArray(s.statusDistribution)?s.statusDistribution:[],typeDistribution:Array.isArray(s.typeDistribution)?s.typeDistribution:[],maintenanceMetrics:s.maintenanceMetrics||{},...s}:{}},[e]);return(0,l.jsx)(n.Document,{children:(0,l.jsxs)(n.Page,{size:"A4",style:o.page,children:[(0,l.jsx)(n.Text,{style:o.header,children:s||"Vehicle Report"}),(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Fleet Summary"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Total Vehicles:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.totalCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Active Vehicles:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.activeCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"In Maintenance:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.maintenanceCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Out of Service:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.outOfServiceCount||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Fleet Utilization Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.utilizationRate?.toFixed(2)||0,"%"]})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Mileage:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.averageMileage?.toFixed(0)||0," km"]})]})]}),a.statusDistribution&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Vehicle Status Distribution"}),(0,l.jsxs)(n.View,{style:o.table,children:[(0,l.jsxs)(n.View,{style:o.tableHeader,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:"Status"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Count"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Percentage"})]}),a.statusDistribution.map((e,s)=>(0,l.jsxs)(n.View,{style:o.tableRow,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.status||e?._id||"Unknown"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.count||e?._count?.status||0}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.percentage?`${Number(e.percentage).toFixed(1)}%`:"N/A"})]},`status-${s}`))]})]}),a.typeDistribution&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Vehicle Type Distribution"}),(0,l.jsxs)(n.View,{style:o.table,children:[(0,l.jsxs)(n.View,{style:o.tableHeader,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:"Type"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Count"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:"Percentage"})]}),a.typeDistribution.map((e,s)=>(0,l.jsxs)(n.View,{style:o.tableRow,children:[(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.type||e?._id||"Unknown"}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.count||e?._count?.type||0}),(0,l.jsx)(n.Text,{style:o.tableCell,children:e?.percentage?`${Number(e.percentage).toFixed(1)}%`:"N/A"})]},`type-${s}`))]})]}),a.maintenanceMetrics&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Maintenance Metrics"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Total Maintenance Records:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.maintenanceMetrics.totalRecords||0})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Cost per Service:"}),(0,l.jsxs)(n.Text,{style:o.value,children:["$",a.maintenanceMetrics.averageCost?.toFixed(2)||0]})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Total Maintenance Cost:"}),(0,l.jsxs)(n.Text,{style:o.value,children:["$",a.maintenanceMetrics.totalCost?.toFixed(2)||0]})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Preventive Maintenance Rate:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.maintenanceMetrics.preventiveRate?.toFixed(2)||0,"%"]})]})]}),a.utilizationMetrics&&(0,l.jsxs)(n.View,{style:o.section,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Utilization Metrics"}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Average Daily Usage:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.utilizationMetrics.averageDailyUsage?.toFixed(2)||0," ","hours"]})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Peak Usage Hours:"}),(0,l.jsx)(n.Text,{style:o.value,children:a.utilizationMetrics.peakUsageHours||"N/A"})]}),(0,l.jsxs)(n.View,{style:o.row,children:[(0,l.jsx)(n.Text,{style:o.label,children:"Idle Time Percentage:"}),(0,l.jsxs)(n.Text,{style:o.value,children:[a.utilizationMetrics.idleTimePercentage?.toFixed(2)||0,"%"]})]})]}),t&&(0,l.jsxs)(n.View,{style:o.metadata,children:[(0,l.jsx)(n.Text,{style:o.subheader,children:"Report Information"}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Report ID: ",t.id]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Type: ",t.type]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Entity Type: ",t.entityType]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Generated: ",new Date(t.generatedAt).toLocaleString()]}),(0,l.jsxs)(n.Text,{style:o.metadataText,children:["Generated By: ",t.generatedBy]})]})]})})};a()}catch(e){a(e)}})}};