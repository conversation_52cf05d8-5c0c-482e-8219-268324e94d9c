(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthContext": (()=>AuthContext),
    "AuthProvider": (()=>AuthProvider),
    "useAuthContext": (()=>useAuthContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"); // Import useRef, useCallback, useState
// Remove circular dependency - auth logic will be moved here
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
// Define a key for cross-tab logout events
const LOGOUT_EVENT_KEY = 'workhub-logout-event';
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    // State management for authentication
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Helper function to transform Supabase user to our User interface
    const transformUser = (supabaseUser)=>({
            app_metadata: supabaseUser.app_metadata,
            created_at: supabaseUser.created_at,
            email: supabaseUser.email,
            email_confirmed_at: supabaseUser.email_confirmed_at,
            id: supabaseUser.id,
            is_anonymous: supabaseUser.is_anonymous,
            is_sso_user: supabaseUser.app_metadata?.provider !== 'email',
            last_sign_in_at: supabaseUser.last_sign_in_at,
            updated_at: supabaseUser.updated_at,
            user_metadata: supabaseUser.user_metadata
        });
    // Initialize Supabase auth state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            let mounted = true;
            // Get initial session with circuit breaker integration
            const initializeAuth = {
                "AuthProvider.useEffect.initializeAuth": async ()=>{
                    // Initialize circuit breaker first
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].initializeCircuitBreaker();
                    // Circuit breaker check for auth initialization
                    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                        console.debug('🔒 Auth initialization blocked by circuit breaker');
                        if (mounted) {
                            setLoading(false);
                            setIsInitialized(true);
                            setError('Authentication system temporarily unavailable');
                        }
                        return;
                    }
                    const operationId = 'auth-initialization';
                    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                        console.debug('🔄 Auth initialization already in progress');
                        return;
                    }
                    try {
                        // First, get the initial session without integrity check to avoid race condition
                        const { data: { session: initialSession }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
                        if (error) {
                            console.error('Error getting initial session:', error);
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                            setError(error.message);
                        } else if (mounted) {
                            console.log('✅ Auth initialization successful');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                            setSession(initialSession);
                            setUser(initialSession?.user ? transformUser(initialSession.user) : null);
                            // Update session manager with successful authentication
                            if (initialSession) {
                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SessionManager"].updateActivity();
                                // Perform session integrity check AFTER authentication is established
                                setTimeout({
                                    "AuthProvider.useEffect.initializeAuth": async ()=>{
                                        try {
                                            const integrityCheck = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SessionManager"].performIntegrityCheck();
                                            if (integrityCheck) {
                                                console.log('✅ Session integrity check passed after auth initialization');
                                            } else {
                                                console.log('📊 Session integrity check failed - automatic recovery will handle this');
                                                const recovered = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SessionManager"].recoverFromCorruptedState();
                                                if (!recovered) {
                                                    console.warn('⚠️ Session recovery completed with warnings after auth initialization');
                                                }
                                            }
                                        } catch (error) {
                                            console.warn('Session integrity check error:', error);
                                        // Don't treat this as a critical error during initialization
                                        }
                                    }
                                }["AuthProvider.useEffect.initializeAuth"], 1000); // Increased delay to allow cookies to be properly set
                            }
                        }
                    } catch (error_) {
                        console.error('Error initializing auth:', error_);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                        if (mounted) {
                            setError('Failed to initialize authentication');
                        }
                    } finally{
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
                        if (mounted) {
                            setLoading(false);
                            setIsInitialized(true);
                        }
                    }
                }
            }["AuthProvider.useEffect.initializeAuth"];
            initializeAuth();
            // Listen for auth changes
            const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange({
                "AuthProvider.useEffect": async (event, session)=>{
                    if (!mounted) return;
                    console.log('Auth state changed:', event, session?.user?.email);
                    setSession(session);
                    setUser(session?.user ? transformUser(session.user) : null);
                    setLoading(false);
                    setIsInitialized(true);
                    if (event === 'SIGNED_OUT') {
                        setError(null);
                    }
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>{
                    mounted = false;
                    subscription.unsubscribe();
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], []);
    // Get user role from metadata
    const getUserRole = (user)=>{
        if (!user) return null;
        return user.user_metadata?.role || user.app_metadata?.role || 'USER';
    };
    // Auth actions
    const signIn = async (email, password)=>{
        try {
            setLoading(true);
            setError(null);
            const { data, error: signInError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
                email,
                password
            });
            if (signInError) {
                setError(signInError.message);
                return {
                    error: signInError.message
                };
            }
            return {};
        } catch (error_) {
            const errorMessage = error_ instanceof Error ? error_.message : 'An unexpected error occurred';
            setError(errorMessage);
            return {
                error: errorMessage
            };
        } finally{
            setLoading(false);
        }
    };
    const signOut = async ()=>{
        // Circuit breaker check for sign out
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
            console.debug('🔒 Sign out blocked by circuit breaker');
            return;
        }
        const operationId = 'auth-signout';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
            console.debug('🔄 Sign out already in progress');
            return;
        }
        try {
            setLoading(true);
            console.log('🔐 Starting sign out process...');
            // Clear session state first
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SessionManager"].clearSessionState();
            // Clear all secure storage
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].clearAllCookies();
            const { error: signOutError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
            if (signOutError) {
                console.error('❌ Supabase sign out error:', signOutError);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                setError(signOutError.message);
            } else {
                console.log('✅ Sign out successful');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                // Clear any remaining authentication state
                setSession(null);
                setUser(null);
                setError(null);
            }
        } catch (error_) {
            console.error('Sign out error:', error_);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            setError('Sign out failed');
        } finally{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
            setLoading(false);
        }
    };
    const clearError = ()=>{
        setError(null);
    };
    // Create auth object
    // SECURITY NOTE: HttpOnly Cookie Compliance
    // The access_token exposed here is for client-side validation and header construction only.
    // Actual authentication relies on HttpOnly cookies set by the backend.
    // This token should NOT be stored in localStorage or used for direct API authentication.
    const auth = {
        clearError,
        error,
        isInitialized,
        loading,
        session: session ? {
            // SECURITY: Token exposed for compatibility but HttpOnly cookies are primary auth method
            access_token: session.access_token,
            user: user ?? null
        } : null,
        signIn,
        signOut,
        user,
        userRole: getUserRole(user)
    };
    const isLoggingOutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
    // Set up secure auth token provider for ALL API clients
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const getSecureToken = {
                "AuthProvider.useEffect.getSecureToken": ()=>auth.session?.access_token || null
            }["AuthProvider.useEffect.getSecureToken"];
            // Set the SINGLE secure token provider for the entire application
            // This replaces legacy setGlobalAuthTokenProvider and setFactoryAuthTokenProvider
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setSecureAuthTokenProvider"])(getSecureToken);
            if ("TURBOPACK compile-time truthy", 1) {
                console.log('🔐 AuthContext: Secure token provider updated', {
                    hasToken: !!auth.session?.access_token,
                    tokenLength: auth.session?.access_token?.length || 0
                });
            }
        }
    }["AuthProvider.useEffect"], [
        auth.session?.access_token
    ]);
    // Handle cross-tab logout with circuit breaker protection
    const handleStorageChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[handleStorageChange]": (event)=>{
            if (event.key === LOGOUT_EVENT_KEY && event.newValue === 'true') {
                console.log('🔐 Cross-tab logout detected. Signing out...');
                // Circuit breaker check for cross-tab logout
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                    console.debug('🔒 Cross-tab logout blocked by circuit breaker');
                    return;
                }
                if (!isLoggingOutRef.current) {
                    isLoggingOutRef.current = true;
                    const operationId = 'cross-tab-logout';
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                        auth.signOut().finally({
                            "AuthProvider.useCallback[handleStorageChange]": ()=>{
                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
                                isLoggingOutRef.current = false;
                                // Clear the event key to allow future logout events
                                localStorage.removeItem(LOGOUT_EVENT_KEY);
                            }
                        }["AuthProvider.useCallback[handleStorageChange]"]);
                    } else {
                        console.debug('🔄 Cross-tab logout already in progress');
                        isLoggingOutRef.current = false;
                    }
                }
            }
        }
    }["AuthProvider.useCallback[handleStorageChange]"], [
        auth.signOut
    ]);
    // Handle critical token refresh failures with circuit breaker protection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const handleCriticalRefreshFailed = {
                "AuthProvider.useEffect.handleCriticalRefreshFailed": ()=>{
                    console.warn('🔐 Critical token refresh failure detected, signing out user');
                    // Circuit breaker check for critical refresh failure
                    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                        console.debug('🔒 Critical refresh failure handling blocked by circuit breaker');
                        return;
                    }
                    if (!isLoggingOutRef.current) {
                        isLoggingOutRef.current = true;
                        const operationId = 'critical-refresh-failure';
                        if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                            auth.signOut().finally({
                                "AuthProvider.useEffect.handleCriticalRefreshFailed": ()=>{
                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
                                    isLoggingOutRef.current = false;
                                }
                            }["AuthProvider.useEffect.handleCriticalRefreshFailed"]);
                        } else {
                            console.debug('🔄 Critical refresh failure handling already in progress');
                            isLoggingOutRef.current = false;
                        }
                    }
                }
            }["AuthProvider.useEffect.handleCriticalRefreshFailed"];
            tokenRefreshService.subscribe({
                "AuthProvider.useEffect": (event)=>{
                    if (event === 'critical_refresh_failed') {
                        handleCriticalRefreshFailed();
                    }
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>{
                // No direct unsubscribe method on the service, but the service is a singleton
                // and callbacks are managed by the Set. For a more robust solution,
                // TokenRefreshService would need an unsubscribe method that takes the specific callback.
                // For now, relying on the singleton nature and the fact that AuthContext is long-lived.
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        auth.signOut,
        tokenRefreshService
    ]);
    // Phase 3: Token management now handled by TokenManager - no manual sync needed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const token = auth.session?.access_token || null;
            globalThis.addEventListener('storage', handleStorageChange);
            // When this tab logs out, signal other tabs
            if (!auth.session && !auth.loading && !isLoggingOutRef.current) {
                // Only set if not already logging out to prevent loop
                localStorage.setItem(LOGOUT_EVENT_KEY, 'true');
            } else if (auth.session && localStorage.getItem(LOGOUT_EVENT_KEY) === 'true') {
                // If a session exists but a logout signal is present, clear the signal
                // This handles cases where a tab might have been open during a logout,
                // then refreshed or re-authenticated.
                localStorage.removeItem(LOGOUT_EVENT_KEY);
            }
            // Development-only authentication debugging
            if ("TURBOPACK compile-time truthy", 1) {
                console.debug('🔐 AuthProvider: Token sync signal sent', {
                    authLoading: auth.loading,
                    tokenAvailable: !!token,
                    userEmail: auth.user?.email
                });
                if (token && auth.user) {
                    console.debug('✅ Authentication ready for API calls (via httpOnly cookies)', {
                        hasToken: true,
                        userEmail: auth.user.email
                    });
                } else if (!auth.loading) {
                    console.warn('⚠️ No token available client-side for direct access (expected for httpOnly cookies)', {
                        hasSession: !!auth.session,
                        hasUser: !!auth.user,
                        loading: auth.loading
                    });
                }
            }
        }
    }["AuthProvider.useEffect"], [
        auth.session?.access_token,
        auth.user,
        auth.loading,
        handleStorageChange
    ]); // Add handleStorageChange to dependencies
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            return ({
                "AuthProvider.useEffect": ()=>{
                    globalThis.removeEventListener('storage', handleStorageChange);
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        handleStorageChange
    ]); // Depend on handleStorageChange
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: auth,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 475,
        columnNumber: 10
    }, this);
}
_s(AuthProvider, "LEvrGSV81UBRtcUzm4en2y/sxV8=");
_c = AuthProvider;
function useAuthContext() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuthContext, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/forms/useLoginValidation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLoginValidation": (()=>useLoginValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useLoginValidation() {
    _s();
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isFormTouched, setIsFormTouched] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Enhanced email validation
    const validateEmail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[validateEmail]": (email)=>{
            if (!email) {
                return 'Email address is required';
            }
            // More comprehensive email validation
            const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
            if (!emailRegex.test(email)) {
                return 'Please enter a valid email address';
            }
            return undefined;
        }
    }["useLoginValidation.useCallback[validateEmail]"], []);
    // Enhanced password validation
    const validatePassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[validatePassword]": (password)=>{
            if (!password) {
                return 'Password is required';
            }
            if (password.length < 6) {
                return 'Password must be at least 6 characters long';
            }
            // Additional password strength checks can be added here
            return undefined;
        }
    }["useLoginValidation.useCallback[validatePassword]"], []);
    // Validate entire form
    const validateForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[validateForm]": (formData)=>{
            const newErrors = {};
            const emailError = validateEmail(formData.email);
            const passwordError = validatePassword(formData.password);
            if (emailError) newErrors.email = emailError;
            if (passwordError) newErrors.password = passwordError;
            setErrors(newErrors);
            return {
                isValid: Object.keys(newErrors).length === 0,
                errors: newErrors
            };
        }
    }["useLoginValidation.useCallback[validateForm]"], [
        validateEmail,
        validatePassword
    ]);
    // Validate single field
    const validateField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[validateField]": (fieldName, value)=>{
            let fieldError;
            switch(fieldName){
                case 'email':
                    fieldError = validateEmail(value);
                    break;
                case 'password':
                    fieldError = validatePassword(value);
                    break;
                default:
                    return;
            }
            setErrors({
                "useLoginValidation.useCallback[validateField]": (prev)=>({
                        ...prev,
                        [fieldName]: fieldError
                    })
            }["useLoginValidation.useCallback[validateField]"]);
        }
    }["useLoginValidation.useCallback[validateField]"], [
        validateEmail,
        validatePassword
    ]);
    // Clear specific field error
    const clearFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[clearFieldError]": (fieldName)=>{
            setErrors({
                "useLoginValidation.useCallback[clearFieldError]": (prev)=>{
                    const newErrors = {
                        ...prev
                    };
                    delete newErrors[fieldName];
                    return newErrors;
                }
            }["useLoginValidation.useCallback[clearFieldError]"]);
        }
    }["useLoginValidation.useCallback[clearFieldError]"], []);
    // Clear all errors
    const clearAllErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[clearAllErrors]": ()=>{
            setErrors({});
        }
    }["useLoginValidation.useCallback[clearAllErrors]"], []);
    // Mark form as touched
    const markFormTouched = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[markFormTouched]": ()=>{
            setIsFormTouched(true);
        }
    }["useLoginValidation.useCallback[markFormTouched]"], []);
    // Reset validation state
    const resetValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[resetValidation]": ()=>{
            setErrors({});
            setIsFormTouched(false);
        }
    }["useLoginValidation.useCallback[resetValidation]"], []);
    // Check if field has been validated successfully
    const isFieldValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoginValidation.useCallback[isFieldValid]": (fieldName, value)=>{
            return isFormTouched && value && !errors[fieldName];
        }
    }["useLoginValidation.useCallback[isFieldValid]"], [
        errors,
        isFormTouched
    ]);
    return {
        errors,
        isFormTouched,
        validateForm,
        validateField,
        clearFieldError,
        clearAllErrors,
        markFormTouched,
        resetValidation,
        isFieldValid
    };
}
_s(useLoginValidation, "8QxiBVk3gu0anugWo9bWtkheTn0=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/ui/useTheme.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Theme management hook using Zustand AppStore
 * @module hooks/useTheme
 */ __turbopack_context__.s({
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
const useTheme = ()=>{
    _s();
    const currentTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useTheme.useAppStore[currentTheme]": (state)=>state.currentTheme
    }["useTheme.useAppStore[currentTheme]"]);
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useTheme.useAppStore[setTheme]": (state)=>state.setTheme
    }["useTheme.useAppStore[setTheme]"]);
    /**
   * Toggle between light and dark themes
   */ const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[toggleTheme]": ()=>{
            setTheme(currentTheme === 'light' ? 'dark' : 'light');
        }
    }["useTheme.useCallback[toggleTheme]"], [
        currentTheme,
        setTheme
    ]);
    /**
   * Check if current theme is dark
   */ const isDark = currentTheme === 'dark';
    /**
   * Check if current theme is light
   */ const isLight = currentTheme === 'light';
    /**
   * Set theme to light
   */ const setLightTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[setLightTheme]": ()=>{
            setTheme('light');
        }
    }["useTheme.useCallback[setLightTheme]"], [
        setTheme
    ]);
    /**
   * Set theme to dark
   */ const setDarkTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[setDarkTheme]": ()=>{
            setTheme('dark');
        }
    }["useTheme.useCallback[setDarkTheme]"], [
        setTheme
    ]);
    /**
   * Get theme-specific CSS classes
   */ const getThemeClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTheme.useCallback[getThemeClasses]": ()=>{
            return {
                background: isDark ? 'bg-gray-900' : 'bg-white',
                border: isDark ? 'border-gray-700' : 'border-gray-200',
                isDark,
                isLight,
                root: currentTheme,
                text: isDark ? 'text-white' : 'text-gray-900'
            };
        }
    }["useTheme.useCallback[getThemeClasses]"], [
        currentTheme,
        isDark,
        isLight
    ]);
    return {
        // State
        currentTheme,
        // Utilities
        getThemeClasses,
        isDark,
        isLight,
        setDarkTheme,
        setLightTheme,
        // Actions
        setTheme,
        toggleTheme
    };
};
_s(useTheme, "0vVtxBTL3pX7nboQ3uGQUr49vGQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/ui/useUiPreferences.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file UI preferences management hook using Zustand UiStore
 * @module hooks/useUiPreferences
 */ __turbopack_context__.s({
    "useUiPreferences": (()=>useUiPreferences)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/uiStore.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
const useUiPreferences = ()=>{
    _s();
    // Font size preferences
    const fontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[fontSize]": (state)=>state.fontSize
    }["useUiPreferences.useUiStore[fontSize]"]);
    const setFontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[setFontSize]": (state)=>state.setFontSize
    }["useUiPreferences.useUiStore[setFontSize]"]);
    // Notification preferences
    const notificationsEnabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[notificationsEnabled]": (state)=>state.notificationsEnabled
    }["useUiPreferences.useUiStore[notificationsEnabled]"]);
    const toggleNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[toggleNotifications]": (state)=>state.toggleNotifications
    }["useUiPreferences.useUiStore[toggleNotifications]"]);
    // WorkHub-specific preferences
    const tableDensity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[tableDensity]": (state)=>state.tableDensity
    }["useUiPreferences.useUiStore[tableDensity]"]);
    const setTableDensity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[setTableDensity]": (state)=>state.setTableDensity
    }["useUiPreferences.useUiStore[setTableDensity]"]);
    const mapViewPreference = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[mapViewPreference]": (state)=>state.mapViewPreference
    }["useUiPreferences.useUiStore[mapViewPreference]"]);
    const setMapViewPreference = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[setMapViewPreference]": (state)=>state.setMapViewPreference
    }["useUiPreferences.useUiStore[setMapViewPreference]"]);
    const dashboardLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[dashboardLayout]": (state)=>state.dashboardLayout
    }["useUiPreferences.useUiStore[dashboardLayout]"]);
    const setDashboardLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[setDashboardLayout]": (state)=>state.setDashboardLayout
    }["useUiPreferences.useUiStore[setDashboardLayout]"]);
    const autoRefreshInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[autoRefreshInterval]": (state)=>state.autoRefreshInterval
    }["useUiPreferences.useUiStore[autoRefreshInterval]"]);
    const setAutoRefreshInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useUiPreferences.useUiStore[setAutoRefreshInterval]": (state)=>state.setAutoRefreshInterval
    }["useUiPreferences.useUiStore[setAutoRefreshInterval]"]);
    /**
   * Get font size CSS class
   */ const getFontSizeClass = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[getFontSizeClass]": ()=>{
            switch(fontSize){
                case 'large':
                    {
                        return 'text-lg';
                    }
                case 'small':
                    {
                        return 'text-sm';
                    }
                default:
                    {
                        return 'text-base';
                    }
            }
        }
    }["useUiPreferences.useCallback[getFontSizeClass]"], [
        fontSize
    ]);
    /**
   * Get table density CSS classes
   */ const getTableDensityClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[getTableDensityClasses]": ()=>{
            switch(tableDensity){
                case 'compact':
                    {
                        return {
                            cell: 'py-1 px-2',
                            row: 'h-8',
                            table: 'table-compact'
                        };
                    }
                case 'spacious':
                    {
                        return {
                            cell: 'py-4 px-4',
                            row: 'h-16',
                            table: 'table-spacious'
                        };
                    }
                default:
                    {
                        return {
                            cell: 'py-2 px-3',
                            row: 'h-12',
                            table: 'table-comfortable'
                        };
                    }
            }
        }
    }["useUiPreferences.useCallback[getTableDensityClasses]"], [
        tableDensity
    ]);
    /**
   * Get dashboard layout CSS classes
   */ const getDashboardLayoutClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[getDashboardLayoutClasses]": ()=>{
            switch(dashboardLayout){
                case 'cards':
                    {
                        return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';
                    }
                case 'grid':
                    {
                        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
                    }
                case 'list':
                    {
                        return 'flex flex-col space-y-4';
                    }
                default:
                    {
                        return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';
                    }
            }
        }
    }["useUiPreferences.useCallback[getDashboardLayoutClasses]"], [
        dashboardLayout
    ]);
    /**
   * Enable notifications
   */ const enableNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[enableNotifications]": ()=>{
            if (!notificationsEnabled) {
                toggleNotifications();
            }
        }
    }["useUiPreferences.useCallback[enableNotifications]"], [
        notificationsEnabled,
        toggleNotifications
    ]);
    /**
   * Disable notifications
   */ const disableNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[disableNotifications]": ()=>{
            if (notificationsEnabled) {
                toggleNotifications();
            }
        }
    }["useUiPreferences.useCallback[disableNotifications]"], [
        notificationsEnabled,
        toggleNotifications
    ]);
    /**
   * Reset all preferences to defaults
   */ const resetPreferences = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[resetPreferences]": ()=>{
            setFontSize('medium');
            setTableDensity('comfortable');
            setMapViewPreference('roadmap');
            setDashboardLayout('cards');
            setAutoRefreshInterval(30);
        // Note: We don't reset notifications as that's a user choice
        }
    }["useUiPreferences.useCallback[resetPreferences]"], [
        setFontSize,
        setTableDensity,
        setMapViewPreference,
        setDashboardLayout,
        setAutoRefreshInterval
    ]);
    /**
   * Get all preferences as an object
   */ const getAllPreferences = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useUiPreferences.useCallback[getAllPreferences]": ()=>{
            return {
                autoRefreshInterval,
                dashboardLayout,
                fontSize,
                mapViewPreference,
                notificationsEnabled,
                tableDensity
            };
        }
    }["useUiPreferences.useCallback[getAllPreferences]"], [
        fontSize,
        notificationsEnabled,
        tableDensity,
        mapViewPreference,
        dashboardLayout,
        autoRefreshInterval
    ]);
    return {
        // Auto-refresh preferences
        autoRefreshInterval,
        // Dashboard preferences
        dashboardLayout,
        disableNotifications,
        enableNotifications,
        // Font size
        fontSize,
        getAllPreferences,
        getDashboardLayoutClasses,
        getFontSizeClass,
        getTableDensityClasses,
        // Map preferences
        mapViewPreference,
        // Notifications
        notificationsEnabled,
        // Utilities
        resetPreferences,
        setAutoRefreshInterval,
        setDashboardLayout,
        setFontSize,
        setMapViewPreference,
        setTableDensity,
        // Table preferences
        tableDensity,
        toggleNotifications
    };
};
_s(useUiPreferences, "JIBIIHN/8B6ke4/m1wWDUcGfjAg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/ui/useModal.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Modal management hook using Zustand UiStore
 * @module hooks/useModal
 */ __turbopack_context__.s({
    "useModal": (()=>useModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/uiStore.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
const useModal = ()=>{
    _s();
    const isModalOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useModal.useUiStore[isModalOpen]": (state)=>state.isModalOpen
    }["useModal.useUiStore[isModalOpen]"]);
    const modalContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useModal.useUiStore[modalContent]": (state)=>state.modalContent
    }["useModal.useUiStore[modalContent]"]);
    const openModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useModal.useUiStore[openModal]": (state)=>state.openModal
    }["useModal.useUiStore[openModal]"]);
    const closeModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"])({
        "useModal.useUiStore[closeModal]": (state)=>state.closeModal
    }["useModal.useUiStore[closeModal]"]);
    /**
   * Open login modal
   */ const openLoginModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openLoginModal]": ()=>{
            openModal('login');
        }
    }["useModal.useCallback[openLoginModal]"], [
        openModal
    ]);
    /**
   * Open signup modal
   */ const openSignupModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openSignupModal]": ()=>{
            openModal('signup');
        }
    }["useModal.useCallback[openSignupModal]"], [
        openModal
    ]);
    /**
   * Open settings modal
   */ const openSettingsModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openSettingsModal]": ()=>{
            openModal('settings');
        }
    }["useModal.useCallback[openSettingsModal]"], [
        openModal
    ]);
    /**
   * Open delegation form modal
   */ const openDelegationFormModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openDelegationFormModal]": ()=>{
            openModal('delegation-form');
        }
    }["useModal.useCallback[openDelegationFormModal]"], [
        openModal
    ]);
    /**
   * Open vehicle details modal
   */ const openVehicleDetailsModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openVehicleDetailsModal]": ()=>{
            openModal('vehicle-details');
        }
    }["useModal.useCallback[openVehicleDetailsModal]"], [
        openModal
    ]);
    /**
   * Open task assignment modal
   */ const openTaskAssignmentModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openTaskAssignmentModal]": ()=>{
            openModal('task-assignment');
        }
    }["useModal.useCallback[openTaskAssignmentModal]"], [
        openModal
    ]);
    /**
   * Open employee profile modal
   */ const openEmployeeProfileModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[openEmployeeProfileModal]": ()=>{
            openModal('employee-profile');
        }
    }["useModal.useCallback[openEmployeeProfileModal]"], [
        openModal
    ]);
    /**
   * Check if a specific modal is open
   */ const isModalOfTypeOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[isModalOfTypeOpen]": (type)=>{
            return isModalOpen && modalContent === type;
        }
    }["useModal.useCallback[isModalOfTypeOpen]"], [
        isModalOpen,
        modalContent
    ]);
    /**
   * Get modal-specific CSS classes
   */ const getModalClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[getModalClasses]": ()=>{
            return {
                backdrop: 'modal-backdrop',
                container: isModalOpen ? 'modal-container-visible' : 'modal-container-hidden',
                content: `modal-content modal-content-${modalContent || 'default'}`,
                overlay: isModalOpen ? 'modal-overlay-visible' : 'modal-overlay-hidden'
            };
        }
    }["useModal.useCallback[getModalClasses]"], [
        isModalOpen,
        modalContent
    ]);
    /**
   * Get modal accessibility attributes
   */ const getModalAriaAttributes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[getModalAriaAttributes]": ()=>{
            return {
                'aria-describedby': modalContent ? `${modalContent}-modal-description` : undefined,
                'aria-hidden': !isModalOpen,
                'aria-labelledby': modalContent ? `${modalContent}-modal-title` : undefined,
                'aria-modal': isModalOpen,
                role: 'dialog'
            };
        }
    }["useModal.useCallback[getModalAriaAttributes]"], [
        isModalOpen,
        modalContent
    ]);
    /**
   * Handle escape key press to close modal
   */ const handleEscapeKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[handleEscapeKey]": (event)=>{
            if (event.key === 'Escape' && isModalOpen) {
                closeModal();
            }
        }
    }["useModal.useCallback[handleEscapeKey]"], [
        isModalOpen,
        closeModal
    ]);
    /**
   * Handle backdrop click to close modal
   */ const handleBackdropClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[handleBackdropClick]": (event)=>{
            if (event.target === event.currentTarget && isModalOpen) {
                closeModal();
            }
        }
    }["useModal.useCallback[handleBackdropClick]"], [
        isModalOpen,
        closeModal
    ]);
    /**
   * Get modal title based on content type
   */ const getModalTitle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[getModalTitle]": ()=>{
            switch(modalContent){
                case 'delegation-form':
                    {
                        return 'Create Delegation';
                    }
                case 'employee-profile':
                    {
                        return 'Employee Profile';
                    }
                case 'login':
                    {
                        return 'Sign In';
                    }
                case 'settings':
                    {
                        return 'Settings';
                    }
                case 'signup':
                    {
                        return 'Create Account';
                    }
                case 'task-assignment':
                    {
                        return 'Assign Task';
                    }
                case 'vehicle-details':
                    {
                        return 'Vehicle Details';
                    }
                default:
                    {
                        return 'Modal';
                    }
            }
        }
    }["useModal.useCallback[getModalTitle]"], [
        modalContent
    ]);
    /**
   * Check if modal content is WorkHub-specific
   */ const isWorkHubModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useModal.useCallback[isWorkHubModal]": ()=>{
            return [
                'delegation-form',
                'employee-profile',
                'task-assignment',
                'vehicle-details'
            ].includes(modalContent || '');
        }
    }["useModal.useCallback[isWorkHubModal]"], [
        modalContent
    ]);
    return {
        closeModal,
        getModalAriaAttributes,
        getModalClasses,
        getModalTitle,
        handleBackdropClick,
        handleEscapeKey,
        // Utilities
        isModalOfTypeOpen,
        // State
        isModalOpen,
        isWorkHubModal: isWorkHubModal(),
        modalContent,
        openDelegationFormModal,
        openEmployeeProfileModal,
        openLoginModal,
        // Actions
        openModal,
        openSettingsModal,
        openSignupModal,
        openTaskAssignmentModal,
        openVehicleDetailsModal
    };
};
_s(useModal, "vYl/HAfCRCuA7Xmin4pHLd5dDHA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiStore"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/utils/use-toast.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "reducer": (()=>reducer),
    "toast": (()=>toast),
    "useToast": (()=>useToast)
});
// Inspired by react-hot-toast library
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 1_000_000;
const actionTypes = {
    ADD_TOAST: 'ADD_TOAST',
    DISMISS_TOAST: 'DISMISS_TOAST',
    REMOVE_TOAST: 'REMOVE_TOAST',
    UPDATE_TOAST: 'UPDATE_TOAST'
};
let count = 0;
function genId() {
    count = (count + 1) % Number.MAX_SAFE_INTEGER;
    return count.toString();
}
const toastTimeouts = new Map();
const addToRemoveQueue = (toastId)=>{
    if (toastTimeouts.has(toastId)) {
        return;
    }
    const timeout = setTimeout(()=>{
        toastTimeouts.delete(toastId);
        dispatch({
            toastId: toastId,
            type: 'REMOVE_TOAST'
        });
    }, TOAST_REMOVE_DELAY);
    toastTimeouts.set(toastId, timeout);
};
const reducer = (state, action)=>{
    switch(action.type){
        case 'ADD_TOAST':
            {
                return {
                    ...state,
                    toasts: [
                        action.toast,
                        ...state.toasts
                    ].slice(0, TOAST_LIMIT)
                };
            }
        case 'DISMISS_TOAST':
            {
                const { toastId } = action;
                // ! Side effects ! - This could be extracted into a dismissToast() action,
                // but I'll keep it here for simplicity
                if (toastId) {
                    addToRemoveQueue(toastId);
                } else {
                    for (const toast of state.toasts){
                        addToRemoveQueue(toast.id);
                    }
                }
                return {
                    ...state,
                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {
                            ...t,
                            open: false
                        } : t)
                };
            }
        case 'REMOVE_TOAST':
            {
                if (action.toastId === undefined) {
                    return {
                        ...state,
                        toasts: []
                    };
                }
                return {
                    ...state,
                    toasts: state.toasts.filter((t)=>t.id !== action.toastId)
                };
            }
        case 'UPDATE_TOAST':
            {
                return {
                    ...state,
                    toasts: state.toasts.map((t)=>t.id === action.toast.id ? {
                            ...t,
                            ...action.toast
                        } : t)
                };
            }
    }
};
const listeners = [];
let memoryState = {
    toasts: []
};
function dispatch(action) {
    memoryState = reducer(memoryState, action);
    for (const listener of listeners){
        listener(memoryState);
    }
}
function toast({ ...props }) {
    const id = genId();
    const update = (props)=>dispatch({
            toast: {
                ...props,
                id
            },
            type: 'UPDATE_TOAST'
        });
    const dismiss = ()=>dispatch({
            toastId: id,
            type: 'DISMISS_TOAST'
        });
    dispatch({
        toast: {
            ...props,
            id,
            onOpenChange: (open)=>{
                if (!open) dismiss();
            },
            open: true
        },
        type: 'ADD_TOAST'
    });
    return {
        dismiss,
        id: id,
        update
    };
}
function useToast() {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(memoryState);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useToast.useEffect": ()=>{
            listeners.push(setState);
            return ({
                "useToast.useEffect": ()=>{
                    const index = listeners.indexOf(setState);
                    if (index !== -1) {
                        listeners.splice(index, 1);
                    }
                }
            })["useToast.useEffect"];
        }
    }["useToast.useEffect"], [
        state
    ]);
    return {
        ...state,
        dismiss: (toastId)=>dispatch({
                type: 'DISMISS_TOAST',
                ...toastId && {
                    toastId
                }
            }),
        toast
    };
}
_s(useToast, "SPWE98mLGnlsnNfIwu/IAKTSZtk=");
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/layout-client.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Client Layout Component with CSP Support
 * @module app/layout-client
 *
 * Client component containing the original layout logic with CSP nonce support.
 * Follows 2025 security standards for script loading and CSP compliance.
 */ __turbopack_context__.s({
    "default": (()=>ClientLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/auth/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/ProtectedRoute.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$AppLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/AppLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$settings$2f$SettingsModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/settings/SettingsModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/theme-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$NotificationDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/NotificationDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/toaster.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$QuickAccessFab$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/QuickAccessFab.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useUiPreferences.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queryClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-client] (ecmascript)");
// Debug components imports removed for cleaner UI
// import { TokenRefreshDebug } from '@/components/debug/TokenRefreshDebug';
// import { EnhancedCSPDebug } from '@/components/debug/EnhancedCSPDebug';
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/CSPProvider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function ClientLayout({ children }) {
    _s();
    const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNonce"])();
    const reportViolation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCSPReporting"])();
    // Initialize CSP violation reporting
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientLayout.useEffect": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initializeCSPViolationReporting"])(reportViolation);
        }
    }["ClientLayout.useEffect"], [
        reportViolation
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        attribute: "class",
        defaultTheme: "system",
        disableTransitionOnChange: true,
        enableSystem: true,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
            client: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["queryClient"],
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SecurityConfigProvider"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProtectedLayoutWrapper, {
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/app/layout-client.tsx",
                            lineNumber: 69,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout-client.tsx",
                        lineNumber: 68,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$NotificationDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NotificationDisplay"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 74,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$settings$2f$SettingsModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SettingsModal"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$QuickAccessFab$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuickAccessFab"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/layout-client.tsx",
            lineNumber: 66,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout-client.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
_s(ClientLayout, "NgXXpzdkcwVWIIzHif+RiWkwomw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNonce"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCSPReporting"]
    ];
});
_c = ClientLayout;
/**
 * Protected Layout Wrapper Component
 *
 * This component determines which routes require authentication.
 * Public routes (like auth-test) are excluded from protection.
 * Also handles initial data prefetching when authentication is ready.
 */ function ProtectedLayoutWrapper({ children }) {
    _s1();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const { isInitialized, loading, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthContext"])();
    // Get UI preferences from Zustand stores (but not theme to avoid hydration issues)
    const { getFontSizeClass } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiPreferences"])();
    // Define public routes that don't require authentication
    const publicRoutes = [
        '/auth-test',
        '/supabase-diagnostics',
        '/login'
    ];
    // Define auth routes that should have no layout wrapper
    const authRoutes = [
        '/login'
    ];
    // Check if current route is public
    const isPublicRoute = publicRoutes.some((route)=>pathname?.startsWith(route));
    // Check if current route is an auth route (no layout needed)
    const isAuthRoute = authRoutes.some((route)=>pathname?.startsWith(route));
    // Determine if authentication system is ready for API calls
    // CRITICAL: Must include user check to prevent API calls before authentication
    // MEMOIZED to prevent infinite re-renders caused by user object reference changes
    const isAuthReady = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ProtectedLayoutWrapper.useMemo[isAuthReady]": ()=>{
            return isInitialized && !loading && !!user;
        }
    }["ProtectedLayoutWrapper.useMemo[isAuthReady]"], [
        isInitialized,
        loading,
        user?.id
    ]); // Use user.id instead of user object to prevent reference issues
    // Trigger initial data prefetching when authentication is ready and on dashboard
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProtectedLayoutWrapper.useEffect": ()=>{
            if (isAuthReady && pathname === '/') {
                console.log('Authentication ready, triggering dashboard data prefetch.');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prefetchUtils"].prefetchDashboardData(isAuthReady).catch({
                    "ProtectedLayoutWrapper.useEffect": (error)=>{
                        console.warn('Failed to prefetch dashboard data:', error);
                    }
                }["ProtectedLayoutWrapper.useEffect"]);
            }
        }
    }["ProtectedLayoutWrapper.useEffect"], [
        isAuthReady,
        pathname
    ]);
    // If it's an auth route, render without any layout wrapper
    if (isAuthRoute) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                children,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 138,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true);
    }
    // If it's a public route, render without protection but with layout
    if (isPublicRoute) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `app-layout ${getFontSizeClass()}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$AppLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppLayout"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 148,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                    className: "no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "© ",
                            new Date().getFullYear(),
                            " WorkHub. All rights reserved."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/layout-client.tsx",
                        lineNumber: 150,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 149,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/layout-client.tsx",
            lineNumber: 146,
            columnNumber: 7
        }, this);
    }
    // For all other routes, require authentication
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `app-layout ${getFontSizeClass()}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProtectedRoute"], {
            requireEmailVerification: true,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$AppLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AppLayout"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 160,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                    className: "no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "© ",
                            new Date().getFullYear(),
                            " WorkHub. All rights reserved."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/layout-client.tsx",
                        lineNumber: 163,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/layout-client.tsx",
            lineNumber: 159,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout-client.tsx",
        lineNumber: 158,
        columnNumber: 5
    }, this);
}
_s1(ProtectedLayoutWrapper, "f4fuZH0o7VB9ZzEHbcWAc+KP09c=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthContext"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUiPreferences"]
    ];
});
_c1 = ProtectedLayoutWrapper;
var _c, _c1;
__turbopack_context__.k.register(_c, "ClientLayout");
__turbopack_context__.k.register(_c1, "ProtectedLayoutWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_77cb818d._.js.map