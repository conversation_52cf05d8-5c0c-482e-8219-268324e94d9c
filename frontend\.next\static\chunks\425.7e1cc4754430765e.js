"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425],{30425:(t,e,a)=>{a.d(e,{aN:()=>d,adminService:()=>u});var i=a(25982),n=a(75908);let r={fromApi:t=>({action:t.action||"",details:t.details||"",id:t.id||"",timestamp:new Date(t.created_at||t.timestamp||new Date),userId:t.user_id||t.userId||t.auth_user_id||"",auth_user_id:t.auth_user_id||"",auth_user:t.auth_user||null}),toApi:t=>t};class s extends i.v{async getByAction(t,e){return(await this.getAll({...e,action:t})).data}async getByDateRange(t,e,a){let i={endDate:e.toISOString(),startDate:t.toISOString(),...a};return this.getAll(i)}async getByUserId(t,e){return(await this.getAll({...e,userId:t})).data}constructor(t,e){super(t,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...e}),this.endpoint="/admin/audit-logs",this.transformer=r}}let c={fromApi:t=>{var e,a;let i=(null==(e=t.users)?void 0:e[0])||{};return{created_at:t.created_at||"",email:i.email||t.email||"",email_confirmed_at:i.email_confirmed_at||t.email_confirmed_at||null,employee_id:t.employee_id||null,full_name:t.full_name||t.name||"",id:t.id,isActive:null==(a=t.is_active)||a,last_sign_in_at:t.last_sign_in_at||null,phone:t.phone||null,phone_confirmed_at:t.phone_confirmed_at||null,role:t.role||"USER",updated_at:t.updated_at||"",users:t.users}},toApi:t=>t};class l extends i.v{async getAll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams(t).toString(),a="".concat(this.endpoint,"?").concat(e),i="".concat(this.endpoint,":getAll:").concat(JSON.stringify(t));return this.executeWithInfrastructure(i,async()=>{var e,i;let n=await this.apiClient.get(a),r=(null==(e=n.data)?void 0:e.data)||[],s=(null==(i=n.data)?void 0:i.pagination)||{hasNext:!1,hasPrevious:!1,limit:t.limit||10,page:1,total:0,totalPages:0};return{data:r.map(t=>this.transformer.fromApi?this.transformer.fromApi(t):t),pagination:s}})}async getUsersByRole(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(await this.getAll({...e,role:t})).data}async toggleActivation(t,e){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch("".concat(this.endpoint,"/").concat(t,"/toggle-activation"),{isActive:e});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(t)),a})}constructor(t,e){super(t,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...e}),this.endpoint="/admin/users",this.transformer=c}}let o={fromApi:t=>t,toApi:t=>t};class h extends i.v{get cacheUtils(){return{clearAll:()=>this.clearCache(),forceRefreshHealth:async()=>(this.cache.invalidate("admin:health"),this.getSystemHealthStatus()),forceRefreshPerformance:async()=>(this.cache.invalidate("admin:performance"),this.getPerformanceMetrics()),getStats:()=>this.cache.getStats(),invalidateAll:()=>this.cache.invalidatePattern(/^admin:/)}}async createAuditLog(t){return this.executeWithInfrastructure(null,async()=>{let e=await this.apiClient.post("/admin/audit-logs",t);return this.cache.invalidatePattern(/^admin:audit:/),e})}async createUser(t){return this.executeWithInfrastructure(null,async()=>{let e=await this.apiClient.post("/admin/users",t);return this.cache.invalidatePattern(/^admin:users:/),e})}async deleteUser(t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("/admin/users/".concat(t)),this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(t))})}async getAllUsers(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.userService.getAll(t)}async getAuditLogs(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.auditService.getAll(t)}getMockHealthStatus(){return{services:{api:{responseTime:23,status:"healthy"},cache:{responseTime:12,status:"healthy"},database:{responseTime:45,status:"healthy"}},status:"healthy",timestamp:new Date().toISOString(),uptime:3600}}getMockPerformanceMetrics(){return{cpu:{cores:4,usage:100*Math.random()},errors:{rate:5*Math.random(),total:Math.floor(500*Math.random())},memory:{percentage:100*Math.random(),total:8e3,used:8e3*Math.random()},requests:{averageResponseTime:1e3*Math.random(),perSecond:100*Math.random(),total:Math.floor(1e4*Math.random())},timestamp:new Date().toISOString()}}getMockRecentErrors(){let t=[{details:{component:"database",errorType:"timeout"},id:"1",level:"ERROR",message:"Database connection timeout",requestId:"req-456",source:"database.service.ts",stack:"Error: Connection timeout\n    at Database.connect...",timestamp:new Date().toISOString(),userId:"user123"},{details:{component:"system",metric:"memory"},id:"2",level:"WARNING",message:"High memory usage detected",requestId:"req-789",source:"monitoring.service.ts",timestamp:new Date(Date.now()-3e5).toISOString()}];return{data:t,pagination:{hasNext:!1,hasPrevious:!1,limit:10,page:1,total:t.length,totalPages:Math.ceil(t.length/10)}}}async getPerformanceMetrics(){return this.executeWithInfrastructure("admin:performance",async()=>this.apiClient.get("/admin/performance"))}async getRecentErrors(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,i=new URLSearchParams;i.append("page",t.toString()),i.append("limit",e.toString()),a&&i.append("level",a);let n="/admin/logs/errors?".concat(i.toString()),r="admin:errors:".concat(t,":").concat(e,":").concat(a||"all");return this.executeWithInfrastructure(r,async()=>this.apiClient.get(n))}async getSystemHealthStatus(){return this.executeWithInfrastructure("admin:health",async()=>this.apiClient.get("/admin/health"))}async toggleUserActivation(t,e){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch("/admin/users/".concat(t,"/toggle-activation"),{isActive:e});return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(t)),a})}async updateUser(t,e){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.put("/admin/users/".concat(t),e);return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(t)),a})}constructor(t,e){let a=t||n.cl.getApiClient();super(a,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...e}),this.endpoint="/admin",this.transformer=o,this.auditService=new s(a),this.userService=new l(a)}}let u=new h,d=u.cacheUtils}}]);