# User Management Data Loading Investigation Plan

## Overview
This comprehensive plan addresses the issue of user data not loading in the User Management page. The investigation covers all potential failure points from the client-side UI to the database layer.

## Architecture Context
- **Frontend**: React/Next.js with TypeScript
- **Backend**: Node.js/Express with Prisma ORM
- **Database**: PostgreSQL via Supabase
- **Authentication**: Supabase Auth with JWT tokens
- **User Management Component**: `frontend/src/components/features/admin/UserManagement.tsx`
- **Backend Service**: `backend/src/services/userManagement.service.ts`
- **API Endpoint**: `/api/admin/users`

---

## Phase 1: Immediate Diagnostics (5-10 minutes)

### 1.1 Client-Side Browser Investigation

#### Browser Console Errors
```bash
# Run the debug script in browser console on User Management page
# Copy and paste frontend/src/debug-auth.js content
```

**Expected Checks:**
- JavaScript errors or exceptions
- Network request failures (failed API calls)
- Authentication token presence and validity
- React component state errors
- CORS issues

#### Network Tab Analysis
```bash
# In browser DevTools Network tab:
1. Refresh the User Management page
2. Look for the /api/admin/users request
3. Check response status (200, 401, 403, 500, etc.)
4. Examine request headers (Authorization token)
5. Review response body for error messages
```

#### Local Storage & Cookies
```bash
# In browser console:
console.log('Supabase session:', localStorage.getItem('sb-' + window.location.hostname.replace(/\./g, '-') + '-auth-token'));
console.log('Auth cookies:', document.cookie);
```

### 1.2 Quick Backend Health Check

#### API Endpoint Test
```bash
# Test direct API call (replace with actual backend URL)
curl -X GET "http://localhost:3001/api/admin/users" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### Backend Service Status
```bash
# Check if backend is running
curl -X GET "http://localhost:3001/api/health"
```

---

## Phase 2: Application Server Logs Analysis (10-15 minutes)

### 2.1 Backend Log Examination

#### Primary Log Locations
```bash
# Navigate to backend logs directory
cd backend/logs/

# Check recent error logs
tail -n 50 error.log

# Check combined application logs
tail -n 100 combined.log | grep -i "user\|admin\|error"

# Check security logs for auth issues
tail -n 50 security.log
```

#### Specific Log Patterns to Search For
```bash
# Authentication failures
grep -i "authentication\|auth.*failed\|invalid.*token" combined.log

# User management service errors
grep -i "userManagement-service" combined.log

# Database connection issues
grep -i "database\|prisma\|supabase.*error" combined.log

# Admin route access
grep -i "admin.*route" combined.log
```

#### Docker Container Logs (if using Docker)
```bash
# Backend container logs
docker logs workhub-backend --tail 100 --follow

# Database container logs (if local)
docker logs workhub-postgres --tail 50
```

### 2.2 Application Metrics

#### Memory and Performance
```bash
# Check system resources
free -h
df -h
top -p $(pgrep -f "node.*backend")
```

#### Process Status
```bash
# Verify backend process is running
ps aux | grep -i "node.*backend"
netstat -tlnp | grep :3001
```

---

## Phase 3: Database Connectivity & Query Performance (15-20 minutes)

### 3.1 Database Connection Verification

#### Supabase Connection Test
```javascript
// Run in backend environment or create test script
const { supabaseAdmin } = require('./src/lib/supabase.js');

async function testConnection() {
  try {
    const { data, error } = await supabaseAdmin.auth.admin.listUsers({
      page: 1,
      perPage: 1
    });
    console.log('Connection successful:', !!data, 'Error:', error);
  } catch (err) {
    console.error('Connection failed:', err.message);
  }
}
testConnection();
```

#### Database Environment Variables
```bash
# Verify environment variables are set
echo "DATABASE_URL: ${DATABASE_URL:+SET}"
echo "SUPABASE_URL: ${SUPABASE_URL:+SET}"
echo "SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY:+SET}"
```

### 3.2 User Table Integrity Checks

#### Direct Database Queries
```sql
-- Connect to your database and run these queries

-- Check auth.users table structure and data
SELECT COUNT(*) as total_users FROM auth.users;
SELECT id, email, created_at, email_confirmed_at, user_metadata 
FROM auth.users 
ORDER BY created_at DESC 
LIMIT 5;

-- Check user_profiles table
SELECT COUNT(*) as total_profiles FROM public.user_profiles;
SELECT id, role, is_active, employee_id, created_at 
FROM public.user_profiles 
ORDER BY created_at DESC 
LIMIT 5;

-- Check for orphaned records
SELECT COUNT(*) as orphaned_profiles 
FROM public.user_profiles up 
LEFT JOIN auth.users au ON up.id = au.id 
WHERE au.id IS NULL;
```

#### Row Level Security (RLS) Verification
```sql
-- Check if RLS is enabled on relevant tables
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('users', 'user_profiles');

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('users', 'user_profiles');
```

### 3.3 Query Performance Analysis

#### User Query Performance Test
```javascript
// Create performance test script
const { performance } = require('perf_hooks');
const { getAllUsers } = require('./backend/src/services/userManagement.service.js');

async function testUserQuery() {
  const start = performance.now();
  try {
    const result = await getAllUsers(1, 10);
    const end = performance.now();
    console.log(`Query completed in ${end - start}ms`);
    console.log(`Returned ${result.data.length} users`);
  } catch (error) {
    console.error('Query failed:', error.message);
  }
}
testUserQuery();
```

---

## Phase 4: Network Latency & API Endpoint Status (10 minutes)

### 4.1 Network Connectivity Tests

#### API Response Time
```bash
# Test API response time
time curl -X GET "http://localhost:3001/api/admin/users" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -w "Time: %{time_total}s\nStatus: %{http_code}\n"
```

#### Network Path Analysis
```bash
# If backend is remote
traceroute your-backend-domain.com
ping -c 5 your-backend-domain.com
```

### 4.2 Load Balancer & Proxy Checks

#### Nginx Configuration (if applicable)
```bash
# Check nginx status and configuration
sudo nginx -t
sudo systemctl status nginx

# Check nginx logs
sudo tail -n 50 /var/log/nginx/error.log
sudo tail -n 50 /var/log/nginx/access.log | grep "/api/admin/users"
```

#### SSL/TLS Issues
```bash
# Check SSL certificate
curl -I https://your-domain.com/api/admin/users
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

---

## Phase 5: Configuration Files Analysis (10 minutes)

### 5.1 Backend Configuration

#### Environment Configuration
```bash
# Check critical environment variables
cd backend/
cat .env | grep -E "(DATABASE_URL|SUPABASE|JWT|AUTH)"
```

#### Middleware Configuration
```javascript
// Check these files for issues:
// - backend/src/middleware/jwtAuth.middleware.js
// - backend/src/middleware/supabaseAuth.js
// - backend/src/config/secrets.ts
```

### 5.2 Frontend Configuration

#### API Client Configuration
```typescript
// Check these files:
// - frontend/src/lib/api/services/admin/adminService.ts
// - frontend/src/lib/supabase.ts
// - frontend/src/contexts/AuthContext.tsx
```

#### Build Configuration
```bash
# Check if build is up to date
cd frontend/
npm run build 2>&1 | grep -i error
```

---

## Phase 6: Recent System Changes Analysis (15 minutes)

### 6.1 Git History Analysis

#### Recent Commits
```bash
# Check recent changes to user management files
git log --oneline --since="7 days ago" -- \
  "backend/src/services/userManagement.service.ts" \
  "frontend/src/components/features/admin/UserManagement.tsx" \
  "backend/src/modules/admin/routes/admin.routes.ts"

# Check recent migrations
git log --oneline --since="7 days ago" -- "backend/prisma/migrations/"
```

#### Deployment History
```bash
# Check recent deployments
git log --oneline --since="7 days ago" --grep="deploy\|release\|prod"
```

### 6.2 Database Migration Issues

#### Migration Status
```bash
cd backend/
npx prisma migrate status
npx prisma db execute --file prisma/migrations/migration_lock.toml
```

#### Recent Migration Analysis
```sql
-- Check the latest migration
SELECT * FROM public._prisma_migrations 
ORDER BY finished_at DESC 
LIMIT 5;
```

---

## Phase 7: Authentication Deep Dive (10 minutes)

### 7.1 JWT Token Validation

#### Token Decoding
```javascript
// Use the decode-jwt.js script in project root
node decode-jwt.js "YOUR_JWT_TOKEN"
```

#### Supabase Auth Status
```bash
# Check Supabase project status (if using hosted Supabase)
curl -X GET "https://your-project.supabase.co/rest/v1/auth/user" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "apikey: YOUR_ANON_KEY"
```

### 7.2 Role-Based Access Control

#### User Role Verification
```sql
-- Check user roles and permissions
SELECT 
  au.email,
  au.user_metadata->>'role' as auth_role,
  up.role as profile_role,
  up.is_active
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
WHERE au.email = '<EMAIL>';
```

---

## Phase 8: Recovery Actions

### 8.1 Immediate Fixes

#### Clear Authentication Cache
```javascript
// Run in browser console
localStorage.clear();
sessionStorage.clear();
location.reload();
```

#### Restart Backend Service
```bash
# If using PM2
pm2 restart workhub-backend

# If using Docker
docker restart workhub-backend

# If running directly
pkill -f "node.*backend" && npm run start
```

### 8.2 Database Recovery

#### Reset User Sessions
```sql
-- Clear invalid sessions (use with caution)
DELETE FROM auth.sessions WHERE expires_at < NOW();
```

#### Rebuild User Profiles
```sql
-- Sync user_profiles with auth.users if needed
INSERT INTO public.user_profiles (id, role, is_active)
SELECT id, 'USER', true
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.user_profiles);
```

---

## Escalation Criteria

### Level 1: Development Team
- Frontend errors or component issues
- API endpoint configuration problems
- Authentication token issues

### Level 2: DevOps/Infrastructure
- Database connectivity issues
- Server resource problems
- Network/DNS issues

### Level 3: Database Administrator
- Database corruption
- Major schema issues
- Performance degradation

---

## Prevention Measures

### 1. Monitoring Setup
```bash
# Implement health checks
curl -X GET "/api/admin/health" every 5 minutes
```

### 2. Logging Enhancement
```javascript
// Add more detailed logging to userManagement.service.ts
logger.info('User fetch started', { 
  requestId, 
  userId, 
  timestamp: new Date().toISOString() 
});
```

### 3. Error Boundaries
```typescript
// Ensure UserManagement component has proper error handling
try {
  const response = await adminService.getAllUsers(params);
  setUsers(response.data);
} catch (error) {
  console.error('User fetch failed:', error);
  // Show user-friendly error message
}
```

---

## Documentation Updates

After resolving the issue, update:
1. Troubleshooting runbook
2. Monitoring alerts
3. Error handling documentation
4. Recovery procedures 