{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/app/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRouter } from 'next/navigation';\r\nimport React from 'react';\r\n\r\nimport { LoginForm } from '../../components/auth/loginForm';\r\nimport { AuthProvider } from '../../contexts/AuthContext';\r\n\r\n/**\r\n * Modern Login Page\r\n *\r\n * A dedicated page for user authentication featuring:\r\n * - Modern, minimalist design\r\n * - Responsive layout for all devices\r\n * - Enhanced user experience\r\n * - Security-focused UI\r\n */\r\nexport default function LoginPage() {\r\n  const router = useRouter();\r\n\r\n  const handleLoginSuccess = () => {\r\n    // Redirect to dashboard after successful login\r\n    router.push('/');\r\n  };\r\n\r\n  const handleForgotPassword = () => {\r\n    // TODO: Implement forgot password flow\r\n    console.log('Forgot password clicked');\r\n    // router.push('/forgot-password');\r\n  };\r\n\r\n  const handleSignUp = () => {\r\n    // TODO: Implement sign up flow\r\n    console.log('Sign up clicked');\r\n    // router.push('/signup');\r\n  };\r\n\r\n  return (\r\n    <AuthProvider>\r\n      <LoginForm\r\n        onForgotPassword={handleForgotPassword}\r\n        onSignUp={handleSignUp}\r\n        onSuccess={handleLoginSuccess}\r\n      />\r\n    </AuthProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AANA;;;;;AAiBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,qBAAqB;QACzB,+CAA+C;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,uBAAuB;QAC3B,uCAAuC;QACvC,QAAQ,GAAG,CAAC;IACZ,mCAAmC;IACrC;IAEA,MAAM,eAAe;QACnB,+BAA+B;QAC/B,QAAQ,GAAG,CAAC;IACZ,0BAA0B;IAC5B;IAEA,qBACE,8OAAC,+HAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,uIAAA,CAAA,YAAS;YACR,kBAAkB;YAClB,UAAU;YACV,WAAW;;;;;;;;;;;AAInB", "debugId": null}}]}