(()=>{var e={};e.id=548,e.ids=[548],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,r,t)=>{"use strict";t.d(r,{O_:()=>a,t6:()=>i});var s=t(43210),o=t(49278);function i(){let e=(0,s.useCallback)((e,r)=>o.JP.success(e,r),[]),r=(0,s.useCallback)((e,r)=>o.JP.error(e,r),[]),t=(0,s.useCallback)((e,r)=>o.JP.info(e,r),[]),i=(0,s.useCallback)(r=>e(r?.successTitle||"Success",r?.successDescription||"Operation completed successfully"),[e]),a=(0,s.useCallback)((e,t)=>{let s=e instanceof Error?e.message:e;return r(t?.errorTitle||"Error",t?.errorDescription||s||"An unexpected error occurred")},[r]);return{showSuccess:e,showError:r,showInfo:t,showFormSuccess:i,showFormError:a}}function a(e){let r;switch(e){case"employee":r=t(49278).Ok;break;case"vehicle":r=t(49278).G7;break;case"task":r=t(49278).z0;break;case"delegation":r=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,r){let{showFormSuccess:t,showFormError:a}=i(),n=r||(e?(0,o.iw)(e):null),l=(0,s.useCallback)(e=>n?n.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,t]),u=(0,s.useCallback)(e=>n?n.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,t]),c=(0,s.useCallback)(e=>n?n.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,t]),d=(0,s.useCallback)(e=>{if(n){let r=e instanceof Error?e.message:e;return n.entityCreationError(r)}return a(e,{errorTitle:"Creation Failed"})},[n,a]);return{showEntityCreated:l,showEntityUpdated:u,showEntityDeleted:c,showEntityCreationError:d,showEntityUpdateError:(0,s.useCallback)(e=>{if(n){let r=e instanceof Error?e.message:e;return n.entityUpdateError(r)}return a(e,{errorTitle:"Update Failed"})},[n,a]),showEntityDeletionError:(0,s.useCallback)(e=>{if(n){let r=e instanceof Error?e.message:e;return n.entityDeletionError(r)}return a(e,{errorTitle:"Deletion Failed"})},[n,a]),showFormSuccess:t,showFormError:a}}(void 0,r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37999:(e,r,t)=>{Promise.resolve().then(t.bind(t,45425))},43820:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var s=t(65239),o=t(48088),i=t(88170),a=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let u={children:["",{children:["employees",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52578)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\add\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\add\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/employees/add/page",pathname:"/employees/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},45425:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687);let o=(0,t(82614).A)("UserRoundPlus",[["path",{d:"M2 21a8 8 0 0 1 13.292-6",key:"bjp14o"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M19 16v6",key:"tddt3s"}],["path",{d:"M22 19h-6",key:"vcuq98"}]]);var i=t(16189),a=t(43210),n=t(96342),l=t(48041),u=t(3940),c=t(19599);function d(){let e=(0,i.useRouter)(),[r,t]=(0,a.useState)(null),{showEntityCreated:d,showEntityCreationError:p,showFormError:m}=(0,u.O_)("employee"),f=(0,c.Ar)(),x=async r=>{t(null);try{await f.mutateAsync(r);let t={name:r.fullName||r.name};d(t),e.push("/employees")}catch(e){if(console.error("Error adding employee:",e),e.validationErrors&&Array.isArray(e.validationErrors)){let r=e.validationErrors.map(e=>`${e.path}: ${e.message}`).join("\n");console.log("Validation errors details:",r),t(`Validation failed: ${r}`),m("Please check the form for errors",{errorTitle:"Validation Error"})}else{let r=e.message||"Failed to add employee. Please try again.";t(r),p(r)}}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(l.z,{description:"Enter the details for the new employee.",icon:o,title:"Add New Employee"}),r&&(0,s.jsx)("div",{className:"rounded-md bg-destructive/20 p-3 text-sm text-destructive",children:r}),(0,s.jsx)(n.N,{isEditing:!1,isLoading:f.isPending,onSubmit:x})]})}},52578:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\employees\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\add\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84855:(e,r,t)=>{Promise.resolve().then(t.bind(t,52578))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,211,1658,8390,2670,9275,6013,101,7055,9599,5785,6342],()=>t(43820));module.exports=s})();