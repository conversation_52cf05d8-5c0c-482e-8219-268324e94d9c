{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__752a1ada._.js", "server/edge/chunks/edge-wrapper_14765a8d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1VkbR5tKYQ99mrouqtj1nHwaDMqQnxI8SWYlQA4xlo8=", "__NEXT_PREVIEW_MODE_ID": "19ebd024f594d56dbfe5260812219e22", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "db20b125a81b2daf7b0f1a6c895ffc490358c6feca2a91c0ec916ef83e24b159", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6ecec910a8a3e346be79c5692cec8d52d609d6a82a8d4aac7304a2bd3e861f20"}}}, "sortedMiddleware": ["/"], "functions": {}}