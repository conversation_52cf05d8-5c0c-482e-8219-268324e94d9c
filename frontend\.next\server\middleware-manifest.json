{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__752a1ada._.js", "server/edge/chunks/edge-wrapper_14765a8d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1VkbR5tKYQ99mrouqtj1nHwaDMqQnxI8SWYlQA4xlo8=", "__NEXT_PREVIEW_MODE_ID": "1bd33c7bba39ab5819b73a753d2023f5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7275a45fa97fe5b1ad7f9f546108d04d4d07a15c8192d04dfa21af9566e93060", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5a7e008fb385d6c956c39ad08e6ac8031302900c824b1db7ee13744a356c7212"}}}, "sortedMiddleware": ["/"], "functions": {}}