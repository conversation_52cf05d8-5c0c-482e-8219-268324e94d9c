(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425,3698],{4607:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},6548:(e,s,t)=>{"use strict";t.d(s,{Sk:()=>i,p9:()=>n});var a=t(28755),r=t(12115),l=t(40879);let i=function(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{toast:i}=(0,l.dj)(),{cacheDuration:n=3e5,enableRetry:c=!0,errorMessage:d,retryAttempts:o=3,showErrorToast:m=!0,showSuccessToast:x=!1,successMessage:h,...u}=t,p=(0,a.I)({gcTime:2*n,queryFn:s,queryKey:e,retry:!!c&&o,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:n,...u});return(0,r.useEffect)(()=>{x&&p.isSuccess&&p.data&&h&&i({description:h,title:"Success"})},[x,p.isSuccess,p.data,h,i]),(0,r.useEffect)(()=>{m&&p.isError&&i({description:d||(p.error instanceof Error?p.error.message:"An error occurred"),title:"Error",variant:"destructive"})},[m,p.isError,p.error,d,i]),{...p,forceRefresh:async()=>await p.refetch(),isStale:p.isStale||!1,lastUpdated:p.dataUpdatedAt||null}},n=function(e,s){var t,a,r;let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{keepPreviousData:n=!0,page:c=1,pageSize:d=10,...o}=l,m=i([...e,"paginated",c,d],()=>s(c,d),{...o,...n?{placeholderData:e=>e}:{}}),x=null==(t=m.data)?void 0:t.pagination;return{...m,currentPage:c,data:null!=(r=null==(a=m.data)?void 0:a.data)?r:[],goToPage:e=>{},hasNextPage:!!x&&x.hasNext,hasPrevPage:!!x&&x.hasPrevious,nextPage:()=>{x&&x.hasNext},pagination:null!=x?x:{hasNext:!1,hasPrevious:!1,limit:d,page:1,total:0,totalPages:1},prevPage:()=>{x&&x.hasPrevious},totalPages:x?x.totalPages:1}}},9572:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},10233:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},14636:(e,s,t)=>{"use strict";t.d(s,{AM:()=>n,Wv:()=>c,hl:()=>d});var a=t(95155),r=t(20547),l=t(12115),i=t(54036);let n=r.bL,c=r.l9;r.bm;let d=l.forwardRef((e,s)=>{let{align:t="center",className:l,sideOffset:n=4,...c}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{align:t,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",l),ref:s,sideOffset:n,...c})})});d.displayName=r.UC.displayName},15292:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},17313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>n});var a=t(95155),r=t(60704),l=t(12115),i=t(54036);let n=r.bL,c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.B8,{className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),ref:s,...l})});c.displayName=r.B8.displayName;let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.l9,{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),ref:s,...l})});d.displayName=r.l9.displayName;let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.UC,{className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),ref:s,...l})});o.displayName=r.UC.displayName},18046:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},19637:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},19968:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},21920:(e,s,t)=>{Promise.resolve().then(t.bind(t,36695))},29797:(e,s,t)=>{"use strict";t.d(s,{$o:()=>g,Eb:()=>u,Iu:()=>m,WA:()=>p,cU:()=>x,dK:()=>o,n$:()=>h});var a=t(95155),r=t(965),l=t(73158),i=t(3561),n=t(12115),c=t(30285),d=t(54036);let o=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex justify-center",t),ref:s,...r})});o.displayName="Pagination";let m=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("ul",{className:(0,d.cn)("flex flex-row items-center gap-1",t),ref:s,...r})});m.displayName="PaginationContent";let x=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("li",{className:(0,d.cn)("",t),ref:s,...r})});x.displayName="PaginationItem";let h=n.forwardRef((e,s)=>{let{className:t,isActive:r,...l}=e;return(0,a.jsx)(c.$,{"aria-current":r?"page":void 0,className:(0,d.cn)("h-9 w-9",t),ref:s,size:"icon",variant:r?"outline":"ghost",...l})});h.displayName="PaginationLink";let u=n.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsxs)(c.$,{className:(0,d.cn)("h-9 w-9 gap-1",t),ref:s,size:"icon",variant:"ghost",...l,children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous page"})]})});u.displayName="PaginationPrevious";let p=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsxs)(c.$,{className:(0,d.cn)("h-9 w-9 gap-1",t),ref:s,size:"icon",variant:"ghost",...r,children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Next page"})]})});p.displayName="PaginationNext";let f=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",t),ref:s,...r,children:[(0,a.jsx)(i.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]})});function g(e){let{className:s,currentPage:t,onPageChange:r,totalPages:l}=e,i=(()=>{let e=[];e.push(1);let s=Math.max(2,t-1),a=Math.min(l-1,t+1);s>2&&e.push("ellipsis1");for(let t=s;t<=a;t++)e.push(t);return a<l-1&&e.push("ellipsis2"),l>1&&e.push(l),e})();return l<=1?null:(0,a.jsx)(o,{className:s,children:(0,a.jsxs)(m,{children:[(0,a.jsx)(x,{children:(0,a.jsx)(u,{"aria-disabled":1===t?"true":void 0,"aria-label":"Go to previous page",disabled:1===t,onClick:()=>r(t-1)})}),i.map((e,s)=>"ellipsis1"===e||"ellipsis2"===e?(0,a.jsx)(x,{children:(0,a.jsx)(f,{})},"ellipsis-".concat(s)):(0,a.jsx)(x,{children:(0,a.jsx)(h,{"aria-label":"Go to page ".concat(e),isActive:t===e,onClick:()=>r(e),children:e})},"page-".concat(e))),(0,a.jsx)(x,{children:(0,a.jsx)(p,{"aria-disabled":t===l?"true":void 0,"aria-label":"Go to next page",disabled:t===l,onClick:()=>r(t+1)})})]})})}f.displayName="PaginationEllipsis"},30425:(e,s,t)=>{"use strict";t.d(s,{aN:()=>x,adminService:()=>m});var a=t(25982),r=t(75908);let l={fromApi:e=>({action:e.action||"",details:e.details||"",id:e.id||"",timestamp:new Date(e.created_at||e.timestamp||new Date),userId:e.user_id||e.userId||e.auth_user_id||"",auth_user_id:e.auth_user_id||"",auth_user:e.auth_user||null}),toApi:e=>e};class i extends a.v{async getByAction(e,s){return(await this.getAll({...s,action:e})).data}async getByDateRange(e,s,t){let a={endDate:s.toISOString(),startDate:e.toISOString(),...t};return this.getAll(a)}async getByUserId(e,s){return(await this.getAll({...s,userId:e})).data}constructor(e,s){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin/audit-logs",this.transformer=l}}let n={fromApi:e=>{var s,t,a,r,l;let i=(null==(t=e.users)||null==(s=t[0])?void 0:s.email)||e.email||"",n=(null==(r=e.users)||null==(a=r[0])?void 0:a.email_confirmed_at)||e.email_confirmed_at||null;return{created_at:e.created_at||"",email:i,email_confirmed_at:n,employee_id:e.employee_id||null,full_name:e.full_name||e.name||"",id:e.id,isActive:null==(l=e.is_active)||l,last_sign_in_at:e.last_sign_in_at||null,phone:e.phone||null,phone_confirmed_at:e.phone_confirmed_at||null,role:e.role||"USER",updated_at:e.updated_at||"",users:e.users}},toApi:e=>e};class c extends a.v{async getUsersByRole(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(await this.getAll({...s,role:e})).data}async toggleActivation(e,s){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/toggle-activation"),{isActive:s});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),this.transformer.fromApi?this.transformer.fromApi(t):t})}constructor(e,s){super(e,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin/users",this.transformer=n}}let d={fromApi:e=>e,toApi:e=>e};class o extends a.v{get cacheUtils(){return{clearAll:()=>this.clearCache(),forceRefreshHealth:async()=>(this.cache.invalidate("admin:health"),this.getSystemHealthStatus()),forceRefreshPerformance:async()=>(this.cache.invalidate("admin:performance"),this.getPerformanceMetrics()),getStats:()=>this.cache.getStats(),invalidateAll:()=>this.cache.invalidatePattern(/^admin:/)}}async createAuditLog(e){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/admin/audit-logs",e);return this.cache.invalidatePattern(/^admin:audit:/),s})}async createUser(e){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/admin/users",e);return this.cache.invalidatePattern(/^admin:users:/),s})}async deleteUser(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("/admin/users/".concat(e)),this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(e))})}async getAllUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.userService.getAll(e)}async getAuditLogs(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.auditService.getAll(e)}getMockHealthStatus(){return{services:{api:{responseTime:23,status:"healthy"},cache:{responseTime:12,status:"healthy"},database:{responseTime:45,status:"healthy"}},status:"healthy",timestamp:new Date().toISOString(),uptime:3600}}getMockPerformanceMetrics(){return{cpu:{cores:4,usage:100*Math.random()},errors:{rate:5*Math.random(),total:Math.floor(500*Math.random())},memory:{percentage:100*Math.random(),total:8e3,used:8e3*Math.random()},requests:{averageResponseTime:1e3*Math.random(),perSecond:100*Math.random(),total:Math.floor(1e4*Math.random())},timestamp:new Date().toISOString()}}getMockRecentErrors(){let e=[{details:{component:"database",errorType:"timeout"},id:"1",level:"ERROR",message:"Database connection timeout",requestId:"req-456",source:"database.service.ts",stack:"Error: Connection timeout\n    at Database.connect...",timestamp:new Date().toISOString(),userId:"user123"},{details:{component:"system",metric:"memory"},id:"2",level:"WARNING",message:"High memory usage detected",requestId:"req-789",source:"monitoring.service.ts",timestamp:new Date(Date.now()-3e5).toISOString()}];return{data:e,pagination:{hasNext:!1,hasPrevious:!1,limit:10,page:1,total:e.length,totalPages:Math.ceil(e.length/10)}}}async getPerformanceMetrics(){return this.executeWithInfrastructure("admin:performance",async()=>this.apiClient.get("/admin/performance"))}async getRecentErrors(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,t=arguments.length>2?arguments[2]:void 0,a=new URLSearchParams;a.append("page",e.toString()),a.append("limit",s.toString()),t&&a.append("level",t);let r="/admin/logs/errors?".concat(a.toString()),l="admin:errors:".concat(e,":").concat(s,":").concat(t||"all");return this.executeWithInfrastructure(l,async()=>this.apiClient.get(r))}async getSystemHealthStatus(){return this.executeWithInfrastructure("admin:health",async()=>this.apiClient.get("/admin/health"))}async toggleUserActivation(e,s){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.patch("/admin/users/".concat(e,"/toggle-activation"),{isActive:s});return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(e)),t})}async updateUser(e,s){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.put("/admin/users/".concat(e),s);return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(e)),t})}constructor(e,s){let t=e||r.cl.getApiClient();super(t,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin",this.transformer=d,this.auditService=new i(t),this.userService=new c(t)}}let m=new o,x=m.cacheUtils},31573:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},31896:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},32087:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},36695:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eA});var a=t(95155),r=t(18271),l=t(50594),i=t(45876),n=t(41784),c=t(75074),d=t(50172),o=t(51920),m=t(12115),x=t(30285),h=t(85511),u=t(62523),p=t(29797),f=t(14636),g=t(85127),j=t(40879),N=t(30425),v=t(54036);function y(){var e,s,t;let[r,l]=(0,m.useState)([]),[i,y]=(0,m.useState)(!0),[b,A]=(0,m.useState)(""),[w,k]=(0,m.useState)(""),[S,C]=(0,m.useState)(""),[E,R]=(0,m.useState)(),[z,_]=(0,m.useState)(),[M,D]=(0,m.useState)(1),[I,P]=(0,m.useState)(0),{toast:U}=(0,j.dj)(),L=Math.ceil(I/10),T=(0,m.useCallback)(async()=>{y(!0);try{let e=await N.adminService.getAuditLogs({action:w,endDate:z,limit:10,page:M,search:b,startDate:E,userId:S});l(e.data),P(e.pagination.total)}catch(s){var e;U({description:null!=(e=s.message)?e:"Failed to load audit log data.",title:"Error fetching audit logs",variant:"destructive"})}finally{y(!1)}},[M,b,w,S,E,z,U,I]);(0,m.useEffect)(()=>{T()},[T]);let F=e=>{D(e)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Audit Log Viewer"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-2 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(u.p,{className:"w-[250px] pl-8",onChange:e=>{A(e.target.value),D(1)},placeholder:"Search logs (user, action, details)...",value:b})]}),(0,a.jsx)(u.p,{className:"w-[200px]",onChange:e=>{k(e.target.value),D(1)},placeholder:"Filter by action (e.g., LOGIN)",value:w}),(0,a.jsx)(u.p,{className:"w-[200px]",onChange:e=>{C(e.target.value),D(1)},placeholder:"Filter by User ID",value:S}),(0,a.jsxs)(f.AM,{children:[(0,a.jsx)(f.Wv,{asChild:!0,children:(0,a.jsxs)(x.$,{className:(0,v.cn)("w-[280px] justify-start text-left font-normal",!E&&!z&&"text-muted-foreground"),variant:"outline",children:[(0,a.jsx)(o.A,{className:"mr-2 size-4"}),E?z?(0,a.jsxs)(a.Fragment,{children:[(0,n.GP)(E,"LLL dd, y")," -"," ",(0,n.GP)(z,"LLL dd, y")]}):(0,n.GP)(E,"LLL dd, y"):(0,a.jsx)("span",{children:"Pick a date range"})]})}),(0,a.jsx)(f.hl,{className:"flex w-auto p-0",children:(0,a.jsx)(h.V,{mode:"range",numberOfMonths:2,onSelect:e=>{R(null==e?void 0:e.from),_(null==e?void 0:e.to),D(1)},selected:{from:E,to:z}})})]}),(null!=(t=null!=(s=null!=(e=null!=E?E:z)?e:w)?s:S)?t:b)&&(0,a.jsx)(x.$,{onClick:()=>{A(""),k(""),C(""),R(void 0),_(void 0),D(1)},variant:"outline",children:"Clear Filters"})]})]}),i?(0,a.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,a.jsx)(d.A,{className:"size-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-lg",children:"Loading audit logs..."})]}):(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(g.XI,{children:[(0,a.jsx)(g.A0,{children:(0,a.jsxs)(g.Hj,{children:[(0,a.jsx)(g.nd,{className:"w-[100px]",children:"ID"}),(0,a.jsx)(g.nd,{children:"Timestamp"}),(0,a.jsx)(g.nd,{children:"User Email"}),(0,a.jsx)(g.nd,{children:"Action"}),(0,a.jsx)(g.nd,{children:"Details"}),(0,a.jsx)(g.nd,{children:"IP Address"})]})}),(0,a.jsx)(g.BF,{children:0===r.length?(0,a.jsx)(g.Hj,{children:(0,a.jsx)(g.nA,{className:"h-24 text-center",colSpan:6,children:"No audit logs found."})}):r.map(e=>(0,a.jsxs)(g.Hj,{children:[(0,a.jsx)(g.nA,{className:"font-medium",children:e.id}),(0,a.jsx)(g.nA,{children:new Date(e.timestamp).toLocaleString()}),(0,a.jsx)(g.nA,{children:e.userId||"N/A"}),(0,a.jsx)(g.nA,{children:e.action}),(0,a.jsx)(g.nA,{className:"max-w-[300px] truncate",children:e.details}),(0,a.jsx)(g.nA,{children:"N/A"})," "]},e.id))})]})}),I>0&&(0,a.jsx)(p.dK,{children:(0,a.jsxs)(p.Iu,{children:[(0,a.jsx)(p.cU,{children:(0,a.jsx)(p.Eb,{className:1===M?"pointer-events-none opacity-50":"",onClick:e=>{e.preventDefault(),F(M-1)}})}),Array.from({length:L},(e,s)=>(0,a.jsx)(p.cU,{children:(0,a.jsx)(p.n$,{isActive:M===s+1,onClick:e=>{e.preventDefault(),F(s+1)},children:s+1})},s)),(0,a.jsx)(p.cU,{children:(0,a.jsx)(p.WA,{className:M===L?"pointer-events-none opacity-50":"",onClick:e=>{e.preventDefault(),F(M+1)}})})]})})]})}var b=t(95120),A=t(19968),w=t(31949);let k=(0,t(40157).A)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var S=t(6874),C=t.n(S),E=t(55365),R=t(66695),z=t(17313),_=t(45727),M=t(67554),D=t(77223),I=t(45731),P=t(37648),U=t(26126),L=t(10694),T=t(77170);function F(){let[e,s]=(0,m.useState)({entries:[],size:0}),[t,r]=(0,m.useState)({}),[l,i]=(0,m.useState)(!1),n=()=>{s(T.Qb.getStats()),r({})};(0,m.useEffect)(()=>{n();let e=setInterval(n,1e3);return()=>clearInterval(e)},[]);let c=async()=>{i(!0),n(),setTimeout(()=>i(!1),500)},d=e=>e<0?"Expired":e<1e3?"".concat(Math.round(e),"ms"):e<6e4?"".concat(Math.round(e/1e3),"s"):"".concat(Math.round(e/6e4),"m"),o=e=>e.includes("health")?"Health":e.includes("performance")?"Performance":e.includes("errors")?"Errors":"Other",h=e=>e.includes("health")?"bg-green-100 text-green-800":e.includes("performance")?"bg-blue-100 text-blue-800":e.includes("errors")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800",u=e.entries.filter(e=>e.key.startsWith("admin:"));return(0,a.jsxs)(R.Zp,{className:"shadow-md",children:[(0,a.jsx)(R.aR,{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(R.ZB,{className:"flex items-center gap-2 text-xl font-semibold text-primary",children:[(0,a.jsx)(_.A,{className:"size-5"}),"Cache Status"]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-muted-foreground",children:"Request cache performance and timing"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(x.$,{className:"flex items-center gap-2",disabled:l,onClick:c,size:"sm",variant:"outline",children:[(0,a.jsx)(M.A,{className:"size-4 ".concat(l?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(x.$,{className:"flex items-center gap-2 text-red-600 hover:text-red-700",onClick:()=>{N.aN.clearAll(),n()},size:"sm",variant:"outline",children:[(0,a.jsx)(D.A,{className:"size-4"}),"Clear Cache"]}),(0,a.jsxs)(x.$,{className:"flex items-center gap-2 text-orange-600 hover:text-orange-700",onClick:()=>{N.aN.clearAll(),n()},size:"sm",variant:"outline",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Reset Breakers"]})]})]})}),(0,a.jsx)(R.Wu,{className:"p-5",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.size}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Entries"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Admin Entries"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:d(T.xR.HEALTH_STATUS)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Health Cache"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:d(T.xR.PERFORMANCE_METRICS)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Performance Cache"})]})]}),u.length>0?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold uppercase tracking-wide text-muted-foreground",children:"Active Cache Entries"}),u.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(U.E,{className:h(e.key),children:o(e.key)}),(0,a.jsx)("span",{className:"font-mono text-sm text-gray-600",children:e.key.replace("admin:","")})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-muted-foreground",children:[(0,a.jsx)(P.A,{className:"size-3"}),"Age: ",d(e.age)]}),(0,a.jsx)("div",{className:"font-medium ".concat(e.expiresIn>0?"text-green-600":"text-red-600"),children:e.expiresIn>0?"Expires in ".concat(d(e.expiresIn)):"Expired"})]})]},s))]}):(0,a.jsxs)("div",{className:"py-8 text-center text-muted-foreground",children:[(0,a.jsx)(_.A,{className:"mx-auto mb-3 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No cache entries found"}),(0,a.jsx)("p",{className:"text-sm",children:"Cache entries will appear here after API calls"})]}),(0,a.jsxs)("div",{className:"mt-6 rounded-lg bg-orange-50 p-4",children:[(0,a.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold text-orange-900",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Circuit Breaker Status"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(t).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"rounded-lg border p-3 ".concat(t.state===L.vz.CLOSED?"border-green-200 bg-green-50":t.state===L.vz.HALF_OPEN?"border-yellow-200 bg-yellow-50":"border-red-200 bg-red-50"),children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:s}),(0,a.jsxs)(U.E,{className:t.state===L.vz.CLOSED?"bg-green-100 text-green-800":t.state===L.vz.HALF_OPEN?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",children:[t.state===L.vz.CLOSED&&(0,a.jsx)(I.A,{className:"mr-1 size-3"}),t.state===L.vz.OPEN&&(0,a.jsx)(w.A,{className:"mr-1 size-3"}),t.state===L.vz.HALF_OPEN&&(0,a.jsx)(M.A,{className:"mr-1 size-3"}),t.state]})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{children:["Failures: ",t.failureCount]}),t.timeUntilRetry&&(0,a.jsxs)("div",{children:["Retry in: ",d(t.timeUntilRetry)]})]})]},s)})})]}),(0,a.jsxs)("div",{className:"mt-6 rounded-lg bg-blue-50 p-4",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold text-blue-900",children:"Cache Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Health Status:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:d(T.xR.HEALTH_STATUS)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Performance:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:d(T.xR.PERFORMANCE_METRICS)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Error Logs:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:d(T.xR.ERROR_LOGS)})]})]})]})]})})]})}var O=t(24371),V=t(9572),B=t(88240),W=t(6560),Z=t(11133),H=t(68856);function $(e){let{className:s,message:t,onRetry:r}=e;return(0,a.jsxs)(E.Fc,{className:(0,v.cn)("my-4",s),variant:"destructive",children:[(0,a.jsx)(Z.A,{className:"size-4"}),(0,a.jsx)(E.XL,{children:"Error"}),(0,a.jsx)(E.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:t}),r&&(0,a.jsxs)(x.$,{className:"flex items-center",onClick:r,size:"sm",variant:"outline",children:[(0,a.jsx)(d.A,{className:"mr-2 size-4"}),"Retry"]})]})})]})}var G=t(66424),q=t(59409),K=t(6548),J=t(89699),X=t(28113);function Y(){let[e,s]=(0,m.useState)(),[t,r]=(0,m.useState)(!1),[i,n]=(0,m.useState)(null),{currentPage:c,data:d,error:o,hasNextPage:x,hasPrevPage:h,isLoading:u,nextPage:p,prevPage:f,refetch:g,totalPages:j}=(0,K.p9)(["adminErrors",e],(s,t)=>N.adminService.getRecentErrors(s,t,e),{pageSize:10});(0,m.useEffect)(()=>{o&&(async()=>{if("status"in o&&(401===o.status||500===o.status)||"code"in o&&("NO_TOKEN"===o.code||"INVALID_TOKEN"===o.code)||o.message.includes("Authentication failed")||o.message.includes("Not Found")){let e=J.SessionManager.getSessionState();if(J.SessionManager.detectTimeout()||!(null==e?void 0:e.isActive))return n('Your session has expired. Click "Refresh Authentication" to renew your session.');try{let e=(0,X.Q)(),s=await e.getSessionInfo();s.isValid?n("Server error occurred. This might be a temporary issue. Try refreshing."):s.isExpired?n('Your session has expired. Click "Refresh Authentication" to renew your session.'):n("Authentication failed. Please refresh the page to sign in again.")}catch(e){n("Authentication system error. Please refresh the page to sign in again.")}}})()},[o]);let v=async()=>{r(!0),n(null),await g(),r(!1)},y=async()=>{r(!0),n(null);try{let e=(0,X.Q)();await e.refreshNow()?await g():n("Failed to refresh authentication. Please sign in again.")}catch(e){n("Authentication refresh failed. Please sign in again.")}finally{r(!1)}},b=e=>"ERROR"===e?(0,a.jsx)(O.A,{className:"size-4 text-red-500"}):"WARNING"===e?(0,a.jsx)(w.A,{className:"size-4 text-yellow-500"}):(0,a.jsx)(l.A,{className:"size-4 text-blue-500"}),A=e=>"ERROR"===e?(0,a.jsx)(U.E,{className:"border-red-500/30 bg-red-500/20 text-red-700",variant:"outline",children:"Error"}):"WARNING"===e?(0,a.jsx)(U.E,{className:"border-yellow-500/30 bg-yellow-500/20 text-yellow-700",variant:"outline",children:"Warning"}):(0,a.jsx)(U.E,{className:"border-blue-500/30 bg-blue-500/20 text-blue-700",variant:"outline",children:"Info"});return(0,a.jsx)(B.A,{children:(0,a.jsxs)(R.Zp,{className:"shadow-md",children:[(0,a.jsxs)(R.aR,{className:"p-5 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-xl font-semibold text-primary",children:"Recent Errors & Warnings"}),(0,a.jsx)(R.BT,{children:"Latest system errors and warnings"})]}),(0,a.jsx)("div",{className:"px-5 pb-2",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(V.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filter by level:"}),(0,a.jsxs)(q.l6,{onValueChange:e=>{s("all"===e?void 0:e)},value:null!=e?e:"all",children:[(0,a.jsx)(q.bq,{className:"w-[140px]",children:(0,a.jsx)(q.yv,{placeholder:"All levels"})}),(0,a.jsxs)(q.gC,{children:[(0,a.jsx)(q.eb,{value:"all",children:"All levels"}),(0,a.jsx)(q.eb,{value:"ERROR",children:"Errors only"}),(0,a.jsx)(q.eb,{value:"WARNING",children:"Warnings only"}),(0,a.jsx)(q.eb,{value:"INFO",children:"Info only"})]})]})]})}),(0,a.jsx)(R.Wu,{className:"p-5",children:i?(0,a.jsx)("div",{className:"rounded-md border border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(O.A,{className:"size-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-red-800",children:"Authentication Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:i}),(0,a.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,a.jsx)(W.r,{actionType:"primary",isLoading:t,loadingText:"Refreshing...",onClick:y,size:"sm",children:"Refresh Authentication"}),(0,a.jsx)(W.r,{actionType:"tertiary",onClick:()=>{globalThis.location.reload()},size:"sm",children:"Refresh Page"})]})]})]})}):u||t?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(H.E,{className:"h-6 w-full"}),(0,a.jsx)(H.E,{className:"h-6 w-full"}),(0,a.jsx)(H.E,{className:"h-6 w-full"}),(0,a.jsx)(H.E,{className:"h-6 w-full"}),(0,a.jsx)(H.E,{className:"h-6 w-full"})]}):o?(0,a.jsx)($,{message:o.message,onRetry:g}):d&&d.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(G.F,{className:"h-[300px] pr-4",children:(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,a.jsxs)("div",{className:"rounded-md border p-3 transition-colors hover:bg-accent/50",children:[(0,a.jsxs)("div",{className:"mb-1 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[b(e.level),(0,a.jsx)("span",{className:"font-medium",children:e.message})]}),A(e.level)]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()}),e.source&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),e.details&&Object.keys(e.details).length>0&&(0,a.jsx)("div",{className:"mt-2 rounded bg-muted p-2 text-xs",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(e.details,null,2)})})]},e.id))})}),j>1&&(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsx)(W.r,{actionType:"tertiary",disabled:!h||u||t,onClick:f,size:"sm",children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",c," of ",j]}),(0,a.jsx)(W.r,{actionType:"tertiary",disabled:!x||u||t,onClick:p,size:"sm",children:"Next"})]})]}):(0,a.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,a.jsx)(w.A,{className:"mx-auto mb-2 size-8 text-muted-foreground/50"}),(0,a.jsx)("p",{children:"No errors or warnings found for the selected filter."}),e&&(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Try changing the filter to see more results."})]})}),(0,a.jsx)(R.wL,{className:"p-5",children:(0,a.jsx)(W.r,{actionType:"tertiary",className:"w-full",icon:(0,a.jsx)(M.A,{className:"size-4"}),isLoading:t||u,loadingText:"Refreshing...",onClick:v,size:"sm",children:"Refresh Logs"})})]})})}var Q=t(15292);function ee(){let[e,s]=(0,m.useState)(null),[t,r]=(0,m.useState)(!1),l=async()=>{r(!0);try{let e=N.aN.getStats(),t=[];s({cacheStats:{errorCacheKeys:t.map(e=>e.key||"unknown"),errorEntries:t.length,totalEntries:e.size},circuitBreakers:{},timestamp:new Date().toISOString()})}catch(e){console.error("Failed to get debug info:",e)}finally{r(!1)}},i=e=>{switch(e){case L.vz.CLOSED:return"bg-green-100 text-green-800";case L.vz.HALF_OPEN:return"bg-yellow-100 text-yellow-800";case L.vz.OPEN:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)(R.Zp,{className:"shadow-md",children:[(0,a.jsx)(R.aR,{className:"p-5",children:(0,a.jsxs)(R.ZB,{className:"flex items-center gap-2 text-xl font-semibold text-primary",children:[(0,a.jsx)(Q.A,{className:"size-5"}),"Error Logs Debug"]})}),(0,a.jsx)(R.Wu,{className:"p-5",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(x.$,{className:"flex items-center gap-2",disabled:t,onClick:l,children:[(0,a.jsx)(M.A,{className:"size-4 ".concat(t?"animate-spin":"")}),"Get Debug Info"]}),(0,a.jsxs)(x.$,{className:"flex items-center gap-2 text-orange-600 hover:text-orange-700",onClick:()=>{N.aN.clearAll(),console.log("\uD83D\uDD04 Circuit breakers reset"),l()},variant:"outline",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Reset Circuit Breakers"]}),(0,a.jsxs)(x.$,{className:"flex items-center gap-2 text-blue-600 hover:text-blue-700",onClick:()=>{N.aN.clearAll(),console.log("\uD83D\uDD04 Error cache cleared"),l()},variant:"outline",children:[(0,a.jsx)(D.A,{className:"size-4"}),"Clear Error Cache"]}),(0,a.jsxs)(x.$,{className:"flex items-center gap-2 text-red-600 hover:text-red-700",onClick:()=>{N.aN.clearAll(),console.log("\uD83D\uDD04 All cache cleared"),l()},variant:"outline",children:[(0,a.jsx)(D.A,{className:"size-4"}),"Clear All Cache"]})]}),e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-4",children:[(0,a.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Circuit Breaker Status"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(e.circuitBreakers).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"rounded border bg-white p-3",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:s}),(0,a.jsx)(U.E,{className:i(t.state),children:t.state})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{children:["Failures: ",t.failureCount]}),t.timeUntilRetry&&(0,a.jsxs)("div",{children:["Retry in:"," ",Math.round(t.timeUntilRetry/1e3),"s"]})]})]},s)})})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-blue-50 p-4",children:[(0,a.jsx)("h4",{className:"mb-3 font-semibold text-blue-900",children:"Cache Status"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Total Entries:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:e.cacheStats.totalEntries})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Error Entries:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:e.cacheStats.errorEntries})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Last Updated:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:new Date(e.timestamp).toLocaleTimeString()})]})]}),e.cacheStats.errorCacheKeys.length>0&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("div",{className:"mb-2 font-medium text-blue-800",children:"Error Cache Keys:"}),(0,a.jsx)("div",{className:"space-y-1",children:e.cacheStats.errorCacheKeys.map((e,s)=>(0,a.jsx)("div",{className:"rounded border bg-white p-2 font-mono text-xs",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-yellow-50 p-4",children:[(0,a.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold text-yellow-900",children:[(0,a.jsx)(w.A,{className:"size-4"}),"Troubleshooting Tips"]}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm text-yellow-800",children:[(0,a.jsx)("li",{children:"• If circuit breakers are OPEN (red), reset them and try again"}),(0,a.jsx)("li",{children:"• If error cache is stale, clear it to force fresh data"}),(0,a.jsx)("li",{children:"• Check browser console for network errors"}),(0,a.jsx)("li",{children:"• Verify backend server is running and accessible"}),(0,a.jsx)("li",{children:"• Check if rate limiting is affecting requests"})]})]})]}),!e&&(0,a.jsxs)("div",{className:"py-8 text-center text-muted-foreground",children:[(0,a.jsx)(Q.A,{className:"mx-auto mb-3 size-12 opacity-50"}),(0,a.jsx)("p",{children:'Click "Get Debug Info" to see error logs debugging information'})]})]})})]})}function es(){return(0,a.jsxs)(R.Zp,{className:"border-none shadow-none",children:[(0,a.jsxs)(R.aR,{className:"px-0 pt-0",children:[(0,a.jsx)(R.ZB,{className:"text-2xl font-bold",children:"Supabase Diagnostics"}),(0,a.jsx)(R.BT,{children:"Monitor and troubleshoot your Supabase database connection"})]}),(0,a.jsxs)(R.Wu,{className:"w-full max-w-full overflow-hidden px-0",children:[(0,a.jsxs)(E.Fc,{className:"mb-6",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)(E.XL,{children:"Enhanced Reliability Dashboard Available"}),(0,a.jsxs)(E.TN,{children:["System health and performance monitoring has been moved to the new Reliability Dashboard with real-time updates, advanced metrics, and comprehensive monitoring capabilities.",(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(x.$,{asChild:!0,variant:"outline",size:"sm",children:(0,a.jsxs)(C(),{href:"/reliability",className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Go to Reliability Dashboard",(0,a.jsx)(A.A,{className:"h-4 w-4"})]})})})]})]}),(0,a.jsxs)(z.tU,{className:"w-full max-w-full overflow-hidden",defaultValue:"errors",children:[(0,a.jsxs)(z.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsxs)(z.Xi,{className:"flex items-center",value:"errors",children:[(0,a.jsx)(w.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Error Logs"})]}),(0,a.jsxs)(z.Xi,{className:"flex items-center",value:"cache",children:[(0,a.jsx)(k,{className:"mr-2 size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Cache Status"})]})]}),(0,a.jsx)(z.av,{className:"mt-4",value:"errors",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(Y,{}),(0,a.jsx)(ee,{})]})}),(0,a.jsx)(z.av,{className:"mt-4",value:"cache",children:(0,a.jsx)(F,{})})]})]})]})}var et=t(8376),ea=t(3561),er=t(18763),el=t(40207),ei=t(18046),en=t(13896),ec=t(50286),ed=t(17607),eo=t(90010),em=t(91394),ex=t(47262),eh=t(54165),eu=t(44838),ep=t(91721),ef=t(85057),eg=t(83761);let ej=e=>{var s,t;let a=null!=(t=null!=(s=e.fullName)?s:e.name)?t:"Unknown",r=e.employeeId?" (ID: ".concat(e.employeeId,")"):"",l=e.position?" - ".concat(e.position):"";return"".concat(a).concat(r).concat(l)};function eN(e){var s;let{allowClear:t=!0,className:r,disabled:l=!1,error:i,label:n,onValueChange:c,placeholder:d="Select employee...",required:o=!1,value:m}=e,{data:x=[],error:h,isLoading:u}=(0,eg.nR)(),p=x.find(e=>e.id===m);return u?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-2",r),children:[n&&(0,a.jsxs)(ef.J,{className:"text-sm font-medium",children:[n,o&&(0,a.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,a.jsx)(q.l6,{disabled:!0,children:(0,a.jsx)(q.bq,{children:(0,a.jsx)(q.yv,{placeholder:"Loading employees..."})})})]}):h?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-2",r),children:[n&&(0,a.jsxs)(ef.J,{className:"text-sm font-medium",children:[n,o&&(0,a.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,a.jsx)(q.l6,{disabled:!0,children:(0,a.jsx)(q.bq,{children:(0,a.jsx)(q.yv,{placeholder:"Error loading employees"})})}),(0,a.jsx)("p",{className:"text-sm text-destructive",children:"Failed to load employees"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-2",r),children:[n&&(0,a.jsxs)(ef.J,{className:"text-sm font-medium",children:[n,o&&(0,a.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,a.jsxs)(q.l6,{disabled:l,onValueChange:e=>{if("clear"===e)return void c(null);let s=Number.parseInt(e,10);c(Number.isNaN(s)?null:s)},value:null!=(s=null==m?void 0:m.toString())?s:"",children:[(0,a.jsx)(q.bq,{className:(0,v.cn)(i&&"border-destructive focus:border-destructive"),children:(0,a.jsx)(q.yv,{placeholder:d,children:p&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ep.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"truncate",children:ej(p)})]})})}),(0,a.jsxs)(q.gC,{children:[t&&m&&(0,a.jsx)(q.eb,{className:"text-muted-foreground",value:"clear",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs",children:"\xd7"}),(0,a.jsx)("span",{children:"Clear selection"})]})}),x.map(e=>{var s,t;return(0,a.jsx)(q.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex w-full items-center space-x-2",children:[(0,a.jsx)(ep.A,{className:"size-4 shrink-0 text-muted-foreground"}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("div",{className:"truncate font-medium",children:null!=(t=null!=(s=e.fullName)?s:e.name)?t:"Unknown"}),(0,a.jsxs)("div",{className:"truncate text-xs text-muted-foreground",children:[e.employeeId&&"ID: ".concat(e.employeeId),e.position&&" • ".concat(e.position),e.department&&" • ".concat(e.department)]})]})]})},e.id)})]})]}),i&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:i})]})}var ev=t(84411),ey=t(53712);function eb(){var e,s,t;let[r,l]=(0,m.useState)([]),[i,n]=(0,m.useState)(!0),[c,o]=(0,m.useState)(!1),[h,p]=(0,m.useState)(null),[f,g]=(0,m.useState)({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"}),[j,v]=(0,m.useState)(0),[y]=(0,m.useState)(10),{showFormError:b,showFormSuccess:A}=(0,ey.t6)(),[w,k]=(0,m.useState)(!1),[S,C]=(0,m.useState)(null),[E,_]=(0,m.useState)(!1),[P,L]=(0,m.useState)(null),[T,F]=(0,m.useState)(null),[V,B]=(0,m.useState)(null),[W,Z]=(0,m.useState)(!1),H=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),$=e=>e?new Date(e).toLocaleDateString("en-US",{day:"numeric",month:"short",year:"numeric"}):"Never",G=e=>{if(!e)return"Never";let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);return a<60?"Just now":a<3600?"".concat(Math.floor(a/60),"m ago"):a<86400?"".concat(Math.floor(a/3600),"h ago"):a<2592e3?"".concat(Math.floor(a/86400),"d ago"):$(e)},K=(e,s)=>{if(null==s?void 0:s.trim()){var t,a;let e=s.trim().split(" ");return e.length>1?"".concat((null==(t=e[0])?void 0:t[0])||"").concat((null==(a=e.at(-1))?void 0:a[0])||"").toUpperCase():s.slice(0,2).toUpperCase()}return e.slice(0,2).toUpperCase()},J=(0,m.useCallback)(async()=>{n(!0),B(null);try{let e=await N.adminService.getAllUsers({limit:100,page:1,search:""});l(e.data||[]);let s=e.pagination||{limit:100,total:0};v(s.total)}catch(t){var e,s;if((null==t?void 0:t.status)===401||(null==t?void 0:t.status)===500||(null==t?void 0:t.code)==="NO_TOKEN"||(null==t?void 0:t.code)==="INVALID_TOKEN"||(null==t||null==(e=t.message)?void 0:e.includes("Authentication failed"))||(null==t||null==(s=t.message)?void 0:s.includes("Failed to fetch users")))try{let e=(0,X.Q)(),s=await e.getSessionInfo();s.isValid?B("Server error occurred. This might be a temporary issue. Try refreshing."):s.isExpired?B('Your session has expired. Click "Refresh Authentication" to renew your session.'):B("Authentication failed. Please refresh the page to sign in again.")}catch(e){B("Authentication system error. Please refresh the page to sign in again.")}else b(t,{errorDescription:t.message||"Failed to load user data.",errorTitle:"Error fetching users"})}finally{n(!1)}},[b]);(0,m.useEffect)(()=>{J()},[J]);let Y=async()=>{Z(!0),B(null);try{let e=(0,X.Q)();await e.refreshNow()?await J():B("Failed to refresh authentication. Please sign in again.")}catch(e){B("Authentication refresh failed. Please sign in again.")}finally{Z(!1)}},Q=async()=>{if(!H(f.email))return void b(Error("Please enter a valid email address."),{errorTitle:"Validation Error"});n(!0);try{let e=await N.adminService.createUser(f);A({successDescription:"User ".concat(e.email," has been added."),successTitle:"User created"}),o(!1),g({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"}),J()}catch(e){b(e.message||"Failed to create user.",{errorTitle:"Error creating user"})}finally{n(!1)}},ee=async()=>{if(h){if(!H(h.email||""))return void b("Please enter a valid email address.",{errorTitle:"Validation Error"});n(!0);try{let e=await N.adminService.updateUser(h.id,{email:h.email,emailVerified:!!h.email_confirmed_at,isActive:h.isActive,role:h.role});A({successDescription:"User ".concat(e.email," has been updated."),successTitle:"User updated"}),o(!1),p(null),J()}catch(e){b(e.message||"Failed to update user.",{errorTitle:"Error updating user"})}finally{n(!1)}}},es=async()=>{if(S){n(!0);try{await N.adminService.deleteUser(S),A({successDescription:"User has been successfully deleted.",successTitle:"User deleted"}),J()}catch(e){b(e.message||"Failed to delete user.",{errorTitle:"Error deleting user"})}finally{n(!1),k(!1),C(null)}}},ep=async()=>{if(P&&null!==T){n(!0);try{let e=await N.adminService.toggleUserActivation(P,!T);A({successDescription:"User ".concat(e.email," is now ").concat(e.isActive?"active":"inactive","."),successTitle:"User status updated"}),J()}catch(e){b(e.message||"Failed to toggle user activation.",{errorTitle:"Error updating status"})}finally{n(!1),_(!1),L(null),F(null)}}},eg=e=>{switch(e){case"ADMIN":return"bg-purple-500 hover:bg-purple-600 text-white";case"MANAGER":return"bg-blue-500 hover:bg-blue-600 text-white";case"READONLY":return"bg-yellow-500 hover:bg-yellow-600 text-white";case"SUPER_ADMIN":return"bg-red-500 hover:bg-red-600 text-white";case"USER":return"bg-green-500 hover:bg-green-600 text-white";default:return"bg-gray-500 hover:bg-gray-600 text-white"}},ej=[{accessorKey:"email",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(em.eu,{className:"size-10",children:[(0,a.jsx)(em.BK,{alt:t.email,src:""}),(0,a.jsx)(em.q5,{className:"text-sm",children:K(t.email,t.full_name)})]}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"truncate text-sm font-medium",children:t.full_name||t.email.split("@")[0]}),t.email_confirmed_at&&(0,a.jsx)(I.A,{className:"size-3 text-green-500"})]}),(0,a.jsx)("p",{className:"truncate text-xs text-muted-foreground",children:t.email}),t.employee_id&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["ID: ",t.employee_id]})]})]})},header:"User"},{accessorKey:"role",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsx)(U.E,{className:eg(t.role),children:(t.role||"USER").replace("_"," ")})},header:"Role"},{accessorKey:"isActive",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(U.E,{variant:t.isActive?"default":"destructive",children:t.isActive?"Active":"Inactive"}),t.email_confirmed_at?(0,a.jsx)(et.A,{className:"size-4 text-green-500"}):(0,a.jsx)(O.A,{className:"size-4 text-red-500"})]})},header:"Status"},{accessorKey:"last_sign_in_at",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsx)("div",{className:"text-sm",children:G(t.last_sign_in_at)})},header:"Last Activity"},{accessorKey:"created_at",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsx)("div",{className:"text-sm",children:$(t.created_at)})},header:"Joined"},{cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)(eu.rI,{children:[(0,a.jsx)(eu.ty,{asChild:!0,children:(0,a.jsxs)(x.$,{className:"size-8 p-0",variant:"ghost",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)(ea.A,{className:"size-4"})]})}),(0,a.jsxs)(eu.SQ,{align:"end",children:[(0,a.jsx)(eu.lp,{children:"Actions"}),(0,a.jsxs)(eu._2,{onClick:()=>{p(t),o(!0)},children:[(0,a.jsx)(er.A,{className:"mr-2 size-4"}),"Edit user"]}),(0,a.jsx)(eu.mB,{}),(0,a.jsxs)(eu._2,{onClick:()=>{L(t.id),F(t.isActive),_(!0)},children:[(0,a.jsx)(el.A,{className:"mr-2 size-4"}),t.isActive?"Deactivate":"Activate"]}),(0,a.jsxs)(eu._2,{className:"text-red-600 focus:text-red-600",onClick:()=>{C(t.id),k(!0)},children:[(0,a.jsx)(D.A,{className:"mr-2 size-4"}),"Delete user"]})]})]})},header:"Actions",id:"actions"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"User Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage user accounts, roles, and permissions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(x.$,{onClick:J,size:"sm",variant:"outline",children:[(0,a.jsx)(M.A,{className:"mr-2 size-4"}),"Refresh"]}),(0,a.jsxs)(eh.lG,{onOpenChange:o,open:c,children:[(0,a.jsx)(eh.zM,{asChild:!0,children:(0,a.jsxs)(x.$,{onClick:()=>{p(null),g({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"})},children:[(0,a.jsx)(ei.A,{className:"mr-2 size-4"})," Add User"]})}),(0,a.jsxs)(eh.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsxs)(eh.c7,{children:[(0,a.jsx)(eh.L3,{className:"flex items-center gap-2",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(er.A,{className:"size-5"}),"Edit User Profile"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(en.A,{className:"size-5"}),"Add New User"]})}),(0,a.jsx)(eh.rr,{children:h?"Update user information and permissions.":"Create a new user account with role and permissions."})]}),(0,a.jsxs)(z.tU,{className:"w-full",defaultValue:"basic",children:[(0,a.jsxs)(z.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(z.Xi,{value:"basic",children:"Basic Info"}),(0,a.jsx)(z.Xi,{value:"advanced",children:"Advanced"})]}),(0,a.jsx)(z.av,{className:"mt-4 space-y-4",value:"basic",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ef.J,{className:"text-right",htmlFor:"full_name",children:"Full Name"}),(0,a.jsx)(u.p,{className:"col-span-3",id:"full_name",onChange:e=>h?p({...h,full_name:e.target.value}):g({...f,full_name:e.target.value}),placeholder:"Enter full name",value:h?h.full_name||"":f.full_name})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ef.J,{className:"text-right",htmlFor:"email",children:"Email *"}),(0,a.jsx)(u.p,{className:"col-span-3",id:"email",onChange:e=>h?p({...h,email:e.target.value}):g({...f,email:e.target.value}),placeholder:"<EMAIL>",type:"email",value:h?h.email:f.email})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ef.J,{className:"text-right",htmlFor:"phone",children:"Phone"}),(0,a.jsx)(u.p,{className:"col-span-3",id:"phone",onChange:e=>h?p({...h,phone:e.target.value}):g({...f,phone:e.target.value}),placeholder:"+****************",type:"tel",value:h?h.phone||"":f.phone})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ef.J,{className:"text-right",htmlFor:"role",children:"Role *"}),(0,a.jsxs)(q.l6,{onValueChange:e=>h?p({...h,role:e}):g({...f,role:e}),value:h?h.role:f.role,children:[(0,a.jsx)(q.bq,{className:"col-span-3",children:(0,a.jsx)(q.yv,{placeholder:"Select a role"})}),(0,a.jsxs)(q.gC,{children:[(0,a.jsx)(q.eb,{value:"SUPER_ADMIN",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"size-4 text-red-500"}),"Super Admin"]})}),(0,a.jsx)(q.eb,{value:"ADMIN",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"size-4 text-purple-500"}),"Admin"]})}),(0,a.jsx)(q.eb,{value:"MANAGER",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ec.A,{className:"size-4 text-blue-500"}),"Manager"]})}),(0,a.jsx)(q.eb,{value:"USER",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ec.A,{className:"size-4 text-green-500"}),"User"]})}),(0,a.jsx)(q.eb,{value:"READONLY",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ed.A,{className:"size-4 text-yellow-500"}),"Read Only"]})})]})]})]})]})}),(0,a.jsx)(z.av,{className:"mt-4 space-y-4",value:"advanced",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,a.jsx)("div",{className:"col-span-4",children:(0,a.jsx)(eN,{allowClear:!0,className:"w-full",label:"Link to Employee (Optional)",onValueChange:e=>h?p({...h,employee_id:e}):g({...f,employee_id:(null==e?void 0:e.toString())||""}),placeholder:"Select an employee to link this user account...",value:h?null!=(t=h.employee_id)?t:null:f.employee_id?Number.parseInt(f.employee_id):null})})}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ef.J,{className:"text-right",htmlFor:"isActive",children:"Account Status"}),(0,a.jsxs)("div",{className:"col-span-3 flex items-center space-x-2",children:[(0,a.jsx)(ex.S,{checked:h?h.isActive:f.isActive,id:"isActive",onCheckedChange:e=>h?p({...h,isActive:!!e}):g({...f,isActive:!!e})}),(0,a.jsx)(ef.J,{className:"text-sm",htmlFor:"isActive",children:"Active account"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ef.J,{className:"text-right",htmlFor:"emailVerified",children:"Email Verification"}),(0,a.jsxs)("div",{className:"col-span-3 flex items-center space-x-2",children:[(0,a.jsx)(ex.S,{checked:h?!!h.email_confirmed_at:f.emailVerified,id:"emailVerified",onCheckedChange:e=>h?p({...h,email_confirmed_at:e?new Date().toISOString():null}):g({...f,emailVerified:!!e})}),(0,a.jsx)(ef.J,{className:"text-sm",htmlFor:"emailVerified",children:"Email verified"})]})]})]})})]}),(0,a.jsx)(eh.Es,{children:(0,a.jsxs)(x.$,{disabled:i,onClick:h?ee:Q,type:"submit",children:[i&&(0,a.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),h?"Save changes":"Add User"]})})]})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Total Users"}),(0,a.jsx)(ec.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:j}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[r.filter(e=>e.isActive).length," active"]})]})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Active Users"}),(0,a.jsx)(el.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r.filter(e=>e.isActive).length}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(r.filter(e=>e.isActive).length/Math.max(r.length,1)*100),"% of total"]})]})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Verified Emails"}),(0,a.jsx)(I.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r.filter(e=>e.email_confirmed_at).length}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(r.filter(e=>e.email_confirmed_at).length/Math.max(r.length,1)*100),"% verified"]})]})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Admins"}),(0,a.jsx)(I.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r.filter(e=>"ADMIN"===e.role||"SUPER_ADMIN"===e.role).length}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"System administrators"})]})]})]})]}),V?(0,a.jsx)("div",{className:"rounded-md border border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(O.A,{className:"size-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-red-800",children:"Authentication Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:V}),(0,a.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,a.jsx)(x.$,{disabled:W,onClick:Y,size:"sm",children:W?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),"Refreshing..."]}):"Refresh Authentication"}),(0,a.jsx)(x.$,{onClick:()=>{globalThis.location.reload()},size:"sm",variant:"outline",children:"Refresh Page"})]})]})]})}):i?(0,a.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,a.jsx)(d.A,{className:"size-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-lg",children:"Loading users..."})]}):(0,a.jsx)(ev.b,{className:"w-full",columns:ej,data:r,emptyMessage:"No users found.",enableColumnVisibility:!0,enableGlobalFilter:!0,enableRowSelection:!1,pageSize:y,searchColumn:"email",searchPlaceholder:"Search users by email or role..."}),(0,a.jsx)(eo.Lt,{onOpenChange:_,open:E,children:(0,a.jsxs)(eo.EO,{children:[(0,a.jsxs)(eo.wd,{children:[(0,a.jsx)(eo.r7,{children:T?"Deactivate User":"Activate User"}),(0,a.jsxs)(eo.$v,{children:["Are you sure you want to"," ",T?"deactivate":"activate"," user"," ",(0,a.jsx)("span",{className:"font-bold",children:null==(e=r.find(e=>e.id===P))?void 0:e.email}),"?"]})]}),(0,a.jsxs)(eo.ck,{children:[(0,a.jsx)(eo.Zr,{children:"Cancel"}),(0,a.jsx)(eo.Rx,{onClick:ep,children:T?"Deactivate":"Activate"})]})]})}),(0,a.jsx)(eo.Lt,{onOpenChange:k,open:w,children:(0,a.jsxs)(eo.EO,{children:[(0,a.jsxs)(eo.wd,{children:[(0,a.jsx)(eo.r7,{children:"Delete User"}),(0,a.jsxs)(eo.$v,{children:["Are you sure you want to permanently delete user"," ",(0,a.jsx)("span",{className:"font-bold",children:null==(s=r.find(e=>e.id===S))?void 0:s.email}),"? This action cannot be undone."]})]}),(0,a.jsxs)(eo.ck,{children:[(0,a.jsx)(eo.Zr,{children:"Cancel"}),(0,a.jsx)(eo.Rx,{onClick:es,children:"Delete"})]})]})})]})}function eA(){return(0,a.jsx)(i.ProtectedRoute,{allowedRoles:["ADMIN","SUPER_ADMIN"],children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center space-x-2",children:[(0,a.jsx)(r.A,{className:"size-8 text-primary"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"System administration and diagnostics"})]})]}),(0,a.jsxs)(E.Fc,{children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)(E.XL,{children:"Information"}),(0,a.jsx)(E.TN,{children:"This admin dashboard provides system diagnostics and monitoring tools. Access is restricted to ADMIN and SUPER_ADMIN roles."})]}),(0,a.jsxs)(z.tU,{className:"w-full",defaultValue:"system-diagnostics",children:[(0,a.jsxs)(z.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(z.Xi,{value:"system-diagnostics",children:"System Diagnostics"}),(0,a.jsx)(z.Xi,{value:"user-management",children:"User Management"}),(0,a.jsx)(z.Xi,{value:"audit-logs",children:"Audit Logs"})]}),(0,a.jsx)(z.av,{className:"mt-6",value:"system-diagnostics",children:(0,a.jsx)(es,{})}),(0,a.jsx)(z.av,{className:"mt-6",value:"user-management",children:(0,a.jsx)(eb,{})}),(0,a.jsx)(z.av,{className:"mt-6",value:"audit-logs",children:(0,a.jsx)(y,{})})]})]})})}},40207:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},45727:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},45876:(e,s,t)=>{"use strict";t.d(s,{ProtectedRoute:()=>m});var a=t(95155),r=t(50172),l=t(31949),i=t(45731);t(12115);var n=t(40283),c=t(55365),d=t(66695),o=t(92999);function m(e){let{allowedRoles:s=[],children:t,fallback:m,requireEmailVerification:x=!0}=e,{error:h,loading:u,session:p,user:f,userRole:g}=(0,n.useAuthContext)();if(u)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:(0,a.jsx)(d.Zp,{className:"mx-auto w-full max-w-md",children:(0,a.jsxs)(d.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(r.A,{className:"mb-4 size-8 animate-spin text-blue-600"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Verifying security credentials..."})]})})});if(h&&!f)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center text-red-600",children:[(0,a.jsx)(l.A,{className:"mr-2 size-5"}),"Authentication Error"]}),(0,a.jsx)(d.BT,{children:"There was a problem with the security system"})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsx)(c.Fc,{variant:"destructive",children:(0,a.jsx)(c.TN,{children:h})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(o.LoginForm,{})})]})]})});if(!f||!p)return m?(0,a.jsx)(a.Fragment,{children:m}):(0,a.jsx)(o.LoginForm,{onSuccess:()=>{globalThis.location.href="/"}});if(x&&!f.email_confirmed_at)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center text-yellow-600",children:[(0,a.jsx)(i.A,{className:"mr-2 size-5"}),"Email Verification Required"]}),(0,a.jsx)(d.BT,{children:"Please verify your email address to continue"})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsxs)(c.Fc,{children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsxs)(c.TN,{children:["We've sent a verification email to ",(0,a.jsx)("strong",{children:f.email}),". Please check your inbox and click the verification link to access the system."]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Didn't receive the email? Check your spam folder or contact your administrator."})})]})]})});if(s.length>0){let e=g||"USER";if(!s.includes(e))return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center text-red-600",children:[(0,a.jsx)(i.A,{className:"mr-2 size-5"}),"Access Denied"]}),(0,a.jsx)(d.BT,{children:"Insufficient permissions to access this resource"})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsx)(c.Fc,{variant:"destructive",children:(0,a.jsxs)(c.TN,{children:["Your account (",e,") does not have permission to access this area. Required roles: ",s.join(", ")]})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Contact your administrator if you believe this is an error."})})]})]})})}return(0,a.jsx)(a.Fragment,{children:t})}},48639:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},54165:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>x,Es:()=>u,L3:()=>p,c7:()=>h,lG:()=>c,rr:()=>f,zM:()=>d});var a=t(95155),r=t(15452),l=t(25318),i=t(12115),n=t(54036);let c=r.bL,d=r.l9,o=r.ZL;r.bm;let m=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),ref:s,...l})});m.displayName=r.hJ.displayName;let x=i.forwardRef((e,s)=>{let{children:t,className:i,...c}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(r.UC,{className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",i),ref:s,...c,children:[t,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=r.UC.displayName;let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};h.displayName="DialogHeader";let u=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};u.displayName="DialogFooter";let p=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hE,{className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",t),ref:s,...l})});p.displayName=r.hE.displayName;let f=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.VY,{className:(0,n.cn)("text-sm text-muted-foreground",t),ref:s,...l})});f.displayName=r.VY.displayName},60704:(e,s,t)=>{"use strict";t.d(s,{B8:()=>R,UC:()=>_,bL:()=>E,l9:()=>z});var a=t(12115),r=t(85185),l=t(46081),i=t(89196),n=t(28905),c=t(63655),d=t(94315),o=t(5845),m=t(61285),x=t(95155),h="Tabs",[u,p]=(0,l.A)(h,[i.RG]),f=(0,i.RG)(),[g,j]=u(h),N=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:r,defaultValue:l,orientation:i="horizontal",dir:n,activationMode:u="automatic",...p}=e,f=(0,d.jH)(n),[j,N]=(0,o.i)({prop:a,onChange:r,defaultProp:null!=l?l:"",caller:h});return(0,x.jsx)(g,{scope:t,baseId:(0,m.B)(),value:j,onValueChange:N,orientation:i,dir:f,activationMode:u,children:(0,x.jsx)(c.sG.div,{dir:f,"data-orientation":i,...p,ref:s})})});N.displayName=h;var v="TabsList",y=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...r}=e,l=j(v,t),n=f(t);return(0,x.jsx)(i.bL,{asChild:!0,...n,orientation:l.orientation,dir:l.dir,loop:a,children:(0,x.jsx)(c.sG.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:s})})});y.displayName=v;var b="TabsTrigger",A=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:l=!1,...n}=e,d=j(b,t),o=f(t),m=S(d.baseId,a),h=C(d.baseId,a),u=a===d.value;return(0,x.jsx)(i.q7,{asChild:!0,...o,focusable:!l,active:u,children:(0,x.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...n,ref:s,onMouseDown:(0,r.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||l||!e||d.onValueChange(a)})})})});A.displayName=b;var w="TabsContent",k=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:l,children:i,...d}=e,o=j(w,t),m=S(o.baseId,r),h=C(o.baseId,r),u=r===o.value,p=a.useRef(u);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(n.C,{present:l||u,children:t=>{let{present:a}=t;return(0,x.jsx)(c.sG.div,{"data-state":u?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&i})}})});function S(e,s){return"".concat(e,"-trigger-").concat(s)}function C(e,s){return"".concat(e,"-content-").concat(s)}k.displayName=w;var E=N,R=y,z=A,_=k},66424:(e,s,t)=>{"use strict";t.d(s,{F:()=>n});var a=t(95155),r=t(47655),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{children:t,className:l,...n}=e;return(0,a.jsxs)(r.bL,{className:(0,i.cn)("relative overflow-hidden",l),ref:s,...n,children:[(0,a.jsx)(r.LM,{className:"size-full rounded-[inherit]",children:t}),(0,a.jsx)(c,{}),(0,a.jsx)(r.OK,{})]})});n.displayName=r.bL.displayName;let c=l.forwardRef((e,s)=>{let{className:t,orientation:l="vertical",...n}=e;return(0,a.jsx)(r.VM,{className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===l&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===l&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),orientation:l,ref:s,...n,children:(0,a.jsx)(r.lr,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=r.VM.displayName},85511:(e,s,t)=>{"use strict";t.d(s,{V:()=>d});var a=t(95155),r=t(965),l=t(73158);t(12115);var i=t(33683),n=t(30285),c=t(54036);function d(e){let{className:s,classNames:t,showOutsideDays:d=!0,...o}=e;return(0,a.jsx)(i.hv,{className:(0,c.cn)("p-3",s),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...t},components:{IconLeft:e=>{let{className:s,...t}=e;return(0,a.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",s),...t})},IconRight:e=>{let{className:s,...t}=e;return(0,a.jsx)(l.A,{className:(0,c.cn)("h-4 w-4",s),...t})}},showOutsideDays:d,...o})}d.displayName="Calendar"},88240:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(95155),r=t(31949),l=t(67554),i=t(12115),n=t(55365),c=t(30285);class d extends i.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,s){this.setState({errorInfo:s}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.props.onError&&this.props.onError(e,s)}render(){let{description:e="An unexpected error occurred.",resetLabel:s="Try Again",title:t="Something went wrong"}=this.props;if(this.state.hasError){var i;return this.props.fallback?this.props.fallback:(0,a.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(r.A,{className:"mr-2 size-4"}),(0,a.jsx)(n.XL,{className:"text-lg font-semibold",children:t}),(0,a.jsxs)(n.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:(null==(i=this.state.error)?void 0:i.message)||e}),!1,(0,a.jsxs)(c.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(l.A,{className:"mr-2 size-4"}),s]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let o=d},90010:(e,s,t)=>{"use strict";t.d(s,{$v:()=>f,EO:()=>x,Lt:()=>c,Rx:()=>g,Zr:()=>j,ck:()=>u,r7:()=>p,tv:()=>d,wd:()=>h});var a=t(95155),r=t(17649),l=t(12115),i=t(30285),n=t(54036);let c=r.bL,d=r.l9,o=r.ZL,m=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l,ref:s})});m.displayName=r.hJ.displayName;let x=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(m,{}),(0,a.jsx)(r.UC,{className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),ref:s,...l})]})});x.displayName=r.UC.displayName;let h=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...t})};h.displayName="AlertDialogHeader";let u=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};u.displayName="AlertDialogFooter";let p=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.hE,{className:(0,n.cn)("text-lg font-semibold",t),ref:s,...l})});p.displayName=r.hE.displayName;let f=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.VY,{className:(0,n.cn)("text-sm text-muted-foreground",t),ref:s,...l})});f.displayName=r.VY.displayName;let g=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.rc,{className:(0,n.cn)((0,i.r)(),t),ref:s,...l})});g.displayName=r.rc.displayName;let j=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.ZD,{className:(0,n.cn)((0,i.r)({variant:"outline"}),"mt-2 sm:mt-0",t),ref:s,...l})});j.displayName=r.ZD.displayName},91394:(e,s,t)=>{"use strict";t.d(s,{BK:()=>c,eu:()=>n,q5:()=>d});var a=t(95155),r=t(54011),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),ref:s,...l})});n.displayName=r.bL.displayName;let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r._V,{className:(0,i.cn)("aspect-square h-full w-full",t),ref:s,...l})});c.displayName=r._V.displayName;let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.H4,{className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),ref:s,...l})});d.displayName=r.H4.displayName},95120:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(40157).A)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,7047,3860,9664,1263,5495,1859,6874,5247,6463,7454,8982,2700,4036,4767,8950,3712,7515,3615,5320,2999,8441,1684,7358],()=>s(21920)),_N_E=e.O()}]);