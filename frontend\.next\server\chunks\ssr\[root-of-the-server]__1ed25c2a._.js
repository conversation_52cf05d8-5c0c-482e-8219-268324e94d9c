module.exports = {

"[project]/src/components/error-boundaries/ErrorBoundary.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-ssr] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
/**
 * Generic Error Boundary component
 * Catches errors in its child component tree and displays a fallback UI
 */ class ErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Component"] {
    constructor(props){
        super(props);
        this.state = {
            error: null,
            errorInfo: null,
            hasError: false
        };
    }
    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return {
            error,
            hasError: true
        };
    }
    componentDidCatch(error, errorInfo) {
        // Update state with error info for detailed reporting
        this.setState({
            errorInfo
        });
        // Log the error
        console.error('Error caught by ErrorBoundary:', error);
        console.error('Component stack:', errorInfo.componentStack);
        // Call onError prop if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
    // In a production app, you would send this to a monitoring service
    // Example: errorReportingService.captureError(error, errorInfo);
    }
    handleRetry = ()=>{
        // Reset the error boundary state to trigger a re-render
        this.setState({
            error: null,
            errorInfo: null,
            hasError: false
        });
    };
    render() {
        const { description = 'An unexpected error occurred.', resetLabel = 'Try Again', title = 'Something went wrong' } = this.props;
        if (this.state.hasError) {
            // If a custom fallback is provided, use it
            if (this.props.fallback) {
                return this.props.fallback;
            }
            // Otherwise, use the default error UI
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Alert"], {
                className: "my-4",
                variant: "destructive",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                        className: "mr-2 size-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                        lineNumber: 92,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertTitle"], {
                        className: "text-lg font-semibold",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                        lineNumber: 93,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertDescription"], {
                        className: "mt-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mb-2",
                                children: this.state.error?.message || description
                            }, void 0, false, {
                                fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this),
                            ("TURBOPACK compile-time value", "development") !== 'production' && this.state.errorInfo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                                className: "mt-2 text-xs",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                        children: "Error details"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                                        lineNumber: 98,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                        className: "mt-2 max-h-[200px] overflow-auto whitespace-pre-wrap rounded bg-slate-100 p-2 dark:bg-slate-900",
                                        children: [
                                            this.state.error?.stack,
                                            '\n\nComponent Stack:\n',
                                            this.state.errorInfo.componentStack
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                                        lineNumber: 99,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                                lineNumber: 97,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                className: "mt-4",
                                onClick: this.handleRetry,
                                size: "sm",
                                variant: "outline",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: "mr-2 size-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this),
                                    resetLabel
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                                lineNumber: 106,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                        lineNumber: 94,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/error-boundaries/ErrorBoundary.tsx",
                lineNumber: 91,
                columnNumber: 9
            }, this);
        }
        // If there's no error, render the children
        return this.props.children;
    }
}
const __TURBOPACK__default__export__ = ErrorBoundary;
}}),
"[project]/src/components/ui/action-button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ActionButton": (()=>ActionButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
;
const ActionButton = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forwardRef(({ actionType = 'primary', asChild = false, children, className, disabled, icon, isLoading = false, loadingText, ...props }, ref)=>{
    // Map action types to shadcn/ui button variants and additional styling
    const actionStyles = {
        danger: {
            className: 'shadow-md',
            variant: 'destructive'
        },
        primary: {
            className: 'shadow-md',
            variant: 'default'
        },
        secondary: {
            className: '',
            variant: 'secondary'
        },
        tertiary: {
            className: '',
            variant: 'outline'
        }
    };
    const { className: actionClassName, variant } = actionStyles[actionType];
    // const Comp = asChild ? Slot : "button"; // This was for an older structure, Button handles asChild now
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
        asChild: asChild,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])(actionClassName, className),
        disabled: isLoading || disabled,
        ref: ref,
        variant: variant,
        ...props,
        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "inline-flex items-center",
            children: [
                ' ',
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                    className: "mr-2 size-4 animate-spin"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/action-button.tsx",
                    lineNumber: 108,
                    columnNumber: 13
                }, this),
                loadingText || children
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/action-button.tsx",
            lineNumber: 105,
            columnNumber: 11
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "inline-flex items-center",
            children: [
                ' ',
                icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "mr-2",
                    children: icon
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/action-button.tsx",
                    lineNumber: 115,
                    columnNumber: 22
                }, this),
                children
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/action-button.tsx",
            lineNumber: 112,
            columnNumber: 11
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/action-button.tsx",
        lineNumber: 96,
        columnNumber: 7
    }, this);
});
ActionButton.displayName = 'ActionButton';
}}),
"[project]/src/components/features/vehicles/VehicleCard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>VehicleCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-ssr] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gauge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gauge$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/gauge.js [app-ssr] (ecmascript) <export default as Gauge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wrench.js [app-ssr] (ecmascript) <export default as Wrench>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/action-button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$imageUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/imageUtils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function VehicleCard({ vehicle }) {
    // Calculate latest odometer, keeping track of whether we have real data
    const hasServiceHistory = (vehicle.serviceHistory?.length || 0) > 0;
    const hasInitialOdometer = vehicle.initialOdometer !== null;
    const latestOdometer = hasServiceHistory ? Math.max(vehicle.initialOdometer || 0, ...(vehicle.serviceHistory || []).map((s)=>s.odometer)) : vehicle.initialOdometer || 0;
    const formatOdometerDisplay = ()=>{
        if (hasServiceHistory) {
            // If we have service history, we can always show the latest reading
            return `${latestOdometer.toLocaleString()} miles`;
        } else if (hasInitialOdometer) {
            // Only initial odometer, show it
            return `${latestOdometer.toLocaleString()} miles`;
        } else {
            // No odometer data at all
            return 'Not recorded';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
        className: "flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                className: "relative p-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative aspect-[16/10] w-full",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        alt: `${vehicle.make} ${vehicle.model}`,
                        className: "bg-muted object-cover",
                        "data-ai-hint": "luxury car",
                        fill: true,
                        priority: true,
                        sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
                        src: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$imageUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSafeVehicleImageUrl"])(vehicle.imageUrl, vehicle.id)
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 69,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                    lineNumber: 68,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "flex grow flex-col p-5",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                        className: "mb-1 text-xl font-semibold text-primary",
                        children: [
                            vehicle.make,
                            " ",
                            vehicle.model
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardDescription"], {
                        className: "mb-3 text-sm text-muted-foreground",
                        children: [
                            vehicle.year,
                            " ",
                            vehicle.color && `• ${vehicle.color}`,
                            ' ',
                            vehicle.licensePlate && `• Plate: ${vehicle.licensePlate}`
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 84,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {
                        className: "my-3 bg-border/50"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grow space-y-2.5 text-sm text-foreground",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$gauge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Gauge$3e$__["Gauge"], {
                                        className: "mr-2.5 size-4 shrink-0 text-accent"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                        lineNumber: 91,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-muted-foreground",
                                                children: "Latest Odometer: "
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                                lineNumber: 93,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                className: "font-semibold",
                                                children: formatOdometerDisplay()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                                lineNumber: 94,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                        lineNumber: 92,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                lineNumber: 90,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"], {
                                        className: "mr-2.5 size-4 shrink-0 text-accent"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                        lineNumber: 100,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-muted-foreground",
                                                children: "Services Logged: "
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                                lineNumber: 102,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                className: "font-semibold",
                                                children: vehicle.serviceHistory?.length || 0
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                                lineNumber: 103,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                        lineNumber: 101,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                                lineNumber: 99,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                lineNumber: 80,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardFooter"], {
                className: "border-t border-border/60 bg-muted/20 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionButton"], {
                    actionType: "tertiary",
                    asChild: true,
                    className: "w-full",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                        className: "size-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 115,
                        columnNumber: 17
                    }, void 0),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: `/vehicles/${vehicle.id}`,
                        children: "Manage Vehicle"
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 117,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                lineNumber: 110,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
function InfoPill({ icon: Icon, label, value }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center space-x-2 rounded-full bg-muted/50 px-3 py-1 text-xs text-foreground",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                className: "size-3.5 text-accent"
            }, void 0, false, {
                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                lineNumber: 127,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                children: [
                    label,
                    ": ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                        children: value
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                        lineNumber: 129,
                        columnNumber: 18
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
                lineNumber: 128,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/features/vehicles/VehicleCard.tsx",
        lineNumber: 126,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Unified Generic Toast Service - Following SRP and DRY principles
 *
 * This service provides a consistent interface for all toast notifications
 * across the application, eliminating code duplication and ensuring
 * consistent messaging patterns using generics.
 */ __turbopack_context__.s({
    "GenericEntityToastService": (()=>GenericEntityToastService),
    "ServiceRecordToastService": (()=>ServiceRecordToastService),
    "createEntityToastService": (()=>createEntityToastService),
    "createSimpleEntityToastService": (()=>createSimpleEntityToastService),
    "delegationToast": (()=>delegationToast),
    "employeeToast": (()=>employeeToast),
    "serviceRecordToast": (()=>serviceRecordToast),
    "taskToast": (()=>taskToast),
    "toastService": (()=>toastService),
    "vehicleToast": (()=>vehicleToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-toast.ts [app-ssr] (ecmascript)");
;
/**
 * Base toast service class following SRP
 */ class ToastService {
    /**
   * Show a generic toast notification
   */ show(options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"])({
            title: options.title,
            description: options.description,
            variant: options.variant || 'default',
            ...options.duration && {
                duration: options.duration
            }
        });
    }
    /**
   * Show a success toast notification
   */ success(title, description) {
        return this.show({
            title,
            description,
            variant: 'default'
        });
    }
    /**
   * Show an error toast notification
   */ error(title, description) {
        return this.show({
            title,
            description,
            variant: 'destructive'
        });
    }
    /**
   * Show an info toast notification
   */ info(title, description) {
        return this.show({
            title,
            description,
            variant: 'default'
        });
    }
}
class GenericEntityToastService extends ToastService {
    config;
    constructor(config){
        super();
        this.config = config;
    }
    /**
   * Show entity created success toast
   */ entityCreated(entity) {
        const displayName = this.config.getDisplayName(entity);
        return this.success(this.config.messages.created.title, this.config.messages.created.description(displayName));
    }
    /**
   * Show entity updated success toast
   */ entityUpdated(entity) {
        const displayName = this.config.getDisplayName(entity);
        return this.success(this.config.messages.updated.title, this.config.messages.updated.description(displayName));
    }
    /**
   * Show entity deleted success toast
   */ entityDeleted(entity) {
        const displayName = this.config.getDisplayName(entity);
        return this.success(this.config.messages.deleted.title, this.config.messages.deleted.description(displayName));
    }
    /**
   * Show entity creation error toast
   */ entityCreationError(error) {
        return this.error(this.config.messages.creationError.title, this.config.messages.creationError.description(error));
    }
    /**
   * Show entity update error toast
   */ entityUpdateError(error) {
        return this.error(this.config.messages.updateError.title, this.config.messages.updateError.description(error));
    }
    /**
   * Show entity deletion error toast
   */ entityDeletionError(error) {
        return this.error(this.config.messages.deletionError.title, this.config.messages.deletionError.description(error));
    }
}
// =============================================================================
// ENTITY CONFIGURATIONS - Define toast messages for each domain
// =============================================================================
/**
 * Employee entity toast configuration
 */ const employeeToastConfig = {
    entityName: 'Employee',
    getDisplayName: (employee)=>employee.name,
    messages: {
        created: {
            title: 'Employee Added',
            description: (name)=>`The employee "${name}" has been successfully created.`
        },
        updated: {
            title: 'Employee Updated Successfully',
            description: (name)=>`${name} has been updated.`
        },
        deleted: {
            title: 'Employee Deleted Successfully',
            description: (name)=>`${name} has been permanently removed from the system.`
        },
        creationError: {
            title: 'Failed to Create Employee',
            description: (error)=>error || 'An unexpected error occurred while creating the employee.'
        },
        updateError: {
            title: 'Update Failed',
            description: (error)=>error || 'An unexpected error occurred while updating the employee.'
        },
        deletionError: {
            title: 'Failed to Delete Employee',
            description: (error)=>error || 'An unexpected error occurred while deleting the employee.'
        }
    }
};
/**
 * Delegation entity toast configuration
 */ const delegationToastConfig = {
    entityName: 'Delegation',
    getDisplayName: (delegation)=>delegation.event || delegation.location || 'Delegation',
    messages: {
        created: {
            title: 'Delegation Created',
            description: (name)=>`The delegation "${name}" has been successfully created.`
        },
        updated: {
            title: 'Delegation Updated Successfully',
            description: (name)=>`${name} has been updated.`
        },
        deleted: {
            title: 'Delegation Deleted Successfully',
            description: (name)=>`${name} has been permanently removed.`
        },
        creationError: {
            title: 'Failed to Create Delegation',
            description: (error)=>error || 'An unexpected error occurred while creating the delegation.'
        },
        updateError: {
            title: 'Update Failed',
            description: (error)=>error || 'An unexpected error occurred while updating the delegation.'
        },
        deletionError: {
            title: 'Failed to Delete Delegation',
            description: (error)=>error || 'An unexpected error occurred while deleting the delegation.'
        }
    }
};
/**
 * Vehicle entity toast configuration
 */ const vehicleToastConfig = {
    entityName: 'Vehicle',
    getDisplayName: (vehicle)=>`${vehicle.make} ${vehicle.model}`,
    messages: {
        created: {
            title: 'Vehicle Added',
            description: (name)=>`The vehicle "${name}" has been successfully created.`
        },
        updated: {
            title: 'Vehicle Updated Successfully',
            description: (name)=>`${name} has been updated.`
        },
        deleted: {
            title: 'Vehicle Deleted Successfully',
            description: (name)=>`${name} has been permanently removed.`
        },
        creationError: {
            title: 'Failed to Create Vehicle',
            description: (error)=>error || 'An unexpected error occurred while creating the vehicle.'
        },
        updateError: {
            title: 'Update Failed',
            description: (error)=>error || 'An unexpected error occurred while updating the vehicle.'
        },
        deletionError: {
            title: 'Failed to Delete Vehicle',
            description: (error)=>error || 'An unexpected error occurred while deleting the vehicle.'
        }
    }
};
/**
 * Task entity toast configuration
 */ const taskToastConfig = {
    entityName: 'Task',
    getDisplayName: (task)=>task.title || task.name || 'Task',
    messages: {
        created: {
            title: 'Task Created',
            description: (name)=>`The task "${name}" has been successfully created.`
        },
        updated: {
            title: 'Task Updated Successfully',
            description: (name)=>`${name} has been updated.`
        },
        deleted: {
            title: 'Task Deleted Successfully',
            description: (name)=>`${name} has been permanently removed.`
        },
        creationError: {
            title: 'Failed to Create Task',
            description: (error)=>error || 'An unexpected error occurred while creating the task.'
        },
        updateError: {
            title: 'Update Failed',
            description: (error)=>error || 'An unexpected error occurred while updating the task.'
        },
        deletionError: {
            title: 'Failed to Delete Task',
            description: (error)=>error || 'An unexpected error occurred while deleting the task.'
        }
    }
};
class ServiceRecordToastService extends ToastService {
    /**
   * Show service record created success toast
   */ serviceRecordCreated(vehicleName, serviceType) {
        return this.success('Service Record Added', `${serviceType} service for "${vehicleName}" has been successfully logged.`);
    }
    /**
   * Show service record updated success toast
   */ serviceRecordUpdated(vehicleName, serviceType) {
        return this.success('Service Record Updated', `${serviceType} service for "${vehicleName}" has been updated.`);
    }
    /**
   * Show service record deleted success toast
   */ serviceRecordDeleted(vehicleName, serviceType) {
        return this.success('Service Record Deleted', `${serviceType} service record for "${vehicleName}" has been permanently removed.`);
    }
    /**
   * Show service record creation error toast
   */ serviceRecordCreationError(error) {
        return this.error('Failed to Log Service Record', error || 'An unexpected error occurred while logging the service record.');
    }
    /**
   * Show service record update error toast
   */ serviceRecordUpdateError(error) {
        return this.error('Update Failed', error || 'An unexpected error occurred while updating the service record.');
    }
    /**
   * Show service record deletion error toast
   */ serviceRecordDeletionError(error) {
        return this.error('Failed to Delete Service Record', error || 'An unexpected error occurred while deleting the service record.');
    }
}
function createEntityToastService(config) {
    return new GenericEntityToastService(config);
}
function createSimpleEntityToastService(entityName, getDisplayName) {
    const config = {
        entityName,
        getDisplayName,
        messages: {
            created: {
                title: `${entityName} Created`,
                description: (displayName)=>`The ${entityName.toLowerCase()} "${displayName}" has been successfully created.`
            },
            updated: {
                title: `${entityName} Updated Successfully`,
                description: (displayName)=>`${displayName} has been updated.`
            },
            deleted: {
                title: `${entityName} Deleted Successfully`,
                description: (displayName)=>`${displayName} has been permanently removed.`
            },
            creationError: {
                title: `Failed to Create ${entityName}`,
                description: (error)=>error || `An unexpected error occurred while creating the ${entityName.toLowerCase()}.`
            },
            updateError: {
                title: 'Update Failed',
                description: (error)=>error || `An unexpected error occurred while updating the ${entityName.toLowerCase()}.`
            },
            deletionError: {
                title: `Failed to Delete ${entityName}`,
                description: (error)=>error || `An unexpected error occurred while deleting the ${entityName.toLowerCase()}.`
            }
        }
    };
    return new GenericEntityToastService(config);
}
const toastService = new ToastService();
const employeeToast = new GenericEntityToastService(employeeToastConfig);
const delegationToast = new GenericEntityToastService(delegationToastConfig);
const vehicleToast = new GenericEntityToastService(vehicleToastConfig);
const taskToast = new GenericEntityToastService(taskToastConfig);
const serviceRecordToast = new ServiceRecordToastService();
}}),
"[project]/src/hooks/forms/useFormToast.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Form Toast Hook - Integrates generic toast service with form operations
 *
 * This hook provides a consistent interface for showing toast notifications
 * in form components, supporting both generic and entity-specific messaging.
 */ __turbopack_context__.s({
    "useEntityFormToast": (()=>useEntityFormToast),
    "useFormToast": (()=>useFormToast),
    "usePredefinedEntityToast": (()=>usePredefinedEntityToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)");
;
;
function useFormToast() {
    const showSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title, description)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success(title, description);
    }, []);
    const showError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title, description)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].error(title, description);
    }, []);
    const showInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title, description)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].info(title, description);
    }, []);
    const showFormSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((options)=>{
        return showSuccess(options?.successTitle || 'Success', options?.successDescription || 'Operation completed successfully');
    }, [
        showSuccess
    ]);
    const showFormError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error, options)=>{
        const errorMessage = error instanceof Error ? error.message : error;
        return showError(options?.errorTitle || 'Error', options?.errorDescription || errorMessage || 'An unexpected error occurred');
    }, [
        showError
    ]);
    return {
        showSuccess,
        showError,
        showInfo,
        showFormSuccess,
        showFormError
    };
}
function useEntityFormToast(entityConfig, entityService) {
    const { showFormSuccess, showFormError } = useFormToast();
    // Create or use provided entity service
    const entityToastService = entityService || (entityConfig ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createEntityToastService"])(entityConfig) : null);
    const showEntityCreated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entity)=>{
        if (entityToastService) {
            return entityToastService.entityCreated(entity);
        }
        return showFormSuccess({
            successTitle: 'Created',
            successDescription: 'Item has been created successfully'
        });
    }, [
        entityToastService,
        showFormSuccess
    ]);
    const showEntityUpdated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entity)=>{
        if (entityToastService) {
            return entityToastService.entityUpdated(entity);
        }
        return showFormSuccess({
            successTitle: 'Updated',
            successDescription: 'Item has been updated successfully'
        });
    }, [
        entityToastService,
        showFormSuccess
    ]);
    const showEntityDeleted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entity)=>{
        if (entityToastService) {
            return entityToastService.entityDeleted(entity);
        }
        return showFormSuccess({
            successTitle: 'Deleted',
            successDescription: 'Item has been deleted successfully'
        });
    }, [
        entityToastService,
        showFormSuccess
    ]);
    const showEntityCreationError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error)=>{
        if (entityToastService) {
            const errorMessage = error instanceof Error ? error.message : error;
            return entityToastService.entityCreationError(errorMessage);
        }
        return showFormError(error, {
            errorTitle: 'Creation Failed'
        });
    }, [
        entityToastService,
        showFormError
    ]);
    const showEntityUpdateError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error)=>{
        if (entityToastService) {
            const errorMessage = error instanceof Error ? error.message : error;
            return entityToastService.entityUpdateError(errorMessage);
        }
        return showFormError(error, {
            errorTitle: 'Update Failed'
        });
    }, [
        entityToastService,
        showFormError
    ]);
    const showEntityDeletionError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error)=>{
        if (entityToastService) {
            const errorMessage = error instanceof Error ? error.message : error;
            return entityToastService.entityDeletionError(errorMessage);
        }
        return showFormError(error, {
            errorTitle: 'Deletion Failed'
        });
    }, [
        entityToastService,
        showFormError
    ]);
    return {
        showEntityCreated,
        showEntityUpdated,
        showEntityDeleted,
        showEntityCreationError,
        showEntityUpdateError,
        showEntityDeletionError,
        // Also expose generic methods
        showFormSuccess,
        showFormError
    };
}
function usePredefinedEntityToast(entityType) {
    let entityService;
    // Lazy import to avoid circular dependencies
    switch(entityType){
        case 'employee':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").employeeToast;
            break;
        case 'vehicle':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").vehicleToast;
            break;
        case 'task':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").taskToast;
            break;
        case 'delegation':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").delegationToast;
            break;
        default:
            throw new Error(`Unknown entity type: ${entityType}`);
    }
    return useEntityFormToast(undefined, entityService);
}
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[project]/src/lib/services/WebSocketManager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Unified WebSocket Manager for WorkHub Application
 * Provides centralized WebSocket connection management with domain-specific channels
 * Follows SRP and DRY principles with smart fallback strategies
 * @module services/WebSocketManager
 */ __turbopack_context__.s({
    "WebSocketManager": (()=>WebSocketManager),
    "getWebSocketManager": (()=>getWebSocketManager),
    "useWebSocketState": (()=>useWebSocketState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-ssr] (ecmascript)");
;
;
;
;
class WebSocketManager {
    static instance = null;
    config;
    connectionState = 'disconnected';
    reconnectAttempts = 0;
    socket = null;
    stateListeners = new Set();
    subscriptions = new Map();
    constructor(config = {}){
        this.config = {
            autoConnect: config.autoConnect ?? true,
            reconnectAttempts: config.reconnectAttempts ?? 5,
            reconnectDelay: config.reconnectDelay ?? 1000,
            timeout: config.timeout ?? 10_000,
            url: config.url ?? process.env.NEXT_PUBLIC_WEBSOCKET_URL ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])().wsUrl.replace('ws://', 'http://').replace('wss://', 'https://')
        };
        if (this.config.autoConnect) {
            this.connect();
        }
        // Subscribe to token refresh events
        this.setupTokenRefreshHandling();
    }
    /**
   * Get singleton instance
   */ static getInstance(config) {
        WebSocketManager.instance ??= new WebSocketManager(config);
        return WebSocketManager.instance;
    }
    /**
   * Connect to WebSocket server
   */ async connect() {
        if (this.socket?.connected) {
            console.debug('WebSocket already connected');
            return;
        }
        this.setConnectionState('connecting');
        try {
            // Get current session and token for authentication
            const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (error) {
                console.warn('Failed to get session for WebSocket connection:', error);
            }
            const connectionOptions = {
                forceNew: true,
                timeout: this.config.timeout,
                transports: [
                    'websocket',
                    'polling'
                ],
                withCredentials: true
            };
            // Add authentication token if available
            if (session?.access_token) {
                connectionOptions.auth = {
                    token: session.access_token
                };
                console.debug('🔐 WebSocket connecting with authentication token');
                // Validate token expiration
                const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;
                const now = Date.now();
                const timeUntilExpiry = tokenExpiry - now;
                if (timeUntilExpiry <= 60_000) {
                    // Less than 1 minute
                    console.warn('⚠️ WebSocket token expires soon, may need refresh');
                }
            } else {
                console.warn('⚠️ WebSocket connecting without authentication token - connection may fail');
            }
            this.socket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(this.config.url, connectionOptions);
            this.setupEventHandlers();
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.setConnectionState('error');
            this.scheduleReconnect();
        }
    }
    /**
   * Cleanup resources
   */ destroy() {
        this.disconnect();
        this.subscriptions.clear();
        this.stateListeners.clear();
        WebSocketManager.instance = null;
    }
    /**
   * Disconnect from WebSocket server
   */ disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        this.setConnectionState('disconnected');
        this.reconnectAttempts = 0;
    }
    /**
   * Emit event to specific domain channel
   */ emit(channel, event, data) {
        if (!this.socket?.connected) {
            console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);
            return;
        }
        this.socket.emit(event, data);
    }
    /**
   * Get current connection state
   */ getConnectionState() {
        return this.connectionState;
    }
    /**
   * Check if WebSocket is connected
   */ isConnected() {
        return this.connectionState === 'connected' && this.socket?.connected === true;
    }
    /**
   * Join domain-specific room
   */ joinRoom(room) {
        if (!this.socket?.connected) {
            console.warn(`Cannot join room ${room} - WebSocket not connected`);
            return;
        }
        this.socket.emit('join-room', room);
    }
    /**
   * Leave domain-specific room
   */ leaveRoom(room) {
        if (!this.socket?.connected) {
            return;
        }
        this.socket.emit('leave-room', room);
    }
    /**
   * Subscribe to connection state changes
   */ onStateChange(callback) {
        this.stateListeners.add(callback);
        return ()=>{
            this.stateListeners.delete(callback);
        };
    }
    /**
   * Subscribe to domain-specific events
   */ subscribe(channel, event, callback) {
        const eventKey = `${channel}:${event}`;
        if (!this.subscriptions.has(eventKey)) {
            this.subscriptions.set(eventKey, new Set());
        }
        this.subscriptions.get(eventKey).add(callback);
        // Set up socket listener if connected
        if (this.socket?.connected && event) {
            this.socket.on(event, callback);
        }
        // Return unsubscribe function
        return ()=>{
            const callbacks = this.subscriptions.get(eventKey);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.subscriptions.delete(eventKey);
                }
            }
            if (this.socket && event) {
                this.socket.off(event, callback);
            }
        };
    }
    /**
   * Handle authentication errors by triggering token refresh
   */ handleAuthenticationError() {
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        console.log('🔐 Handling WebSocket authentication error...');
        // Disconnect current socket to prevent further auth errors
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        // Attempt to refresh token
        tokenRefreshService.refreshNow().then((success)=>{
            if (success) {
                console.log('🔄 Token refresh successful, retrying WebSocket connection');
            // The reconnection will be handled by setupTokenRefreshHandling
            } else {
                console.error('🔄 Token refresh failed, scheduling normal reconnect');
                this.scheduleReconnect();
            }
        }).catch((error)=>{
            console.error('🔄 Token refresh error:', error);
            this.scheduleReconnect();
        });
    }
    /**
   * Resubscribe to all events after reconnection
   */ resubscribeToEvents() {
        if (!this.socket) return;
        for (const [eventKey, callbacks] of this.subscriptions){
            const [, event] = eventKey.split(':');
            for (const callback of callbacks){
                if (event) {
                    this.socket.on(event, callback);
                }
            }
        }
    }
    /**
   * Schedule reconnection with exponential backoff
   */ scheduleReconnect() {
        if (this.reconnectAttempts >= this.config.reconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.setConnectionState('error');
            return;
        }
        this.setConnectionState('reconnecting');
        this.reconnectAttempts++;
        const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        setTimeout(()=>{
            console.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`);
            this.connect();
        }, delay);
    }
    /**
   * Set connection state and notify listeners
   */ setConnectionState(state) {
        if (this.connectionState !== state) {
            this.connectionState = state;
            for (const listener of this.stateListeners)listener(state);
        }
    }
    /**
   * Setup socket event handlers
   */ setupEventHandlers() {
        if (!this.socket) return;
        this.socket.on('connect', ()=>{
            console.info('WebSocket connected');
            this.setConnectionState('connected');
            this.reconnectAttempts = 0;
            this.resubscribeToEvents();
        });
        this.socket.on('disconnect', (reason)=>{
            console.warn('WebSocket disconnected:', reason);
            this.setConnectionState('disconnected');
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, don't reconnect automatically
                return;
            }
            this.scheduleReconnect();
        });
        this.socket.on('connect_error', (error)=>{
            console.error('WebSocket connection error:', error);
            this.setConnectionState('error');
            // Check if error is authentication-related
            if (error.message?.includes('Authentication') || error.message?.includes('token') || error.message?.includes('No token provided') || error.message?.includes('Unauthorized')) {
                console.warn('🔐 Authentication error detected, attempting token refresh');
                this.handleAuthenticationError();
            } else {
                this.scheduleReconnect();
            }
        });
        // Listen for authentication errors from the server
        this.socket.on('auth_error', (errorData)=>{
            console.error('🔐 Server authentication error:', errorData);
            this.handleAuthenticationError();
        });
        // Listen for token refresh requests from server
        this.socket.on('token_refresh_required', ()=>{
            console.warn('🔄 Server requested token refresh');
            this.handleAuthenticationError();
        });
    }
    /**
   * Setup token refresh event handling
   */ setupTokenRefreshHandling() {
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        tokenRefreshService.subscribe((event, _data)=>{
            switch(event){
                case 'critical_refresh_failed':
                    {
                        console.error('🔄 Critical token refresh failure, disconnecting WebSocket');
                        this.disconnect();
                        this.setConnectionState('error');
                        break;
                    }
                case 'refresh_failed':
                    {
                        console.error('🔄 Token refresh failed, WebSocket may lose connection');
                        break;
                    }
                case 'refresh_success':
                    {
                        console.log('🔄 Token refreshed, reconnecting WebSocket with new token');
                        // Disconnect current connection and reconnect with new token
                        if (this.socket) {
                            this.socket.disconnect();
                            this.socket = null;
                        }
                        // Reconnect with fresh token
                        setTimeout(()=>this.connect(), 500);
                        break;
                    }
            }
        });
    }
}
const getWebSocketManager = (config)=>{
    return WebSocketManager.getInstance(config);
};
const useWebSocketState = ()=>{
    const manager = getWebSocketManager();
    return {
        connectionState: manager.getConnectionState(),
        isConnected: manager.isConnected()
    };
};
}}),
"[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Smart Query Hook with WebSocket Integration
 * Automatically disables polling when WebSocket is connected
 * Follows modern best practices for real-time data management
 * @module hooks/useSmartQuery
 */ __turbopack_context__.s({
    "useCrudQuery": (()=>useCrudQuery),
    "useNotificationQuery": (()=>useNotificationQuery),
    "useReliabilityQuery": (()=>useReliabilityQuery),
    "useSmartQuery": (()=>useSmartQuery),
    "useSystemQuery": (()=>useSystemQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/WebSocketManager.ts [app-ssr] (ecmascript)");
;
;
;
/**
 * Mapping of domain channels to Socket.IO room names
 * This ensures the frontend joins the correct rooms that the backend emits events to
 */ const CHANNEL_ROOM_MAPPING = {
    crud: 'entity-updates',
    notifications: 'notifications-monitoring',
    reliability: 'reliability-monitoring',
    system: 'system-monitoring'
};
function useCrudQuery(queryKey, queryFn, entityType, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'crud',
        events: [
            `${entityType}:created`,
            `${entityType}:updated`,
            `${entityType}:deleted`,
            `refresh:${entityType}`
        ],
        fallbackInterval: 30_000
    }, options);
}
function useNotificationQuery(queryKey, queryFn, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'notifications',
        events: [
            'notification-created',
            'notification-updated'
        ],
        fallbackInterval: 60_000
    }, options);
}
function useReliabilityQuery(queryKey, queryFn, monitoringType, options) {
    // Increased intervals to reduce aggressive polling and cancellations
    const intervalMap = {
        alerts: 30_000,
        'circuit-breakers': 60_000,
        health: 45_000,
        metrics: 60_000
    };
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Join reliability monitoring room when WebSocket is connected
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (webSocketManager.isConnected()) {
            console.debug(`[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`);
            webSocketManager.joinRoom('reliability-monitoring');
        }
        // Subscribe to connection state changes to join room when connected
        const unsubscribe = webSocketManager.onStateChange((state)=>{
            if (state === 'connected') {
                console.debug(`[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`);
                webSocketManager.joinRoom('reliability-monitoring');
            }
        });
        return ()=>{
            unsubscribe();
            // Leave room when component unmounts
            if (webSocketManager.isConnected()) {
                webSocketManager.leaveRoom('reliability-monitoring');
            }
        };
    }, [
        webSocketManager,
        monitoringType
    ]);
    return useSmartQuery(queryKey, queryFn, {
        channel: 'reliability',
        events: [
            `${monitoringType}-update`,
            `${monitoringType}-created`,
            `${monitoringType}-resolved`
        ],
        fallbackInterval: intervalMap[monitoringType]
    }, options);
}
function useSmartQuery(queryKey, queryFn, config, options) {
    const { channel, enableFallback = true, enableWebSocket = true, events, fallbackInterval = 30_000 } = config;
    const [isWebSocketConnected, setIsWebSocketConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Track WebSocket connection state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateConnectionState = ()=>{
            setIsWebSocketConnected(webSocketManager.isConnected());
        };
        // Initial state
        updateConnectionState();
        // Subscribe to state changes
        const unsubscribe = webSocketManager.onStateChange(updateConnectionState);
        return unsubscribe;
    }, [
        webSocketManager
    ]);
    // Determine if we should use fallback polling
    const isUsingFallback = enableFallback && (!enableWebSocket || !isWebSocketConnected);
    // Configure React Query options based on WebSocket state
    const queryOptions = {
        // Longer cache time for better performance
        gcTime: 10 * 60 * 1000,
        queryFn,
        queryKey,
        // Disable polling when WebSocket is connected
        refetchInterval: isUsingFallback ? fallbackInterval : false,
        refetchOnReconnect: true,
        // Enable background refetch only when using fallback
        refetchOnWindowFocus: isUsingFallback,
        // Shorter stale time when using WebSocket (real-time updates)
        staleTime: isWebSocketConnected ? 0 : 30_000,
        ...options
    };
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const queryResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])(queryOptions);
    // Manage Socket.IO room joining/leaving based on channel
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enableWebSocket || !isWebSocketConnected) {
            return;
        }
        const roomName = CHANNEL_ROOM_MAPPING[channel];
        if (!roomName) {
            console.warn(`[SmartQuery] No room mapping found for channel: ${channel}`);
            return;
        }
        // Join the appropriate room for this channel
        try {
            webSocketManager.joinRoom(roomName);
            console.log(`[SmartQuery] Joined room: ${roomName} for channel: ${channel}`);
        } catch (error) {
            console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);
        }
        // Cleanup: leave room when component unmounts or dependencies change
        return ()=>{
            try {
                webSocketManager.leaveRoom(roomName);
                console.log(`[SmartQuery] Left room: ${roomName} for channel: ${channel}`);
            } catch (error) {
                console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);
            }
        };
    }, [
        enableWebSocket,
        isWebSocketConnected,
        channel,
        webSocketManager
    ]);
    // Subscribe to WebSocket events for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {
            return;
        }
        const unsubscribers = [];
        // Subscribe to each event
        for (const event of events){
            const unsubscribe = webSocketManager.subscribe(channel, event, (data)=>{
                console.log(`[SmartQuery] WebSocket event received: ${channel}:${event}`, data);
                // Invalidate the specific query to trigger refetch
                queryClient.invalidateQueries({
                    queryKey
                });
            });
            unsubscribers.push(unsubscribe);
        }
        return ()=>{
            for (const unsubscribe of unsubscribers)unsubscribe();
        };
    }, [
        enableWebSocket,
        isWebSocketConnected,
        events,
        channel,
        webSocketManager,
        queryClient,
        queryKey
    ]);
    return {
        ...queryResult,
        isUsingFallback,
        isWebSocketConnected
    };
}
function useSystemQuery(queryKey, queryFn, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'system',
        events: [
            'system-update',
            'config-changed'
        ],
        fallbackInterval: 120_000
    }, options);
}
}}),
"[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Custom hook for notification management using Zustand AppStore
 * @module hooks/useNotifications
 */ __turbopack_context__.s({
    "useNotifications": (()=>useNotifications),
    "useWorkHubNotifications": (()=>useWorkHubNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
;
;
const useNotifications = ()=>{
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.addNotification);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.removeNotification);
    const clearAllNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.clearAllNotifications);
    const unreadCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.unreadNotificationCount);
    /**
   * Show a success notification
   */ const showSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'success'
        });
    }, [
        addNotification
    ]);
    /**
   * Show an error notification
   */ const showError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'error'
        });
    }, [
        addNotification
    ]);
    /**
   * Show a warning notification
   */ const showWarning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'warning'
        });
    }, [
        addNotification
    ]);
    /**
   * Show an info notification
   */ const showInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'info'
        });
    }, [
        addNotification
    ]);
    /**
   * Show a notification for API operation results
   */ const showApiResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((success, successMessage, errorMessage)=>{
        if (success) {
            showSuccess(successMessage);
        } else {
            showError(errorMessage);
        }
    }, [
        showSuccess,
        showError
    ]);
    /**
   * Show a notification with auto-dismiss after specified time
   */ const showTemporary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((type, message, dismissAfter = 5000)=>{
        addNotification({
            message,
            type
        });
        // Auto-dismiss after specified time
        setTimeout(()=>{
            // Note: This is a simplified approach. In a real implementation,
            // you might want to store the notification ID and remove specifically that one
            const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
            const latestNotification = notifications.at(-1);
            if (latestNotification && latestNotification.message === message) {
                removeNotification(latestNotification.id);
            }
        }, dismissAfter);
    }, [
        addNotification,
        removeNotification
    ]);
    /**
   * Show a loading notification that can be updated
   */ const showLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message = 'Loading...')=>{
        addNotification({
            message,
            type: 'info'
        });
        // Return the notification ID for potential updates
        const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
        return notifications.at(-1)?.id;
    }, [
        addNotification
    ]);
    /**
   * Update a loading notification to success or error
   */ const updateLoadingNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((notificationId, success, message)=>{
        removeNotification(notificationId);
        if (success) {
            showSuccess(message);
        } else {
            showError(message);
        }
    }, [
        removeNotification,
        showSuccess,
        showError
    ]);
    return {
        clearAllNotifications,
        // Store methods
        removeNotification,
        // Advanced methods
        showApiResult,
        showError,
        showInfo,
        showLoading,
        // Basic notification methods
        showSuccess,
        showTemporary,
        showWarning,
        unreadCount,
        updateLoadingNotification
    };
};
const useWorkHubNotifications = ()=>{
    const { clearAllNotifications, removeNotification, showError, showInfo, showSuccess, showWarning, unreadCount } = useNotifications();
    /**
   * Show delegation-related notifications
   */ const showDelegationUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'delegation',
            message,
            type: 'delegation-update'
        });
    }, []);
    /**
   * Show vehicle maintenance notifications
   */ const showVehicleMaintenance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'vehicle',
            message,
            type: 'vehicle-maintenance'
        });
    }, []);
    /**
   * Show task assignment notifications
   */ const showTaskAssigned = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'task',
            message,
            type: 'task-assigned'
        });
    }, []);
    /**
   * Show employee update notifications
   */ const showEmployeeUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'employee',
            message,
            type: 'employee-update'
        });
    }, []);
    return {
        clearAllNotifications,
        // Management
        removeNotification,
        // WorkHub-specific notifications
        showDelegationUpdate,
        showEmployeeUpdate,
        showError,
        showInfo,
        // Basic notifications
        showSuccess,
        showTaskAssigned,
        showVehicleMaintenance,
        showWarning,
        unreadCount
    };
};
}}),
"[project]/src/lib/api/services/domain/reliabilityApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReliabilityApiService": (()=>ReliabilityApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const ReliabilityTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class ReliabilityApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/reliability';
    transformer = ReliabilityTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 1 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getSystemHealth() {
        return this.executeWithInfrastructure('health:system', async ()=>{
            const response = await this.apiClient.get('/health');
            return response;
        });
    }
    async getDetailedHealth() {
        return this.executeWithInfrastructure('health:detailed', async ()=>{
            const response = await this.apiClient.get('/health/detailed');
            return response;
        });
    }
    async getDependencyHealth() {
        return this.executeWithInfrastructure('health:dependencies', async ()=>{
            const response = await this.apiClient.get('/health/dependencies');
            return response;
        });
    }
    async getCircuitBreakerStatus() {
        return this.executeWithInfrastructure('monitoring:circuit-breakers', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/monitoring/circuit-breakers');
                const circuitBreakers = apiResponse?.circuitBreakers || [];
                return {
                    circuitBreakers: circuitBreakers || [],
                    summary: {
                        total: circuitBreakers?.length || 0,
                        closed: circuitBreakers?.filter((cb)=>cb.state === 'CLOSED').length || 0,
                        open: circuitBreakers?.filter((cb)=>cb.state === 'OPEN').length || 0,
                        halfOpen: circuitBreakers?.filter((cb)=>cb.state === 'HALF_OPEN').length || 0
                    }
                };
            } catch (error) {
                console.error('Failed to get circuit breaker status:', error);
                return {
                    circuitBreakers: [],
                    summary: {
                        total: 0,
                        closed: 0,
                        open: 0,
                        halfOpen: 0
                    }
                };
            }
        });
    }
    async getDeduplicationMetrics() {
        return this.executeWithInfrastructure('monitoring:deduplication', async ()=>{
            const response = await this.apiClient.get('/monitoring/deduplication');
            return response;
        });
    }
    async getMetrics() {
        return this.executeWithInfrastructure('metrics:system', async ()=>{
            const response = await this.apiClient.get('/metrics', {
                headers: {
                    Accept: 'application/json'
                }
            });
            return response;
        });
    }
    async getActiveAlerts() {
        return this.executeWithInfrastructure('alerts:active', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/alerts');
                return apiResponse?.alerts || [];
            } catch (error) {
                console.error('Failed to get active alerts:', error);
                return [];
            }
        });
    }
    async getAlertHistory(page = 1, limit = 50) {
        return this.executeWithInfrastructure(`alerts:history:${page}:${limit}`, async ()=>{
            const queryParams = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });
            const response = await this.apiClient.get(`/alerts/history?${queryParams.toString()}`);
            return response;
        });
    }
    async getAlertStatistics() {
        return this.executeWithInfrastructure('alerts:statistics', async ()=>{
            try {
                const response = await this.apiClient.get('/alerts/statistics');
                return response;
            } catch (error) {
                console.error('Failed to get alert statistics:', error);
                return {
                    total: 0,
                    active: 0,
                    acknowledged: 0,
                    resolved: 0,
                    bySeverity: {
                        low: 0,
                        medium: 0,
                        high: 0,
                        critical: 0
                    },
                    averageResolutionTime: 0,
                    recentTrends: {
                        last24Hours: 0,
                        last7Days: 0,
                        last30Days: 0
                    }
                };
            }
        });
    }
    async resolveAlert(alertId, reason, resolvedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/resolve`, {
                reason,
                resolvedBy
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async acknowledgeAlert(alertId, note, acknowledgedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/acknowledge`, {
                note,
                acknowledgedBy
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async testAlerts() {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post('/alerts/test');
            return {
                success: response?.status === 'success',
                message: response?.message || 'Test alert triggered',
                testAlertId: response?.data?.id
            };
        });
    }
    async getReliabilityDashboardData() {
        const [systemHealth, detailedHealth, circuitBreakers, metrics, activeAlerts, alertStatistics] = await Promise.all([
            this.getSystemHealth(),
            this.getDetailedHealth(),
            this.getCircuitBreakerStatus(),
            this.getMetrics(),
            this.getActiveAlerts(),
            this.getAlertStatistics()
        ]);
        return {
            systemHealth,
            detailedHealth,
            circuitBreakers,
            metrics,
            activeAlerts,
            alertStatistics
        };
    }
    async isSystemHealthy() {
        try {
            const health = await this.getSystemHealth();
            return health.status === 'healthy';
        } catch (error) {
            return false;
        }
    }
    async getCriticalAlertCount() {
        try {
            const statistics = await this.getAlertStatistics();
            return statistics.bySeverity.critical;
        } catch (error) {
            return 0;
        }
    }
    async getHealthTrends(timeframe = '24h') {
        return this.executeWithInfrastructure(`health:trends:${timeframe}`, async ()=>{
            const response = await this.apiClient.get(`/health/trends?timeframe=${timeframe}`);
            return response;
        });
    }
    async getCircuitBreakerHistory(timeframe = '24h', breakerName) {
        return this.executeWithInfrastructure(`circuit-breakers:history:${timeframe}:${breakerName || 'all'}`, async ()=>{
            const params = new URLSearchParams({
                timeframe
            });
            if (breakerName) {
                params.append('breakerName', breakerName);
            }
            const response = await this.apiClient.get(`/monitoring/circuit-breakers/history?${params.toString()}`);
            return response;
        });
    }
    async getHttpRequestMetrics() {
        return this.executeWithInfrastructure('http:metrics', async ()=>{
            const response = await this.apiClient.get('/monitoring/http-request-metrics');
            return response;
        });
    }
}
}}),
"[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Factory for creating and managing API service instances.
 * @module api/services/apiServiceFactory
 */ __turbopack_context__.s({
    "ApiServiceFactory": (()=>ApiServiceFactory),
    "apiServiceFactory": (()=>apiServiceFactory),
    "delegationApiService": (()=>delegationApiService),
    "employeeApiService": (()=>employeeApiService),
    "reliabilityApiService": (()=>reliabilityApiService),
    "setFactoryAuthTokenProvider": (()=>setFactoryAuthTokenProvider),
    "taskApiService": (()=>taskApiService),
    "vehicleApiService": (()=>vehicleApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/reliabilityApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
// Import secure auth token provider
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
/**
 * Get the current auth token from the secure provider
 * Uses the single source of truth for authentication tokens
 */ function getSecureAuthToken() {
    const provider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getSecureAuthTokenProvider"])();
    if (!provider) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');
        }
        return null;
    }
    try {
        return provider();
    } catch (error) {
        console.error('❌ Factory: Error getting auth token from secure provider:', error);
        return null;
    }
}
function setFactoryAuthTokenProvider(provider) {
    console.warn('⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.');
// This function is now a no-op since we use the secure provider
// The warning guides developers to use the correct function
}
class ApiServiceFactory {
    apiClient;
    delegationService;
    employeeService;
    reliabilityService;
    taskService;
    vehicleService;
    /**
   * Creates an instance of ApiServiceFactory.
   * @param config - Configuration for the API services.
   */ constructor(config){
        this.apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"]({
            ...config,
            getAuthToken: getSecureAuthToken
        });
    }
    /**
   * Gets the underlying ApiClient instance.
   * @returns The ApiClient instance.
   */ getApiClient() {
        return this.apiClient;
    }
    /**
   * Gets the Delegation API service instance.
   * @returns The DelegationApiService instance.
   */ getDelegationService() {
        if (!this.delegationService) {
            this.delegationService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationApiService"](this.apiClient);
        }
        return this.delegationService;
    }
    /**
   * Gets the Employee API service instance.
   * @returns The EmployeeApiService instance.
   */ getEmployeeService() {
        if (!this.employeeService) {
            this.employeeService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeApiService"](this.apiClient);
        }
        return this.employeeService;
    }
    /**
   * Gets the Reliability API service instance.
   * @returns The ReliabilityApiService instance.
   */ getReliabilityService() {
        if (!this.reliabilityService) {
            this.reliabilityService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReliabilityApiService"](this.apiClient);
        }
        return this.reliabilityService;
    }
    /**
   * Gets the Task API service instance.
   * @returns The TaskApiService instance.
   */ getTaskService() {
        if (!this.taskService) {
            this.taskService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskApiService"](this.apiClient);
        }
        return this.taskService;
    }
    /**
   * Gets the Vehicle API service instance.
   * @returns The VehicleApiService instance.
   */ getVehicleService() {
        if (!this.vehicleService) {
            this.vehicleService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleApiService"](this.apiClient);
        }
        return this.vehicleService;
    }
}
// Create a default factory instance for the application with environment-aware configuration
const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
const defaultConfig = {
    baseURL: envConfig.apiBaseUrl,
    headers: {
        'Content-Type': 'application/json'
    },
    retryAttempts: 3,
    timeout: 10_000
};
const apiServiceFactory = new ApiServiceFactory(defaultConfig);
const vehicleApiService = apiServiceFactory.getVehicleService();
const delegationApiService = apiServiceFactory.getDelegationService();
const taskApiService = apiServiceFactory.getTaskService();
const employeeApiService = apiServiceFactory.getEmployeeService();
const reliabilityApiService = apiServiceFactory.getReliabilityService();
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file API Service Factory - Backward Compatibility Export
 * @module api/services/apiServiceFactory
 *
 * This file provides backward compatibility for imports that expect
 * apiServiceFactory.ts instead of factory.ts
 */ // Re-export everything from the factory module
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
// Re-export secure auth provider for convenience
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
;
;
;
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/stores/queries/useVehicles.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Vehicle-related data.
 * @module stores/queries/useVehicles
 */ __turbopack_context__.s({
    "useCreateVehicle": (()=>useCreateVehicle),
    "useDeleteVehicle": (()=>useDeleteVehicle),
    "useUpdateVehicle": (()=>useUpdateVehicle),
    "useVehicle": (()=>useVehicle),
    "useVehicles": (()=>useVehicles),
    "vehicleQueryKeys": (()=>vehicleQueryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>"); // Use centralized service from factory
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
;
;
;
;
;
const vehicleQueryKeys = {
    all: [
        'vehicles'
    ],
    detail: (id)=>[
            'vehicles',
            id
        ]
};
const useVehicles = (options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...vehicleQueryKeys.all
    ], async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll();
        return response.data;
    }, 'vehicle', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
const useVehicle = (id, options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...vehicleQueryKeys.detail(id)
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getById(id), 'vehicle', {
        enabled: !!id && (options?.enabled ?? true),
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
const useCreateVehicle = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (newVehicleData)=>{
            const transformedData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].toCreateRequest(newVehicleData);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].create(transformedData);
        },
        onError: (error)=>{
            showError(`Failed to create vehicle: ${error.message || 'Unknown error occurred'}`);
        },
        onSuccess: (data)=>{
            // Invalidate and refetch all vehicles query after a successful creation
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.all
            });
            showSuccess(`Vehicle "${data.licensePlate}" has been created successfully!`);
        }
    });
};
const useUpdateVehicle = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ data, id })=>{
            const transformedData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].toUpdateRequest(data);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].update(id, transformedData);
        },
        onError: (error)=>{
            showError(`Failed to update vehicle: ${error.message || 'Unknown error occurred'}`);
        },
        onSuccess: (updatedVehicle)=>{
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.all
            });
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.detail(updatedVehicle.id)
            });
            showSuccess(`Vehicle "${updatedVehicle.licensePlate}" has been updated successfully!`);
        }
    });
};
const useDeleteVehicle = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].delete(id),
        onError: (error)=>{
            showError(`Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`);
        },
        onSuccess: (_data, id)=>{
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.all
            });
            queryClient.removeQueries({
                queryKey: vehicleQueryKeys.detail(id)
            });
            showSuccess('Vehicle has been deleted successfully!');
        }
    });
};
}}),
"[project]/src/components/features/vehicles/VehicleList.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
// import { useSocketRefresh } from '@/hooks/useSocketRefresh'; // Removed
// import { SOCKET_EVENTS } from '@/hooks/useSocket'; // Removed
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormToast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormToast.ts [app-ssr] (ecmascript)");
// import {
//   getVehicles,
//   deleteVehicle as deleteVehicleFromStore,
// } from '@/lib/store'; // Removed
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useVehicles.ts [app-ssr] (ecmascript)"); // Added
'use client';
;
;
const VehicleListContainer = ({ children })=>{
    const { showEntityDeleted, showEntityDeletionError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormToast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePredefinedEntityToast"])('vehicle');
    const { data: vehiclesData, error: vehiclesError, isFetching: isVehiclesFetching, isLoading: isVehiclesLoading, refetch: refetchVehicles } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useVehicles"])();
    const { isPending: isDeletingVehicle, mutateAsync: deleteVehicleMutation } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDeleteVehicle"])();
    const handleDelete = async (id)=>{
        if (globalThis.confirm('Are you sure you want to delete this vehicle?')) {
            try {
                await deleteVehicleMutation(id); // useDeleteVehicle expects number ID
                // Note: We need the vehicle object to show proper toast, but since it's deleted,
                // we'll use a generic message. In a real app, you might want to get the vehicle
                // data before deletion to show the proper name.
                showEntityDeleted({
                    make: 'Vehicle',
                    model: ''
                }); // Generic fallback
            // No need to manually update local state, React Query handles cache invalidation/update via useDeleteVehicle's onSettled/onSuccess
            } catch (error) {
                console.error('Error deleting vehicle:', error);
                showEntityDeletionError(error.message || 'Could not delete the vehicle.');
            // Optionally rethrow if the caller needs to handle it, though often not needed with RQ
            // throw err;
            }
        }
    };
    // Note: Real-time updates via sockets (previously useSocketRefresh) would need
    // a different implementation with React Query, e.g., listening to socket events
    // and calling queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all }).
    return children({
        error: vehiclesError?.message || null,
        fetchVehicles: refetchVehicles,
        handleDelete,
        isRefreshing: isVehiclesFetching,
        loading: isVehiclesLoading,
        vehicles: vehiclesData || []
    });
};
const __TURBOPACK__default__export__ = VehicleListContainer;
}}),
"[project]/src/components/ui/breadcrumb.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Breadcrumb": (()=>Breadcrumb),
    "BreadcrumbEllipsis": (()=>BreadcrumbEllipsis),
    "BreadcrumbItem": (()=>BreadcrumbItem),
    "BreadcrumbLink": (()=>BreadcrumbLink),
    "BreadcrumbList": (()=>BreadcrumbList),
    "BreadcrumbPage": (()=>BreadcrumbPage),
    "BreadcrumbSeparator": (()=>BreadcrumbSeparator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis.js [app-ssr] (ecmascript) <export default as MoreHorizontal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
;
const Breadcrumb = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        "aria-label": "breadcrumb",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 13,
        columnNumber: 3
    }, this));
Breadcrumb.displayName = 'Breadcrumb';
const BreadcrumbList = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('flex flex-wrap items-center gap-1.5 break-words text-sm', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 29,
        columnNumber: 3
    }, this));
BreadcrumbList.displayName = 'BreadcrumbList';
const BreadcrumbItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('inline-flex items-center gap-1.5', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 44,
        columnNumber: 3
    }, this));
BreadcrumbItem.displayName = 'BreadcrumbItem';
const BreadcrumbLink = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ asChild, className, ...props }, ref)=>{
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"] : 'a';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('transition-colors hover:text-foreground', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
});
BreadcrumbLink.displayName = 'BreadcrumbLink';
const BreadcrumbPage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "aria-current": "page",
        "aria-disabled": "true",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('font-normal text-foreground', className),
        ref: ref,
        role: "link",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 74,
        columnNumber: 3
    }, this));
BreadcrumbPage.displayName = 'BreadcrumbPage';
const BreadcrumbSeparator = ({ children, className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "aria-hidden": "true",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('[&>svg]:size-3.5', className),
        role: "presentation",
        ...props,
        children: children ?? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
            className: "size-4"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/breadcrumb.tsx",
            lineNumber: 96,
            columnNumber: 18
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 90,
        columnNumber: 3
    }, this);
BreadcrumbSeparator.displayName = 'BreadcrumbSeparator';
const BreadcrumbEllipsis = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "aria-hidden": "true",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('flex h-9 w-9 items-center justify-center', className),
        role: "presentation",
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__["MoreHorizontal"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                lineNumber: 111,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "sr-only",
                children: "More"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                lineNumber: 112,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 105,
        columnNumber: 3
    }, this);
BreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';
;
}}),
"[project]/src/components/ui/app-breadcrumb.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppBreadcrumb": (()=>AppBreadcrumb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/breadcrumb.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
;
;
function AppBreadcrumb({ className, homeHref = '/', homeLabel = 'Dashboard', showContainer = true }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const pathSegments = pathname ? pathname.split('/').filter(Boolean) : [];
    /**
   * Format path segments with intelligent handling of different segment types
   */ const formatSegment = (segment)=>{
        // Handle numeric IDs (don't capitalize, show as "ID: 123")
        if (/^\d+$/.test(segment)) {
            return `ID: ${segment}`;
        }
        // Handle UUIDs or long alphanumeric strings (show as "Details")
        if (segment.length > 10 && /^[a-zA-Z0-9-]+$/.test(segment)) {
            return 'Details';
        }
        // Handle special cases
        const specialCases = {
            add: 'Add New',
            admin: 'Administration',
            edit: 'Edit',
            reports: 'Reports',
            'service-history': 'Service History',
            settings: 'Settings'
        };
        if (specialCases[segment]) {
            return specialCases[segment];
        }
        // Default formatting: capitalize and replace dashes with spaces
        return segment.split('-').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    };
    const breadcrumbItems = pathSegments.map((segment, index)=>{
        const href = '/' + pathSegments.slice(0, index + 1).join('/');
        const isLast = index === pathSegments.length - 1;
        const displaySegment = formatSegment(segment);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbItem"], {
                    children: isLast ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbPage"], {
                        className: "font-medium text-foreground",
                        children: displaySegment
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                        lineNumber: 92,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbLink"], {
                        asChild: true,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            className: "rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",
                            href: href,
                            children: displaySegment
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                            lineNumber: 97,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                        lineNumber: 96,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                    lineNumber: 90,
                    columnNumber: 9
                }, this),
                !isLast && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbSeparator"], {}, void 0, false, {
                    fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                    lineNumber: 106,
                    columnNumber: 21
                }, this)
            ]
        }, href, true, {
            fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this);
    });
    const breadcrumbContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Breadcrumb"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('text-sm', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbList"], {
            className: "flex-wrap",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbItem"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbLink"], {
                        asChild: true,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            className: "rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",
                            href: homeHref,
                            children: homeLabel
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                            lineNumber: 116,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                        lineNumber: 115,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                    lineNumber: 114,
                    columnNumber: 9
                }, this),
                pathSegments.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BreadcrumbSeparator"], {}, void 0, false, {
                    fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
                    lineNumber: 124,
                    columnNumber: 37
                }, this),
                breadcrumbItems
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
            lineNumber: 113,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
        lineNumber: 112,
        columnNumber: 5
    }, this);
    if (!showContainer) {
        return breadcrumbContent;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center",
            children: breadcrumbContent
        }, void 0, false, {
            fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
            lineNumber: 136,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/app-breadcrumb.tsx",
        lineNumber: 135,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/ui/skeleton.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Skeleton": (()=>Skeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
;
;
function Skeleton({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('animate-pulse rounded-md bg-muted', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/skeleton.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/components/ui/loading.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DataLoader": (()=>DataLoader),
    "EmptyState": (()=>EmptyState),
    "ErrorDisplay": (()=>ErrorDisplay),
    "LoadingSpinner": (()=>LoadingSpinner),
    "SkeletonLoader": (()=>SkeletonLoader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-ssr] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/action-button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/skeleton.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
;
;
// Size mappings for the spinner
const spinnerSizeClasses = {
    lg: 'h-8 w-8',
    md: 'h-6 w-6',
    sm: 'h-4 w-4',
    xl: 'h-12 w-12'
};
// Text size mappings for the spinner
const spinnerTextSizeClasses = {
    lg: 'text-base',
    md: 'text-sm',
    sm: 'text-xs',
    xl: 'text-lg'
};
function DataLoader({ children, className, data, emptyComponent, error, errorComponent, isLoading, loadingComponent, onRetry }) {
    if (isLoading) {
        return loadingComponent || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingSpinner, {
            ...className && {
                className
            },
            text: "Loading..."
        }, void 0, false, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 217,
            columnNumber: 9
        }, this);
    }
    if (error) {
        return errorComponent || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ErrorDisplay, {
            ...className && {
                className
            },
            message: error,
            ...onRetry && {
                onRetry
            }
        }, void 0, false, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 225,
            columnNumber: 9
        }, this);
    }
    if (!data || Array.isArray(data) && data.length === 0) {
        return emptyComponent || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('text-center py-8', className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-muted-foreground",
                children: "No data available"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 238,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 237,
            columnNumber: 9
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: children(data)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/loading.tsx",
        lineNumber: 244,
        columnNumber: 10
    }, this);
}
function EmptyState({ className, description, icon: Icon, primaryAction, secondaryAction, title }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('space-y-6 text-center py-12', className),
        children: [
            Icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                    className: "h-10 w-10 text-muted-foreground"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 278,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 277,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-2xl font-semibold text-foreground",
                        children: title
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/loading.tsx",
                        lineNumber: 283,
                        columnNumber: 9
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-muted-foreground max-w-md mx-auto",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/loading.tsx",
                        lineNumber: 285,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 282,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row gap-3 justify-center",
                children: [
                    primaryAction && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionButton"], {
                        actionType: "primary",
                        asChild: !!primaryAction.href,
                        icon: primaryAction.icon,
                        onClick: primaryAction.onClick,
                        children: primaryAction.href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                            href: primaryAction.href,
                            children: primaryAction.label
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 300,
                            columnNumber: 15
                        }, this) : primaryAction.label
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/loading.tsx",
                        lineNumber: 293,
                        columnNumber: 11
                    }, this),
                    secondaryAction && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionButton"], {
                        actionType: "tertiary",
                        asChild: !!secondaryAction.href,
                        icon: secondaryAction.icon,
                        onClick: secondaryAction.onClick,
                        children: secondaryAction.href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                            href: secondaryAction.href,
                            children: secondaryAction.label
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 315,
                            columnNumber: 15
                        }, this) : secondaryAction.label
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/loading.tsx",
                        lineNumber: 308,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 291,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/loading.tsx",
        lineNumber: 275,
        columnNumber: 5
    }, this);
}
function ErrorDisplay({ className, message, onRetry }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Alert"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('my-4', className),
        variant: "destructive",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 339,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertTitle"], {
                children: "Error"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 340,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertDescription"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-4 text-sm text-muted-foreground",
                            children: message
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 343,
                            columnNumber: 11
                        }, this),
                        onRetry && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionButton"], {
                            actionType: "tertiary",
                            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                className: "size-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/loading.tsx",
                                lineNumber: 347,
                                columnNumber: 21
                            }, void 0),
                            onClick: onRetry,
                            size: "sm",
                            children: "Try Again"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 345,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 342,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 341,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/loading.tsx",
        lineNumber: 338,
        columnNumber: 5
    }, this);
}
function LoadingSpinner({ className, fullPage = false, size = 'md', text }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('flex items-center justify-center', fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('animate-spin text-primary', spinnerSizeClasses[size])
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 381,
                    columnNumber: 9
                }, this),
                text && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('mt-2 text-muted-foreground', spinnerTextSizeClasses[size]),
                    children: text
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 385,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 380,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/loading.tsx",
        lineNumber: 373,
        columnNumber: 5
    }, this);
}
function SkeletonLoader({ className, count = 1, testId = 'loading-skeleton', variant = 'default' }) {
    // Render card skeleton (for entity cards like vehicles, employees)
    if (variant === 'card') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6', className),
            "data-testid": testId,
            children: new Array(count).fill(0).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "overflow-hidden rounded-lg border bg-card shadow-md",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "aspect-[16/10] w-full"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 426,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "mb-1 h-7 w-3/4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 428,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "mb-3 h-4 w-1/2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 429,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "my-3 h-px w-full"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 430,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2.5",
                                    children: Array.from({
                                        length: 3
                                    }).fill(0).map((_, j)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                                    className: "mr-2.5 size-5 rounded-full"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/loading.tsx",
                                                    lineNumber: 436,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                                    className: "h-5 w-2/3"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/loading.tsx",
                                                    lineNumber: 437,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, j, true, {
                                            fileName: "[project]/src/components/ui/loading.tsx",
                                            lineNumber: 435,
                                            columnNumber: 21
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 431,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 427,
                            columnNumber: 13
                        }, this)
                    ]
                }, i, true, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 422,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 414,
            columnNumber: 7
        }, this);
    }
    // Render table skeleton
    if (variant === 'table') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('space-y-3', className),
            "data-testid": testId,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex gap-4",
                    children: Array.from({
                        length: 3
                    }).fill(0).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "h-8 flex-1"
                        }, i, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 456,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 452,
                    columnNumber: 9
                }, this),
                new Array(count).fill(0).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-4",
                        children: Array.from({
                            length: 3
                        }).fill(0).map((_, j)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                className: "h-6 flex-1"
                            }, j, false, {
                                fileName: "[project]/src/components/ui/loading.tsx",
                                lineNumber: 464,
                                columnNumber: 17
                            }, this))
                    }, i, false, {
                        fileName: "[project]/src/components/ui/loading.tsx",
                        lineNumber: 460,
                        columnNumber: 11
                    }, this))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 451,
            columnNumber: 7
        }, this);
    }
    // Render list skeleton
    if (variant === 'list') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('space-y-3', className),
            "data-testid": testId,
            children: new Array(count).fill(0).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "size-12 rounded-full"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 478,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 space-y-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "h-4 w-1/3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 480,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "h-4 w-full"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 481,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 479,
                            columnNumber: 13
                        }, this)
                    ]
                }, i, true, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 477,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 475,
            columnNumber: 7
        }, this);
    }
    // Render stats skeleton
    if (variant === 'stats') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className),
            "data-testid": testId,
            children: new Array(count).fill(0).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "rounded-lg border bg-card p-5 shadow-sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "h-5 w-1/3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 499,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                                    className: "size-5 rounded-full"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/loading.tsx",
                                    lineNumber: 500,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 498,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "mt-3 h-8 w-1/2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 502,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                            className: "mt-2 h-4 w-2/3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/loading.tsx",
                            lineNumber: 503,
                            columnNumber: 13
                        }, this)
                    ]
                }, i, true, {
                    fileName: "[project]/src/components/ui/loading.tsx",
                    lineNumber: 497,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/ui/loading.tsx",
            lineNumber: 492,
            columnNumber: 7
        }, this);
    }
    // Default skeleton
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('space-y-2', className),
        "data-testid": testId,
        children: new Array(count).fill(0).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Skeleton"], {
                className: "h-5 w-full"
            }, i, false, {
                fileName: "[project]/src/components/ui/loading.tsx",
                lineNumber: 514,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ui/loading.tsx",
        lineNumber: 512,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/ui/PageHeader.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageHeader": (()=>PageHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function PageHeader({ children, description, icon: Icon, title }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-6 flex items-center justify-between border-b border-border/50 pb-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-3",
                        children: [
                            Icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                className: "size-8 text-primary"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/PageHeader.tsx",
                                lineNumber: 22,
                                columnNumber: 20
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-bold tracking-tight text-foreground",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/PageHeader.tsx",
                                lineNumber: 23,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/PageHeader.tsx",
                        lineNumber: 21,
                        columnNumber: 9
                    }, this),
                    description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-muted-foreground",
                        children: description
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/PageHeader.tsx",
                        lineNumber: 28,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/PageHeader.tsx",
                lineNumber: 20,
                columnNumber: 7
            }, this),
            children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/ui/PageHeader.tsx",
                lineNumber: 31,
                columnNumber: 20
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/PageHeader.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/vehicles/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>VehiclesPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/car.js [app-ssr] (ecmascript) <export default as Car>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-plus.js [app-ssr] (ecmascript) <export default as PlusCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundaries$2f$ErrorBoundary$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/error-boundaries/ErrorBoundary.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$vehicles$2f$VehicleCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/vehicles/VehicleCard.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$vehicles$2f$VehicleList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/vehicles/VehicleList.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/action-button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$app$2d$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/app-breadcrumb.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/loading.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$PageHeader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/PageHeader.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
// Main page component content
const VehiclesPageContent = ()=>{
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    // Removed const { toast } = useToast(); as it's not used
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$vehicles$2f$VehicleList$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        children: ({ error, fetchVehicles, handleDelete, isRefreshing, loading, vehicles })=>{
            // Move filtering logic outside the render prop, but still within the component
            const filteredVehicles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
                if (loading || error) {
                    return [];
                }
                const lowercasedFilter = searchTerm.toLowerCase();
                return vehicles.filter((vehicle)=>{
                    return vehicle.make.toLowerCase().includes(lowercasedFilter) || vehicle.model.toLowerCase().includes(lowercasedFilter) || vehicle.year.toString().includes(lowercasedFilter) || vehicle.licensePlate?.toLowerCase().includes(lowercasedFilter) || vehicle.vin?.toLowerCase().includes(lowercasedFilter) || vehicle.ownerName?.toLowerCase().includes(lowercasedFilter);
                });
            }, [
                vehicles,
                searchTerm,
                loading,
                error
            ]);
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$app$2d$breadcrumb$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AppBreadcrumb"], {
                        homeHref: "/",
                        homeLabel: "Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/app/vehicles/page.tsx",
                        lineNumber: 65,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$PageHeader$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PageHeader"], {
                        description: "Manage, track, and gain insights into your vehicles.",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
                        title: "My Vehicle Fleet",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-2",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionButton"], {
                                actionType: "primary",
                                asChild: true,
                                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusCircle$3e$__["PlusCircle"], {
                                    className: "size-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/vehicles/page.tsx",
                                    lineNumber: 75,
                                    columnNumber: 25
                                }, void 0),
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/vehicles/new",
                                    children: "Add New Vehicle"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/vehicles/page.tsx",
                                    lineNumber: 77,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/vehicles/page.tsx",
                                lineNumber: 72,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/vehicles/page.tsx",
                            lineNumber: 71,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/vehicles/page.tsx",
                        lineNumber: 66,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative mb-6 rounded-lg bg-card p-4 shadow",
                        children: [
                            isRefreshing && !loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute right-4 top-4 flex items-center text-xs text-muted-foreground",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                        className: "mr-1 size-3 animate-spin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/vehicles/page.tsx",
                                        lineNumber: 86,
                                        columnNumber: 19
                                    }, this),
                                    "Updating list..."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/vehicles/page.tsx",
                                lineNumber: 85,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                        className: "absolute left-3 top-1/2 size-5 -translate-y-1/2 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/vehicles/page.tsx",
                                        lineNumber: 91,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                        className: "w-full pl-10",
                                        onChange: (e)=>setSearchTerm(e.target.value),
                                        placeholder: "Search vehicles (Make, Model, Year, VIN, Plate, Owner...)",
                                        type: "text",
                                        value: searchTerm
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/vehicles/page.tsx",
                                        lineNumber: 92,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/vehicles/page.tsx",
                                lineNumber: 90,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/vehicles/page.tsx",
                        lineNumber: 83,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DataLoader"], {
                        data: filteredVehicles,
                        emptyComponent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "rounded-lg bg-card py-12 text-center shadow-md",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"], {
                                    className: "mx-auto mb-6 size-16 text-muted-foreground"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/vehicles/page.tsx",
                                    lineNumber: 106,
                                    columnNumber: 19
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "mb-2 text-2xl font-semibold text-foreground",
                                    children: searchTerm ? 'No Vehicles Match Your Search' : 'Your Garage is Empty!'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/vehicles/page.tsx",
                                    lineNumber: 107,
                                    columnNumber: 19
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "mx-auto mb-6 mt-2 max-w-md text-muted-foreground",
                                    children: searchTerm ? 'Try adjusting your search terms or add a new vehicle to your fleet.' : "It looks like you haven't added any vehicles yet. Let's get your first one set up."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/vehicles/page.tsx",
                                    lineNumber: 112,
                                    columnNumber: 19
                                }, void 0),
                                !searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$action$2d$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ActionButton"], {
                                    actionType: "primary",
                                    asChild: true,
                                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__PlusCircle$3e$__["PlusCircle"], {
                                        className: "size-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/vehicles/page.tsx",
                                        lineNumber: 121,
                                        columnNumber: 29
                                    }, void 0),
                                    size: "lg",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/vehicles/new",
                                        children: "Add Your First Vehicle"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/vehicles/page.tsx",
                                        lineNumber: 124,
                                        columnNumber: 23
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/vehicles/page.tsx",
                                    lineNumber: 118,
                                    columnNumber: 21
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/vehicles/page.tsx",
                            lineNumber: 105,
                            columnNumber: 17
                        }, void 0),
                        error: error,
                        isLoading: loading,
                        loadingComponent: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$loading$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SkeletonLoader"], {
                            count: 3,
                            variant: "card"
                        }, void 0, false, {
                            fileName: "[project]/src/app/vehicles/page.tsx",
                            lineNumber: 131,
                            columnNumber: 33
                        }, void 0),
                        onRetry: fetchVehicles,
                        children: (vehiclesData // Renamed data to vehiclesData to avoid conflict
                        )=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",
                                children: vehiclesData.map((vehicle)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$vehicles$2f$VehicleCard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        vehicle: vehicle
                                    }, vehicle.id, false, {
                                        fileName: "[project]/src/app/vehicles/page.tsx",
                                        lineNumber: 139,
                                        columnNumber: 21
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/vehicles/page.tsx",
                                lineNumber: 137,
                                columnNumber: 17
                            }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/vehicles/page.tsx",
                        lineNumber: 102,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/vehicles/page.tsx",
                lineNumber: 64,
                columnNumber: 11
            }, this);
        }
    }, void 0, false, {
        fileName: "[project]/src/app/vehicles/page.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
function VehiclesPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$error$2d$boundaries$2f$ErrorBoundary$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(VehiclesPageContent, {}, void 0, false, {
            fileName: "[project]/src/app/vehicles/page.tsx",
            lineNumber: 155,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/vehicles/page.tsx",
        lineNumber: 154,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1ed25c2a._.js.map