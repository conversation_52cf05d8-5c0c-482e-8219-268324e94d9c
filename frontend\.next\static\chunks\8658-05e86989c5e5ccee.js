"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8658],{976:(e,t,i)=>{i.d(t,{y:()=>o});var s=i(31203),r=i(25982);let n={fromApi:e=>s.G.fromApi(e),toApi:e=>e},a={toCreateRequest:e=>e};class o extends r.v{async getByStatus(e){return(await this.getAll({status:e})).data}async manageFlightDetails(e,t){return this.executeWithInfrastructure(null,async()=>{let i=a.toCreateRequest(t),r=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/flight-details"),i);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.G.fromApi(r)})}async setDelegates(e,t){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/delegates"),{delegates:t});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.G.fromApi(i)})}async setDrivers(e,t){return this.executeWithInfrastructure(null,async()=>{let i=t.map(e=>({employeeId:e})),r=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/drivers"),{drivers:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.G.fromApi(r)})}async setEscorts(e,t){return this.executeWithInfrastructure(null,async()=>{let i=t.map(e=>({employeeId:e})),r=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/escorts"),{escorts:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.G.fromApi(r)})}async setVehicles(e,t){return this.executeWithInfrastructure(null,async()=>{let i=t.map(e=>({assignedDate:new Date().toISOString(),vehicleId:e})),r=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/vehicles"),{vehicleAssignments:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.G.fromApi(r)})}async updateStatus(e,t,i){return this.executeWithInfrastructure(null,async()=>{let r=await this.apiClient.put("".concat(this.endpoint,"/").concat(e),{status:t,statusChangeReason:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.G.fromApi(r)})}constructor(e,t){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/delegations",this.transformer=n}}},12430:(e,t,i)=>{i.d(t,{Q:()=>a});var s=i(38069),r=i(25982);let n={fromApi:e=>s.A.fromApi(e),toApi:e=>e};class a extends r.v{async getByRole(e){return(await this.getAll({role:e})).data}async updateAvailabilityStatus(e,t){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/availability"),{availability:t});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.A.fromApi(i)})}constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/employees",this.transformer=n}}},14163:(e,t,i)=>{i.d(t,{N:()=>a});var s=i(34982);let r="https://abylqjnpaegeqwktcukn.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o";if(!r||!n)throw Error("Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file");let a=(0,s.UU)(r,n)},25982:(e,t,i)=>{i.d(t,{v:()=>a});var s=i(3695);class r{async execute(e){if("OPEN"===this.state)if(Date.now()-this.lastFailureTime>this.timeout)this.state="HALF_OPEN";else throw new s._7("Circuit breaker is OPEN","CIRCUIT_BREAKER_OPEN");try{let t=await e();return this.onSuccess(),t}catch(e){throw this.onFailure(),e}}getState(){return{failures:this.failures,lastFailureTime:this.lastFailureTime,name:this.name,state:this.state}}onFailure(){this.failures++,this.lastFailureTime=Date.now(),this.failures>=this.threshold&&(this.state="OPEN")}onSuccess(){this.failures=0,this.state="CLOSED"}constructor(e,t=5,i=6e4){this.name=e,this.threshold=t,this.timeout=i,this.failures=0,this.lastFailureTime=0,this.state="CLOSED"}}class n{clear(){this.cache.clear()}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),null):t.data:null}getStats(){return{keys:[...this.cache.keys()],size:this.cache.size}}invalidate(e){this.cache.delete(e)}invalidatePattern(e){for(let t of this.cache.keys())e.test(t)&&this.cache.delete(t)}set(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5;this.cache.set(e,{data:t,expiry:Date.now()+i})}constructor(){this.cache=new Map}}class a{clearCache(){this.cache.clear()}async create(e){return this.executeWithInfrastructure(null,async()=>{let t=this.transformer.toApi?this.transformer.toApi(e):e,i=await this.apiClient.post(this.endpoint,t);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.transformer.fromApi?this.transformer.fromApi(i):i})}async delete(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("".concat(this.endpoint,"/").concat(e)),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e))})}async getAll(e){let t="".concat(this.endpoint,":getAll:").concat(JSON.stringify(e||{}));return this.executeWithInfrastructure(t,async()=>{let t,i=new URLSearchParams;if(e)for(let[t,s]of Object.entries(e))null!=s&&i.append(t,String(s));let s=i.toString(),r=s?"".concat(this.endpoint,"?").concat(s):this.endpoint,n=await this.apiClient.get(r),a={};if(n&&"success"===n.status&&n.data){var o,l,c,u,h,d,m,p,g;let e=n.data;Array.isArray(e)?(t=e,n.pagination&&(a={pagination:{hasNext:null!=(o=n.pagination.hasNext)&&o,hasPrevious:null!=(l=n.pagination.hasPrevious)&&l,limit:n.pagination.limit,page:n.pagination.page,total:n.pagination.total,totalPages:null!=(c=n.pagination.totalPages)?c:Math.ceil(n.pagination.total/n.pagination.limit)}})):e&&Array.isArray(e.data)?(t=e.data,e.pagination?a={pagination:{hasNext:null!=(u=e.pagination.hasNext)&&u,hasPrevious:null!=(h=e.pagination.hasPrevious)&&h,limit:e.pagination.limit,page:e.pagination.page,total:e.pagination.total,totalPages:null!=(d=e.pagination.totalPages)?d:Math.ceil(e.pagination.total/e.pagination.limit)}}:n.pagination&&(a={pagination:{hasNext:null!=(m=n.pagination.hasNext)&&m,hasPrevious:null!=(p=n.pagination.hasPrevious)&&p,limit:n.pagination.limit,page:n.pagination.page,total:n.pagination.total,totalPages:null!=(g=n.pagination.totalPages)?g:Math.ceil(n.pagination.total/n.pagination.limit)}})):t=[e]}else if(Array.isArray(n))t=n;else if(n&&(n.error||"error"===n.status))throw Error(n.message||n.error||"API request failed");else if(n&&"object"==typeof n)t=[n];else throw Error("Invalid response format from API: ".concat(JSON.stringify(n)));return{data:t.map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e),...a}})}async getById(e){let t="".concat(this.endpoint,":getById:").concat(e);return this.executeWithInfrastructure(t,async()=>{let t=await this.apiClient.get("".concat(this.endpoint,"/").concat(e));return this.transformer.fromApi?this.transformer.fromApi(t):t})}getHealthStatus(){return{cacheStats:this.cache.getStats(),circuitBreakerState:this.circuitBreaker.getState(),endpoint:this.endpoint,metrics:this.metrics,service:this.constructor.name}}resetMetrics(){this.metrics={averageResponseTime:0,cacheHitRatio:0,errorCount:0,requestCount:0}}async update(e,t){return this.executeWithInfrastructure(null,async()=>{let i=this.transformer.toApi?this.transformer.toApi(t):t,s=await this.apiClient.put("".concat(this.endpoint,"/").concat(e),i);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),this.transformer.fromApi?this.transformer.fromApi(s):s})}async executeWithInfrastructure(e,t){let i=Date.now();try{if(this.metrics.requestCount++,e){let t=this.cache.get(e);if(t)return this.metrics.cacheHitRatio=(this.metrics.cacheHitRatio*(this.metrics.requestCount-1)+1)/this.metrics.requestCount,t}let s=await this.circuitBreaker.execute(async()=>o(t,this.config.retryAttempts));e&&s&&this.cache.set(e,s,this.config.cacheDuration);let r=Date.now()-i;return this.metrics.averageResponseTime=(this.metrics.averageResponseTime*(this.metrics.requestCount-1)+r)/this.metrics.requestCount,s}catch(e){var r;if(this.metrics.errorCount++,console.error("Service error in ".concat(this.constructor.name,":"),{endpoint:this.endpoint,errorDetails:e instanceof Error?{message:e.message,name:e.name,stack:e.stack}:e,errorMessage:e instanceof Error?e.message:String(e),errorType:(null==e||null==(r=e.constructor)?void 0:r.name)||typeof e,timestamp:new Date().toISOString()}),e instanceof s._7)throw e;if(e instanceof Error){if(e.message.includes("fetch")||e.message.includes("network"))throw new s._7("Network connection failed. Please check your internet connection and try again.","NETWORK_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name});if(e.message.includes("500")||e.message.includes("Internal Server Error"))throw new s._7("Server error occurred. Please try again later.","SERVER_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name})}throw new s._7(e instanceof Error?e.message:"Unknown service error","SERVICE_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name})}}constructor(e,t={}){this.apiClient=e,this.config={cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t},this.circuitBreaker=new r("".concat(this.constructor.name),this.config.circuitBreakerThreshold),this.cache=new n,this.metrics={averageResponseTime:0,cacheHitRatio:0,errorCount:0,requestCount:0}}}async function o(e){let t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let r=1;r<=i;r++)try{return await e()}catch(e){if(t=e instanceof Error?e:Error("Unknown error"),r===i)throw t;await new Promise(e=>setTimeout(e,s*r))}throw t}},28113:(e,t,i)=>{i.d(t,{Q:()=>l});var s=i(38549),r=i(89699),n=i(14163);let a={baseRetryDelay:1e3,enableDebugLogging:!1,maxRetryAttempts:3,refreshBeforeExpiryMinutes:5};class o{static getInstance(e){return o.instance||(o.instance=new o(e)),o.instance}async getSessionInfo(){try{var e;let{data:{session:t},error:i}=await n.N.auth.getSession();if(i)return this.log("Error getting session info",{error:i}),r.SessionManager.handleSessionValidation(!1,{error:i.message}),{error:i.message,isValid:!1};if(!t)return this.log("No active session found"),r.SessionManager.handleSessionValidation(!1,{error:"No active session"}),{error:"No active session",isValid:!1};let s=Math.floor(Date.now()/1e3),a=!!t.expires_at&&t.expires_at<s,o={isExpired:a,isValid:!a,user:{id:t.user.id}};return t.expires_at&&(o.expiresAt=t.expires_at),t.user.email&&(o.user.email=t.user.email),(null==(e=t.user.user_metadata)?void 0:e.role)&&(o.user.role=t.user.user_metadata.role),o}catch(e){return this.log("Exception in getSessionInfo",{error:e}),{error:e instanceof Error?e.message:"Unknown error",isValid:!1}}}async refreshNow(){return this.isRefreshing?(this.log("Refresh already in progress, skipping"),!1):this.performRefresh()}stop(){this.clearScheduledRefresh(),this.clearRetryTimeout(),this.callbacks.clear(),this.currentSession=null,this.isRefreshing=!1,this.retryAttempts=0,this.log("TokenRefreshService stopped")}subscribe(e){return this.callbacks.add(e),()=>this.callbacks.delete(e)}updateSession(e){this.currentSession=e,e?(this.scheduleRefresh(e),this.log("Session updated, refresh scheduled",{expiresAt:new Date(1e3*e.expires_at).toISOString()})):(this.clearScheduledRefresh(),this.log("Session cleared, refresh cancelled"))}clearRetryTimeout(){this.retryTimeout&&(clearTimeout(this.retryTimeout),this.retryTimeout=null)}clearScheduledRefresh(){this.refreshTimeout&&(clearTimeout(this.refreshTimeout),this.refreshTimeout=null)}emitEvent(e,t){for(let i of this.callbacks)try{i(e,t)}catch(e){console.error("Error in token refresh callback:",e)}}handleRefreshFailure(e){if(this.retryAttempts++,this.retryAttempts>=this.config.maxRetryAttempts){this.log("Max retry attempts reached, giving up and signaling critical failure"),this.emitEvent("critical_refresh_failed",{attempts:this.retryAttempts,error:"Max retry attempts exceeded, session unrecoverable",...e});return}let t=this.config.baseRetryDelay*Math.pow(2,this.retryAttempts-1);this.log("Scheduling retry",{attempt:this.retryAttempts,delayMs:t}),this.retryTimeout=setTimeout(()=>{this.performRefresh()},t)}log(e,t){this.config.enableDebugLogging&&console.log("\uD83D\uDD04 TokenRefreshService: ".concat(e),t||"")}async performRefresh(){if(this.isRefreshing)return!1;this.isRefreshing=!0,this.log("Starting token refresh");try{var e,t;let i={"Content-Type":"application/json"};(null==(e=this.currentSession)?void 0:e.access_token)&&(i.Authorization="Bearer ".concat(this.currentSession.access_token));let a={};(null==(t=this.currentSession)?void 0:t.refresh_token)?(a.refresh_token=this.currentSession.refresh_token,this.log("Including refresh token in request body")):this.log("Warning: No refresh token available in current session");let o=(0,s.Qq)().apiBaseUrl,l=await fetch("".concat(o,"/auth/refresh"),{credentials:"include",headers:i,method:"POST",body:JSON.stringify(a)});if(l.ok){let e=await l.json();this.log("Token refresh successful",{expiresIn:e.expiresIn}),this.retryAttempts=0,this.clearRetryTimeout();try{let{newTokens:t}=e;(null==t?void 0:t.session)&&(null==t?void 0:t.user)?(await n.N.auth.setSession({access_token:t.session.access_token,refresh_token:t.session.refresh_token}),this.currentSession=t.session,this.log("Supabase session explicitly updated with new tokens")):this.log("Warning: New tokens from backend did not contain full session/user data",{data:e})}catch(e){this.log("Error explicitly updating Supabase session after refresh",{sessionUpdateError:e})}return this.emitEvent("refresh_success",e),r.SessionManager.handleTokenRefresh(!0,e),!0}{let e=await l.json().catch(()=>({}));return this.log("Token refresh failed",{error:e,status:l.status}),this.handleRefreshFailure(e),r.SessionManager.handleTokenRefresh(!1,e),!1}}catch(e){return this.log("Token refresh error",{error:e instanceof Error?e.message:String(e)}),this.handleRefreshFailure({error:"Network error"}),r.SessionManager.handleTokenRefresh(!1,{error:"Network error"}),!1}finally{this.isRefreshing=!1}}scheduleRefresh(e){if(this.clearScheduledRefresh(),!e.expires_at)return void this.log("No expiration time in session, cannot schedule refresh");let t=1e3*e.expires_at-60*this.config.refreshBeforeExpiryMinutes*1e3,i=Math.max(0,t-Date.now());if(0===i){this.log("Token expired or about to expire, refreshing immediately"),this.performRefresh();return}this.refreshTimeout=setTimeout(()=>{this.performRefresh()},i),this.log("Refresh scheduled",{delayMinutes:Math.round(i/6e4),refreshAt:new Date(t).toISOString()}),this.emitEvent("refresh_scheduled",{delay:i,refreshAt:t})}setupVisibilityHandling(){"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{if(this.isTabVisible=!document.hidden,this.isTabVisible&&this.currentSession){let e=Date.now();1e3*(this.currentSession.expires_at||0)-e<=60*this.config.refreshBeforeExpiryMinutes*1e3&&(this.log("Tab visible and token needs refresh"),this.performRefresh())}})}constructor(e={}){this.callbacks=new Set,this.currentSession=null,this.isRefreshing=!1,this.isTabVisible=!0,this.refreshTimeout=null,this.retryAttempts=0,this.retryTimeout=null,this.config={...a,...e},this.setupVisibilityHandling(),this.log("TokenRefreshService initialized")}}o.instance=null;let l=e=>o.getInstance(e)},30285:(e,t,i)=>{i.d(t,{$:()=>c,r:()=>l});var s=i(95155),r=i(12115),n=i(99708),a=i(74466),o=i(54036);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:i,variant:r,size:a,asChild:c=!1,...u}=e,h=c?n.DX:"button";return(0,s.jsx)(h,{className:(0,o.cn)(l({variant:r,size:a,className:i})),ref:t,...u})});c.displayName="Button"},31203:(e,t,i)=>{i.d(t,{G:()=>a});let s={toDelegateApiStructure(e){var t;return{name:e.name,notes:null!=(t=e.notes)?t:null,title:e.title}},toDriverApiStructure(e){var t;return{employeeId:e.employeeId,notes:null!=(t=e.notes)?t:null}},toEscortApiStructure(e){var t;return{employeeId:e.employeeId,notes:null!=(t=e.notes)?t:null}},toVehicleAssignmentApiStructure:e=>e},r={fromApi(e){var t,i,s,r,n,a,o,l,c,u,h,d;return{availability:e.availability,contactEmail:null!=(t=e.contactEmail)?t:null,contactInfo:e.contactInfo,contactMobile:null!=(i=e.contactMobile)?i:null,contactPhone:null!=(s=e.contactPhone)?s:null,createdAt:e.createdAt,currentLocation:null!=(r=e.currentLocation)?r:null,department:null!=(n=e.department)?n:null,employeeId:e.employeeId,fullName:null!=(a=e.fullName)?a:null,generalAssignments:e.generalAssignments,hireDate:null!=(o=e.hireDate)?o:null,id:e.id,name:e.name,notes:null!=(l=e.notes)?l:null,position:null!=(c=e.position)?c:null,profileImageUrl:null!=(u=e.profileImageUrl)?u:null,role:e.role,shiftSchedule:null!=(h=e.shiftSchedule)?h:null,skills:e.skills,status:e.status,updatedAt:e.updatedAt,workingHours:null!=(d=e.workingHours)?d:null}}},n={fromApi:e=>({color:e.color,createdAt:e.createdAt,id:e.id,imageUrl:e.imageUrl,initialOdometer:e.initialOdometer,licensePlate:e.licensePlate,make:e.make,model:e.model,ownerContact:e.ownerContact,ownerName:e.ownerName,serviceHistory:[],updatedAt:e.updatedAt,vin:e.vin,year:e.year})},a={fromApi(e){var t,i,s,a,l,c,u;return{arrivalFlight:e.flightArrivalDetails?o.fromApi(e.flightArrivalDetails):null,createdAt:e.createdAt,delegates:e.delegates||[],departureFlight:e.flightDepartureDetails?o.fromApi(e.flightDepartureDetails):null,drivers:(null==(t=e.drivers)?void 0:t.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,employee:r.fromApi(t),employeeId:t.id,id:"driver-".concat(e.id,"-").concat(t.id),updatedAt:e.updatedAt})))||[],durationFrom:e.durationFrom,durationTo:e.durationTo,escorts:(null==(i=e.escorts)?void 0:i.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,employee:r.fromApi(t),employeeId:t.id,id:"escort-".concat(e.id,"-").concat(t.id),updatedAt:e.updatedAt})))||[],eventName:e.eventName,id:e.id,imageUrl:null!=(a=e.imageUrl)?a:null,invitationFrom:null!=(l=e.invitationFrom)?l:null,invitationTo:null!=(c=e.invitationTo)?c:null,location:e.location,notes:null!=(u=e.notes)?u:null,status:e.status,statusHistory:e.statusHistory||[],updatedAt:e.updatedAt,vehicles:(null==(s=e.vehicles)?void 0:s.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,id:"vehicle-".concat(e.id,"-").concat(t.id),updatedAt:e.updatedAt,vehicle:n.fromApi(t),vehicleId:t.id})))||[]}},toCreateRequest(e){var t,i,r,n,a,l,c,u,h,d,m,p;return{delegates:null!=(a=null==(t=e.delegates)?void 0:t.map(e=>s.toDelegateApiStructure(e)))?a:[],driverEmployeeIds:null!=(l=null==(i=e.drivers)?void 0:i.map(e=>e.employeeId))?l:[],durationFrom:e.durationFrom,durationTo:e.durationTo,escortEmployeeIds:null!=(c=null==(r=e.escorts)?void 0:r.map(e=>e.employeeId))?c:[],eventName:e.eventName,flightArrivalDetails:e.flightArrivalDetails?o.toApiStructureForCreate(e.flightArrivalDetails):void 0,flightDepartureDetails:e.flightDepartureDetails?o.toApiStructureForCreate(e.flightDepartureDetails):void 0,imageUrl:null!=(u=e.imageUrl)?u:null,invitationFrom:null!=(h=e.invitationFrom)?h:null,invitationTo:null!=(d=e.invitationTo)?d:null,location:e.location,notes:null!=(m=e.notes)?m:null,status:e.status,vehicleIds:null!=(p=null==(n=e.vehicles)?void 0:n.map(e=>e.vehicleId))?p:[]}},toUpdateRequest(e){var t,i;let s={};return void 0!==e.eventName&&(s.eventName=e.eventName),void 0!==e.location&&(s.location=e.location),void 0!==e.durationFrom&&(s.durationFrom=e.durationFrom),void 0!==e.durationTo&&(s.durationTo=e.durationTo),void 0!==e.status&&(s.status=e.status),void 0!==e.notes&&(s.notes=e.notes),void 0!==e.imageUrl&&(s.imageUrl=e.imageUrl),void 0!==e.invitationFrom&&(s.invitationFrom=e.invitationFrom),void 0!==e.invitationTo&&(s.invitationTo=e.invitationTo),void 0!==e.flightArrivalDetails&&(s.flightArrivalDetails=null!=(t=e.flightArrivalDetails)?t:void 0),void 0!==e.flightDepartureDetails&&(s.flightDepartureDetails=null!=(i=e.flightDepartureDetails)?i:void 0),void 0!==e.escorts&&(s.escortEmployeeIds=e.escorts.map(e=>e.employeeId)),void 0!==e.drivers&&(s.driverEmployeeIds=e.drivers.map(e=>e.employeeId)),void 0!==e.vehicles&&(s.vehicleIds=e.vehicles.map(e=>e.vehicleId)),s}},o={fromApi:e=>e?{airport:e.airport,dateTime:e.dateTime,flightNumber:e.flightNumber,id:e.id,notes:e.notes||null,terminal:e.terminal||null}:null,toApiStructureForCreate:e=>e,toCreateRequest(e){var t,i;return{airport:e.airport,dateTime:e.dateTime,flightNumber:e.flightNumber,notes:null!=(t=e.notes)?t:null,terminal:null!=(i=e.terminal)?i:null}}}},38069:(e,t,i)=>{i.d(t,{A:()=>r});var s=i(21876);let r={fromApi(e){var t,i,s,r,n,a,o,l,c,u,h,d;return{availability:e.availability,contactEmail:null!=(t=e.contactEmail)?t:null,contactInfo:e.contactInfo,contactMobile:null!=(i=e.contactMobile)?i:null,contactPhone:null!=(s=e.contactPhone)?s:null,createdAt:e.createdAt,currentLocation:null!=(r=e.currentLocation)?r:null,department:null!=(n=e.department)?n:null,employeeId:e.employeeId,fullName:null!=(a=e.fullName)?a:null,generalAssignments:e.generalAssignments,hireDate:null!=(o=e.hireDate)?o:null,id:e.id,name:e.name||"",notes:null!=(l=e.notes)?l:null,position:null!=(c=e.position)?c:null,profileImageUrl:null!=(u=e.profileImageUrl)?u:null,role:e.role,shiftSchedule:null!=(h=e.shiftSchedule)?h:null,skills:e.skills,status:e.status,updatedAt:e.updatedAt,workingHours:null!=(d=e.workingHours)?d:null}},toCreateRequest(e){var t,i,r,n,a,o,l,c,u,h,d,m,p,g,v,f,y,S,A;return{availability:e.availability,contactEmail:null!=(u=null==(t=e.contactEmail)?void 0:t.trim())?u:null,contactInfo:e.contactInfo.trim(),contactMobile:null!=(h=null==(i=e.contactMobile)?void 0:i.trim())?h:null,contactPhone:null!=(d=null==(r=e.contactPhone)?void 0:r.trim())?d:null,currentLocation:null!=(m=e.currentLocation)?m:null,department:null!=(p=null==(n=e.department)?void 0:n.trim())?p:null,employeeId:e.employeeId,fullName:null!=(g=null==(a=e.fullName)?void 0:a.trim())?g:null,generalAssignments:e.generalAssignments,hireDate:e.hireDate?(0,s.B7)(e.hireDate):null,name:e.name.trim(),notes:null!=(v=null==(o=e.notes)?void 0:o.trim())?v:null,position:null!=(f=null==(l=e.position)?void 0:l.trim())?f:null,profileImageUrl:null!=(y=null==(c=e.profileImageUrl)?void 0:c.trim())?y:null,role:e.role,shiftSchedule:null!=(S=e.shiftSchedule)?S:null,skills:e.skills,status:e.status,workingHours:null!=(A=e.workingHours)?A:null}},toUpdateRequest(e){var t,i,r,n,a,o,l,c,u,h,d,m,p,g,v,f,y,S,A,w;let I={};return void 0!==e.name&&(I.name=null!=(d=null==(t=e.name)?void 0:t.trim())?d:null),void 0!==e.employeeId&&(I.employeeId=e.employeeId),void 0!==e.contactInfo&&(I.contactInfo=null!=(m=null==(i=e.contactInfo)?void 0:i.trim())?m:null),void 0!==e.contactEmail&&(I.contactEmail=null!=(p=null==(r=e.contactEmail)?void 0:r.trim())?p:null),void 0!==e.contactMobile&&(I.contactMobile=null!=(g=null==(n=e.contactMobile)?void 0:n.trim())?g:null),void 0!==e.contactPhone&&(I.contactPhone=null!=(v=null==(a=e.contactPhone)?void 0:a.trim())?v:null),void 0!==e.position&&(I.position=null!=(f=null==(o=e.position)?void 0:o.trim())?f:null),void 0!==e.department&&(I.department=null!=(y=null==(l=e.department)?void 0:l.trim())?y:null),void 0!==e.hireDate&&(I.hireDate=e.hireDate?(0,s.B7)(e.hireDate):null),void 0!==e.fullName&&(I.fullName=null!=(S=null==(c=e.fullName)?void 0:c.trim())?S:null),void 0!==e.role&&(I.role=e.role),void 0!==e.status&&(I.status=e.status),void 0!==e.availability&&(I.availability=e.availability),void 0!==e.currentLocation&&(I.currentLocation=e.currentLocation),void 0!==e.workingHours&&(I.workingHours=e.workingHours),void 0!==e.generalAssignments&&(I.generalAssignments=e.generalAssignments),void 0!==e.notes&&(I.notes=null!=(A=null==(u=e.notes)?void 0:u.trim())?A:null),void 0!==e.profileImageUrl&&(I.profileImageUrl=null!=(w=null==(h=e.profileImageUrl)?void 0:h.trim())?w:null),void 0!==e.shiftSchedule&&(I.shiftSchedule=e.shiftSchedule),void 0!==e.skills&&(I.skills=e.skills),I}}},38549:(e,t,i)=>{i.d(t,{Qq:()=>r});let s=function(){let e=function(){let e=window.location.hostname;return(window.location.port,e.includes(".vercel.app")||e.includes(".netlify.app")||e.includes(".herokuapp.com")||e.includes("cloudworkstations.dev"))?"cloud":"localhost"===e||"127.0.0.1"===e||e.startsWith("192.168.")?e.startsWith("192.168.")||e.startsWith("10.")||e.startsWith("172.")?"network":"localhost":"cloud"}(),{apiUrl:t,apiBaseUrl:i,wsUrl:s}={apiUrl:"http://localhost:3001",apiBaseUrl:"http://localhost:3001/api",wsUrl:"ws://localhost:3001"};return{apiUrl:t,apiBaseUrl:i,wsUrl:s,environment:"production",deploymentContext:e,isProduction:!0,isDevelopment:!1,enableDebugLogging:!0}}();function r(){return s}},54120:(e,t,i)=>{function s(e){return void 0===e?null:e}i.d(t,{d$:()=>s})},55411:(e,t,i)=>{i.d(t,{O:()=>o});var s=i(54120),r=i(3695);let n=e=>new Promise(t=>setTimeout(t,e)),a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return Math.min(t*Math.pow(2,e),3e4)};class o{async delete(e,t){return this.request("DELETE",e,void 0,t)}async get(e,t){return this.request("GET",e,void 0,t)}async patch(e,t,i){return this.request("PATCH",e,t,i)}async post(e,t,i){return this.request("POST",e,t,i)}async put(e,t,i){return this.request("PUT",e,t,i)}async request(e,t,i,o){let l="".concat(this.baseURL).concat(t),c={...this.defaultHeaders,...null==o?void 0:o.headers};if(this.getAuthToken){let e=this.getAuthToken();e&&(c.Authorization="Bearer ".concat(e))}let u={body:i?JSON.stringify(i):null,credentials:"include",headers:c,method:e,signal:(0,s.d$)(null==o?void 0:o.signal)},h=new AbortController,d=setTimeout(()=>h.abort(),(null==o?void 0:o.timeout)||this.timeout);u.signal=(null==o?void 0:o.signal)||h.signal;for(let e=0;e<this.retryAttempts;e++)try{let e=await fetch(l,u);clearTimeout(d);let t=await e.json().catch(()=>null);if(!e.ok){if(t&&"error"===t.status)throw new r.hD(t.message,{code:t.code,details:t.error,status:e.status});let i=(null==t?void 0:t.message)||e.statusText;switch(e.status){case 400:throw new r.v7(i,t);case 401:throw new r.v3(i,t);case 404:throw new r.m_(i,t);case 500:throw new r.PO(i,t);default:throw new r.hD(i,{details:t,status:e.status})}}if(t&&"object"==typeof t&&"success"===t.status)return t.data;if(204===e.status)return;return t}catch(i){if(i instanceof r.hD)throw i;if(e===this.retryAttempts-1){let e=i instanceof Error?i.message:String(i);throw new r.Dr("Request failed after ".concat(this.retryAttempts," attempts: ").concat(e))}let t=a(e);await n(t)}throw new r.Dr("Request failed after multiple retries.")}constructor(e){this.baseURL=e.baseURL,this.timeout=e.timeout||1e4,this.retryAttempts=e.retryAttempts||3,this.getAuthToken=e.getAuthToken,this.defaultHeaders={"Content-Type":"application/json",...e.headers}}}},62494:(e,t,i)=>{i.d(t,{J:()=>a});var s=i(38069),r=i(99605);let n={fromApi:e=>({completed:e.completed,id:e.id,taskId:e.taskId,title:e.title}),toApiRequest:e=>({completed:void 0!==e.completed&&e.completed,taskId:e.taskId,title:e.title.trim()})},a={fromApi(e){var t,i,a,o;return{createdAt:e.createdAt,dateTime:e.dateTime,deadline:null!=(t=e.deadline)?t:null,description:e.description,driverEmployee:e.Employee_Task_driverEmployeeIdToEmployee?s.A.fromApi(e.Employee_Task_driverEmployeeIdToEmployee):null,driverEmployeeId:null!=(i=e.driverEmployeeId)?i:null,estimatedDuration:e.estimatedDuration,id:e.id,location:e.location,notes:null!=(a=e.notes)?a:null,priority:e.priority,requiredSkills:e.requiredSkills,staffEmployee:e.Employee_Task_staffEmployeeIdToEmployee?s.A.fromApi(e.Employee_Task_staffEmployeeIdToEmployee):null,staffEmployeeId:e.staffEmployeeId,status:e.status,subtasks:Array.isArray(e.SubTask)?e.SubTask.map(e=>n.fromApi(e)):[],updatedAt:e.updatedAt,vehicle:e.Vehicle?r.M.fromApi(e.Vehicle):null,vehicleId:null!=(o=e.vehicleId)?o:null}},toCreateRequest(e){var t,i,s,r,a,o;return{dateTime:e.dateTime,deadline:null!=(i=e.deadline)?i:null,description:e.description,driverEmployeeId:null!=(s=e.driverEmployeeId)?s:null,estimatedDuration:e.estimatedDuration,location:e.location,notes:null!=(r=e.notes)?r:null,priority:e.priority,requiredSkills:e.requiredSkills,staffEmployeeId:e.staffEmployeeId,status:e.status,subTasks:null!=(a=null==(t=e.subtasks)?void 0:t.map(n.toApiRequest))?a:[],vehicleId:null!=(o=e.vehicleId)?o:null}},toUpdateRequest(e){let t={};return void 0!==e.description&&(t.description=e.description),void 0!==e.notes&&(t.notes=e.notes),void 0!==e.location&&(t.location=e.location),void 0!==e.dateTime&&(t.dateTime=e.dateTime),void 0!==e.estimatedDuration&&(t.estimatedDuration=e.estimatedDuration),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.status&&(t.status=e.status),void 0!==e.deadline&&(t.deadline=e.deadline),void 0!==e.requiredSkills&&(t.requiredSkills=e.requiredSkills),void 0!==e.vehicleId&&(t.vehicleId=e.vehicleId),void 0!==e.staffEmployeeId&&(t.staffEmployeeId=e.staffEmployeeId),void 0!==e.driverEmployeeId&&(t.driverEmployeeId=e.driverEmployeeId),void 0!==e.subtasks&&(t.subTasks=e.subtasks.map(n.toApiRequest)),t}}},75908:(e,t,i)=>{i.d(t,{cl:()=>v,delegationApiService:()=>y,employeeApiService:()=>A,reliabilityApiService:()=>w,Hy:()=>m,taskApiService:()=>S,vehicleApiService:()=>f});var s=i(55411),r=i(976),n=i(12430),a=i(25982);let o={fromApi:e=>e,toApi:e=>e};class l extends a.v{async getSystemHealth(){return this.executeWithInfrastructure("health:system",async()=>await this.apiClient.get("/health"))}async getDetailedHealth(){return this.executeWithInfrastructure("health:detailed",async()=>await this.apiClient.get("/health/detailed"))}async getDependencyHealth(){return this.executeWithInfrastructure("health:dependencies",async()=>await this.apiClient.get("/health/dependencies"))}async getCircuitBreakerStatus(){return this.executeWithInfrastructure("monitoring:circuit-breakers",async()=>{try{let e=await this.apiClient.get("/monitoring/circuit-breakers"),t=(null==e?void 0:e.circuitBreakers)||[];return{circuitBreakers:t||[],summary:{total:(null==t?void 0:t.length)||0,closed:(null==t?void 0:t.filter(e=>"CLOSED"===e.state).length)||0,open:(null==t?void 0:t.filter(e=>"OPEN"===e.state).length)||0,halfOpen:(null==t?void 0:t.filter(e=>"HALF_OPEN"===e.state).length)||0}}}catch(e){return console.error("Failed to get circuit breaker status:",e),{circuitBreakers:[],summary:{total:0,closed:0,open:0,halfOpen:0}}}})}async getDeduplicationMetrics(){return this.executeWithInfrastructure("monitoring:deduplication",async()=>await this.apiClient.get("/monitoring/deduplication"))}async getMetrics(){return this.executeWithInfrastructure("metrics:system",async()=>await this.apiClient.get("/metrics",{headers:{Accept:"application/json"}}))}async getActiveAlerts(){return this.executeWithInfrastructure("alerts:active",async()=>{try{let e=await this.apiClient.get("/alerts");return(null==e?void 0:e.alerts)||[]}catch(e){return console.error("Failed to get active alerts:",e),[]}})}async getAlertHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return this.executeWithInfrastructure("alerts:history:".concat(e,":").concat(t),async()=>{let i=new URLSearchParams({page:e.toString(),limit:t.toString()});return await this.apiClient.get("/alerts/history?".concat(i.toString()))})}async getAlertStatistics(){return this.executeWithInfrastructure("alerts:statistics",async()=>{try{return await this.apiClient.get("/alerts/statistics")}catch(e){return console.error("Failed to get alert statistics:",e),{total:0,active:0,acknowledged:0,resolved:0,bySeverity:{low:0,medium:0,high:0,critical:0},averageResolutionTime:0,recentTrends:{last24Hours:0,last7Days:0,last30Days:0}}}})}async resolveAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/alerts/".concat(e,"/resolve"),{reason:t,resolvedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),s})}async acknowledgeAlert(e,t,i){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/alerts/".concat(e,"/acknowledge"),{note:t,acknowledgedBy:i});return this.cache.invalidatePattern(RegExp("^alerts:")),s})}async testAlerts(){return this.executeWithInfrastructure(null,async()=>{var e;let t=await this.apiClient.post("/alerts/test");return{success:(null==t?void 0:t.status)==="success",message:(null==t?void 0:t.message)||"Test alert triggered",testAlertId:null==t||null==(e=t.data)?void 0:e.id}})}async getReliabilityDashboardData(){let[e,t,i,s,r,n]=await Promise.all([this.getSystemHealth(),this.getDetailedHealth(),this.getCircuitBreakerStatus(),this.getMetrics(),this.getActiveAlerts(),this.getAlertStatistics()]);return{systemHealth:e,detailedHealth:t,circuitBreakers:i,metrics:s,activeAlerts:r,alertStatistics:n}}async isSystemHealthy(){try{let e=await this.getSystemHealth();return"healthy"===e.status}catch(e){return!1}}async getCriticalAlertCount(){try{return(await this.getAlertStatistics()).bySeverity.critical}catch(e){return 0}}async getHealthTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h";return this.executeWithInfrastructure("health:trends:".concat(e),async()=>await this.apiClient.get("/health/trends?timeframe=".concat(e)))}async getCircuitBreakerHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h",t=arguments.length>1?arguments[1]:void 0;return this.executeWithInfrastructure("circuit-breakers:history:".concat(e,":").concat(t||"all"),async()=>{let i=new URLSearchParams({timeframe:e});return t&&i.append("breakerName",t),await this.apiClient.get("/monitoring/circuit-breakers/history?".concat(i.toString()))})}async getHttpRequestMetrics(){return this.executeWithInfrastructure("http:metrics",async()=>await this.apiClient.get("/monitoring/http-request-metrics"))}constructor(e,t){super(e,{cacheDuration:6e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/reliability",this.transformer=o}}var c=i(90137),u=i(97966),h=i(38549);let d=null;function m(e){d=e}function p(){return d?d():null}class g{getApiClient(){return this.apiClient}getDelegationService(){return this.delegationService||(this.delegationService=new r.y(this.apiClient)),this.delegationService}getEmployeeService(){return this.employeeService||(this.employeeService=new n.Q(this.apiClient)),this.employeeService}getReliabilityService(){return this.reliabilityService||(this.reliabilityService=new l(this.apiClient)),this.reliabilityService}getTaskService(){return this.taskService||(this.taskService=new c.D(this.apiClient)),this.taskService}getVehicleService(){return this.vehicleService||(this.vehicleService=new u.C(this.apiClient)),this.vehicleService}constructor(e){this.apiClient=new s.O({...e,getAuthToken:p})}}let v=new g({baseURL:(0,h.Qq)().apiBaseUrl,headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4}),f=v.getVehicleService(),y=v.getDelegationService(),S=v.getTaskService(),A=v.getEmployeeService(),w=v.getReliabilityService()},89699:(e,t,i)=>{i.d(t,{SessionManager:()=>a});var s=i(38549);let r={MAX_CONCURRENT_SESSIONS:5,SESSION_TIMEOUT_MINUTES:30},n={CROSS_TAB_LOGOUT:"cross_tab_logout",SESSION_INVALID:"session_invalid",SESSION_TIMEOUT:"session_timeout",SESSION_VALIDATED:"session_validated",TOKEN_REFRESH_FAILED:"token_refresh_failed",TOKEN_REFRESH_SUCCESS:"token_refresh_success"};class a{static addSessionEventListener(e){var t;if(void 0===globalThis.window)return()=>{};let i=t=>{e(t.data)};return null==(t=this.broadcastChannel)||t.addEventListener("message",i),()=>{var e;null==(e=this.broadcastChannel)||e.removeEventListener("message",i)}}static cleanup(){var e;for(let t of(this.sessionCheckInterval&&(clearInterval(this.sessionCheckInterval),this.sessionCheckInterval=null),null==(e=this.broadcastChannel)||e.close(),this.broadcastChannel=null,this.activityListeners))t();this.activityListeners=[]}static clearSessionState(){if(void 0!==globalThis.window)for(let e of Object.values(this.STORAGE_KEYS))localStorage.removeItem(e)}static detectAndResolveConflicts(){if(void 0===globalThis.window)return!0;try{let e=this.getSessionState(),t=this.getLastActivity(),i=this.getConcurrentSessions();if(e&&t&&Math.abs(e.lastActivity.getTime()-t.getTime())>3e5){console.warn("⚠️ Session timestamp conflict detected, resolving...");let i=e.lastActivity.getTime()>t.getTime()?e.lastActivity:t;localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY,i.toISOString()),this.setSessionState({...e,lastActivity:i}),console.log("✅ Session timestamp conflict resolved")}let s=this.getCurrentSessionId(),r=i.filter(e=>e.sessionId===s);if(r.length>1){console.warn("⚠️ Duplicate session entries detected, cleaning up...");let e=r.reduce((e,t)=>t.lastActivity>e.lastActivity?t:e),t=i.filter(e=>e.sessionId!==s);t.push(e),this.setConcurrentSessions(t),console.log("✅ Duplicate sessions cleaned up")}return!0}catch(e){return console.error("Failed to detect and resolve conflicts:",e),!1}}static detectTimeout(){if(void 0===globalThis.window)return!1;let e=this.getLastActivity();if(!e)return!0;let t=60*r.SESSION_TIMEOUT_MINUTES*1e3;return Date.now()-e.getTime()>t}static getCurrentSessionId(){if(void 0===globalThis.window)return"";let e=localStorage.getItem(this.STORAGE_KEYS.SESSION_ID);return e||(e=this.generateSessionId(),localStorage.setItem(this.STORAGE_KEYS.SESSION_ID,e)),e}static getSessionState(){if(void 0===globalThis.window)return null;try{let e=localStorage.getItem(this.STORAGE_KEYS.SESSION_STATE);if(!e)return null;let t=JSON.parse(e);return{...t,expiresAt:new Date(t.expiresAt),lastActivity:new Date(t.lastActivity)}}catch(e){return null}}static handleCrossTabLogout(){if(void 0===globalThis.window)return;let e={sessionId:this.getCurrentSessionId(),timestamp:new Date,type:n.CROSS_TAB_LOGOUT};this.broadcastSessionEvent(e),this.clearSessionState()}static handleSessionValidation(e,t){if(void 0===globalThis.window)return;let i={data:t,sessionId:this.getCurrentSessionId(),timestamp:new Date,type:e?n.SESSION_VALIDATED:n.SESSION_INVALID};this.broadcastSessionEvent(i),e?this.updateActivity():this.clearSessionState()}static handleTokenRefresh(e,t){if(void 0===globalThis.window)return;let i={data:t,sessionId:this.getCurrentSessionId(),timestamp:new Date,type:e?n.TOKEN_REFRESH_SUCCESS:n.TOKEN_REFRESH_FAILED};if(this.broadcastSessionEvent(i),e){this.updateActivity();let e=this.getSessionState();if(e){let t=new Date,i=new Date(t.getTime()+60*r.SESSION_TIMEOUT_MINUTES*1e3);this.setSessionState({...e,expiresAt:i,lastActivity:t})}}}static initialize(){void 0!==globalThis.window&&(this.initializeBroadcastChannel(),this.startSessionMonitoring(),this.setupActivityTracking(),this.initializeSessionState())}static manageConcurrentSessions(){if(void 0===globalThis.window)return;let e=this.getConcurrentSessions(),t=this.getCurrentSessionId();if(!e.find(e=>e.sessionId===t)){let i={lastActivity:new Date,sessionId:t,startTime:new Date,userAgent:navigator.userAgent};e.push(i)}let i=e.find(e=>e.sessionId===t);i&&(i.lastActivity=new Date);let s=e.filter(e=>{let t=60*r.SESSION_TIMEOUT_MINUTES*1e3;return Date.now()-e.lastActivity.getTime()<=t});s.length>r.MAX_CONCURRENT_SESSIONS&&(s.sort((e,t)=>t.lastActivity.getTime()-e.lastActivity.getTime()),s.splice(r.MAX_CONCURRENT_SESSIONS)),this.setConcurrentSessions(s)}static async performIntegrityCheck(){if(void 0===globalThis.window)return!0;try{if(!this.validateSessionConsistency())return console.warn("\uD83D\uDCCA Local session state is inconsistent"),!1;if(!await this.validateWithBackend())return console.warn("\uD83D\uDD17 Backend session validation failed"),!1;return this.cleanupStaleSessions(),console.log("✅ Session integrity check passed"),!0}catch(e){return console.error("❌ Session integrity check failed:",e),!1}}static recoverFromCorruptedState(){if(void 0===globalThis.window)return!0;try{if(console.log("\uD83D\uDD27 Attempting session state recovery..."),this.validateSessionConsistency())return console.log("✅ Session state is already consistent"),!0;let e=this.preserveNonSecurityData();return this.clearSessionState(),this.restorePreservedData(e),this.initializeSessionState(),console.log("✅ Session state recovery completed"),!0}catch(e){return console.error("❌ Session state recovery failed:",e),!1}}static setSessionState(e){if(void 0!==globalThis.window)try{localStorage.setItem(this.STORAGE_KEYS.SESSION_STATE,JSON.stringify(e))}catch(e){console.error("Failed to set session state:",e)}}static updateActivity(){if(void 0===globalThis.window)return;let e=new Date;localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY,e.toISOString()),this.manageConcurrentSessions()}static validateSessionConsistency(){if(void 0===globalThis.window)return!0;try{let e=this.getSessionState(),t=this.getLastActivity(),i=this.getCurrentSessionId();if(e&&!t)return console.warn("\uD83D\uDD0D Session state exists but no last activity found"),!1;if(t&&!i)return console.warn("\uD83D\uDD0D Last activity exists but no session ID found"),!1;if(e&&e.sessionId!==i)return console.warn("\uD83D\uDD0D Session state ID mismatch with current session ID"),!1;if(e&&e.expiresAt<new Date)return console.warn("\uD83D\uDD0D Session state has expired"),!1;return!0}catch(e){return console.error("Failed to validate session consistency:",e),!1}}static broadcastSessionEvent(e){var t;null==(t=this.broadcastChannel)||t.postMessage(e)}static cleanupStaleSessions(){try{let e=this.getConcurrentSessions(),t=Date.now(),i=60*r.SESSION_TIMEOUT_MINUTES*1e3,s=e.filter(e=>t-e.lastActivity.getTime()<=i);s.length!==e.length&&(console.log("\uD83E\uDDF9 Cleaned up ".concat(e.length-s.length," stale sessions")),this.setConcurrentSessions(s))}catch(e){console.warn("Failed to cleanup stale sessions:",e)}}static generateSessionId(){return"session_".concat(Date.now(),"_").concat(Math.random().toString(36).slice(2,11))}static getConcurrentSessions(){try{let e=localStorage.getItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS);if(!e)return[];return JSON.parse(e).map(e=>({...e,lastActivity:new Date(e.lastActivity),startTime:new Date(e.startTime)}))}catch(e){return[]}}static getLastActivity(){let e=localStorage.getItem(this.STORAGE_KEYS.LAST_ACTIVITY);return e?new Date(e):null}static handleSessionTimeout(){let e={sessionId:this.getCurrentSessionId(),timestamp:new Date,type:n.SESSION_TIMEOUT};this.broadcastSessionEvent(e),this.clearSessionState()}static initializeBroadcastChannel(){"undefined"!=typeof BroadcastChannel&&(this.broadcastChannel=new BroadcastChannel(this.BROADCAST_CHANNEL_NAME))}static initializeSessionState(){let e=this.getCurrentSessionId(),t=new Date,i=new Date(t.getTime()+60*r.SESSION_TIMEOUT_MINUTES*1e3);this.setSessionState({expiresAt:i,isActive:!0,lastActivity:t,sessionId:e}),this.updateActivity()}static preserveNonSecurityData(){let e={};for(let t of["workhub-app-store","workhub_user_preferences"])try{e[t]=localStorage.getItem(t)}catch(e){console.warn("Failed to preserve data for key ".concat(t,":"),e)}return e}static restorePreservedData(e){for(let[t,i]of Object.entries(e))if(null!==i)try{localStorage.setItem(t,i)}catch(e){console.warn("Failed to restore data for key ".concat(t,":"),e)}}static setConcurrentSessions(e){try{localStorage.setItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS,JSON.stringify(e))}catch(e){console.error("Failed to set concurrent sessions:",e)}}static setupActivityTracking(){let e=()=>{this.updateActivity()};for(let t of["mousedown","mousemove","keypress","scroll","touchstart","click"])document.addEventListener(t,e,{passive:!0}),this.activityListeners.push(()=>{document.removeEventListener(t,e)})}static startSessionMonitoring(){this.sessionCheckInterval=setInterval(()=>{this.detectTimeout()&&this.handleSessionTimeout()},6e4)}static async validateWithBackend(){try{let e=(0,s.Qq)().apiBaseUrl;await new Promise(e=>setTimeout(e,200));let t=await fetch("".concat(e,"/health"),{method:"GET",headers:{"Content-Type":"application/json"},cache:"no-cache"});if(t.ok)return console.log("✅ Backend connectivity validation successful"),!0;return console.log("\uD83D\uDD0D Backend connectivity check failed with status: ".concat(t.status)),!1}catch(e){return console.warn("Backend validation failed:",e),!0}}}a.activityListeners=[],a.BROADCAST_CHANNEL_NAME="workhub_session_events",a.broadcastChannel=null,a.sessionCheckInterval=null,a.STORAGE_KEYS={CONCURRENT_SESSIONS:"workhub_concurrent_sessions",LAST_ACTIVITY:"workhub_last_activity",SESSION_ID:"workhub_session_id",SESSION_STATE:"workhub_session_state"}},90137:(e,t,i)=>{i.d(t,{D:()=>a});var s=i(62494),r=i(25982);let n={fromApi:e=>s.J.fromApi(e),toApi:e=>e};class a extends r.v{async getByStatus(e){return(await this.getAll({status:e})).data}async updateTaskStatus(e,t){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/status"),{status:t});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),s.J.fromApi(i)})}constructor(e,t){super(e,{cacheDuration:18e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/tasks",this.transformer=n}}},97966:(e,t,i)=>{i.d(t,{C:()=>a});var s=i(25982),r=i(99605);let n={fromApi:e=>r.M.fromApi(e),toApi:e=>e};class a extends s.v{async getAvailableVehicles(e,t){return(await this.getAll({available:!0,endDate:t.toISOString(),startDate:e.toISOString()})).data}async getByStatus(e){return(await this.getAll({status:e})).data}constructor(e,t){super(e,{cacheDuration:6e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/vehicles",this.transformer=n}}},99605:(e,t,i)=>{i.d(t,{M:()=>r});let s={fromApi:e=>({cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId})},r={fromApi(e){var t,i,r,n;return{color:null!=(t=e.color)?t:null,createdAt:e.createdAt,id:e.id,imageUrl:null!=(i=e.imageUrl)?i:null,initialOdometer:null!=(r=e.initialOdometer)?r:null,licensePlate:e.licensePlate,make:e.make,model:e.model,ownerContact:e.ownerContact,ownerName:e.ownerName,serviceHistory:(()=>{let t=e.serviceHistory||e.ServiceRecord;return Array.isArray(t)?t.map(s.fromApi):[]})(),updatedAt:e.updatedAt,vin:null!=(n=e.vin)?n:null,year:e.year}},toCreateRequest(e){var t,i,s,r;let n=(null==(t=e.vin)?void 0:t.trim())||this.generateDefaultVin(e),a=(null==(i=e.ownerContact)?void 0:i.trim())||"<EMAIL>",o=(null==(s=e.ownerName)?void 0:s.trim())||"WorkHub Fleet Management",l={color:e.color?e.color.trim():null,imageUrl:e.imageUrl?e.imageUrl.trim():"",initialOdometer:null!=(r=e.initialOdometer)?r:null,licensePlate:e.licensePlate.trim(),make:e.make.trim(),model:e.model.trim(),ownerContact:a,ownerName:o,vin:n,year:e.year};if(!l.make||!l.model||!l.year||!l.licensePlate)throw Error("Missing required fields for creating a vehicle (make, model, year, licensePlate)");if(!/^[A-HJ-NPR-Z0-9]{17}$/.test(l.vin))throw Error("VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)");return l},generateDefaultVin(e){let t="ABCDEFGHJKLMNPRSTUVWXYZ0123456789",i=e.make.substring(0,3).toUpperCase().replace(/[IOQ]/g,"X").padEnd(3,"X"),s=e.model.substring(0,2).toUpperCase().replace(/[IOQ]/g,"X").padEnd(2,"X"),r=e.year.toString().substring(2),n="";for(let e=0;e<10;e++)n+=t.charAt(Math.floor(Math.random()*t.length));return"".concat(i).concat(s).concat(r).concat(n).substring(0,17).padEnd(17,"X")},toUpdateRequest(e){let t={};return void 0!==e.make&&(t.make=e.make.trim()),void 0!==e.model&&(t.model=e.model.trim()),void 0!==e.year&&(t.year=e.year),void 0!==e.vin&&(t.vin=e.vin.trim()),void 0!==e.licensePlate&&(t.licensePlate=e.licensePlate.trim()),void 0!==e.ownerName&&(t.ownerName=e.ownerName.trim()),void 0!==e.ownerContact&&(t.ownerContact=e.ownerContact.trim()),void 0!==e.color&&(t.color=e.color?e.color.trim():null),void 0!==e.initialOdometer&&(t.initialOdometer=e.initialOdometer),void 0!==e.imageUrl&&(t.imageUrl=e.imageUrl?e.imageUrl.trim():""),t}}}}]);