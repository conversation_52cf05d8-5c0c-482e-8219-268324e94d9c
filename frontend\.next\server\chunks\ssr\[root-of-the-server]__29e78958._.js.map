{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport * as React from 'react';\r\nimport { DayPicker } from 'react-day-picker';\r\n\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>;\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: CalendarProps) {\r\n  return (\r\n    <DayPicker\r\n      className={cn('p-3', className)}\r\n      classNames={{\r\n        caption: 'flex justify-center pt-1 relative items-center',\r\n        caption_label: 'text-sm font-medium',\r\n        cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',\r\n        day: cn(\r\n          buttonVariants({ variant: 'ghost' }),\r\n          'h-9 w-9 p-0 font-normal aria-selected:opacity-100'\r\n        ),\r\n        day_disabled: 'text-muted-foreground opacity-50',\r\n        day_hidden: 'invisible',\r\n        day_outside:\r\n          'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',\r\n        day_range_end: 'day-range-end',\r\n        day_range_middle:\r\n          'aria-selected:bg-accent aria-selected:text-accent-foreground',\r\n        day_selected:\r\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\r\n        day_today: 'bg-accent text-accent-foreground',\r\n        head_cell:\r\n          'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',\r\n        head_row: 'flex',\r\n        month: 'space-y-4',\r\n        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',\r\n        nav: 'space-x-1 flex items-center',\r\n        nav_button: cn(\r\n          buttonVariants({ variant: 'outline' }),\r\n          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'\r\n        ),\r\n        nav_button_next: 'absolute right-1',\r\n        nav_button_previous: 'absolute left-1',\r\n        row: 'flex w-full mt-2',\r\n        table: 'w-full border-collapse space-y-1',\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn('h-4 w-4', className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn('h-4 w-4', className)} {...props} />\r\n        ),\r\n      }}\r\n      showOutsideDays={showOutsideDays}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\nCalendar.displayName = 'Calendar';\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AAEA;AACA;AAAA;AAPA;;;;;;AAWA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,SAAS;YACT,eAAe;YACf,MAAM;YACN,KAAK,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,cAAc;YACd,YAAY;YACZ,aACE;YACF,eAAe;YACf,kBACE;YACF,cACE;YACF,WAAW;YACX,WACE;YACF,UAAU;YACV,OAAO;YACP,QAAQ;YACR,KAAK;YACL,YAAY,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,iBAAiB;YACjB,qBAAqB;YACrB,KAAK;YACL,OAAO;YACP,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;YAE7D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;QAEhE;QACA,iBAAiB;QAChB,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Pagination = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className={cn('flex justify-center', className)} ref={ref} {...props} />\r\n));\r\nPagination.displayName = 'Pagination';\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.HTMLAttributes<HTMLUListElement>\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    className={cn('flex flex-row items-center gap-1', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nPaginationContent.displayName = 'PaginationContent';\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.HTMLAttributes<HTMLLIElement>\r\n>(({ className, ...props }, ref) => (\r\n  <li className={cn('', className)} ref={ref} {...props} />\r\n));\r\nPaginationItem.displayName = 'PaginationItem';\r\n\r\ntype PaginationLinkProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {\r\n  isActive?: boolean;\r\n};\r\n\r\nconst PaginationLink = React.forwardRef<HTMLButtonElement, PaginationLinkProps>(\r\n  ({ className, isActive, ...props }, ref) => (\r\n    <Button\r\n      aria-current={isActive ? 'page' : undefined}\r\n      className={cn('h-9 w-9', className)}\r\n      ref={ref}\r\n      size=\"icon\"\r\n      variant={isActive ? 'outline' : 'ghost'}\r\n      {...props}\r\n    />\r\n  )\r\n);\r\nPaginationLink.displayName = 'PaginationLink';\r\n\r\nconst PaginationPrevious = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ButtonHTMLAttributes<HTMLButtonElement>\r\n>(({ className, ...props }, ref) => (\r\n  <Button\r\n    className={cn('h-9 w-9 gap-1', className)}\r\n    ref={ref}\r\n    size=\"icon\"\r\n    variant=\"ghost\"\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"size-4\" />\r\n    <span className=\"sr-only\">Previous page</span>\r\n  </Button>\r\n));\r\nPaginationPrevious.displayName = 'PaginationPrevious';\r\n\r\nconst PaginationNext = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ButtonHTMLAttributes<HTMLButtonElement>\r\n>(({ className, ...props }, ref) => (\r\n  <Button\r\n    className={cn('h-9 w-9 gap-1', className)}\r\n    ref={ref}\r\n    size=\"icon\"\r\n    variant=\"ghost\"\r\n    {...props}\r\n  >\r\n    <ChevronRight className=\"size-4\" />\r\n    <span className=\"sr-only\">Next page</span>\r\n  </Button>\r\n));\r\nPaginationNext.displayName = 'PaginationNext';\r\n\r\nconst PaginationEllipsis = React.forwardRef<\r\n  HTMLSpanElement,\r\n  React.HTMLAttributes<HTMLSpanElement>\r\n>(({ className, ...props }, ref) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"size-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n));\r\nPaginationEllipsis.displayName = 'PaginationEllipsis';\r\n\r\n/**\r\n * Props for the PaginationControls component\r\n */\r\ninterface PaginationControlsProps {\r\n  /** Additional CSS class names */\r\n  className?: string;\r\n  /** Current active page number (1-based) */\r\n  currentPage: number;\r\n  /** Callback function when page is changed */\r\n  onPageChange: (page: number) => void;\r\n  /** Total number of pages */\r\n  totalPages: number;\r\n}\r\n\r\n/**\r\n * A component that displays pagination controls with dynamic page number calculation\r\n *\r\n * @example\r\n * ```tsx\r\n * <PaginationControls\r\n *   currentPage={currentPage}\r\n *   totalPages={totalPages}\r\n *   onPageChange={handlePageChange}\r\n * />\r\n * ```\r\n */\r\nexport function PaginationControls({\r\n  className,\r\n  currentPage,\r\n  onPageChange,\r\n  totalPages,\r\n}: PaginationControlsProps) {\r\n  // Calculate which page numbers to show\r\n  const getPageNumbers = () => {\r\n    const pageNumbers: (number | string)[] = [];\r\n\r\n    // Always show first page\r\n    pageNumbers.push(1);\r\n\r\n    // Calculate range around current page\r\n    const rangeStart = Math.max(2, currentPage - 1);\r\n    const rangeEnd = Math.min(totalPages - 1, currentPage + 1);\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (rangeStart > 2) {\r\n      pageNumbers.push('ellipsis1');\r\n    }\r\n\r\n    // Add pages in range\r\n    for (let i = rangeStart; i <= rangeEnd; i++) {\r\n      pageNumbers.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (rangeEnd < totalPages - 1) {\r\n      pageNumbers.push('ellipsis2');\r\n    }\r\n\r\n    // Always show last page if there is more than one page\r\n    if (totalPages > 1) {\r\n      pageNumbers.push(totalPages);\r\n    }\r\n\r\n    return pageNumbers;\r\n  };\r\n\r\n  const pageNumbers = getPageNumbers();\r\n\r\n  // Don't render pagination if there's only one page\r\n  if (totalPages <= 1) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Pagination className={className}>\r\n      <PaginationContent>\r\n        <PaginationItem>\r\n          <PaginationPrevious\r\n            aria-disabled={currentPage === 1 ? 'true' : undefined}\r\n            aria-label=\"Go to previous page\"\r\n            disabled={currentPage === 1}\r\n            onClick={() => onPageChange(currentPage - 1)}\r\n          />\r\n        </PaginationItem>\r\n\r\n        {pageNumbers.map((page, index) => {\r\n          if (page === 'ellipsis1' || page === 'ellipsis2') {\r\n            return (\r\n              <PaginationItem key={`ellipsis-${index}`}>\r\n                <PaginationEllipsis />\r\n              </PaginationItem>\r\n            );\r\n          }\r\n\r\n          const pageNumber = page as number;\r\n\r\n          return (\r\n            <PaginationItem key={`page-${pageNumber}`}>\r\n              <PaginationLink\r\n                aria-label={`Go to page ${pageNumber}`}\r\n                isActive={currentPage === pageNumber}\r\n                onClick={() => onPageChange(pageNumber)}\r\n              >\r\n                {pageNumber}\r\n              </PaginationLink>\r\n            </PaginationItem>\r\n          );\r\n        })}\r\n\r\n        <PaginationItem>\r\n          <PaginationNext\r\n            aria-disabled={currentPage === totalPages ? 'true' : undefined}\r\n            aria-label=\"Go to next page\"\r\n            disabled={currentPage === totalPages}\r\n            onClick={() => onPageChange(currentPage + 1)}\r\n          />\r\n        </PaginationItem>\r\n      </PaginationContent>\r\n    </Pagination>\r\n  );\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAE3E,WAAW,WAAW,GAAG;AAEzB,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAG,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAEvD,eAAe,WAAW,GAAG;AAM7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EACpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,KAAK;QACL,MAAK;QACL,SAAS,WAAW,YAAY;QAC/B,GAAG,KAAK;;;;;;AAIf,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kIAAA,CAAA,SAAM;QACL,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,KAAK;QACL,MAAK;QACL,SAAQ;QACP,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG;AAEjC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kIAAA,CAAA,SAAM;QACL,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,KAAK;QACL,MAAK;QACL,SAAQ;QACP,GAAG,KAAK;;0BAET,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;0BACxB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG;AA4B1B,SAAS,mBAAmB,EACjC,SAAS,EACT,WAAW,EACX,YAAY,EACZ,UAAU,EACc;IACxB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,MAAM,cAAmC,EAAE;QAE3C,yBAAyB;QACzB,YAAY,IAAI,CAAC;QAEjB,sCAAsC;QACtC,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,cAAc;QAC7C,MAAM,WAAW,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAExD,0CAA0C;QAC1C,IAAI,aAAa,GAAG;YAClB,YAAY,IAAI,CAAC;QACnB;QAEA,qBAAqB;QACrB,IAAK,IAAI,IAAI,YAAY,KAAK,UAAU,IAAK;YAC3C,YAAY,IAAI,CAAC;QACnB;QAEA,0CAA0C;QAC1C,IAAI,WAAW,aAAa,GAAG;YAC7B,YAAY,IAAI,CAAC;QACnB;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,YAAY,IAAI,CAAC;QACnB;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,mDAAmD;IACnD,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAW,WAAW;kBACrB,cAAA,8OAAC;;8BACC,8OAAC;8BACC,cAAA,8OAAC;wBACC,iBAAe,gBAAgB,IAAI,SAAS;wBAC5C,cAAW;wBACX,UAAU,gBAAgB;wBAC1B,SAAS,IAAM,aAAa,cAAc;;;;;;;;;;;gBAI7C,YAAY,GAAG,CAAC,CAAC,MAAM;oBACtB,IAAI,SAAS,eAAe,SAAS,aAAa;wBAChD,qBACE,8OAAC;sCACC,cAAA,8OAAC;;;;;2BADkB,CAAC,SAAS,EAAE,OAAO;;;;;oBAI5C;oBAEA,MAAM,aAAa;oBAEnB,qBACE,8OAAC;kCACC,cAAA,8OAAC;4BACC,cAAY,CAAC,WAAW,EAAE,YAAY;4BACtC,UAAU,gBAAgB;4BAC1B,SAAS,IAAM,aAAa;sCAE3B;;;;;;uBANgB,CAAC,KAAK,EAAE,YAAY;;;;;gBAU7C;8BAEA,8OAAC;8BACC,cAAA,8OAAC;wBACC,iBAAe,gBAAgB,aAAa,SAAS;wBACrD,cAAW;wBACX,UAAU,gBAAgB;wBAC1B,SAAS,IAAM,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;AAMtD", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Popover = PopoverPrimitive.Root;\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger;\r\n\r\nconst PopoverClose = PopoverPrimitive.Close;\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ align = 'center', className, sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      align={align}\r\n      className={cn(\r\n        'z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n));\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\r\n\r\nexport { Popover, PopoverContent, PopoverTrigger, PopoverClose };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,eAAe,mKAAA,CAAA,QAAsB;AAE3C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,OAAO;YACP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,geACA;YAEF,KAAK;YACL,YAAY;YACX,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      className={cn('w-full caption-bottom text-sm', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = 'Table';\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead className={cn('[&_tr]:border-b', className)} ref={ref} {...props} />\r\n));\r\nTableHeader.displayName = 'TableHeader';\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    className={cn('[&_tr:last-child]:border-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = 'TableBody';\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    className={cn(\r\n      'border-t bg-muted/50 font-medium [&>tr]:last:border-b-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = 'TableFooter';\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    className={cn(\r\n      'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = 'TableRow';\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    className={cn(\r\n      'h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = 'TableHead';\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = 'TableCell';\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    className={cn('mt-4 text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = 'TableCaption';\r\n\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC/C,KAAK;YACJ,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAChE,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/domain/reliabilityApi.ts"], "sourcesContent": ["import type {\r\n  AlertApiResponse,\r\n  AlertHistoryApiResponse,\r\n  AlertStatisticsApiResponse,\r\n  CircuitBreakerStatusApiResponse,\r\n  DeduplicationMetricsApiResponse,\r\n  DependencyHealthApiResponse,\r\n  DetailedHealthApiResponse,\r\n  HealthCheckApiResponse,\r\n  MetricsApiResponse,\r\n  TestAlertsApiResponse,\r\n} from '../../../types/api';\r\nimport type {\r\n  Alert,\r\n  AlertHistory,\r\n  AlertStatistics,\r\n  CircuitBreakerStatus,\r\n  DeduplicationMetrics,\r\n  DependencyHealth,\r\n  DetailedHealthCheck,\r\n  HealthCheck,\r\n  SystemMetrics,\r\n  TestAlertsResult,\r\n} from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\nimport logger from '../../../utils/logger';\r\n\r\nconst ReliabilityTransformer: DataTransformer<any> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class ReliabilityApiService extends BaseApiService<any, any, any> {\r\n  protected endpoint = '/reliability';\r\n  protected transformer: DataTransformer<any> = ReliabilityTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 1 * 60 * 1000, // 1 minute for reliability data\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getSystemHealth(): Promise<HealthCheck> {\r\n    return this.executeWithInfrastructure('health:system', async () => {\r\n      const response =\r\n        await this.apiClient.get<HealthCheckApiResponse>('/health');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDetailedHealth(): Promise<DetailedHealthCheck> {\r\n    return this.executeWithInfrastructure('health:detailed', async () => {\r\n      const response =\r\n        await this.apiClient.get<DetailedHealthApiResponse>('/health/detailed');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDependencyHealth(): Promise<DependencyHealth> {\r\n    return this.executeWithInfrastructure('health:dependencies', async () => {\r\n      const response = await this.apiClient.get<DependencyHealthApiResponse>(\r\n        '/health/dependencies'\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getCircuitBreakerStatus(): Promise<CircuitBreakerStatus> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:circuit-breakers',\r\n      async () => {\r\n        try {\r\n          const apiResponse = await this.apiClient.get<any>(\r\n            '/monitoring/circuit-breakers'\r\n          );\r\n\r\n          const circuitBreakers = apiResponse?.circuitBreakers || [];\r\n\r\n          return {\r\n            circuitBreakers: circuitBreakers || [],\r\n            summary: {\r\n              total: circuitBreakers?.length || 0,\r\n              closed:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'CLOSED')\r\n                  .length || 0,\r\n              open:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'OPEN')\r\n                  .length || 0,\r\n              halfOpen:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'HALF_OPEN')\r\n                  .length || 0,\r\n            },\r\n          };\r\n        } catch (error) {\r\n          console.error('Failed to get circuit breaker status:', error);\r\n          return {\r\n            circuitBreakers: [],\r\n            summary: { total: 0, closed: 0, open: 0, halfOpen: 0 },\r\n          };\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  async getDeduplicationMetrics(): Promise<DeduplicationMetrics> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:deduplication',\r\n      async () => {\r\n        const response =\r\n          await this.apiClient.get<DeduplicationMetricsApiResponse>(\r\n            '/monitoring/deduplication'\r\n          );\r\n        return response as any;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getMetrics(): Promise<SystemMetrics> {\r\n    return this.executeWithInfrastructure('metrics:system', async () => {\r\n      const response = await this.apiClient.get<MetricsApiResponse>(\r\n        '/metrics',\r\n        {\r\n          headers: { Accept: 'application/json' },\r\n        }\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getActiveAlerts(): Promise<Alert[]> {\r\n    return this.executeWithInfrastructure('alerts:active', async () => {\r\n      try {\r\n        const apiResponse = await this.apiClient.get<any>('/alerts');\r\n\r\n        return apiResponse?.alerts || [];\r\n      } catch (error) {\r\n        console.error('Failed to get active alerts:', error);\r\n        return [];\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAlertHistory(\r\n    page: number = 1,\r\n    limit: number = 50\r\n  ): Promise<AlertHistory> {\r\n    return this.executeWithInfrastructure(\r\n      `alerts:history:${page}:${limit}`,\r\n      async () => {\r\n        const queryParams = new URLSearchParams({\r\n          page: page.toString(),\r\n          limit: limit.toString(),\r\n        });\r\n        const response = await this.apiClient.get<AlertHistoryApiResponse>(\r\n          `/alerts/history?${queryParams.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getAlertStatistics(): Promise<AlertStatistics> {\r\n    return this.executeWithInfrastructure('alerts:statistics', async () => {\r\n      try {\r\n        const response =\r\n          await this.apiClient.get<AlertStatisticsApiResponse>(\r\n            '/alerts/statistics'\r\n          );\r\n        return response;\r\n      } catch (error) {\r\n        console.error('Failed to get alert statistics:', error);\r\n        return {\r\n          total: 0,\r\n          active: 0,\r\n          acknowledged: 0,\r\n          resolved: 0,\r\n          bySeverity: { low: 0, medium: 0, high: 0, critical: 0 },\r\n          averageResolutionTime: 0,\r\n          recentTrends: { last24Hours: 0, last7Days: 0, last30Days: 0 },\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  async resolveAlert(\r\n    alertId: string,\r\n    reason?: string,\r\n    resolvedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/resolve`,\r\n        {\r\n          reason,\r\n          resolvedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async acknowledgeAlert(\r\n    alertId: string,\r\n    note?: string,\r\n    acknowledgedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/acknowledge`,\r\n        {\r\n          note,\r\n          acknowledgedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async testAlerts(): Promise<TestAlertsResult> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>('/alerts/test');\r\n      return {\r\n        success: response?.status === 'success',\r\n        message: response?.message || 'Test alert triggered',\r\n        testAlertId: response?.data?.id,\r\n      };\r\n    });\r\n  }\r\n\r\n  async getReliabilityDashboardData(): Promise<{\r\n    systemHealth: HealthCheck;\r\n    detailedHealth: DetailedHealthCheck;\r\n    circuitBreakers: CircuitBreakerStatus;\r\n    metrics: SystemMetrics;\r\n    activeAlerts: Alert[];\r\n    alertStatistics: AlertStatistics;\r\n  }> {\r\n    const [\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    ] = await Promise.all([\r\n      this.getSystemHealth(),\r\n      this.getDetailedHealth(),\r\n      this.getCircuitBreakerStatus(),\r\n      this.getMetrics(),\r\n      this.getActiveAlerts(),\r\n      this.getAlertStatistics(),\r\n    ]);\r\n\r\n    return {\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    };\r\n  }\r\n\r\n  async isSystemHealthy(): Promise<boolean> {\r\n    try {\r\n      const health = await this.getSystemHealth();\r\n      return health.status === 'healthy';\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async getCriticalAlertCount(): Promise<number> {\r\n    try {\r\n      const statistics = await this.getAlertStatistics();\r\n      return statistics.bySeverity.critical;\r\n    } catch (error) {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  async getHealthTrends(\r\n    timeframe: '1h' | '6h' | '24h' | '7d' = '24h'\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `health:trends:${timeframe}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<any>(\r\n          `/health/trends?timeframe=${timeframe}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCircuitBreakerHistory(\r\n    timeframe: '1h' | '6h' | '24h' | '7d' = '24h',\r\n    breakerName?: string\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `circuit-breakers:history:${timeframe}:${breakerName || 'all'}`,\r\n      async () => {\r\n        const params = new URLSearchParams({ timeframe });\r\n        if (breakerName) {\r\n          params.append('breakerName', breakerName);\r\n        }\r\n        const response = await this.apiClient.get<any>(\r\n          `/monitoring/circuit-breakers/history?${params.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getHttpRequestMetrics(): Promise<any> {\r\n    return this.executeWithInfrastructure('http:metrics', async () => {\r\n      const response = await this.apiClient.get<any>(\r\n        '/monitoring/http-request-metrics'\r\n      );\r\n      return response;\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAyBA;;AAOA,MAAM,yBAA+C;IACnD,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,8BAA8B,2IAAA,CAAA,iBAAc;IAC7C,WAAW,eAAe;IAC1B,cAAoC,uBAAuB;IAErE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,kBAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB;YACnD,OAAO;QACT;IACF;IAEA,MAAM,oBAAkD;QACtD,OAAO,IAAI,CAAC,yBAAyB,CAAC,mBAAmB;YACvD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAA4B;YACtD,OAAO;QACT;IACF;IAEA,MAAM,sBAAiD;QACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,uBAAuB;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,+BACA;YACE,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAC1C;gBAGF,MAAM,kBAAkB,aAAa,mBAAmB,EAAE;gBAE1D,OAAO;oBACL,iBAAiB,mBAAmB,EAAE;oBACtC,SAAS;wBACP,OAAO,iBAAiB,UAAU;wBAClC,QACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,UAC/C,UAAU;wBACf,MACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,QAC/C,UAAU;wBACf,UACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,aAC/C,UAAU;oBACjB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;oBACL,iBAAiB,EAAE;oBACnB,SAAS;wBAAE,OAAO;wBAAG,QAAQ;wBAAG,MAAM;wBAAG,UAAU;oBAAE;gBACvD;YACF;QACF;IAEJ;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,4BACA;YACE,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;YAEJ,OAAO;QACT;IAEJ;IAEA,MAAM,aAAqC;QACzC,OAAO,IAAI,CAAC,yBAAyB,CAAC,kBAAkB;YACtD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,YACA;gBACE,SAAS;oBAAE,QAAQ;gBAAmB;YACxC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAElD,OAAO,aAAa,UAAU,EAAE;YAClC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBACJ,OAAe,CAAC,EAChB,QAAgB,EAAE,EACK;QACvB,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,OAAO,EACjC;YACE,MAAM,cAAc,IAAI,gBAAgB;gBACtC,MAAM,KAAK,QAAQ;gBACnB,OAAO,MAAM,QAAQ;YACvB;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;YAE7C,OAAO;QACT;IAEJ;IAEA,MAAM,qBAA+C;QACnD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB;YACzD,IAAI;gBACF,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,YAAY;wBAAE,KAAK;wBAAG,QAAQ;wBAAG,MAAM;wBAAG,UAAU;oBAAE;oBACtD,uBAAuB;oBACvB,cAAc;wBAAE,aAAa;wBAAG,WAAW;wBAAG,YAAY;oBAAE;gBAC9D;YACF;QACF;IACF;IAEA,MAAM,aACJ,OAAe,EACf,MAAe,EACf,UAAmB,EACH;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,iBACJ,OAAe,EACf,IAAa,EACb,cAAuB,EACP;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,YAAY,CAAC,EAChC;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,aAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAM;YAChD,OAAO;gBACL,SAAS,UAAU,WAAW;gBAC9B,SAAS,UAAU,WAAW;gBAC9B,aAAa,UAAU,MAAM;YAC/B;QACF;IACF;IAEA,MAAM,8BAOH;QACD,MAAM,CACJ,cACA,gBACA,iBACA,SACA,cACA,gBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,kBAAkB;SACxB;QAED,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe;YACzC,OAAO,OAAO,MAAM,KAAK;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,wBAAyC;QAC7C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB;YAChD,OAAO,WAAW,UAAU,CAAC,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,gBACJ,YAAwC,KAAK,EAC/B;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,cAAc,EAAE,WAAW,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,yBAAyB,EAAE,WAAW;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,yBACJ,YAAwC,KAAK,EAC7C,WAAoB,EACN;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,yBAAyB,EAAE,UAAU,CAAC,EAAE,eAAe,OAAO,EAC/D;YACE,MAAM,SAAS,IAAI,gBAAgB;gBAAE;YAAU;YAC/C,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,eAAe;YAC/B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,qCAAqC,EAAE,OAAO,QAAQ,IAAI;YAE7D,OAAO;QACT;IAEJ;IAEA,MAAM,wBAAsC;QAC1C,OAAO,IAAI,CAAC,yBAAyB,CAAC,gBAAgB;YACpD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/factory.ts"], "sourcesContent": ["/**\r\n * @file Factory for creating and managing API service instances.\r\n * @module api/services/apiServiceFactory\r\n */\r\n\r\nimport { ApiClient } from '../core/apiClient';\r\nimport { DelegationApiService } from './domain/delegationApi';\r\nimport { EmployeeApiService } from './domain/employeeApi';\r\nimport { ReliabilityApiService } from './domain/reliabilityApi';\r\nimport { TaskApiService } from './domain/taskApi';\r\nimport { VehicleApiService } from './domain/vehicleApi';\r\nimport { getEnvironmentConfig } from '../../config/environment';\r\n// Import secure auth token provider\r\nimport { getSecureAuthTokenProvider } from '../index';\r\n\r\n/**\r\n * Get the current auth token from the secure provider\r\n * Uses the single source of truth for authentication tokens\r\n */\r\nfunction getSecureAuthToken(): string | null {\r\n  const provider = getSecureAuthTokenProvider();\r\n  if (!provider) {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('⚠️ Factory: Secure Auth Token Provider not initialized');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return provider();\r\n  } catch (error) {\r\n    console.error(\r\n      '❌ Factory: Error getting auth token from secure provider:',\r\n      error\r\n    );\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use setSecureAuthTokenProvider from main API module instead\r\n */\r\nexport function setFactoryAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  console.warn(\r\n    '⚠️ setFactoryAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider from @/lib/api instead.'\r\n  );\r\n  // This function is now a no-op since we use the secure provider\r\n  // The warning guides developers to use the correct function\r\n}\r\n\r\n/**\r\n * Configuration for the API service factory.\r\n */\r\nexport interface ApiServiceFactoryConfig {\r\n  authToken?: string;\r\n  baseURL: string;\r\n  headers?: Record<string, string>;\r\n  retryAttempts?: number;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Factory class for creating and managing API service instances.\r\n * Provides a centralized way to configure and access all API services.\r\n */\r\nexport class ApiServiceFactory {\r\n  private readonly apiClient: ApiClient;\r\n  private delegationService?: DelegationApiService;\r\n  private employeeService?: EmployeeApiService;\r\n  private reliabilityService?: ReliabilityApiService;\r\n  private taskService?: TaskApiService;\r\n  private vehicleService?: VehicleApiService;\r\n\r\n  /**\r\n   * Creates an instance of ApiServiceFactory.\r\n   * @param config - Configuration for the API services.\r\n   */\r\n  constructor(config: ApiServiceFactoryConfig) {\r\n    this.apiClient = new ApiClient({\r\n      ...config,\r\n      getAuthToken: getSecureAuthToken, // Use consistent secure naming\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the underlying ApiClient instance.\r\n   * @returns The ApiClient instance.\r\n   */\r\n  public getApiClient(): ApiClient {\r\n    return this.apiClient;\r\n  }\r\n\r\n  /**\r\n   * Gets the Delegation API service instance.\r\n   * @returns The DelegationApiService instance.\r\n   */\r\n  public getDelegationService(): DelegationApiService {\r\n    if (!this.delegationService) {\r\n      this.delegationService = new DelegationApiService(this.apiClient);\r\n    }\r\n    return this.delegationService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Employee API service instance.\r\n   * @returns The EmployeeApiService instance.\r\n   */\r\n  public getEmployeeService(): EmployeeApiService {\r\n    if (!this.employeeService) {\r\n      this.employeeService = new EmployeeApiService(this.apiClient);\r\n    }\r\n    return this.employeeService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Reliability API service instance.\r\n   * @returns The ReliabilityApiService instance.\r\n   */\r\n  public getReliabilityService(): ReliabilityApiService {\r\n    if (!this.reliabilityService) {\r\n      this.reliabilityService = new ReliabilityApiService(this.apiClient);\r\n    }\r\n    return this.reliabilityService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Task API service instance.\r\n   * @returns The TaskApiService instance.\r\n   */\r\n  public getTaskService(): TaskApiService {\r\n    if (!this.taskService) {\r\n      this.taskService = new TaskApiService(this.apiClient);\r\n    }\r\n    return this.taskService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Vehicle API service instance.\r\n   * @returns The VehicleApiService instance.\r\n   */\r\n  public getVehicleService(): VehicleApiService {\r\n    if (!this.vehicleService) {\r\n      this.vehicleService = new VehicleApiService(this.apiClient);\r\n    }\r\n    return this.vehicleService;\r\n  }\r\n}\r\n\r\n// Create a default factory instance for the application with environment-aware configuration\r\nconst envConfig = getEnvironmentConfig();\r\nconst defaultConfig: ApiServiceFactoryConfig = {\r\n  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  retryAttempts: 3,\r\n  timeout: 10_000,\r\n};\r\n\r\nexport const apiServiceFactory = new ApiServiceFactory(defaultConfig);\r\n\r\n// Export individual service instances for convenience\r\nexport const vehicleApiService = apiServiceFactory.getVehicleService();\r\nexport const delegationApiService = apiServiceFactory.getDelegationService();\r\nexport const taskApiService = apiServiceFactory.getTaskService();\r\nexport const employeeApiService = apiServiceFactory.getEmployeeService();\r\nexport const reliabilityApiService = apiServiceFactory.getReliabilityService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AAAA;;;;;;;;;AAEA;;;CAGC,GACD,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,0IAAA,CAAA,6BAA0B,AAAD;IAC1C,IAAI,CAAC,UAAU;QACb,wCAA4C;YAC1C,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,6DACA;QAEF,OAAO;IACT;AACF;AAMO,SAAS,4BACd,QAA6B;IAE7B,QAAQ,IAAI,CACV;AAEF,gEAAgE;AAChE,4DAA4D;AAC9D;AAiBO,MAAM;IACM,UAAqB;IAC9B,kBAAyC;IACzC,gBAAqC;IACrC,mBAA2C;IAC3C,YAA6B;IAC7B,eAAmC;IAE3C;;;GAGC,GACD,YAAY,MAA+B,CAAE;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,sIAAA,CAAA,YAAS,CAAC;YAC7B,GAAG,MAAM;YACT,cAAc;QAChB;IACF;IAEA;;;GAGC,GACD,AAAO,eAA0B;QAC/B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;GAGC,GACD,AAAO,uBAA6C;QAClD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,wJAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,SAAS;QAClE;QACA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;;GAGC,GACD,AAAO,qBAAyC;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,sJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,SAAS;QAC9D;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;;GAGC,GACD,AAAO,wBAA+C;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,yJAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,SAAS;QACpE;QACA,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA;;;GAGC,GACD,AAAO,iBAAiC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,kJAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,SAAS;QACtD;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;;GAGC,GACD,AAAO,oBAAuC;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,qJAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,SAAS;QAC5D;QACA,OAAO,IAAI,CAAC,cAAc;IAC5B;AACF;AAEA,6FAA6F;AAC7F,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD;AACrC,MAAM,gBAAyC;IAC7C,SAAS,UAAU,UAAU;IAC7B,SAAS;QACP,gBAAgB;IAClB;IACA,eAAe;IACf,SAAS;AACX;AAEO,MAAM,oBAAoB,IAAI,kBAAkB;AAGhD,MAAM,oBAAoB,kBAAkB,iBAAiB;AAC7D,MAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,MAAM,iBAAiB,kBAAkB,cAAc;AACvD,MAAM,qBAAqB,kBAAkB,kBAAkB;AAC/D,MAAM,wBAAwB,kBAAkB,qBAAqB", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/apiServiceFactory.ts"], "sourcesContent": ["/**\n * @file API Service Factory - Backward Compatibility Export\n * @module api/services/apiServiceFactory\n *\n * This file provides backward compatibility for imports that expect\n * apiServiceFactory.ts instead of factory.ts\n */\n\n// Re-export everything from the factory module\nexport * from './factory';\n\n// Ensure all the commonly used exports are available\nexport {\n  ApiServiceFactory,\n  apiServiceFactory,\n  setFactoryAuthTokenProvider, // Legacy compatibility - deprecated\n  vehicleApiService,\n  delegationApiService,\n  taskApiService,\n  employeeApiService,\n  reliabilityApiService,\n} from './factory';\n\n// Re-export secure auth provider for convenience\nexport { setSecureAuthTokenProvider } from '../index';\n\nexport type { ApiServiceFactoryConfig } from './factory';\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,+CAA+C;;AAC/C;AAcA,iDAAiD;AACjD", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/admin/auditService.ts"], "sourcesContent": ["import type { ApiClient } from '@/lib/api/core/apiClient';\r\nimport type { AuditLog, Filters, PaginatedResponse } from '@/types';\r\n\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '@/lib/api/core/baseApiService';\r\n\r\n// Define AuditLogTransformer for consistent data transformation\r\nconst AuditLogTransformer: DataTransformer<AuditLog> = {\r\n  fromApi: (data: any): AuditLog => ({\r\n    action: data.action || '',\r\n    details: data.details || '',\r\n    id: data.id || '',\r\n    timestamp: new Date(data.created_at || data.timestamp || new Date()),\r\n    userId: data.user_id || data.userId || data.auth_user_id || '',\r\n    auth_user_id: data.auth_user_id || '',\r\n    auth_user: data.auth_user || null,\r\n    // Add other audit log properties as needed\r\n  }),\r\n  toApi: (data: any) => data,\r\n};\r\n\r\n/**\r\n * Enhanced Audit Service using BaseApiService infrastructure\r\n * Provides audit log management operations with production-grade reliability\r\n */\r\nexport class AuditService extends BaseApiService<\r\n  AuditLog,\r\n  Partial<AuditLog>,\r\n  Partial<AuditLog>\r\n> {\r\n  protected endpoint = '/admin/audit-logs';\r\n  protected transformer: DataTransformer<AuditLog> = AuditLogTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 2 * 60 * 1000, // 2 minutes for audit logs\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get audit logs by action type\r\n   */\r\n  async getByAction(action: string, filters?: Filters): Promise<AuditLog[]> {\r\n    const result = await this.getAll({ ...filters, action });\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get audit logs by date range with filtering\r\n   */\r\n  async getByDateRange(\r\n    startDate: Date,\r\n    endDate: Date,\r\n    filters?: Filters\r\n  ): Promise<PaginatedResponse<AuditLog>> {\r\n    const dateFilters = {\r\n      endDate: endDate.toISOString(),\r\n      startDate: startDate.toISOString(),\r\n      ...filters,\r\n    };\r\n\r\n    return this.getAll(dateFilters);\r\n  }\r\n\r\n  /**\r\n   * Get audit logs by user ID\r\n   */\r\n  async getByUserId(userId: string, filters?: Filters): Promise<AuditLog[]> {\r\n    const result = await this.getAll({ ...filters, userId });\r\n    return result.data;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA;;AAMA,gEAAgE;AAChE,MAAM,sBAAiD;IACrD,SAAS,CAAC,OAAwB,CAAC;YACjC,QAAQ,KAAK,MAAM,IAAI;YACvB,SAAS,KAAK,OAAO,IAAI;YACzB,IAAI,KAAK,EAAE,IAAI;YACf,WAAW,IAAI,KAAK,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,IAAI;YAC7D,QAAQ,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,YAAY,IAAI;YAC5D,cAAc,KAAK,YAAY,IAAI;YACnC,WAAW,KAAK,SAAS,IAAI;QAE/B,CAAC;IACD,OAAO,CAAC,OAAc;AACxB;AAMO,MAAM,qBAAqB,2IAAA,CAAA,iBAAc;IAKpC,WAAW,oBAAoB;IAC/B,cAAyC,oBAAoB;IAEvE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,MAAc,EAAE,OAAiB,EAAuB;QACxE,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE;QAAO;QACtD,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eACJ,SAAe,EACf,OAAa,EACb,OAAiB,EACqB;QACtC,MAAM,cAAc;YAClB,SAAS,QAAQ,WAAW;YAC5B,WAAW,UAAU,WAAW;YAChC,GAAG,OAAO;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,MAAc,EAAE,OAAiB,EAAuB;QACxE,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE;QAAO;QACtD,OAAO,OAAO,IAAI;IACpB;AACF", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/admin/UserService.ts"], "sourcesContent": ["import type { PaginatedResponse, User, UserFilters } from '@/types';\r\n\r\nimport { ApiClient } from '@/lib/api/core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '@/lib/api/core/baseApiService';\r\n\r\n// Define UserTransformer for consistent data transformation\r\nconst UserTransformer: DataTransformer<User> = {\r\n  fromApi: (data: any): User => {\r\n    // Extract email from the nested users array structure returned by backend\r\n    const email = data.users?.[0]?.email || data.email || '';\r\n    const email_confirmed_at =\r\n      data.users?.[0]?.email_confirmed_at || data.email_confirmed_at || null;\r\n\r\n    return {\r\n      created_at: data.created_at || '',\r\n      email,\r\n      email_confirmed_at,\r\n      employee_id: data.employee_id || null,\r\n      full_name: data.full_name || data.name || '',\r\n      id: data.id,\r\n      isActive: data.is_active ?? true,\r\n      last_sign_in_at: data.last_sign_in_at || null,\r\n      phone: data.phone || null,\r\n      phone_confirmed_at: data.phone_confirmed_at || null,\r\n      role: data.role || 'USER',\r\n      updated_at: data.updated_at || '',\r\n      users: data.users,\r\n    };\r\n  },\r\n  toApi: (data: any) => data,\r\n};\r\n\r\n/**\r\n * Enhanced User Service using BaseApiService infrastructure\r\n * Provides user management operations with production-grade reliability\r\n */\r\nexport class UserService extends BaseApiService<\r\n  User,\r\n  Partial<User>,\r\n  Partial<User>\r\n> {\r\n  protected endpoint = '/admin/users';\r\n  protected transformer: DataTransformer<User> = UserTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for user data\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get users by role with filtering\r\n   */\r\n  async getUsersByRole(\r\n    role: string,\r\n    filters: UserFilters = {}\r\n  ): Promise<User[]> {\r\n    const result = await this.getAll({ ...filters, role });\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Toggle user activation status\r\n   */\r\n  async toggleActivation(id: string, isActive: boolean): Promise<User> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.patch<User>(\r\n        `${this.endpoint}/${id}/toggle-activation`,\r\n        { isActive }\r\n      );\r\n\r\n      // Invalidate related caches\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return this.transformer.fromApi\r\n        ? this.transformer.fromApi(response)\r\n        : response;\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA;;AAMA,4DAA4D;AAC5D,MAAM,kBAAyC;IAC7C,SAAS,CAAC;QACR,0EAA0E;QAC1E,MAAM,QAAQ,KAAK,KAAK,EAAE,CAAC,EAAE,EAAE,SAAS,KAAK,KAAK,IAAI;QACtD,MAAM,qBACJ,KAAK,KAAK,EAAE,CAAC,EAAE,EAAE,sBAAsB,KAAK,kBAAkB,IAAI;QAEpE,OAAO;YACL,YAAY,KAAK,UAAU,IAAI;YAC/B;YACA;YACA,aAAa,KAAK,WAAW,IAAI;YACjC,WAAW,KAAK,SAAS,IAAI,KAAK,IAAI,IAAI;YAC1C,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,SAAS,IAAI;YAC5B,iBAAiB,KAAK,eAAe,IAAI;YACzC,OAAO,KAAK,KAAK,IAAI;YACrB,oBAAoB,KAAK,kBAAkB,IAAI;YAC/C,MAAM,KAAK,IAAI,IAAI;YACnB,YAAY,KAAK,UAAU,IAAI;YAC/B,OAAO,KAAK,KAAK;QACnB;IACF;IACA,OAAO,CAAC,OAAc;AACxB;AAMO,MAAM,oBAAoB,2IAAA,CAAA,iBAAc;IAKnC,WAAW,eAAe;IAC1B,cAAqC,gBAAgB;IAE/D,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA;;GAEC,GACD,MAAM,eACJ,IAAY,EACZ,UAAuB,CAAC,CAAC,EACR;QACjB,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE,GAAG,OAAO;YAAE;QAAK;QACpD,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAE,QAAiB,EAAiB;QACnE,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,kBAAkB,CAAC,EAC1C;gBAAE;YAAS;YAGb,4BAA4B;YAC5B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/admin/adminService.ts"], "sourcesContent": ["/**\r\n * Enhanced Admin Service - Production-Ready Service Foundation\r\n *\r\n * This service provides a unified interface for all admin operations,\r\n * consolidating functionality from multiple admin services while maintaining\r\n * Single Responsibility Principle through delegation to specialized services.\r\n *\r\n * Features:\r\n * - BaseApiService integration for consistent patterns\r\n * - Circuit breaker protection\r\n * - Request caching and deduplication\r\n * - Comprehensive error handling with retry logic\r\n * - Performance monitoring and metrics\r\n * - Audit logging integration\r\n *\r\n * Built upon the BaseApiService foundation for production-grade reliability.\r\n */\r\n\r\nimport type { PaginatedApiResponse } from '@/hooks/api/useApiQuery'; // Import PaginatedApiResponse\r\nimport type { ApiClient } from '@/lib/api/core/apiClient';\r\nimport type { ErrorLogEntry, LogLevel } from '@/lib/types/domain'; // Direct import for problematic types\r\nimport type {\r\n  AuditLog,\r\n  AuditLogFilters,\r\n  HealthResponse,\r\n  PaginatedResponse, // Keep PaginatedResponse for other uses if needed\r\n  PerformanceMetrics,\r\n  User,\r\n  UserFilters,\r\n} from '@/types'; // Keep other types from '@/types'\r\n\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '@/lib/api/core/baseApiService';\r\nimport { apiServiceFactory } from '@/lib/api/services/apiServiceFactory';\r\n\r\nimport { AuditService } from './auditService';\r\nimport { UserService } from './UserService';\r\n\r\n/**\r\n * Admin data transformer for consistent data transformation\r\n */\r\nconst AdminTransformer: DataTransformer<any> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\n/**\r\n * Enhanced Admin Service Class\r\n * Extends BaseApiService for consistent patterns and production-grade reliability\r\n */\r\nexport class AdminService extends BaseApiService<any> {\r\n  /**\r\n   * Enhanced cache management utilities using BaseApiService infrastructure\r\n   */\r\n  get cacheUtils() {\r\n    return {\r\n      /**\r\n       * Clear all cache\r\n       */\r\n      clearAll: () => this.clearCache(),\r\n\r\n      /**\r\n       * Force refresh health status (bypasses cache)\r\n       */\r\n      forceRefreshHealth: async (): Promise<HealthResponse> => {\r\n        this.cache.invalidate('admin:health');\r\n        return this.getSystemHealthStatus();\r\n      },\r\n\r\n      /**\r\n       * Force refresh performance metrics (bypasses cache)\r\n       */\r\n      forceRefreshPerformance: async (): Promise<PerformanceMetrics> => {\r\n        this.cache.invalidate('admin:performance');\r\n        return this.getPerformanceMetrics();\r\n      },\r\n\r\n      /**\r\n       * Get cache statistics\r\n       */\r\n      getStats: () => this.cache.getStats(),\r\n\r\n      /**\r\n       * Invalidate all admin cache entries\r\n       */\r\n      invalidateAll: () => this.cache.invalidatePattern(/^admin:/),\r\n    };\r\n  }\r\n  protected endpoint = '/admin';\r\n\r\n  protected transformer: DataTransformer<any> = AdminTransformer;\r\n  // Specialized service instances\r\n  private readonly auditService: AuditService;\r\n\r\n  private readonly userService: UserService;\r\n\r\n  constructor(apiClient?: ApiClient, config?: ServiceConfig) {\r\n    const client = apiClient || apiServiceFactory.getApiClient();\r\n    super(client, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for admin operations\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n\r\n    // Initialize specialized services with the same API client\r\n    this.auditService = new AuditService(client);\r\n    this.userService = new UserService(client);\r\n  }\r\n\r\n  // ===== USER MANAGEMENT =====\r\n\r\n  /**\r\n   * Create audit log entry using BaseApiService infrastructure\r\n   */\r\n  async createAuditLog(logData: {\r\n    action: string;\r\n    details: string;\r\n    ipAddress?: string;\r\n    userAgent?: string;\r\n    userId: string;\r\n  }): Promise<AuditLog> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AuditLog>(\r\n        '/admin/audit-logs',\r\n        logData\r\n      );\r\n\r\n      // Invalidate audit logs cache\r\n      this.cache.invalidatePattern(/^admin:audit:/);\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Create a new user using BaseApiService infrastructure\r\n   */\r\n  async createUser(userData: {\r\n    email: string;\r\n    emailVerified?: boolean;\r\n    isActive?: boolean;\r\n    role: string;\r\n  }): Promise<User> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<User>(\r\n        '/admin/users',\r\n        userData\r\n      );\r\n\r\n      // Invalidate users cache after creation\r\n      this.cache.invalidatePattern(/^admin:users:/);\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  // ===== USER MANAGEMENT OPERATIONS =====\r\n\r\n  /**\r\n   * Delete a user using BaseApiService infrastructure\r\n   */\r\n  async deleteUser(userId: string): Promise<void> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      await this.apiClient.delete(`/admin/users/${userId}`);\r\n\r\n      // Invalidate relevant caches\r\n      this.cache.invalidatePattern(/^admin:users:/);\r\n      this.cache.invalidate(`admin:user:${userId}`);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get all users with pagination and filtering (delegates to UserService)\r\n   */\r\n  async getAllUsers(\r\n    filters: UserFilters = {}\r\n  ): Promise<PaginatedResponse<User>> {\r\n    return this.userService.getAll(filters);\r\n  }\r\n\r\n  /**\r\n   * Get audit logs with pagination and filtering (delegates to AuditService)\r\n   */\r\n  async getAuditLogs(\r\n    filters: AuditLogFilters = {}\r\n  ): Promise<PaginatedResponse<AuditLog>> {\r\n    // Use the AuditService's getAll method directly with proper filters\r\n    return this.auditService.getAll(filters);\r\n  }\r\n\r\n  /**\r\n   * Get mock health status data\r\n   */\r\n  getMockHealthStatus(): HealthResponse {\r\n    return {\r\n      services: {\r\n        api: { responseTime: 23, status: 'healthy' },\r\n        cache: { responseTime: 12, status: 'healthy' },\r\n        database: { responseTime: 45, status: 'healthy' },\r\n      },\r\n      status: 'healthy',\r\n      timestamp: new Date().toISOString(),\r\n      uptime: 3600,\r\n    };\r\n  }\r\n\r\n  // ===== AUDIT LOG MANAGEMENT =====\r\n\r\n  /**\r\n   * Get mock performance metrics\r\n   */\r\n  getMockPerformanceMetrics(): PerformanceMetrics {\r\n    return {\r\n      cpu: {\r\n        cores: 4,\r\n        usage: Math.random() * 100,\r\n      },\r\n\r\n      errors: {\r\n        rate: Math.random() * 5,\r\n        total: Math.floor(Math.random() * 500),\r\n      },\r\n      memory: {\r\n        percentage: Math.random() * 100,\r\n        total: 8000,\r\n        used: Math.random() * 8000,\r\n      },\r\n      requests: {\r\n        averageResponseTime: Math.random() * 1000,\r\n        perSecond: Math.random() * 100,\r\n        total: Math.floor(Math.random() * 10_000),\r\n      },\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n  }\r\n\r\n  // ===== HEALTH MONITORING =====\r\n\r\n  /**\r\n   * Get mock recent errors\r\n   */\r\n  getMockRecentErrors(): PaginatedResponse<ErrorLogEntry> {\r\n    const mockErrors: ErrorLogEntry[] = [\r\n      {\r\n        details: { component: 'database', errorType: 'timeout' }, // Example direct details\r\n        id: '1',\r\n        level: 'ERROR',\r\n        message: 'Database connection timeout',\r\n        requestId: 'req-456',\r\n        source: 'database.service.ts', // Now a direct property\r\n        stack: 'Error: Connection timeout\\n    at Database.connect...',\r\n        timestamp: new Date().toISOString(),\r\n        userId: 'user123',\r\n      },\r\n      {\r\n        details: { component: 'system', metric: 'memory' }, // Example direct details\r\n        id: '2',\r\n        level: 'WARNING', // Changed from 'WARN' to 'WARNING'\r\n        message: 'High memory usage detected',\r\n        requestId: 'req-789',\r\n        source: 'monitoring.service.ts', // Now a direct property\r\n        timestamp: new Date(Date.now() - 300_000).toISOString(),\r\n      },\r\n    ];\r\n\r\n    return {\r\n      data: mockErrors,\r\n      pagination: {\r\n        hasNext: false,\r\n        hasPrevious: false,\r\n        limit: 10,\r\n        page: 1,\r\n        total: mockErrors.length,\r\n        totalPages: Math.ceil(mockErrors.length / 10),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get performance metrics using BaseApiService infrastructure\r\n   */\r\n  async getPerformanceMetrics(): Promise<PerformanceMetrics> {\r\n    const cacheKey = 'admin:performance';\r\n\r\n    return this.executeWithInfrastructure(cacheKey, async () => {\r\n      return this.apiClient.get<PerformanceMetrics>('/admin/performance');\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get recent error logs using BaseApiService infrastructure\r\n   */\r\n  async getRecentErrors(\r\n    page = 1,\r\n    limit = 10,\r\n    level?: string\r\n  ): Promise<PaginatedApiResponse<ErrorLogEntry>> {\r\n    // Changed return type\r\n    const params = new URLSearchParams();\r\n    params.append('page', page.toString());\r\n    params.append('limit', limit.toString());\r\n    if (level) params.append('level', level);\r\n\r\n    const url = `/admin/logs/errors?${params.toString()}`;\r\n    const cacheKey = `admin:errors:${page}:${limit}:${level || 'all'}`;\r\n\r\n    return this.executeWithInfrastructure(cacheKey, async () => {\r\n      return this.apiClient.get<PaginatedApiResponse<ErrorLogEntry>>(url); // Changed return type\r\n    });\r\n  }\r\n\r\n  // ===== MOCK DATA METHODS (for development) =====\r\n\r\n  /**\r\n   * Get system health status using BaseApiService infrastructure\r\n   */\r\n  async getSystemHealthStatus(): Promise<HealthResponse> {\r\n    const cacheKey = 'admin:health';\r\n\r\n    return this.executeWithInfrastructure(cacheKey, async () => {\r\n      return this.apiClient.get<HealthResponse>('/admin/health');\r\n    });\r\n  }\r\n\r\n  // ===== MOCK DATA METHODS (for development) =====\r\n\r\n  /**\r\n   * Toggle user activation status using BaseApiService infrastructure\r\n   */\r\n  async toggleUserActivation(userId: string, isActive: boolean): Promise<User> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.patch<User>(\r\n        `/admin/users/${userId}/toggle-activation`,\r\n        { isActive }\r\n      );\r\n\r\n      // Invalidate relevant caches\r\n      this.cache.invalidatePattern(/^admin:users:/);\r\n      this.cache.invalidate(`admin:user:${userId}`);\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update user details using BaseApiService infrastructure\r\n   */\r\n  async updateUser(\r\n    userId: string,\r\n    userData: Partial<{\r\n      email: string;\r\n      emailVerified: boolean;\r\n      isActive: boolean;\r\n      role: string;\r\n    }>\r\n  ): Promise<User> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.put<User>(\r\n        `/admin/users/${userId}`,\r\n        userData\r\n      );\r\n\r\n      // Invalidate relevant caches\r\n      this.cache.invalidatePattern(/^admin:users:/);\r\n      this.cache.invalidate(`admin:user:${userId}`);\r\n\r\n      return response;\r\n    });\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const adminService = new AdminService();\r\n\r\n// Export cache utilities for backward compatibility\r\nexport const adminCache = adminService.cacheUtils;\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;CAgBC;;;;;AAeD;AAKA;AAAA;AAEA;AACA;;;;;AAEA;;CAEC,GACD,MAAM,mBAAyC;IAC7C,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAMO,MAAM,qBAAqB,2IAAA,CAAA,iBAAc;IAC9C;;GAEC,GACD,IAAI,aAAa;QACf,OAAO;YACL;;OAEC,GACD,UAAU,IAAM,IAAI,CAAC,UAAU;YAE/B;;OAEC,GACD,oBAAoB;gBAClB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACtB,OAAO,IAAI,CAAC,qBAAqB;YACnC;YAEA;;OAEC,GACD,yBAAyB;gBACvB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACtB,OAAO,IAAI,CAAC,qBAAqB;YACnC;YAEA;;OAEC,GACD,UAAU,IAAM,IAAI,CAAC,KAAK,CAAC,QAAQ;YAEnC;;OAEC,GACD,eAAe,IAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;QACpD;IACF;IACU,WAAW,SAAS;IAEpB,cAAoC,iBAAiB;IAC/D,gCAAgC;IACf,aAA2B;IAE3B,YAAyB;IAE1C,YAAY,SAAqB,EAAE,MAAsB,CAAE;QACzD,MAAM,SAAS,aAAa,wIAAA,CAAA,oBAAiB,CAAC,YAAY;QAC1D,KAAK,CAAC,QAAQ;YACZ,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;QAEA,2DAA2D;QAC3D,IAAI,CAAC,YAAY,GAAG,IAAI,sJAAA,CAAA,eAAY,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG,IAAI,qJAAA,CAAA,cAAW,CAAC;IACrC;IAEA,8BAA8B;IAE9B;;GAEC,GACD,MAAM,eAAe,OAMpB,EAAqB;QACpB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,qBACA;YAGF,8BAA8B;YAC9B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;YAE7B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,QAKhB,EAAiB;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,gBACA;YAGF,wCAAwC;YACxC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;YAE7B,OAAO;QACT;IACF;IAEA,yCAAyC;IAEzC;;GAEC,GACD,MAAM,WAAW,MAAc,EAAiB;QAC9C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,QAAQ;YAEpD,6BAA6B;YAC7B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,QAAQ;QAC9C;IACF;IAEA;;GAEC,GACD,MAAM,YACJ,UAAuB,CAAC,CAAC,EACS;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACjC;IAEA;;GAEC,GACD,MAAM,aACJ,UAA2B,CAAC,CAAC,EACS;QACtC,oEAAoE;QACpE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IAClC;IAEA;;GAEC,GACD,sBAAsC;QACpC,OAAO;YACL,UAAU;gBACR,KAAK;oBAAE,cAAc;oBAAI,QAAQ;gBAAU;gBAC3C,OAAO;oBAAE,cAAc;oBAAI,QAAQ;gBAAU;gBAC7C,UAAU;oBAAE,cAAc;oBAAI,QAAQ;gBAAU;YAClD;YACA,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;QACV;IACF;IAEA,mCAAmC;IAEnC;;GAEC,GACD,4BAAgD;QAC9C,OAAO;YACL,KAAK;gBACH,OAAO;gBACP,OAAO,KAAK,MAAM,KAAK;YACzB;YAEA,QAAQ;gBACN,MAAM,KAAK,MAAM,KAAK;gBACtB,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YACpC;YACA,QAAQ;gBACN,YAAY,KAAK,MAAM,KAAK;gBAC5B,OAAO;gBACP,MAAM,KAAK,MAAM,KAAK;YACxB;YACA,UAAU;gBACR,qBAAqB,KAAK,MAAM,KAAK;gBACrC,WAAW,KAAK,MAAM,KAAK;gBAC3B,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YACpC;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,gCAAgC;IAEhC;;GAEC,GACD,sBAAwD;QACtD,MAAM,aAA8B;YAClC;gBACE,SAAS;oBAAE,WAAW;oBAAY,WAAW;gBAAU;gBACvD,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,OAAO;gBACP,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YACA;gBACE,SAAS;oBAAE,WAAW;oBAAU,QAAQ;gBAAS;gBACjD,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;YACvD;SACD;QAED,OAAO;YACL,MAAM;YACN,YAAY;gBACV,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,OAAO,WAAW,MAAM;gBACxB,YAAY,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;YAC5C;QACF;IACF;IAEA;;GAEC,GACD,MAAM,wBAAqD;QACzD,MAAM,WAAW;QAEjB,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAqB;QAChD;IACF;IAEA;;GAEC,GACD,MAAM,gBACJ,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,KAAc,EACgC;QAC9C,sBAAsB;QACtB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;QACnC,OAAO,MAAM,CAAC,SAAS,MAAM,QAAQ;QACrC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;QAElC,MAAM,MAAM,CAAC,mBAAmB,EAAE,OAAO,QAAQ,IAAI;QACrD,MAAM,WAAW,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,OAAO;QAElE,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAsC,MAAM,sBAAsB;QAC7F;IACF;IAEA,kDAAkD;IAElD;;GAEC,GACD,MAAM,wBAAiD;QACrD,MAAM,WAAW;QAEjB,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAiB;QAC5C;IACF;IAEA,kDAAkD;IAElD;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAAE,QAAiB,EAAiB;QAC3E,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,CAAC,aAAa,EAAE,OAAO,kBAAkB,CAAC,EAC1C;gBAAE;YAAS;YAGb,6BAA6B;YAC7B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,QAAQ;YAE5C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,WACJ,MAAc,EACd,QAKE,EACa;QACf,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,aAAa,EAAE,QAAQ,EACxB;YAGF,6BAA6B;YAC7B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,QAAQ;YAE5C,OAAO;QACT;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa,aAAa,UAAU", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/admin/index.ts"], "sourcesContent": ["/**\r\n * Admin Services Index\r\n *\r\n * Centralized exports for all admin-related services, providing a clean\r\n * interface for importing admin functionality throughout the application.\r\n *\r\n * This consolidates:\r\n * - Unified AdminService (main interface)\r\n * - Specialized services (UserService, AuditService)\r\n * - Cache utilities\r\n * - Type definitions\r\n */\r\n\r\n// Export the main AdminService class and singleton instance\r\nexport { adminCache, AdminService, adminService } from './adminService';\r\n\r\n// Export legacy function names for backward compatibility\r\nexport {\r\n  adminService as createAuditLog,\r\n  adminService as createUser,\r\n  adminService as default,\r\n  adminService as deleteUser,\r\n  adminService as getAllUsers,\r\n  adminService as getAuditLogs,\r\n  adminService as getHealthStatus,\r\n  adminService as getPerformanceMetrics,\r\n  adminService as getRecentErrors,\r\n  adminService as toggleUserActivation,\r\n  adminService as updateUser,\r\n} from './adminService';\r\n// Export specialized services for direct access if needed\r\nexport { AuditService } from './auditService';\r\n\r\nexport { UserService } from './UserService';\r\n\r\n// Re-export types for convenience\r\nexport type {\r\n  AuditLog,\r\n  AuditLogFilters,\r\n  ErrorLogEntry,\r\n  HealthResponse,\r\n  PaginatedResponse,\r\n  PerformanceMetrics,\r\n  User,\r\n  UserFilters,\r\n} from '@/types';\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC,GAED,4DAA4D;;AAC5D;AAgBA,0DAA0D;AAC1D;AAEA", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/admin/auditLogViewer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { format } from 'date-fns';\r\nimport { Loader2, Search } from 'lucide-react';\r\nimport { Calendar as CalendarIcon } from 'lucide-react';\r\nimport React, { useCallback, useEffect, useState } from 'react';\r\n\r\nimport type { AuditLog, AuditLogFilters } from '@/types';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar as ShadcnCalendar } from '@/components/ui/calendar';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from '@/components/ui/pagination';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\nimport { adminService } from '@/lib/api/services/admin';\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst ITEMS_PER_PAGE = 10; // This should ideally match the backend's default limit\r\n\r\nexport function AuditLogViewer() {\r\n  const [logs, setLogs] = useState<AuditLog[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filterAction, setFilterAction] = useState('');\r\n  const [filterUserId, setFilterUserId] = useState('');\r\n  const [startDate, setStartDate] = useState<Date | undefined>();\r\n  const [endDate, setEndDate] = useState<Date | undefined>();\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalLogs, setTotalLogs] = useState(0);\r\n  const { toast } = useToast();\r\n\r\n  const totalPages = Math.ceil(totalLogs / ITEMS_PER_PAGE); // Calculate totalPages dynamically\r\n\r\n  const fetchAuditLogs = useCallback(async () => {\r\n    setLoading(true);\r\n    try {\r\n      const filters: AuditLogFilters = {\r\n        action: filterAction,\r\n        endDate: endDate,\r\n        limit: ITEMS_PER_PAGE,\r\n        page: currentPage,\r\n        search: searchTerm,\r\n        startDate: startDate,\r\n        userId: filterUserId,\r\n      };\r\n      const response = await adminService.getAuditLogs(filters);\r\n      setLogs(response.data);\r\n      setTotalLogs(response.pagination.total); // Set totalLogs from response\r\n    } catch (error: any) {\r\n      toast({\r\n        description: error.message ?? 'Failed to load audit log data.',\r\n        title: 'Error fetching audit logs',\r\n        variant: 'destructive',\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [\r\n    currentPage,\r\n    searchTerm,\r\n    filterAction,\r\n    filterUserId,\r\n    startDate,\r\n    endDate,\r\n    toast,\r\n    totalLogs, // Add totalLogs to dependencies\r\n  ]);\r\n\r\n  useEffect(() => {\r\n    fetchAuditLogs();\r\n  }, [fetchAuditLogs]);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <h2 className=\"text-2xl font-bold\">Audit Log Viewer</h2>\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"relative\">\r\n            <Search className=\"absolute left-2 top-1/2 size-4 -translate-y-1/2 text-muted-foreground\" />\r\n            <Input\r\n              className=\"w-[250px] pl-8\"\r\n              onChange={e => {\r\n                setSearchTerm(e.target.value);\r\n                setCurrentPage(1); // Reset to first page on search\r\n              }}\r\n              placeholder=\"Search logs (user, action, details)...\"\r\n              value={searchTerm}\r\n            />\r\n          </div>\r\n          <Input\r\n            className=\"w-[200px]\"\r\n            onChange={e => {\r\n              setFilterAction(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on filter change\r\n            }}\r\n            placeholder=\"Filter by action (e.g., LOGIN)\"\r\n            value={filterAction}\r\n          />\r\n          <Input\r\n            className=\"w-[200px]\"\r\n            onChange={e => {\r\n              setFilterUserId(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on filter change\r\n            }}\r\n            placeholder=\"Filter by User ID\"\r\n            value={filterUserId}\r\n          />\r\n          <Popover>\r\n            <PopoverTrigger asChild>\r\n              <Button\r\n                className={cn(\r\n                  'w-[280px] justify-start text-left font-normal',\r\n                  !startDate && !endDate && 'text-muted-foreground'\r\n                )}\r\n                variant={'outline'}\r\n              >\r\n                <CalendarIcon className=\"mr-2 size-4\" />\r\n                {startDate ? (\r\n                  endDate ? (\r\n                    <>\r\n                      {format(startDate, 'LLL dd, y')} -{' '}\r\n                      {format(endDate, 'LLL dd, y')}\r\n                    </>\r\n                  ) : (\r\n                    format(startDate, 'LLL dd, y')\r\n                  )\r\n                ) : (\r\n                  <span>Pick a date range</span>\r\n                )}\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"flex w-auto p-0\">\r\n              <ShadcnCalendar\r\n                mode=\"range\"\r\n                numberOfMonths={2}\r\n                onSelect={range => {\r\n                  setStartDate(range?.from);\r\n                  setEndDate(range?.to);\r\n                  setCurrentPage(1); // Reset to first page on date filter change\r\n                }}\r\n                selected={{ from: startDate, to: endDate }}\r\n              />\r\n            </PopoverContent>\r\n          </Popover>\r\n          {(startDate ??\r\n            endDate ??\r\n            filterAction ??\r\n            filterUserId ??\r\n            searchTerm) && (\r\n            <Button\r\n              onClick={() => {\r\n                setSearchTerm('');\r\n                setFilterAction('');\r\n                setFilterUserId('');\r\n                setStartDate(undefined);\r\n                setEndDate(undefined);\r\n                setCurrentPage(1);\r\n              }}\r\n              variant=\"outline\"\r\n            >\r\n              Clear Filters\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"flex h-64 items-center justify-center\">\r\n          <Loader2 className=\"size-8 animate-spin text-primary\" />\r\n          <span className=\"ml-2 text-lg\">Loading audit logs...</span>\r\n        </div>\r\n      ) : (\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader>\r\n              <TableRow>\r\n                <TableHead className=\"w-[100px]\">ID</TableHead>\r\n                <TableHead>Timestamp</TableHead>\r\n                <TableHead>User Email</TableHead>\r\n                <TableHead>Action</TableHead>\r\n                <TableHead>Details</TableHead>\r\n                <TableHead>IP Address</TableHead>\r\n              </TableRow>\r\n            </TableHeader>\r\n            <TableBody>\r\n              {logs.length === 0 ? (\r\n                <TableRow>\r\n                  <TableCell className=\"h-24 text-center\" colSpan={6}>\r\n                    No audit logs found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              ) : (\r\n                logs.map(log => (\r\n                  <TableRow key={log.id}>\r\n                    <TableCell className=\"font-medium\">{log.id}</TableCell>\r\n                    <TableCell>\r\n                      {new Date(log.timestamp).toLocaleString()}\r\n                    </TableCell>\r\n                    <TableCell>{log.userId || 'N/A'}</TableCell>\r\n                    <TableCell>{log.action}</TableCell>\r\n                    <TableCell className=\"max-w-[300px] truncate\">\r\n                      {log.details}\r\n                    </TableCell>\r\n                    <TableCell>{'N/A'}</TableCell> {/* ip_address removed */}\r\n                  </TableRow>\r\n                ))\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      )}\r\n\r\n      {totalLogs > 0 && (\r\n        <Pagination>\r\n          <PaginationContent>\r\n            <PaginationItem>\r\n              <PaginationPrevious\r\n                className={\r\n                  currentPage === 1 ? 'pointer-events-none opacity-50' : ''\r\n                }\r\n                onClick={e => {\r\n                  e.preventDefault();\r\n                  handlePageChange(currentPage - 1);\r\n                }}\r\n              />\r\n            </PaginationItem>\r\n            {Array.from({ length: totalPages }, (_, i) => (\r\n              <PaginationItem key={i}>\r\n                <PaginationLink\r\n                  isActive={currentPage === i + 1}\r\n                  onClick={e => {\r\n                    e.preventDefault();\r\n                    handlePageChange(i + 1);\r\n                  }}\r\n                >\r\n                  {i + 1}\r\n                </PaginationLink>\r\n              </PaginationItem>\r\n            ))}\r\n            <PaginationItem>\r\n              <PaginationNext\r\n                className={\r\n                  currentPage === totalPages\r\n                    ? 'pointer-events-none opacity-50'\r\n                    : ''\r\n                }\r\n                onClick={e => {\r\n                  e.preventDefault();\r\n                  handlePageChange(currentPage + 1);\r\n                }}\r\n              />\r\n            </PaginationItem>\r\n          </PaginationContent>\r\n        </Pagination>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAIA;AACA;AACA;AACA;AAQA;AAKA;AAQA;AACA;AAAA;AACA;AAAA;AAnCA;;;;;;;;;;;;;;;AAqCA,MAAM,iBAAiB,IAAI,wDAAwD;AAE5E,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,aAAa,KAAK,IAAI,CAAC,YAAY,iBAAiB,mCAAmC;IAE7F,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,WAAW;QACX,IAAI;YACF,MAAM,UAA2B;gBAC/B,QAAQ;gBACR,SAAS;gBACT,OAAO;gBACP,MAAM;gBACN,QAAQ;gBACR,WAAW;gBACX,QAAQ;YACV;YACA,MAAM,WAAW,MAAM,sJAAA,CAAA,eAAY,CAAC,YAAY,CAAC;YACjD,QAAQ,SAAS,IAAI;YACrB,aAAa,SAAS,UAAU,CAAC,KAAK,GAAG,8BAA8B;QACzE,EAAE,OAAO,OAAY;YACnB,MAAM;gBACJ,aAAa,MAAM,OAAO,IAAI;gBAC9B,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,WAAU;wCACV,UAAU,CAAA;4CACR,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC5B,eAAe,IAAI,gCAAgC;wCACrD;wCACA,aAAY;wCACZ,OAAO;;;;;;;;;;;;0CAGX,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAU;gCACV,UAAU,CAAA;oCACR,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC9B,eAAe,IAAI,uCAAuC;gCAC5D;gCACA,aAAY;gCACZ,OAAO;;;;;;0CAET,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAU;gCACV,UAAU,CAAA;oCACR,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC9B,eAAe,IAAI,uCAAuC;gCAC5D;gCACA,aAAY;gCACZ,OAAO;;;;;;0CAET,8OAAC,mIAAA,CAAA,UAAO;;kDACN,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iDACA,CAAC,aAAa,CAAC,WAAW;4CAE5B,SAAS;;8DAET,8OAAC,0MAAA,CAAA,WAAY;oDAAC,WAAU;;;;;;gDACvB,YACC,wBACE;;wDACG,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;wDAAa;wDAAG;wDAClC,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;;mEAGnB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,6BAGpB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIZ,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,WAAU;kDACxB,cAAA,8OAAC,oIAAA,CAAA,WAAc;4CACb,MAAK;4CACL,gBAAgB;4CAChB,UAAU,CAAA;gDACR,aAAa,OAAO;gDACpB,WAAW,OAAO;gDAClB,eAAe,IAAI,4CAA4C;4CACjE;4CACA,UAAU;gDAAE,MAAM;gDAAW,IAAI;4CAAQ;;;;;;;;;;;;;;;;;4BAI9C,CAAC,aACA,WACA,gBACA,gBACA,UAAU,mBACV,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;oCACP,cAAc;oCACd,gBAAgB;oCAChB,gBAAgB;oCAChB,aAAa;oCACb,WAAW;oCACX,eAAe;gCACjB;gCACA,SAAQ;0CACT;;;;;;;;;;;;;;;;;;YAON,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;qCAGjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;kDACjC,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;;;;;;;sCAGf,8OAAC,iIAAA,CAAA,YAAS;sCACP,KAAK,MAAM,KAAK,kBACf,8OAAC,iIAAA,CAAA,WAAQ;0CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oCAAC,WAAU;oCAAmB,SAAS;8CAAG;;;;;;;;;;uCAKtD,KAAK,GAAG,CAAC,CAAA,oBACP,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAe,IAAI,EAAE;;;;;;sDAC1C,8OAAC,iIAAA,CAAA,YAAS;sDACP,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;sDAEzC,8OAAC,iIAAA,CAAA,YAAS;sDAAE,IAAI,MAAM,IAAI;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,YAAS;sDAAE,IAAI,MAAM;;;;;;sDACtB,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,IAAI,OAAO;;;;;;sDAEd,8OAAC,iIAAA,CAAA,YAAS;sDAAE;;;;;;wCAAkB;;mCAVjB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;YAmBhC,YAAY,mBACX,8OAAC,sIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;;sCAChB,8OAAC,sIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;gCACjB,WACE,gBAAgB,IAAI,mCAAmC;gCAEzD,SAAS,CAAA;oCACP,EAAE,cAAc;oCAChB,iBAAiB,cAAc;gCACjC;;;;;;;;;;;wBAGH,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAW,GAAG,CAAC,GAAG,kBACtC,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCACb,UAAU,gBAAgB,IAAI;oCAC9B,SAAS,CAAA;wCACP,EAAE,cAAc;wCAChB,iBAAiB,IAAI;oCACvB;8CAEC,IAAI;;;;;;+BARY;;;;;sCAYvB,8OAAC,sIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;gCACb,WACE,gBAAgB,aACZ,mCACA;gCAEN,SAAS,CAAA;oCACP,EAAE,cAAc;oCAChB,iBAAiB,cAAc;gCACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/admin/CacheStatus.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  <PERSON>ert<PERSON>riangle,\r\n  Clock,\r\n  Database,\r\n  RefreshCw,\r\n  Shield,\r\n  Trash2,\r\n} from 'lucide-react';\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { adminCache } from '@/lib/api/services/admin';\r\nimport { CircuitState } from '@/lib/utils/circuitBreaker';\r\nimport { CACHE_DURATIONS, requestCache } from '@/lib/utils/requestCache';\r\n\r\ninterface CacheEntry {\r\n  age: number;\r\n  expiresIn: number;\r\n  key: string;\r\n}\r\n\r\ninterface CacheStats {\r\n  entries: CacheEntry[];\r\n  size: number;\r\n}\r\n\r\ninterface CircuitBreakerStatus {\r\n  failureCount: number;\r\n  lastFailureTime: number;\r\n  state: CircuitState;\r\n  timeUntilRetry?: number;\r\n}\r\n\r\nexport function CacheStatus() {\r\n  const [stats, setStats] = useState<CacheStats>({ entries: [], size: 0 });\r\n  const [circuitBreakers, setCircuitBreakers] = useState<\r\n    Record<string, CircuitBreakerStatus>\r\n  >({});\r\n  const [refreshing, setRefreshing] = useState(false);\r\n\r\n  const refreshStats = () => {\r\n    const newStats = requestCache.getStats();\r\n    setStats(newStats);\r\n\r\n    const cbStatus: Record<string, CircuitBreakerStatus> = {};\r\n    setCircuitBreakers(cbStatus);\r\n  };\r\n\r\n  useEffect(() => {\r\n    refreshStats();\r\n    const interval = setInterval(refreshStats, 1000); // Update every second\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const handleClearCache = () => {\r\n    adminCache.clearAll();\r\n    refreshStats();\r\n  };\r\n\r\n  const handleResetCircuitBreakers = () => {\r\n    // Mock implementation - just clear all cache (no resetCircuitBreakers available)\r\n    adminCache.clearAll();\r\n    refreshStats();\r\n  };\r\n\r\n  const handleRefreshStats = async () => {\r\n    setRefreshing(true);\r\n    refreshStats();\r\n    setTimeout(() => setRefreshing(false), 500);\r\n  };\r\n\r\n  const formatDuration = (ms: number): string => {\r\n    if (ms < 0) return 'Expired';\r\n    if (ms < 1000) return `${Math.round(ms)}ms`;\r\n    if (ms < 60_000) return `${Math.round(ms / 1000)}s`;\r\n    return `${Math.round(ms / 60_000)}m`;\r\n  };\r\n\r\n  const getCacheType = (key: string): string => {\r\n    if (key.includes('health')) return 'Health';\r\n    if (key.includes('performance')) return 'Performance';\r\n    if (key.includes('errors')) return 'Errors';\r\n    return 'Other';\r\n  };\r\n\r\n  const getCacheTypeColor = (key: string): string => {\r\n    if (key.includes('health')) return 'bg-green-100 text-green-800';\r\n    if (key.includes('performance')) return 'bg-blue-100 text-blue-800';\r\n    if (key.includes('errors')) return 'bg-red-100 text-red-800';\r\n    return 'bg-gray-100 text-gray-800';\r\n  };\r\n\r\n  const adminEntries = stats.entries.filter(entry =>\r\n    entry.key.startsWith('admin:')\r\n  );\r\n\r\n  return (\r\n    <Card className=\"shadow-md\">\r\n      <CardHeader className=\"p-5\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <CardTitle className=\"flex items-center gap-2 text-xl font-semibold text-primary\">\r\n              <Database className=\"size-5\" />\r\n              Cache Status\r\n            </CardTitle>\r\n            <p className=\"mt-1 text-sm text-muted-foreground\">\r\n              Request cache performance and timing\r\n            </p>\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <Button\r\n              className=\"flex items-center gap-2\"\r\n              disabled={refreshing}\r\n              onClick={handleRefreshStats}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <RefreshCw\r\n                className={`size-4 ${refreshing ? 'animate-spin' : ''}`}\r\n              />\r\n              Refresh\r\n            </Button>\r\n            <Button\r\n              className=\"flex items-center gap-2 text-red-600 hover:text-red-700\"\r\n              onClick={handleClearCache}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <Trash2 className=\"size-4\" />\r\n              Clear Cache\r\n            </Button>\r\n            <Button\r\n              className=\"flex items-center gap-2 text-orange-600 hover:text-orange-700\"\r\n              onClick={handleResetCircuitBreakers}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <Shield className=\"size-4\" />\r\n              Reset Breakers\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\"p-5\">\r\n        <div className=\"space-y-4\">\r\n          {/* Cache Summary */}\r\n          <div className=\"grid grid-cols-2 gap-4 md:grid-cols-4\">\r\n            <div className=\"rounded-lg bg-gray-50 p-3 text-center\">\r\n              <div className=\"text-2xl font-bold text-primary\">\r\n                {stats.size}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Total Entries</div>\r\n            </div>\r\n            <div className=\"rounded-lg bg-gray-50 p-3 text-center\">\r\n              <div className=\"text-2xl font-bold text-green-600\">\r\n                {adminEntries.length}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Admin Entries</div>\r\n            </div>\r\n            <div className=\"rounded-lg bg-gray-50 p-3 text-center\">\r\n              <div className=\"text-2xl font-bold text-blue-600\">\r\n                {formatDuration(CACHE_DURATIONS.HEALTH_STATUS)}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Health Cache</div>\r\n            </div>\r\n            <div className=\"rounded-lg bg-gray-50 p-3 text-center\">\r\n              <div className=\"text-2xl font-bold text-purple-600\">\r\n                {formatDuration(CACHE_DURATIONS.PERFORMANCE_METRICS)}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">\r\n                Performance Cache\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Cache Entries */}\r\n          {adminEntries.length > 0 ? (\r\n            <div className=\"space-y-2\">\r\n              <h4 className=\"text-sm font-semibold uppercase tracking-wide text-muted-foreground\">\r\n                Active Cache Entries\r\n              </h4>\r\n              {adminEntries.map((entry, index) => (\r\n                <div\r\n                  className=\"flex items-center justify-between rounded-lg bg-gray-50 p-3\"\r\n                  key={index}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <Badge className={getCacheTypeColor(entry.key)}>\r\n                      {getCacheType(entry.key)}\r\n                    </Badge>\r\n                    <span className=\"font-mono text-sm text-gray-600\">\r\n                      {entry.key.replace('admin:', '')}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-4 text-sm\">\r\n                    <div className=\"flex items-center gap-1 text-muted-foreground\">\r\n                      <Clock className=\"size-3\" />\r\n                      Age: {formatDuration(entry.age)}\r\n                    </div>\r\n                    <div\r\n                      className={`font-medium ${\r\n                        entry.expiresIn > 0 ? 'text-green-600' : 'text-red-600'\r\n                      }`}\r\n                    >\r\n                      {entry.expiresIn > 0\r\n                        ? `Expires in ${formatDuration(entry.expiresIn)}`\r\n                        : 'Expired'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"py-8 text-center text-muted-foreground\">\r\n              <Database className=\"mx-auto mb-3 size-12 opacity-50\" />\r\n              <p>No cache entries found</p>\r\n              <p className=\"text-sm\">\r\n                Cache entries will appear here after API calls\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Circuit Breaker Status */}\r\n          <div className=\"mt-6 rounded-lg bg-orange-50 p-4\">\r\n            <h4 className=\"mb-3 flex items-center gap-2 font-semibold text-orange-900\">\r\n              <Shield className=\"size-4\" />\r\n              Circuit Breaker Status\r\n            </h4>\r\n            <div className=\"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4\">\r\n              {Object.entries(circuitBreakers).map(([name, status]) => (\r\n                <div\r\n                  className={`rounded-lg border p-3 ${\r\n                    status.state === CircuitState.CLOSED\r\n                      ? 'border-green-200 bg-green-50'\r\n                      : status.state === CircuitState.HALF_OPEN\r\n                        ? 'border-yellow-200 bg-yellow-50'\r\n                        : 'border-red-200 bg-red-50'\r\n                  }`}\r\n                  key={name}\r\n                >\r\n                  <div className=\"mb-2 flex items-center justify-between\">\r\n                    <span className=\"text-sm font-medium capitalize\">\r\n                      {name}\r\n                    </span>\r\n                    <Badge\r\n                      className={\r\n                        status.state === CircuitState.CLOSED\r\n                          ? 'bg-green-100 text-green-800'\r\n                          : status.state === CircuitState.HALF_OPEN\r\n                            ? 'bg-yellow-100 text-yellow-800'\r\n                            : 'bg-red-100 text-red-800'\r\n                      }\r\n                    >\r\n                      {status.state === CircuitState.CLOSED && (\r\n                        <Shield className=\"mr-1 size-3\" />\r\n                      )}\r\n                      {status.state === CircuitState.OPEN && (\r\n                        <AlertTriangle className=\"mr-1 size-3\" />\r\n                      )}\r\n                      {status.state === CircuitState.HALF_OPEN && (\r\n                        <RefreshCw className=\"mr-1 size-3\" />\r\n                      )}\r\n                      {status.state}\r\n                    </Badge>\r\n                  </div>\r\n                  <div className=\"text-xs text-muted-foreground\">\r\n                    <div>Failures: {status.failureCount}</div>\r\n                    {status.timeUntilRetry && (\r\n                      <div>\r\n                        Retry in: {formatDuration(status.timeUntilRetry)}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Cache Configuration */}\r\n          <div className=\"mt-6 rounded-lg bg-blue-50 p-4\">\r\n            <h4 className=\"mb-2 font-semibold text-blue-900\">\r\n              Cache Configuration\r\n            </h4>\r\n            <div className=\"grid grid-cols-2 gap-3 text-sm md:grid-cols-3\">\r\n              <div>\r\n                <span className=\"font-medium text-blue-800\">\r\n                  Health Status:\r\n                </span>\r\n                <span className=\"ml-2 text-blue-600\">\r\n                  {formatDuration(CACHE_DURATIONS.HEALTH_STATUS)}\r\n                </span>\r\n              </div>\r\n              <div>\r\n                <span className=\"font-medium text-blue-800\">Performance:</span>\r\n                <span className=\"ml-2 text-blue-600\">\r\n                  {formatDuration(CACHE_DURATIONS.PERFORMANCE_METRICS)}\r\n                </span>\r\n              </div>\r\n              <div>\r\n                <span className=\"font-medium text-blue-800\">Error Logs:</span>\r\n                <span className=\"ml-2 text-blue-600\">\r\n                  {formatDuration(CACHE_DURATIONS.ERROR_LOGS)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;AAqCO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,SAAS,EAAE;QAAE,MAAM;IAAE;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAEnD,CAAC;IACH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,WAAW,mIAAA,CAAA,eAAY,CAAC,QAAQ;QACtC,SAAS;QAET,MAAM,WAAiD,CAAC;QACxD,mBAAmB;IACrB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,MAAM,WAAW,YAAY,cAAc,OAAO,sBAAsB;QACxE,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,sJAAA,CAAA,aAAU,CAAC,QAAQ;QACnB;IACF;IAEA,MAAM,6BAA6B;QACjC,iFAAiF;QACjF,sJAAA,CAAA,aAAU,CAAC,QAAQ;QACnB;IACF;IAEA,MAAM,qBAAqB;QACzB,cAAc;QACd;QACA,WAAW,IAAM,cAAc,QAAQ;IACzC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,GAAG,OAAO;QACnB,IAAI,KAAK,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;QAC3C,IAAI,KAAK,QAAQ,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC;QACnD,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC;IACtC;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,IAAI,QAAQ,CAAC,WAAW,OAAO;QACnC,IAAI,IAAI,QAAQ,CAAC,gBAAgB,OAAO;QACxC,IAAI,IAAI,QAAQ,CAAC,WAAW,OAAO;QACnC,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,IAAI,QAAQ,CAAC,WAAW,OAAO;QACnC,IAAI,IAAI,QAAQ,CAAC,gBAAgB,OAAO;QACxC,IAAI,IAAI,QAAQ,CAAC,WAAW,OAAO;QACnC,OAAO;IACT;IAEA,MAAM,eAAe,MAAM,OAAO,CAAC,MAAM,CAAC,CAAA,QACxC,MAAM,GAAG,CAAC,UAAU,CAAC;IAGvB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAGjC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU;oCACV,SAAS;oCACT,MAAK;oCACL,SAAQ;;sDAER,8OAAC,gNAAA,CAAA,YAAS;4CACR,WAAW,CAAC,OAAO,EAAE,aAAa,iBAAiB,IAAI;;;;;;wCACvD;;;;;;;8CAGJ,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,MAAK;oCACL,SAAQ;;sDAER,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,MAAK;oCACL,SAAQ;;sDAER,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,MAAM;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,eAAe,mIAAA,CAAA,kBAAe,CAAC,aAAa;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,eAAe,mIAAA,CAAA,kBAAe,CAAC,mBAAmB;;;;;;sDAErD,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;wBAOlD,aAAa,MAAM,GAAG,kBACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsE;;;;;;gCAGnF,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;wCACC,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,kBAAkB,MAAM,GAAG;kEAC1C,aAAa,MAAM,GAAG;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,MAAM,GAAG,CAAC,OAAO,CAAC,UAAU;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAW;4DACtB,eAAe,MAAM,GAAG;;;;;;;kEAEhC,8OAAC;wDACC,WAAW,CAAC,YAAY,EACtB,MAAM,SAAS,GAAG,IAAI,mBAAmB,gBACzC;kEAED,MAAM,SAAS,GAAG,IACf,CAAC,WAAW,EAAE,eAAe,MAAM,SAAS,GAAG,GAC/C;;;;;;;;;;;;;uCAtBH;;;;;;;;;;iDA6BX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;sCAO3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,iBAClD,8OAAC;4CACC,WAAW,CAAC,sBAAsB,EAChC,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,MAAM,GAChC,iCACA,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,SAAS,GACrC,mCACA,4BACN;;8DAGF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb;;;;;;sEAEH,8OAAC,iIAAA,CAAA,QAAK;4DACJ,WACE,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,MAAM,GAChC,gCACA,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,SAAS,GACrC,kCACA;;gEAGP,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,MAAM,kBACnC,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAEnB,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,IAAI,kBACjC,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAE1B,OAAO,KAAK,KAAK,qIAAA,CAAA,eAAY,CAAC,SAAS,kBACtC,8OAAC,gNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAEtB,OAAO,KAAK;;;;;;;;;;;;;8DAGjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAI;gEAAW,OAAO,YAAY;;;;;;;wDAClC,OAAO,cAAc,kBACpB,8OAAC;;gEAAI;gEACQ,eAAe,OAAO,cAAc;;;;;;;;;;;;;;2CA/BhD;;;;;;;;;;;;;;;;sCAyCb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAG5C,8OAAC;oDAAK,WAAU;8DACb,eAAe,mIAAA,CAAA,kBAAe,CAAC,aAAa;;;;;;;;;;;;sDAGjD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DACb,eAAe,mIAAA,CAAA,kBAAe,CAAC,mBAAmB;;;;;;;;;;;;sDAGvD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DACb,eAAe,mIAAA,CAAA,kBAAe,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5D", "debugId": null}}, {"offset": {"line": 2553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/error-boundaries/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ErrorInfo, ReactNode } from 'react';\r\n\r\nimport { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';\r\nimport React, { Component } from 'react';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface Props {\r\n  children: ReactNode;\r\n  description?: string;\r\n  fallback?: ReactNode;\r\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\r\n  resetLabel?: string;\r\n  title?: string;\r\n}\r\n\r\ninterface State {\r\n  error: Error | null;\r\n  errorInfo: ErrorInfo | null;\r\n  hasError: boolean;\r\n}\r\n\r\n/**\r\n * Generic Error Boundary component\r\n * Catches errors in its child component tree and displays a fallback UI\r\n */\r\nclass ErrorBoundary extends Component<Props, State> {\r\n  constructor(props: Props) {\r\n    super(props);\r\n    this.state = {\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): Partial<State> {\r\n    // Update state so the next render will show the fallback UI\r\n    return {\r\n      error,\r\n      hasError: true,\r\n    };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\r\n    // Update state with error info for detailed reporting\r\n    this.setState({\r\n      errorInfo,\r\n    });\r\n\r\n    // Log the error\r\n    console.error('Error caught by ErrorBoundary:', error);\r\n    console.error('Component stack:', errorInfo.componentStack);\r\n\r\n    // Call onError prop if provided\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo);\r\n    }\r\n\r\n    // In a production app, you would send this to a monitoring service\r\n    // Example: errorReportingService.captureError(error, errorInfo);\r\n  }\r\n\r\n  handleRetry = (): void => {\r\n    // Reset the error boundary state to trigger a re-render\r\n    this.setState({\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    });\r\n  };\r\n\r\n  render(): ReactNode {\r\n    const {\r\n      description = 'An unexpected error occurred.',\r\n      resetLabel = 'Try Again',\r\n      title = 'Something went wrong',\r\n    } = this.props;\r\n\r\n    if (this.state.hasError) {\r\n      // If a custom fallback is provided, use it\r\n      if (this.props.fallback) {\r\n        return this.props.fallback;\r\n      }\r\n\r\n      // Otherwise, use the default error UI\r\n      return (\r\n        <Alert className=\"my-4\" variant=\"destructive\">\r\n          <AlertTriangle className=\"mr-2 size-4\" />\r\n          <AlertTitle className=\"text-lg font-semibold\">{title}</AlertTitle>\r\n          <AlertDescription className=\"mt-2\">\r\n            <p className=\"mb-2\">{this.state.error?.message || description}</p>\r\n            {process.env.NODE_ENV !== 'production' && this.state.errorInfo && (\r\n              <details className=\"mt-2 text-xs\">\r\n                <summary>Error details</summary>\r\n                <pre className=\"mt-2 max-h-[200px] overflow-auto whitespace-pre-wrap rounded bg-slate-100 p-2 dark:bg-slate-900\">\r\n                  {this.state.error?.stack}\r\n                  {'\\n\\nComponent Stack:\\n'}\r\n                  {this.state.errorInfo.componentStack}\r\n                </pre>\r\n              </details>\r\n            )}\r\n            <Button\r\n              className=\"mt-4\"\r\n              onClick={this.handleRetry}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <RefreshCw className=\"mr-2 size-4\" />\r\n              {resetLabel}\r\n            </Button>\r\n          </AlertDescription>\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // If there's no error, render the children\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default ErrorBoundary;\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AACA;AAEA;AACA;AARA;;;;;;AAyBA;;;CAGC,GACD,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAkB;QAC5D,4DAA4D;QAC5D,OAAO;YACL;YACA,UAAU;QACZ;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAQ;QAC1D,sDAAsD;QACtD,IAAI,CAAC,QAAQ,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,QAAQ,KAAK,CAAC,oBAAoB,UAAU,cAAc;QAE1D,gCAAgC;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IAEA,mEAAmE;IACnE,iEAAiE;IACnE;IAEA,cAAc;QACZ,wDAAwD;QACxD,IAAI,CAAC,QAAQ,CAAC;YACZ,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF,EAAE;IAEF,SAAoB;QAClB,MAAM,EACJ,cAAc,+BAA+B,EAC7C,aAAa,WAAW,EACxB,QAAQ,sBAAsB,EAC/B,GAAG,IAAI,CAAC,KAAK;QAEd,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,2CAA2C;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,sCAAsC;YACtC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;gBAAO,SAAQ;;kCAC9B,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC,iIAAA,CAAA,aAAU;wBAAC,WAAU;kCAAyB;;;;;;kCAC/C,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAC1B,8OAAC;gCAAE,WAAU;0CAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;4BACjD,oDAAyB,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC5D,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;kDAAQ;;;;;;kDACT,8OAAC;wCAAI,WAAU;;4CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;4CAClB;4CACA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;0CAI1C,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAI,CAAC,WAAW;gCACzB,MAAK;gCACL,SAAQ;;kDAER,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;QAKX;QAEA,2CAA2C;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 2720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/action-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { Loader2 } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { ButtonProps } from '@/components/ui/button';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface ActionButtonProps extends Omit<ButtonProps, 'variant'> {\r\n  /**\r\n   * The type of action this button represents\r\n   * - primary: Main actions (Create, Save, Submit)\r\n   * - secondary: Alternative actions (View, Edit)\r\n   * - tertiary: Optional actions (Cancel, Back)\r\n   * - danger: Destructive actions (Delete, Remove)\r\n   */\r\n  actionType?: ActionType;\r\n\r\n  /**\r\n   * Icon to display before the button text\r\n   * Should be a Lucide icon with consistent sizing (h-4 w-4)\r\n   */\r\n  icon?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether the button is in a loading state\r\n   */\r\n  isLoading?: boolean;\r\n\r\n  /**\r\n   * Text to display when button is loading\r\n   * If not provided, will use children\r\n   */\r\n  loadingText?: string;\r\n}\r\n\r\nexport type ActionType = 'danger' | 'primary' | 'secondary' | 'tertiary';\r\n\r\n/**\r\n * ActionButton component for consistent action styling across the application\r\n *\r\n * @example\r\n * <ActionButton actionType=\"primary\" icon={<PlusCircle />}>\r\n *   Add New\r\n * </ActionButton>\r\n */\r\nexport const ActionButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  ActionButtonProps\r\n>(\r\n  (\r\n    {\r\n      actionType = 'primary',\r\n      asChild = false,\r\n      children,\r\n      className,\r\n      disabled,\r\n      icon,\r\n      isLoading = false,\r\n      loadingText,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    // Map action types to shadcn/ui button variants and additional styling\r\n    const actionStyles: Record<\r\n      ActionType,\r\n      { className: string; variant: ButtonProps['variant']; }\r\n    > = {\r\n      danger: {\r\n        className: 'shadow-md',\r\n        variant: 'destructive',\r\n      },\r\n      primary: {\r\n        className: 'shadow-md',\r\n        variant: 'default',\r\n      },\r\n      secondary: {\r\n        className: '',\r\n        variant: 'secondary',\r\n      },\r\n      tertiary: {\r\n        className: '',\r\n        variant: 'outline',\r\n      },\r\n    };\r\n\r\n    const { className: actionClassName, variant } = actionStyles[actionType];\r\n\r\n    // const Comp = asChild ? Slot : \"button\"; // This was for an older structure, Button handles asChild now\r\n\r\n    return (\r\n      <Button\r\n        asChild={asChild} // This is passed to the underlying shadcn Button\r\n        className={cn(actionClassName, className)}\r\n        disabled={isLoading || disabled}\r\n        ref={ref}\r\n        variant={variant}\r\n        {...props}\r\n      >\r\n        {isLoading ? (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n            {loadingText || children}\r\n          </span>\r\n        ) : (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            {icon && <span className=\"mr-2\">{icon}</span>}\r\n            {children}\r\n          </span>\r\n        )}\r\n      </Button>\r\n    );\r\n  }\r\n);\r\n\r\nActionButton.displayName = 'ActionButton';\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAIA;AACA;AAAA;AATA;;;;;;AAiDO,MAAM,6BAAe,qMAAA,CAAA,UAAK,CAAC,UAAU,CAI1C,CACE,EACE,aAAa,SAAS,EACtB,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,YAAY,KAAK,EACjB,WAAW,EACX,GAAG,OACJ,EACD;IAEA,uEAAuE;IACvE,MAAM,eAGF;QACF,QAAQ;YACN,WAAW;YACX,SAAS;QACX;QACA,SAAS;YACP,WAAW;YACX,SAAS;QACX;QACA,WAAW;YACT,WAAW;YACX,SAAS;QACX;QACA,UAAU;YACR,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,EAAE,WAAW,eAAe,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,WAAW;IAExE,yGAAyG;IAEzG,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,UAAU,aAAa;QACvB,KAAK;QACL,SAAS;QACR,GAAG,KAAK;kBAER,0BACC,8OAAC;YAAK,WAAU;;gBACb;8BAED,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAClB,eAAe;;;;;;iCAGlB,8OAAC;YAAK,WAAU;;gBACb;gBAEA,sBAAQ,8OAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;;;;;AAKX;AAGF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn('animate-pulse rounded-md bg-muted', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/loading-states.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Props for LoadingCard component\r\n */\r\ninterface LoadingCardProps {\r\n  /** Custom CSS class names */\r\n  className?: string;\r\n  /** Number of skeleton items to display */\r\n  count?: number;\r\n  /** Height of each skeleton item */\r\n  height?: number;\r\n}\r\n\r\n/**\r\n * Props for LoadingContainer component\r\n */\r\ninterface LoadingContainerProps {\r\n  /** Content to display when loaded successfully */\r\n  children: React.ReactNode;\r\n  /** Custom CSS class names */\r\n  className?: string;\r\n  /** Error message, if any */\r\n  error?: null | string;\r\n  /** Custom error component */\r\n  errorComponent?: React.ReactNode;\r\n  /** Whether the data is currently loading */\r\n  isLoading: boolean;\r\n  /** Custom loading component */\r\n  loadingComponent?: React.ReactNode;\r\n  /** Function to retry the operation */\r\n  onRetry?: () => void;\r\n}\r\n\r\n/**\r\n * Props for LoadingError component\r\n */\r\ninterface LoadingErrorProps {\r\n  /** Custom CSS class names */\r\n  className?: string;\r\n  /** Error message to display */\r\n  message: string;\r\n  /** Function to retry the operation */\r\n  onRetry?: () => void;\r\n}\r\n\r\n/**\r\n * Props for LoadingSpinner component\r\n */\r\ninterface LoadingSpinnerProps {\r\n  /** Custom CSS class names */\r\n  className?: string;\r\n  /** Size of the spinner in pixels */\r\n  size?: number;\r\n  /** Text to display next to the spinner */\r\n  text?: string;\r\n}\r\n\r\n/**\r\n * A card loading state with skeleton UI\r\n */\r\nexport function LoadingCard({\r\n  className,\r\n  count = 3,\r\n  height = 20,\r\n}: LoadingCardProps) {\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      <Skeleton className=\"mb-4 h-8 w-[250px]\" />\r\n      {Array.from({ length: count }).map((_, i) => (\r\n        <Skeleton\r\n          className=\"w-full\"\r\n          key={i}\r\n          style={{ height: `${height}px` }}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * A container component that handles loading, error, and success states\r\n */\r\nexport function LoadingContainer({\r\n  children,\r\n  className,\r\n  error,\r\n  errorComponent,\r\n  isLoading,\r\n  loadingComponent,\r\n  onRetry,\r\n}: LoadingContainerProps) {\r\n  if (isLoading) {\r\n    return (\r\n      loadingComponent || (\r\n        <LoadingSpinner {...(className && { className })} text=\"Loading...\" />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      errorComponent || (\r\n        <LoadingError\r\n          {...(className && { className })}\r\n          message={error}\r\n          {...(onRetry && { onRetry })}\r\n        />\r\n      )\r\n    );\r\n  }\r\n\r\n  return <div className={className}>{children}</div>;\r\n}\r\n\r\n/**\r\n * An error state component for failed loading operations\r\n */\r\nexport function LoadingError({\r\n  className,\r\n  message,\r\n  onRetry,\r\n}: LoadingErrorProps) {\r\n  return (\r\n    <Alert className={cn('my-4', className)} variant=\"destructive\">\r\n      <AlertCircle className=\"size-4\" />\r\n      <AlertTitle>Error</AlertTitle>\r\n      <AlertDescription>\r\n        <div className=\"mt-2\">\r\n          <p className=\"mb-4 text-sm text-muted-foreground\">{message}</p>\r\n          {onRetry && (\r\n            <Button\r\n              className=\"flex items-center\"\r\n              onClick={onRetry}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <Loader2 className=\"mr-2 size-4\" />\r\n              Retry\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n\r\n/**\r\n * A simple loading spinner component\r\n */\r\nexport function LoadingSpinner({\r\n  className,\r\n  size = 24,\r\n  text,\r\n}: LoadingSpinnerProps) {\r\n  return (\r\n    <div className={cn('flex items-center justify-center', className)}>\r\n      <Loader2\r\n        className=\"animate-spin text-muted-foreground\"\r\n        style={{ height: size, width: size }}\r\n      />\r\n      {text && (\r\n        <span className=\"ml-2 text-sm text-muted-foreground\">{text}</span>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AARA;;;;;;;AAqEO,SAAS,YAAY,EAC1B,SAAS,EACT,QAAQ,CAAC,EACT,SAAS,EAAE,EACM;IACjB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YACnB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,8OAAC,oIAAA,CAAA,WAAQ;oBACP,WAAU;oBAEV,OAAO;wBAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;oBAAC;mBAD1B;;;;;;;;;;;AAMf;AAKO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,SAAS,EACT,KAAK,EACL,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,OAAO,EACe;IACtB,IAAI,WAAW;QACb,OACE,kCACE,8OAAC;YAAgB,GAAI,aAAa;gBAAE;YAAU,CAAC;YAAG,MAAK;;;;;;IAG7D;IAEA,IAAI,OAAO;QACT,OACE,gCACE,8OAAC;YACE,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC/B,SAAS;YACR,GAAI,WAAW;gBAAE;YAAQ,CAAC;;;;;;IAInC;IAEA,qBAAO,8OAAC;QAAI,WAAW;kBAAY;;;;;;AACrC;AAKO,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,OAAO,EACW;IAClB,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,SAAQ;;0BAC/C,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC,iIAAA,CAAA,aAAU;0BAAC;;;;;;0BACZ,8OAAC,iIAAA,CAAA,mBAAgB;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,yBACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS;4BACT,MAAK;4BACL,SAAQ;;8CAER,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;AAKO,SAAS,eAAe,EAC7B,SAAS,EACT,OAAO,EAAE,EACT,IAAI,EACgB;IACpB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;;0BACrD,8OAAC,iNAAA,CAAA,UAAO;gBACN,WAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAM,OAAO;gBAAK;;;;;;YAEpC,sBACC,8OAAC;gBAAK,WAAU;0BAAsC;;;;;;;;;;;;AAI9D", "debugId": null}}, {"offset": {"line": 3031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ children, className, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    className={cn('relative overflow-hidden', className)}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"size-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n));\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName;\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = 'vertical', ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    className={cn(\r\n      'flex touch-none select-none transition-colors',\r\n      orientation === 'vertical' &&\r\n        'h-full w-2.5 border-l border-l-transparent p-[1px]',\r\n      orientation === 'horizontal' &&\r\n        'h-2.5 flex-col border-t border-t-transparent p-[1px]',\r\n      className\r\n    )}\r\n    orientation={orientation}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n));\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName;\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAEF,aAAa;QACb,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { Check, ChevronDown, ChevronUp } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ children, className, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    className={cn(\r\n      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"size-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"size-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"size-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ children, className, position = 'popper', ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      className={cn(\r\n        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\r\n        position === 'popper' &&\r\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n        className\r\n      )}\r\n      position={position}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          'p-1',\r\n          position === 'popper' &&\r\n            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.memo(\r\n  React.forwardRef<\r\n    React.ElementRef<typeof SelectPrimitive.Item>,\r\n    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n  >(({ children, className, ...props }, forwardedRef) => {\r\n    const composedRefs = React.useCallback(\r\n      (node: any) => {\r\n        if (typeof forwardedRef === 'function') {\r\n          forwardedRef(node);\r\n        } else if (forwardedRef) {\r\n          (forwardedRef as React.MutableRefObject<any>).current = node;\r\n        }\r\n      },\r\n      [forwardedRef]\r\n    );\r\n\r\n    return (\r\n      <SelectPrimitive.Item\r\n        className={cn(\r\n          'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n          className\r\n        )}\r\n        ref={composedRefs}\r\n        {...props}\r\n      >\r\n        <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\r\n          <SelectPrimitive.ItemIndicator>\r\n            <Check className=\"size-4\" />\r\n          </SelectPrimitive.ItemIndicator>\r\n        </span>\r\n\r\n        <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n      </SelectPrimitive.Item>\r\n    );\r\n  })\r\n);\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACV,KAAK;YACJ,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,OAAU,AAAD,gBAC1B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGb,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACnC,CAAC;QACC,IAAI,OAAO,iBAAiB,YAAY;YACtC,aAAa;QACf,OAAO,IAAI,cAAc;YACtB,aAA6C,OAAO,GAAG;QAC1D;IACF,GACA;QAAC;KAAa;IAGhB,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEF,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/api/useApiQuery.ts"], "sourcesContent": ["/**\r\n * @file Standardized API Query Hook\r\n * @description Provides consistent API query patterns with error handling, caching, and loading states\r\n */\r\n\r\nimport {\r\n  type QueryKey,\r\n  useQuery,\r\n  type UseQueryOptions,\r\n  type UseQueryResult,\r\n} from '@tanstack/react-query';\r\nimport { useEffect } from 'react';\r\n\r\nimport type { PaginatedResponse } from '@/types';\r\n\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\n/**\r\n * Configuration options for API queries\r\n */\r\nexport interface ApiQueryOptions<T>\r\n  extends Omit<UseQueryOptions<T>, 'queryFn' | 'queryKey'> {\r\n  /** Cache duration in milliseconds */\r\n  cacheDuration?: number;\r\n  /** Whether to retry on error */\r\n  enableRetry?: boolean;\r\n  /** Custom error message */\r\n  errorMessage?: string;\r\n  /** Number of retry attempts */\r\n  retryAttempts?: number;\r\n  /** Whether to show error toasts automatically */\r\n  showErrorToast?: boolean;\r\n  /** Whether to show success toasts automatically */\r\n  showSuccessToast?: boolean;\r\n  /** Custom success message */\r\n  successMessage?: string;\r\n}\r\n\r\n/**\r\n * Enhanced API query result with additional utilities\r\n */\r\nexport type ApiQueryResult<T> = UseQueryResult<T> & {\r\n  /** Force refresh the query */\r\n  forceRefresh: () => Promise<UseQueryResult<T>>;\r\n  /** Check if data is stale */\r\n  isStale: boolean;\r\n  /** Last updated timestamp */\r\n  lastUpdated: null | number;\r\n};\r\n\r\n/**\r\n * Standardized API query hook with consistent error handling and caching\r\n *\r\n * @example\r\n * ```typescript\r\n * const { data, isLoading, error, forceRefresh } = useApiQuery(\r\n *   ['users', filters],\r\n *   () => userService.getAll(filters),\r\n *   {\r\n *     showErrorToast: true,\r\n *     cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n *     enableRetry: true,\r\n *   }\r\n * );\r\n * ```\r\n */\r\nexport const useApiQuery = <T>(\r\n  queryKey: QueryKey,\r\n  queryFn: () => Promise<T>,\r\n  options: ApiQueryOptions<T> = {}\r\n): ApiQueryResult<T> => {\r\n  const { toast } = useToast();\r\n\r\n  const {\r\n    cacheDuration = 5 * 60 * 1000, // 5 minutes default\r\n    enableRetry = true,\r\n    errorMessage,\r\n    retryAttempts = 3,\r\n    showErrorToast = true,\r\n    showSuccessToast = false,\r\n    successMessage,\r\n    ...queryOptions\r\n  } = options;\r\n\r\n  const queryResult = useQuery({\r\n    gcTime: cacheDuration * 2, // Keep in cache for twice the stale time\r\n    queryFn,\r\n    queryKey,\r\n    retry: enableRetry ? retryAttempts : false,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n    staleTime: cacheDuration,\r\n    ...queryOptions,\r\n  });\r\n\r\n  // FIXED: Handle success notifications in useEffect to prevent setState during render\r\n  useEffect(() => {\r\n    if (\r\n      showSuccessToast &&\r\n      queryResult.isSuccess &&\r\n      queryResult.data &&\r\n      successMessage\r\n    ) {\r\n      toast({\r\n        description: successMessage,\r\n        title: 'Success',\r\n      });\r\n    }\r\n  }, [\r\n    showSuccessToast,\r\n    queryResult.isSuccess,\r\n    queryResult.data,\r\n    successMessage,\r\n    toast,\r\n  ]);\r\n\r\n  // FIXED: Handle error notifications in useEffect to prevent setState during render\r\n  useEffect(() => {\r\n    if (showErrorToast && queryResult.isError) {\r\n      const message =\r\n        errorMessage ||\r\n        (queryResult.error instanceof Error\r\n          ? queryResult.error.message\r\n          : 'An error occurred');\r\n\r\n      toast({\r\n        description: message,\r\n        title: 'Error',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  }, [\r\n    showErrorToast,\r\n    queryResult.isError,\r\n    queryResult.error,\r\n    errorMessage,\r\n    toast,\r\n  ]);\r\n\r\n  // Enhanced result with additional utilities\r\n  const enhancedResult: ApiQueryResult<T> = {\r\n    ...queryResult,\r\n    forceRefresh: async () => await queryResult.refetch(),\r\n    isStale: queryResult.isStale || false,\r\n    lastUpdated: queryResult.dataUpdatedAt || null,\r\n  };\r\n\r\n  return enhancedResult;\r\n};\r\n\r\n/**\r\n * Hook for queries that depend on other queries\r\n * Automatically handles dependency loading states\r\n */\r\nexport const useDependentApiQuery = <T, TDep>(\r\n  queryKey: QueryKey,\r\n  queryFn: (dependency: TDep) => Promise<T>,\r\n  dependency: TDep | undefined,\r\n  options: ApiQueryOptions<T> = {}\r\n): ApiQueryResult<T> => {\r\n  return useApiQuery(\r\n    queryKey,\r\n    () => {\r\n      if (!dependency) {\r\n        throw new Error('Dependency not available');\r\n      }\r\n      return queryFn(dependency);\r\n    },\r\n    {\r\n      ...options,\r\n      enabled: !!dependency && options.enabled !== false,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Type for the raw API response of a paginated query\r\n */\r\nexport interface PaginatedApiResponse<T> {\r\n  data: T[];\r\n  pagination: PaginatedResponse<any>['pagination'];\r\n}\r\n\r\n/**\r\n * Hook for paginated API queries with consistent pagination handling\r\n */\r\nexport interface PaginatedQueryOptions<T>\r\n  extends ApiQueryOptions<PaginatedApiResponse<T>> {\r\n  /** Whether to keep previous data while loading new page */\r\n  keepPreviousData?: boolean;\r\n  /** Current page number */\r\n  page?: number;\r\n  /** Items per page */\r\n  pageSize?: number;\r\n}\r\n\r\nexport type PaginatedQueryResult<T> = Omit<\r\n  ApiQueryResult<PaginatedApiResponse<T>>,\r\n  'data'\r\n> & {\r\n  /** Current page number */\r\n  currentPage: number;\r\n  /** The actual data array */\r\n  data: T[];\r\n  /** Go to specific page */\r\n  goToPage: (page: number) => void;\r\n  /** Whether there's a next page */\r\n  hasNextPage: boolean;\r\n  /** Whether there's a previous page */\r\n  hasPrevPage: boolean;\r\n  /** Go to next page */\r\n  nextPage: () => void;\r\n  /** The pagination object */\r\n  pagination: PaginatedResponse<any>['pagination'];\r\n  /** Go to previous page */\r\n  prevPage: () => void;\r\n  /** Total number of pages */\r\n  totalPages: number;\r\n};\r\n\r\n/**\r\n * Standardized paginated API query hook\r\n */\r\nexport const usePaginatedApiQuery = <T>(\r\n  baseQueryKey: QueryKey,\r\n  queryFn: (page: number, pageSize: number) => Promise<PaginatedApiResponse<T>>,\r\n  options: PaginatedQueryOptions<T> = {}\r\n): PaginatedQueryResult<T> => {\r\n  const {\r\n    keepPreviousData = true,\r\n    page = 1,\r\n    pageSize = 10,\r\n    ...apiOptions\r\n  } = options;\r\n\r\n  const queryKey = [...baseQueryKey, 'paginated', page, pageSize];\r\n\r\n  const queryResult = useApiQuery<PaginatedApiResponse<T>>(\r\n    queryKey,\r\n    () => queryFn(page, pageSize),\r\n    {\r\n      ...apiOptions,\r\n      ...(keepPreviousData\r\n        ? {\r\n            placeholderData: (prev: PaginatedApiResponse<T> | undefined) =>\r\n              prev,\r\n          }\r\n        : {}),\r\n    }\r\n  );\r\n\r\n  const pagination = queryResult.data?.pagination;\r\n\r\n  const enhancedResult: PaginatedQueryResult<T> = {\r\n    ...queryResult,\r\n    currentPage: page,\r\n    data: queryResult.data?.data ?? [],\r\n    goToPage: (newPage: number) => {\r\n      // This would typically be handled by the parent component\r\n    },\r\n    hasNextPage: pagination ? pagination.hasNext : false,\r\n    hasPrevPage: pagination ? pagination.hasPrevious : false,\r\n    nextPage: () => {\r\n      if (pagination && pagination.hasNext) {\r\n        // This would typically be handled by the parent component\r\n        // by updating the page state that's passed to this hook\r\n      }\r\n    },\r\n    pagination: pagination ?? {\r\n      hasNext: false,\r\n      hasPrevious: false,\r\n      limit: pageSize,\r\n      page: 1,\r\n      total: 0,\r\n      totalPages: 1,\r\n    },\r\n    prevPage: () => {\r\n      if (pagination && pagination.hasPrevious) {\r\n        // This would typically be handled by the parent component\r\n      }\r\n    },\r\n    totalPages: pagination ? pagination.totalPages : 1,\r\n  };\r\n\r\n  return enhancedResult;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAMA;AAIA;;;;AAmDO,MAAM,cAAc,CACzB,UACA,SACA,UAA8B,CAAC,CAAC;IAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EACJ,gBAAgB,IAAI,KAAK,IAAI,EAC7B,cAAc,IAAI,EAClB,YAAY,EACZ,gBAAgB,CAAC,EACjB,iBAAiB,IAAI,EACrB,mBAAmB,KAAK,EACxB,cAAc,EACd,GAAG,cACJ,GAAG;IAEJ,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,QAAQ,gBAAgB;QACxB;QACA;QACA,OAAO,cAAc,gBAAgB;QACrC,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,WAAW;QACX,GAAG,YAAY;IACjB;IAEA,qFAAqF;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,oBACA,YAAY,SAAS,IACrB,YAAY,IAAI,IAChB,gBACA;YACA,MAAM;gBACJ,aAAa;gBACb,OAAO;YACT;QACF;IACF,GAAG;QACD;QACA,YAAY,SAAS;QACrB,YAAY,IAAI;QAChB;QACA;KACD;IAED,mFAAmF;IACnF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,YAAY,OAAO,EAAE;YACzC,MAAM,UACJ,gBACA,CAAC,YAAY,KAAK,YAAY,QAC1B,YAAY,KAAK,CAAC,OAAO,GACzB,mBAAmB;YAEzB,MAAM;gBACJ,aAAa;gBACb,OAAO;gBACP,SAAS;YACX;QACF;IACF,GAAG;QACD;QACA,YAAY,OAAO;QACnB,YAAY,KAAK;QACjB;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,iBAAoC;QACxC,GAAG,WAAW;QACd,cAAc,UAAY,MAAM,YAAY,OAAO;QACnD,SAAS,YAAY,OAAO,IAAI;QAChC,aAAa,YAAY,aAAa,IAAI;IAC5C;IAEA,OAAO;AACT;AAMO,MAAM,uBAAuB,CAClC,UACA,SACA,YACA,UAA8B,CAAC,CAAC;IAEhC,OAAO,YACL,UACA;QACE,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,QAAQ;IACjB,GACA;QACE,GAAG,OAAO;QACV,SAAS,CAAC,CAAC,cAAc,QAAQ,OAAO,KAAK;IAC/C;AAEJ;AAkDO,MAAM,uBAAuB,CAClC,cACA,SACA,UAAoC,CAAC,CAAC;IAEtC,MAAM,EACJ,mBAAmB,IAAI,EACvB,OAAO,CAAC,EACR,WAAW,EAAE,EACb,GAAG,YACJ,GAAG;IAEJ,MAAM,WAAW;WAAI;QAAc;QAAa;QAAM;KAAS;IAE/D,MAAM,cAAc,YAClB,UACA,IAAM,QAAQ,MAAM,WACpB;QACE,GAAG,UAAU;QACb,GAAI,mBACA;YACE,iBAAiB,CAAC,OAChB;QACJ,IACA,CAAC,CAAC;IACR;IAGF,MAAM,aAAa,YAAY,IAAI,EAAE;IAErC,MAAM,iBAA0C;QAC9C,GAAG,WAAW;QACd,aAAa;QACb,MAAM,YAAY,IAAI,EAAE,QAAQ,EAAE;QAClC,UAAU,CAAC;QACT,0DAA0D;QAC5D;QACA,aAAa,aAAa,WAAW,OAAO,GAAG;QAC/C,aAAa,aAAa,WAAW,WAAW,GAAG;QACnD,UAAU;YACR,IAAI,cAAc,WAAW,OAAO,EAAE;YACpC,0DAA0D;YAC1D,wDAAwD;YAC1D;QACF;QACA,YAAY,cAAc;YACxB,SAAS;YACT,aAAa;YACb,OAAO;YACP,MAAM;YACN,OAAO;YACP,YAAY;QACd;QACA,UAAU;YACR,IAAI,cAAc,WAAW,WAAW,EAAE;YACxC,0DAA0D;YAC5D;QACF;QACA,YAAY,aAAa,WAAW,UAAU,GAAG;IACnD;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/admin/ErrorLog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON>riangle, Filter, Info, RefreshCw, XCircle } from 'lucide-react';\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nimport type { ErrorLogEntry, LogLevel } from '@/types';\r\n\r\nimport ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { LoadingError } from '@/components/ui/loading-states';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { usePaginatedApiQuery } from '@/hooks/api/useApiQuery';\r\nimport { adminService } from '@/lib/api/services/admin'; // Import adminService directly\r\nimport { SessionManager } from '@/lib/security/SessionManager';\r\nimport { getTokenRefreshService } from '@/lib/services/TokenRefreshService';\r\n\r\n/**\r\n * Component that displays recent system error logs\r\n */\r\nexport function ErrorLog() {\r\n  const [level, setLevel] = useState<LogLevel | undefined>();\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [authError, setAuthError] = useState<null | string>(null);\r\n\r\n  const {\r\n    currentPage: page,\r\n    data: errors,\r\n    error,\r\n    hasNextPage,\r\n    hasPrevPage,\r\n    isLoading,\r\n    nextPage,\r\n    prevPage,\r\n    refetch,\r\n    totalPages,\r\n  } = usePaginatedApiQuery<ErrorLogEntry>(\r\n    ['adminErrors', level], // Add query key\r\n    (currentPage: number, pageSize: number) =>\r\n      adminService.getRecentErrors(currentPage, pageSize, level),\r\n    {\r\n      pageSize: 10,\r\n    }\r\n  );\r\n\r\n  // Handle authentication errors using SessionManager and TokenRefreshService\r\n  useEffect(() => {\r\n    if (error) {\r\n      const handleAuthError = async () => {\r\n        // Check if this is an authentication error\r\n        const isAuthError =\r\n          ('status' in error &&\r\n            (error.status === 401 || error.status === 500)) ||\r\n          ('code' in error &&\r\n            (error.code === 'NO_TOKEN' || error.code === 'INVALID_TOKEN')) ||\r\n          error.message.includes('Authentication failed') ||\r\n          error.message.includes('Not Found'); // Backend returns 500 but frontend sees 404\r\n\r\n        if (isAuthError) {\r\n          // Check session state using SessionManager\r\n          const sessionState = SessionManager.getSessionState();\r\n          const isSessionTimeout = SessionManager.detectTimeout();\r\n\r\n          if (isSessionTimeout || !sessionState?.isActive) {\r\n            setAuthError(\r\n              'Your session has expired. Click \"Refresh Authentication\" to renew your session.'\r\n            );\r\n            return;\r\n          }\r\n\r\n          // Try to validate and refresh the session using TokenRefreshService\r\n          try {\r\n            const tokenService = getTokenRefreshService();\r\n            const sessionInfo = await tokenService.getSessionInfo();\r\n\r\n            if (sessionInfo.isValid) {\r\n              // Session seems valid but API call failed - might be a server issue\r\n              setAuthError(\r\n                'Server error occurred. This might be a temporary issue. Try refreshing.'\r\n              );\r\n            } else {\r\n              if (sessionInfo.isExpired) {\r\n                setAuthError(\r\n                  'Your session has expired. Click \"Refresh Authentication\" to renew your session.'\r\n                );\r\n              } else {\r\n                setAuthError(\r\n                  'Authentication failed. Please refresh the page to sign in again.'\r\n                );\r\n              }\r\n            }\r\n          } catch {\r\n            setAuthError(\r\n              'Authentication system error. Please refresh the page to sign in again.'\r\n            );\r\n          }\r\n        }\r\n      };\r\n\r\n      handleAuthError();\r\n    }\r\n  }, [error]);\r\n\r\n  const handleRefresh = async () => {\r\n    setRefreshing(true);\r\n    setAuthError(null); // Clear any previous auth errors\r\n    await refetch();\r\n    setRefreshing(false);\r\n  };\r\n\r\n  const handleLevelChange = (value: string) => {\r\n    setLevel(value === 'all' ? undefined : (value as LogLevel));\r\n  };\r\n\r\n  const handleAuthRefresh = async () => {\r\n    setRefreshing(true);\r\n    setAuthError(null);\r\n\r\n    try {\r\n      const tokenService = getTokenRefreshService();\r\n      const refreshSuccess = await tokenService.refreshNow();\r\n\r\n      if (refreshSuccess) {\r\n        // Token refreshed successfully, retry the API call\r\n        await refetch();\r\n      } else {\r\n        setAuthError('Failed to refresh authentication. Please sign in again.');\r\n      }\r\n    } catch {\r\n      setAuthError('Authentication refresh failed. Please sign in again.');\r\n    } finally {\r\n      setRefreshing(false);\r\n    }\r\n  };\r\n\r\n  const handlePageRefresh = () => {\r\n    globalThis.location.reload();\r\n  };\r\n\r\n  const getLevelIcon = (level: string) => {\r\n    if (level === 'ERROR') return <XCircle className=\"size-4 text-red-500\" />;\r\n    if (level === 'WARNING')\r\n      return <AlertTriangle className=\"size-4 text-yellow-500\" />;\r\n    return <Info className=\"size-4 text-blue-500\" />;\r\n  };\r\n\r\n  const getLevelBadge = (level: string) => {\r\n    if (level === 'ERROR')\r\n      return (\r\n        <Badge\r\n          className=\"border-red-500/30 bg-red-500/20 text-red-700\"\r\n          variant=\"outline\"\r\n        >\r\n          Error\r\n        </Badge>\r\n      );\r\n    if (level === 'WARNING')\r\n      return (\r\n        <Badge\r\n          className=\"border-yellow-500/30 bg-yellow-500/20 text-yellow-700\"\r\n          variant=\"outline\"\r\n        >\r\n          Warning\r\n        </Badge>\r\n      );\r\n    return (\r\n      <Badge\r\n        className=\"border-blue-500/30 bg-blue-500/20 text-blue-700\"\r\n        variant=\"outline\"\r\n      >\r\n        Info\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <ErrorBoundary>\r\n      <Card className=\"shadow-md\">\r\n        <CardHeader className=\"p-5 pb-2\">\r\n          <CardTitle className=\"text-xl font-semibold text-primary\">\r\n            Recent Errors & Warnings\r\n          </CardTitle>\r\n          <CardDescription>Latest system errors and warnings</CardDescription>\r\n        </CardHeader>\r\n\r\n        <div className=\"px-5 pb-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Filter className=\"size-4 text-muted-foreground\" />\r\n            <span className=\"text-sm font-medium\">Filter by level:</span>\r\n            <Select onValueChange={handleLevelChange} value={level ?? 'all'}>\r\n              <SelectTrigger className=\"w-[140px]\">\r\n                <SelectValue placeholder=\"All levels\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"all\">All levels</SelectItem>\r\n                <SelectItem value=\"ERROR\">Errors only</SelectItem>\r\n                <SelectItem value=\"WARNING\">Warnings only</SelectItem>\r\n                <SelectItem value=\"INFO\">Info only</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n\r\n        <CardContent className=\"p-5\">\r\n          {authError ? (\r\n            <div className=\"rounded-md border border-red-200 bg-red-50 p-4\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <XCircle className=\"size-5 text-red-500\" />\r\n                <div>\r\n                  <h3 className=\"font-medium text-red-800\">\r\n                    Authentication Error\r\n                  </h3>\r\n                  <p className=\"text-sm text-red-700\">{authError}</p>\r\n                  <div className=\"mt-3 flex space-x-2\">\r\n                    <ActionButton\r\n                      actionType=\"primary\"\r\n                      isLoading={refreshing}\r\n                      loadingText=\"Refreshing...\"\r\n                      onClick={handleAuthRefresh}\r\n                      size=\"sm\"\r\n                    >\r\n                      Refresh Authentication\r\n                    </ActionButton>\r\n                    <ActionButton\r\n                      actionType=\"tertiary\"\r\n                      onClick={handlePageRefresh}\r\n                      size=\"sm\"\r\n                    >\r\n                      Refresh Page\r\n                    </ActionButton>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : isLoading || refreshing ? (\r\n            <div className=\"space-y-3\">\r\n              <Skeleton className=\"h-6 w-full\" />\r\n              <Skeleton className=\"h-6 w-full\" />\r\n              <Skeleton className=\"h-6 w-full\" />\r\n              <Skeleton className=\"h-6 w-full\" />\r\n              <Skeleton className=\"h-6 w-full\" />\r\n            </div>\r\n          ) : error ? (\r\n            <LoadingError message={error.message} onRetry={refetch} />\r\n          ) : errors && errors.length > 0 ? (\r\n            <>\r\n              <ScrollArea className=\"h-[300px] pr-4\">\r\n                <div className=\"space-y-3\">\r\n                  {errors.map((entry: ErrorLogEntry) => (\r\n                    <div\r\n                      className=\"rounded-md border p-3 transition-colors hover:bg-accent/50\"\r\n                      key={entry.id}\r\n                    >\r\n                      <div className=\"mb-1 flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          {getLevelIcon(entry.level)}\r\n                          <span className=\"font-medium\">{entry.message}</span>\r\n                        </div>\r\n                        {getLevelBadge(entry.level)}\r\n                      </div>\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div className=\"text-xs text-muted-foreground\">\r\n                          {new Date(entry.timestamp).toLocaleString()}\r\n                        </div>\r\n                        {entry.source && (\r\n                          <div className=\"text-xs text-muted-foreground\">\r\n                            Source: {entry.source}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      {entry.details &&\r\n                        Object.keys(entry.details).length > 0 && (\r\n                          <div className=\"mt-2 rounded bg-muted p-2 text-xs\">\r\n                            <pre className=\"whitespace-pre-wrap\">\r\n                              {JSON.stringify(entry.details, null, 2)}\r\n                            </pre>\r\n                          </div>\r\n                        )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </ScrollArea>\r\n\r\n              {totalPages > 1 && (\r\n                <div className=\"mt-4 flex items-center justify-between\">\r\n                  <ActionButton\r\n                    actionType=\"tertiary\"\r\n                    disabled={!hasPrevPage || isLoading || refreshing}\r\n                    onClick={prevPage}\r\n                    size=\"sm\"\r\n                  >\r\n                    Previous\r\n                  </ActionButton>\r\n                  <span className=\"text-sm text-muted-foreground\">\r\n                    Page {page} of {totalPages}\r\n                  </span>\r\n                  <ActionButton\r\n                    actionType=\"tertiary\"\r\n                    disabled={!hasNextPage || isLoading || refreshing}\r\n                    onClick={nextPage}\r\n                    size=\"sm\"\r\n                  >\r\n                    Next\r\n                  </ActionButton>\r\n                </div>\r\n              )}\r\n            </>\r\n          ) : (\r\n            <div className=\"p-8 text-center text-muted-foreground\">\r\n              <AlertTriangle className=\"mx-auto mb-2 size-8 text-muted-foreground/50\" />\r\n              <p>No errors or warnings found for the selected filter.</p>\r\n              {level && (\r\n                <p className=\"mt-2 text-sm\">\r\n                  Try changing the filter to see more results.\r\n                </p>\r\n              )}\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n        <CardFooter className=\"p-5\">\r\n          <ActionButton\r\n            actionType=\"tertiary\"\r\n            className=\"w-full\"\r\n            icon={<RefreshCw className=\"size-4\" />}\r\n            isLoading={refreshing || isLoading}\r\n            loadingText=\"Refreshing...\"\r\n            onClick={handleRefresh}\r\n            size=\"sm\"\r\n          >\r\n            Refresh Logs\r\n          </ActionButton>\r\n        </CardFooter>\r\n      </Card>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AACA;AACA;AACA;AAQA;AACA;AACA;AAOA;AACA;AACA,uSAAyD,+BAA+B;AAAxF;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;AAoCO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,EACJ,aAAa,IAAI,EACjB,MAAM,MAAM,EACZ,KAAK,EACL,WAAW,EACX,WAAW,EACX,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,UAAU,EACX,GAAG,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EACrB;QAAC;QAAe;KAAM,EACtB,CAAC,aAAqB,WACpB,sJAAA,CAAA,eAAY,CAAC,eAAe,CAAC,aAAa,UAAU,QACtD;QACE,UAAU;IACZ;IAGF,4EAA4E;IAC5E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,MAAM,kBAAkB;gBACtB,2CAA2C;gBAC3C,MAAM,cACJ,AAAC,YAAY,SACX,CAAC,MAAM,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,GAAG,KAC9C,UAAU,SACT,CAAC,MAAM,IAAI,KAAK,cAAc,MAAM,IAAI,KAAK,eAAe,KAC9D,MAAM,OAAO,CAAC,QAAQ,CAAC,4BACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc,4CAA4C;gBAEnF,IAAI,aAAa;oBACf,2CAA2C;oBAC3C,MAAM,eAAe,wIAAA,CAAA,iBAAc,CAAC,eAAe;oBACnD,MAAM,mBAAmB,wIAAA,CAAA,iBAAc,CAAC,aAAa;oBAErD,IAAI,oBAAoB,CAAC,cAAc,UAAU;wBAC/C,aACE;wBAEF;oBACF;oBAEA,oEAAoE;oBACpE,IAAI;wBACF,MAAM,eAAe,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;wBAC1C,MAAM,cAAc,MAAM,aAAa,cAAc;wBAErD,IAAI,YAAY,OAAO,EAAE;4BACvB,oEAAoE;4BACpE,aACE;wBAEJ,OAAO;4BACL,IAAI,YAAY,SAAS,EAAE;gCACzB,aACE;4BAEJ,OAAO;gCACL,aACE;4BAEJ;wBACF;oBACF,EAAE,OAAM;wBACN,aACE;oBAEJ;gBACF;YACF;YAEA;QACF;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,gBAAgB;QACpB,cAAc;QACd,aAAa,OAAO,iCAAiC;QACrD,MAAM;QACN,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,UAAU,QAAQ,YAAa;IAC1C;IAEA,MAAM,oBAAoB;QACxB,cAAc;QACd,aAAa;QAEb,IAAI;YACF,MAAM,eAAe,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;YAC1C,MAAM,iBAAiB,MAAM,aAAa,UAAU;YAEpD,IAAI,gBAAgB;gBAClB,mDAAmD;gBACnD,MAAM;YACR,OAAO;gBACL,aAAa;YACf;QACF,EAAE,OAAM;YACN,aAAa;QACf,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW,QAAQ,CAAC,MAAM;IAC5B;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,SAAS,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACjD,IAAI,UAAU,WACZ,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAClC,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU,SACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YACJ,WAAU;YACV,SAAQ;sBACT;;;;;;QAIL,IAAI,UAAU,WACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YACJ,WAAU;YACV,SAAQ;sBACT;;;;;;QAIL,qBACE,8OAAC,iIAAA,CAAA,QAAK;YACJ,WAAU;YACV,SAAQ;sBACT;;;;;;IAIL;IAEA,qBACE,8OAAC,0JAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAqC;;;;;;sCAG1D,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAGnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,eAAe;gCAAmB,OAAO,SAAS;;kDACxD,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAU;;;;;;0DAC5B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMjC,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4IAAA,CAAA,eAAY;oDACX,YAAW;oDACX,WAAW;oDACX,aAAY;oDACZ,SAAS;oDACT,MAAK;8DACN;;;;;;8DAGD,8OAAC,4IAAA,CAAA,eAAY;oDACX,YAAW;oDACX,SAAS;oDACT,MAAK;8DACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAOP,aAAa,2BACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;+BAEpB,sBACF,8OAAC,6IAAA,CAAA,eAAY;wBAAC,SAAS,MAAM,OAAO;wBAAE,SAAS;;;;;+BAC7C,UAAU,OAAO,MAAM,GAAG,kBAC5B;;0CACE,8OAAC,0IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;4CACC,WAAU;;8DAGV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,aAAa,MAAM,KAAK;8EACzB,8OAAC;oEAAK,WAAU;8EAAe,MAAM,OAAO;;;;;;;;;;;;wDAE7C,cAAc,MAAM,KAAK;;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc;;;;;;wDAE1C,MAAM,MAAM,kBACX,8OAAC;4DAAI,WAAU;;gEAAgC;gEACpC,MAAM,MAAM;;;;;;;;;;;;;gDAI1B,MAAM,OAAO,IACZ,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE,MAAM,GAAG,mBAClC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,KAAK,SAAS,CAAC,MAAM,OAAO,EAAE,MAAM;;;;;;;;;;;;2CAvBxC,MAAM,EAAE;;;;;;;;;;;;;;;4BAgCpB,aAAa,mBACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAA,CAAA,eAAY;wCACX,YAAW;wCACX,UAAU,CAAC,eAAe,aAAa;wCACvC,SAAS;wCACT,MAAK;kDACN;;;;;;kDAGD,8OAAC;wCAAK,WAAU;;4CAAgC;4CACxC;4CAAK;4CAAK;;;;;;;kDAElB,8OAAC,4IAAA,CAAA,eAAY;wCACX,YAAW;wCACX,UAAU,CAAC,eAAe,aAAa;wCACvC,SAAS;wCACT,MAAK;kDACN;;;;;;;;;;;;;qDAOP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;0CAAE;;;;;;4BACF,uBACC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;8BAOpC,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,WAAU;wBACV,oBAAM,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAC3B,WAAW,cAAc;wBACzB,aAAY;wBACZ,SAAS;wBACT,MAAK;kCACN;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 4078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/admin/ErrorLogDebug.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ug, <PERSON>fresh<PERSON><PERSON>, <PERSON>, Trash2 } from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { adminCache } from '@/lib/api/services/admin';\r\nimport { CircuitState } from '@/lib/utils/circuitBreaker';\r\nimport type { CircuitBreakerStatus } from '@/lib/types/domain';\r\n\r\n/**\r\n * Debug component for error logs troubleshooting\r\n */\r\nexport function ErrorLogDebug() {\r\n  const [debugInfo, setDebugInfo] = useState<any>(null);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleGetDebugInfo = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Get circuit breaker status\r\n      const circuitBreakers: Record<string, CircuitBreakerStatus> = {}; // Mock implementation\r\n\r\n      // Get cache stats\r\n      const cacheStats = adminCache.getStats();\r\n\r\n      // Get error-specific cache entries\r\n      const errorCacheEntries: any[] = []; // Mock implementation since entries property doesn't exist\r\n\r\n      setDebugInfo({\r\n        cacheStats: {\r\n          errorCacheKeys: errorCacheEntries.map((e: any) => e.key || 'unknown'),\r\n          errorEntries: errorCacheEntries.length,\r\n          totalEntries: cacheStats.size,\r\n        },\r\n        circuitBreakers,\r\n        timestamp: new Date().toISOString(),\r\n      });\r\n    } catch (error) {\r\n      console.error('Failed to get debug info:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleResetCircuitBreakers = () => {\r\n    // Mock implementation - just clear all cache\r\n    adminCache.clearAll();\r\n    console.log('🔄 Circuit breakers reset');\r\n    handleGetDebugInfo(); // Refresh debug info\r\n  };\r\n\r\n  const handleClearErrorCache = () => {\r\n    // Mock implementation - just clear all cache\r\n    adminCache.clearAll();\r\n    console.log('🔄 Error cache cleared');\r\n    handleGetDebugInfo(); // Refresh debug info\r\n  };\r\n\r\n  const handleClearAllCache = () => {\r\n    adminCache.clearAll();\r\n    console.log('🔄 All cache cleared');\r\n    handleGetDebugInfo(); // Refresh debug info\r\n  };\r\n\r\n  const getCircuitBreakerColor = (state: CircuitState) => {\r\n    switch (state) {\r\n      case CircuitState.CLOSED: {\r\n        return 'bg-green-100 text-green-800';\r\n      }\r\n      case CircuitState.HALF_OPEN: {\r\n        return 'bg-yellow-100 text-yellow-800';\r\n      }\r\n      case CircuitState.OPEN: {\r\n        return 'bg-red-100 text-red-800';\r\n      }\r\n      default: {\r\n        return 'bg-gray-100 text-gray-800';\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className=\"shadow-md\">\r\n      <CardHeader className=\"p-5\">\r\n        <CardTitle className=\"flex items-center gap-2 text-xl font-semibold text-primary\">\r\n          <Bug className=\"size-5\" />\r\n          Error Logs Debug\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"p-5\">\r\n        <div className=\"space-y-4\">\r\n          {/* Action Buttons */}\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <Button\r\n              className=\"flex items-center gap-2\"\r\n              disabled={loading}\r\n              onClick={handleGetDebugInfo}\r\n            >\r\n              <RefreshCw\r\n                className={`size-4 ${loading ? 'animate-spin' : ''}`}\r\n              />\r\n              Get Debug Info\r\n            </Button>\r\n            <Button\r\n              className=\"flex items-center gap-2 text-orange-600 hover:text-orange-700\"\r\n              onClick={handleResetCircuitBreakers}\r\n              variant=\"outline\"\r\n            >\r\n              <Shield className=\"size-4\" />\r\n              Reset Circuit Breakers\r\n            </Button>\r\n            <Button\r\n              className=\"flex items-center gap-2 text-blue-600 hover:text-blue-700\"\r\n              onClick={handleClearErrorCache}\r\n              variant=\"outline\"\r\n            >\r\n              <Trash2 className=\"size-4\" />\r\n              Clear Error Cache\r\n            </Button>\r\n            <Button\r\n              className=\"flex items-center gap-2 text-red-600 hover:text-red-700\"\r\n              onClick={handleClearAllCache}\r\n              variant=\"outline\"\r\n            >\r\n              <Trash2 className=\"size-4\" />\r\n              Clear All Cache\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Debug Information */}\r\n          {debugInfo && (\r\n            <div className=\"space-y-4\">\r\n              {/* Circuit Breaker Status */}\r\n              <div className=\"rounded-lg bg-gray-50 p-4\">\r\n                <h4 className=\"mb-3 flex items-center gap-2 font-semibold\">\r\n                  <Shield className=\"size-4\" />\r\n                  Circuit Breaker Status\r\n                </h4>\r\n                <div className=\"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4\">\r\n                  {Object.entries(debugInfo.circuitBreakers).map(\r\n                    ([name, status]: [string, any]) => (\r\n                      <div className=\"rounded border bg-white p-3\" key={name}>\r\n                        <div className=\"mb-2 flex items-center justify-between\">\r\n                          <span className=\"text-sm font-medium capitalize\">\r\n                            {name}\r\n                          </span>\r\n                          <Badge\r\n                            className={getCircuitBreakerColor(status.state)}\r\n                          >\r\n                            {status.state}\r\n                          </Badge>\r\n                        </div>\r\n                        <div className=\"text-xs text-muted-foreground\">\r\n                          <div>Failures: {status.failureCount}</div>\r\n                          {status.timeUntilRetry && (\r\n                            <div>\r\n                              Retry in:{' '}\r\n                              {Math.round(status.timeUntilRetry / 1000)}s\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    )\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Cache Status */}\r\n              <div className=\"rounded-lg bg-blue-50 p-4\">\r\n                <h4 className=\"mb-3 font-semibold text-blue-900\">\r\n                  Cache Status\r\n                </h4>\r\n                <div className=\"grid grid-cols-2 gap-4 text-sm md:grid-cols-3\">\r\n                  <div>\r\n                    <span className=\"font-medium text-blue-800\">\r\n                      Total Entries:\r\n                    </span>\r\n                    <span className=\"ml-2 text-blue-600\">\r\n                      {debugInfo.cacheStats.totalEntries}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    <span className=\"font-medium text-blue-800\">\r\n                      Error Entries:\r\n                    </span>\r\n                    <span className=\"ml-2 text-blue-600\">\r\n                      {debugInfo.cacheStats.errorEntries}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    <span className=\"font-medium text-blue-800\">\r\n                      Last Updated:\r\n                    </span>\r\n                    <span className=\"ml-2 text-blue-600\">\r\n                      {new Date(debugInfo.timestamp).toLocaleTimeString()}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                {debugInfo.cacheStats.errorCacheKeys.length > 0 && (\r\n                  <div className=\"mt-3\">\r\n                    <div className=\"mb-2 font-medium text-blue-800\">\r\n                      Error Cache Keys:\r\n                    </div>\r\n                    <div className=\"space-y-1\">\r\n                      {debugInfo.cacheStats.errorCacheKeys.map(\r\n                        (key: string, index: number) => (\r\n                          <div\r\n                            className=\"rounded border bg-white p-2 font-mono text-xs\"\r\n                            key={index}\r\n                          >\r\n                            {key}\r\n                          </div>\r\n                        )\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Troubleshooting Tips */}\r\n              <div className=\"rounded-lg bg-yellow-50 p-4\">\r\n                <h4 className=\"mb-3 flex items-center gap-2 font-semibold text-yellow-900\">\r\n                  <AlertTriangle className=\"size-4\" />\r\n                  Troubleshooting Tips\r\n                </h4>\r\n                <ul className=\"space-y-1 text-sm text-yellow-800\">\r\n                  <li>\r\n                    • If circuit breakers are OPEN (red), reset them and try\r\n                    again\r\n                  </li>\r\n                  <li>\r\n                    • If error cache is stale, clear it to force fresh data\r\n                  </li>\r\n                  <li>• Check browser console for network errors</li>\r\n                  <li>• Verify backend server is running and accessible</li>\r\n                  <li>• Check if rate limiting is affecting requests</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!debugInfo && (\r\n            <div className=\"py-8 text-center text-muted-foreground\">\r\n              <Bug className=\"mx-auto mb-3 size-12 opacity-50\" />\r\n              <p>\r\n                Click \"Get Debug Info\" to see error logs debugging information\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;AAeO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YACF,6BAA6B;YAC7B,MAAM,kBAAwD,CAAC,GAAG,sBAAsB;YAExF,kBAAkB;YAClB,MAAM,aAAa,sJAAA,CAAA,aAAU,CAAC,QAAQ;YAEtC,mCAAmC;YACnC,MAAM,oBAA2B,EAAE,EAAE,2DAA2D;YAEhG,aAAa;gBACX,YAAY;oBACV,gBAAgB,kBAAkB,GAAG,CAAC,CAAC,IAAW,EAAE,GAAG,IAAI;oBAC3D,cAAc,kBAAkB,MAAM;oBACtC,cAAc,WAAW,IAAI;gBAC/B;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B;QACjC,6CAA6C;QAC7C,sJAAA,CAAA,aAAU,CAAC,QAAQ;QACnB,QAAQ,GAAG,CAAC;QACZ,sBAAsB,qBAAqB;IAC7C;IAEA,MAAM,wBAAwB;QAC5B,6CAA6C;QAC7C,sJAAA,CAAA,aAAU,CAAC,QAAQ;QACnB,QAAQ,GAAG,CAAC;QACZ,sBAAsB,qBAAqB;IAC7C;IAEA,MAAM,sBAAsB;QAC1B,sJAAA,CAAA,aAAU,CAAC,QAAQ;QACnB,QAAQ,GAAG,CAAC;QACZ,sBAAsB,qBAAqB;IAC7C;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK,qIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE;oBACxB,OAAO;gBACT;YACA,KAAK,qIAAA,CAAA,eAAY,CAAC,SAAS;gBAAE;oBAC3B,OAAO;gBACT;YACA,KAAK,qIAAA,CAAA,eAAY,CAAC,IAAI;gBAAE;oBACtB,OAAO;gBACT;YACA;gBAAS;oBACP,OAAO;gBACT;QACF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBAAW;;;;;;;;;;;;0BAI9B,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU;oCACV,SAAS;;sDAET,8OAAC,gNAAA,CAAA,YAAS;4CACR,WAAW,CAAC,OAAO,EAAE,UAAU,iBAAiB,IAAI;;;;;;wCACpD;;;;;;;8CAGJ,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,SAAQ;;sDAER,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,SAAQ;;sDAER,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,SAAQ;;sDAER,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;;;;;;;wBAMhC,2BACC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAW;;;;;;;sDAG/B,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,UAAU,eAAe,EAAE,GAAG,CAC5C,CAAC,CAAC,MAAM,OAAsB,iBAC5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACb;;;;;;8EAEH,8OAAC,iIAAA,CAAA,QAAK;oEACJ,WAAW,uBAAuB,OAAO,KAAK;8EAE7C,OAAO,KAAK;;;;;;;;;;;;sEAGjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAI;wEAAW,OAAO,YAAY;;;;;;;gEAClC,OAAO,cAAc,kBACpB,8OAAC;;wEAAI;wEACO;wEACT,KAAK,KAAK,CAAC,OAAO,cAAc,GAAG;wEAAM;;;;;;;;;;;;;;mDAhBA;;;;;;;;;;;;;;;;8CA2B1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAG5C,8OAAC;4DAAK,WAAU;sEACb,UAAU,UAAU,CAAC,YAAY;;;;;;;;;;;;8DAGtC,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAG5C,8OAAC;4DAAK,WAAU;sEACb,UAAU,UAAU,CAAC,YAAY;;;;;;;;;;;;8DAGtC,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAG5C,8OAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,UAAU,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;wCAKtD,UAAU,UAAU,CAAC,cAAc,CAAC,MAAM,GAAG,mBAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAGhD,8OAAC;oDAAI,WAAU;8DACZ,UAAU,UAAU,CAAC,cAAc,CAAC,GAAG,CACtC,CAAC,KAAa,sBACZ,8OAAC;4DACC,WAAU;sEAGT;2DAFI;;;;;;;;;;;;;;;;;;;;;;8CAYnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAW;;;;;;;sDAGtC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DAIJ,8OAAC;8DAAG;;;;;;8DAGJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,CAAC,2BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjB", "debugId": null}}, {"offset": {"line": 4638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/admin/SupabaseDiagnostics.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  Activity,\r\n  AlertTriangle,\r\n  ArrowRight,\r\n  Database,\r\n  HardDrive,\r\n  Monitor,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\n\r\nimport { CacheStatus } from './CacheStatus';\r\nimport { ErrorLog } from './ErrorLog';\r\nimport { ErrorLogDebug } from './ErrorLogDebug';\r\n\r\nexport function SupabaseDiagnostics() {\r\n  return (\r\n    <Card className=\"border-none shadow-none\">\r\n      <CardHeader className=\"px-0 pt-0\">\r\n        <CardTitle className=\"text-2xl font-bold\">\r\n          Supabase Diagnostics\r\n        </CardTitle>\r\n        <CardDescription>\r\n          Monitor and troubleshoot your Supabase database connection\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"w-full max-w-full overflow-hidden px-0\">\r\n        {/* Migration Notice */}\r\n        <Alert className=\"mb-6\">\r\n          <Monitor className=\"h-4 w-4\" />\r\n          <AlertTitle>Enhanced Reliability Dashboard Available</AlertTitle>\r\n          <AlertDescription>\r\n            System health and performance monitoring has been moved to the new\r\n            Reliability Dashboard with real-time updates, advanced metrics, and\r\n            comprehensive monitoring capabilities.\r\n            <div className=\"mt-3\">\r\n              <Button asChild variant=\"outline\" size=\"sm\">\r\n                <Link href=\"/reliability\" className=\"flex items-center gap-2\">\r\n                  <Monitor className=\"h-4 w-4\" />\r\n                  Go to Reliability Dashboard\r\n                  <ArrowRight className=\"h-4 w-4\" />\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n          </AlertDescription>\r\n        </Alert>\r\n\r\n        <Tabs\r\n          className=\"w-full max-w-full overflow-hidden\"\r\n          defaultValue=\"errors\"\r\n        >\r\n          <TabsList className=\"grid w-full grid-cols-2\">\r\n            <TabsTrigger className=\"flex items-center\" value=\"errors\">\r\n              <AlertTriangle className=\"mr-2 size-4\" />\r\n              <span className=\"hidden sm:inline\">Error Logs</span>\r\n            </TabsTrigger>\r\n            <TabsTrigger className=\"flex items-center\" value=\"cache\">\r\n              <HardDrive className=\"mr-2 size-4\" />\r\n              <span className=\"hidden sm:inline\">Cache Status</span>\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent className=\"mt-4\" value=\"errors\">\r\n            <div className=\"space-y-6\">\r\n              <ErrorLog />\r\n              <ErrorLogDebug />\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent className=\"mt-4\" value=\"cache\">\r\n            <CacheStatus />\r\n          </TabsContent>\r\n        </Tabs>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAQA;AAEA;AACA;AACA;AAOA;AAEA;AACA;AACA;AAzBA;;;;;;;;;;;AA2BO,SAAS;IACd,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAqB;;;;;;kCAG1C,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;;oCAAC;kDAIhB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAU,MAAK;sDACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;;kEAClC,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;kEAE/B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhC,8OAAC,gIAAA,CAAA,OAAI;wBACH,WAAU;wBACV,cAAa;;0CAEb,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;wCAAoB,OAAM;;0DAC/C,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;wCAAoB,OAAM;;0DAC/C,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;0CAIvC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;gCAAO,OAAM;0CAClC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mJAAA,CAAA,WAAQ;;;;;sDACT,8OAAC,wJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;0CAIlB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;gCAAO,OAAM;0CAClC,cAAA,8OAAC,sJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 4897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\r\nimport * as React from 'react';\r\n\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root;\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger;\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal;\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n));\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName;\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      className={cn(\r\n        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n));\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName;\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col space-y-2 text-center sm:text-left',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nAlertDialogHeader.displayName = 'AlertDialogHeader';\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nAlertDialogFooter.displayName = 'AlertDialogFooter';\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    className={cn('text-lg font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName;\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    className={cn('text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName;\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    className={cn(buttonVariants(), className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName;\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    className={cn(\r\n      buttonVariants({ variant: 'outline' }),\r\n      'mt-2 sm:mt-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName;\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogOverlay,\r\n  AlertDialogPortal,\r\n  AlertDialogTitle,\r\n  AlertDialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AAAA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACvC,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAChC,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/services/WebSocketManager.ts"], "sourcesContent": ["/**\r\n * @file Unified WebSocket Manager for WorkHub Application\r\n * Provides centralized WebSocket connection management with domain-specific channels\r\n * Follows SRP and DRY principles with smart fallback strategies\r\n * @module services/WebSocketManager\r\n */\r\n\r\nimport type { Socket } from 'socket.io-client';\r\n\r\nimport { io } from 'socket.io-client';\r\n\r\nimport { getEnvironmentConfig } from '../config/environment';\r\nimport { supabase } from '../supabase';\r\nimport { getTokenRefreshService } from './TokenRefreshService';\r\n/*import logger from '../utils/logger';\r\n\r\n/**\r\n * WebSocket connection states\r\n */\r\nexport type ConnectionState =\r\n  | 'connected'\r\n  | 'connecting'\r\n  | 'disconnected'\r\n  | 'error'\r\n  | 'reconnecting';\r\n\r\n/**\r\n * Domain-specific channels for organized event management\r\n */\r\nexport type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';\r\n\r\n/**\r\n * Event subscription callback type\r\n */\r\nexport type EventCallback<T = any> = (data: T) => void;\r\n\r\n/**\r\n * WebSocket configuration options\r\n */\r\nexport interface WebSocketConfig {\r\n  autoConnect?: boolean;\r\n  reconnectAttempts?: number;\r\n  reconnectDelay?: number;\r\n  timeout?: number;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Unified WebSocket Manager\r\n * Implements Singleton pattern for single connection per application\r\n * Provides domain-specific channels and centralized subscription management\r\n */\r\nexport class WebSocketManager {\r\n  private static instance: null | WebSocketManager = null;\r\n  private readonly config: Required<WebSocketConfig>;\r\n  private connectionState: ConnectionState = 'disconnected';\r\n  private reconnectAttempts = 0;\r\n  private socket: null | Socket = null;\r\n  private readonly stateListeners = new Set<(state: ConnectionState) => void>();\r\n  private readonly subscriptions = new Map<string, Set<EventCallback>>();\r\n\r\n  private constructor(config: WebSocketConfig = {}) {\r\n    this.config = {\r\n      autoConnect: config.autoConnect ?? true,\r\n      reconnectAttempts: config.reconnectAttempts ?? 5,\r\n      reconnectDelay: config.reconnectDelay ?? 1000,\r\n      timeout: config.timeout ?? 10_000,\r\n      url:\r\n        config.url ??\r\n        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??\r\n        getEnvironmentConfig()\r\n          .wsUrl.replace('ws://', 'http://')\r\n          .replace('wss://', 'https://'),\r\n    };\r\n\r\n    if (this.config.autoConnect) {\r\n      this.connect();\r\n    }\r\n\r\n    // Subscribe to token refresh events\r\n    this.setupTokenRefreshHandling();\r\n  }\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  public static getInstance(config?: WebSocketConfig): WebSocketManager {\r\n    WebSocketManager.instance ??= new WebSocketManager(config);\r\n    return WebSocketManager.instance;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  public async connect(): Promise<void> {\r\n    if (this.socket?.connected) {\r\n      console.debug('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('connecting');\r\n\r\n    try {\r\n      // Get current session and token for authentication\r\n      const {\r\n        data: { session },\r\n        error,\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (error) {\r\n        console.warn('Failed to get session for WebSocket connection:', error);\r\n      }\r\n\r\n      const connectionOptions: any = {\r\n        forceNew: true,\r\n        timeout: this.config.timeout,\r\n        transports: ['websocket', 'polling'],\r\n        withCredentials: true, // Ensure cookies are sent with WebSocket handshake\r\n      };\r\n\r\n      // Add authentication token if available\r\n      if (session?.access_token) {\r\n        connectionOptions.auth = {\r\n          token: session.access_token,\r\n        };\r\n        console.debug('🔐 WebSocket connecting with authentication token');\r\n\r\n        // Validate token expiration\r\n        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;\r\n        const now = Date.now();\r\n        const timeUntilExpiry = tokenExpiry - now;\r\n\r\n        if (timeUntilExpiry <= 60_000) {\r\n          // Less than 1 minute\r\n          console.warn('⚠️ WebSocket token expires soon, may need refresh');\r\n        }\r\n      } else {\r\n        console.warn(\r\n          '⚠️ WebSocket connecting without authentication token - connection may fail'\r\n        );\r\n      }\r\n\r\n      this.socket = io(this.config.url, connectionOptions);\r\n\r\n      this.setupEventHandlers();\r\n    } catch (error) {\r\n      console.error('Failed to connect WebSocket:', error);\r\n      this.setConnectionState('error');\r\n      this.scheduleReconnect();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  public destroy(): void {\r\n    this.disconnect();\r\n    this.subscriptions.clear();\r\n    this.stateListeners.clear();\r\n    WebSocketManager.instance = null;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n    this.setConnectionState('disconnected');\r\n    this.reconnectAttempts = 0;\r\n  }\r\n\r\n  /**\r\n   * Emit event to specific domain channel\r\n   */\r\n  public emit(channel: DomainChannel, event: string, data?: any): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit(event, data);\r\n  }\r\n\r\n  /**\r\n   * Get current connection state\r\n   */\r\n  public getConnectionState(): ConnectionState {\r\n    return this.connectionState;\r\n  }\r\n\r\n  /**\r\n   * Check if WebSocket is connected\r\n   */\r\n  public isConnected(): boolean {\r\n    return (\r\n      this.connectionState === 'connected' && this.socket?.connected === true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Join domain-specific room\r\n   */\r\n  public joinRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot join room ${room} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('join-room', room);\r\n  }\r\n\r\n  /**\r\n   * Leave domain-specific room\r\n   */\r\n  public leaveRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('leave-room', room);\r\n  }\r\n\r\n  /**\r\n   * Subscribe to connection state changes\r\n   */\r\n  public onStateChange(callback: (state: ConnectionState) => void): () => void {\r\n    this.stateListeners.add(callback);\r\n\r\n    return () => {\r\n      this.stateListeners.delete(callback);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to domain-specific events\r\n   */\r\n  public subscribe<T = any>(\r\n    channel: DomainChannel,\r\n    event: string,\r\n    callback: EventCallback<T>\r\n  ): () => void {\r\n    const eventKey = `${channel}:${event}`;\r\n\r\n    if (!this.subscriptions.has(eventKey)) {\r\n      this.subscriptions.set(eventKey, new Set());\r\n    }\r\n\r\n    this.subscriptions.get(eventKey)!.add(callback);\r\n\r\n    // Set up socket listener if connected\r\n    if (this.socket?.connected && event) {\r\n      this.socket.on(event, callback);\r\n    }\r\n\r\n    // Return unsubscribe function\r\n    return () => {\r\n      const callbacks = this.subscriptions.get(eventKey);\r\n      if (callbacks) {\r\n        callbacks.delete(callback);\r\n        if (callbacks.size === 0) {\r\n          this.subscriptions.delete(eventKey);\r\n        }\r\n      }\r\n\r\n      if (this.socket && event) {\r\n        this.socket.off(event, callback);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Handle authentication errors by triggering token refresh\r\n   */\r\n  private handleAuthenticationError(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    console.log('🔐 Handling WebSocket authentication error...');\r\n\r\n    // Disconnect current socket to prevent further auth errors\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n\r\n    // Attempt to refresh token\r\n    tokenRefreshService\r\n      .refreshNow()\r\n      .then(success => {\r\n        if (success) {\r\n          console.log(\r\n            '🔄 Token refresh successful, retrying WebSocket connection'\r\n          );\r\n          // The reconnection will be handled by setupTokenRefreshHandling\r\n        } else {\r\n          console.error('🔄 Token refresh failed, scheduling normal reconnect');\r\n          this.scheduleReconnect();\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('🔄 Token refresh error:', error);\r\n        this.scheduleReconnect();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resubscribe to all events after reconnection\r\n   */\r\n  private resubscribeToEvents(): void {\r\n    if (!this.socket) return;\r\n\r\n    for (const [eventKey, callbacks] of this.subscriptions) {\r\n      const [, event] = eventKey.split(':');\r\n      for (const callback of callbacks) {\r\n        if (event) {\r\n          this.socket!.on(event, callback);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection with exponential backoff\r\n   */\r\n  private scheduleReconnect(): void {\r\n    if (this.reconnectAttempts >= this.config.reconnectAttempts) {\r\n      console.error('Max reconnection attempts reached');\r\n      this.setConnectionState('error');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('reconnecting');\r\n    this.reconnectAttempts++;\r\n\r\n    const delay =\r\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\r\n\r\n    setTimeout(() => {\r\n      console.info(\r\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`\r\n      );\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * Set connection state and notify listeners\r\n   */\r\n  private setConnectionState(state: ConnectionState): void {\r\n    if (this.connectionState !== state) {\r\n      this.connectionState = state;\r\n      for (const listener of this.stateListeners) listener(state);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup socket event handlers\r\n   */\r\n  private setupEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      console.info('WebSocket connected');\r\n      this.setConnectionState('connected');\r\n      this.reconnectAttempts = 0;\r\n      this.resubscribeToEvents();\r\n    });\r\n\r\n    this.socket.on('disconnect', reason => {\r\n      console.warn('WebSocket disconnected:', reason);\r\n      this.setConnectionState('disconnected');\r\n\r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, don't reconnect automatically\r\n        return;\r\n      }\r\n\r\n      this.scheduleReconnect();\r\n    });\r\n\r\n    this.socket.on('connect_error', error => {\r\n      console.error('WebSocket connection error:', error);\r\n      this.setConnectionState('error');\r\n\r\n      // Check if error is authentication-related\r\n      if (\r\n        error.message?.includes('Authentication') ||\r\n        error.message?.includes('token') ||\r\n        error.message?.includes('No token provided') ||\r\n        error.message?.includes('Unauthorized')\r\n      ) {\r\n        console.warn(\r\n          '🔐 Authentication error detected, attempting token refresh'\r\n        );\r\n        this.handleAuthenticationError();\r\n      } else {\r\n        this.scheduleReconnect();\r\n      }\r\n    });\r\n\r\n    // Listen for authentication errors from the server\r\n    this.socket.on('auth_error', errorData => {\r\n      console.error('🔐 Server authentication error:', errorData);\r\n      this.handleAuthenticationError();\r\n    });\r\n\r\n    // Listen for token refresh requests from server\r\n    this.socket.on('token_refresh_required', () => {\r\n      console.warn('🔄 Server requested token refresh');\r\n      this.handleAuthenticationError();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup token refresh event handling\r\n   */\r\n  private setupTokenRefreshHandling(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    tokenRefreshService.subscribe((event, _data) => {\r\n      switch (event) {\r\n        case 'critical_refresh_failed': {\r\n          console.error(\r\n            '🔄 Critical token refresh failure, disconnecting WebSocket'\r\n          );\r\n          this.disconnect();\r\n          this.setConnectionState('error');\r\n          break;\r\n        }\r\n\r\n        case 'refresh_failed': {\r\n          console.error(\r\n            '🔄 Token refresh failed, WebSocket may lose connection'\r\n          );\r\n          break;\r\n        }\r\n\r\n        case 'refresh_success': {\r\n          console.log(\r\n            '🔄 Token refreshed, reconnecting WebSocket with new token'\r\n          );\r\n          // Disconnect current connection and reconnect with new token\r\n          if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n          }\r\n          // Reconnect with fresh token\r\n          setTimeout(() => this.connect(), 500);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the singleton WebSocket manager instance\r\n */\r\nexport const getWebSocketManager = (\r\n  config?: WebSocketConfig\r\n): WebSocketManager => {\r\n  return WebSocketManager.getInstance(config);\r\n};\r\n\r\n/**\r\n * Hook for WebSocket connection state\r\n */\r\nexport const useWebSocketState = () => {\r\n  const manager = getWebSocketManager();\r\n  return {\r\n    connectionState: manager.getConnectionState(),\r\n    isConnected: manager.isConnected(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAID;AAAA;AAEA;AACA;AACA;;;;;AAuCO,MAAM;IACX,OAAe,WAAoC,KAAK;IACvC,OAAkC;IAC3C,kBAAmC,eAAe;IAClD,oBAAoB,EAAE;IACtB,SAAwB,KAAK;IACpB,iBAAiB,IAAI,MAAwC;IAC7D,gBAAgB,IAAI,MAAkC;IAEvE,YAAoB,SAA0B,CAAC,CAAC,CAAE;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,OAAO,WAAW,IAAI;YACnC,mBAAmB,OAAO,iBAAiB,IAAI;YAC/C,gBAAgB,OAAO,cAAc,IAAI;YACzC,SAAS,OAAO,OAAO,IAAI;YAC3B,KACE,OAAO,GAAG,IACV,QAAQ,GAAG,CAAC,yBAAyB,IACrC,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD,IAChB,KAAK,CAAC,OAAO,CAAC,SAAS,WACvB,OAAO,CAAC,UAAU;QACzB;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,OAAO;QACd;QAEA,oCAAoC;QACpC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,OAAc,YAAY,MAAwB,EAAoB;QACpE,iBAAiB,QAAQ,KAAK,IAAI,iBAAiB;QACnD,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QAExB,IAAI;YACF,mDAAmD;YACnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EACjB,KAAK,EACN,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,mDAAmD;YAClE;YAEA,MAAM,oBAAyB;gBAC7B,UAAU;gBACV,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,iBAAiB;YACnB;YAEA,wCAAwC;YACxC,IAAI,SAAS,cAAc;gBACzB,kBAAkB,IAAI,GAAG;oBACvB,OAAO,QAAQ,YAAY;gBAC7B;gBACA,QAAQ,KAAK,CAAC;gBAEd,4BAA4B;gBAC5B,MAAM,cAAc,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO;gBACrE,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,kBAAkB,cAAc;gBAEtC,IAAI,mBAAmB,QAAQ;oBAC7B,qBAAqB;oBACrB,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CACV;YAEJ;YAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAElC,IAAI,CAAC,kBAAkB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,iBAAiB,QAAQ,GAAG;IAC9B;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAO,KAAK,OAAsB,EAAE,KAAa,EAAE,IAAU,EAAQ;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,0BAA0B,CAAC;YACxE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAsC;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OACE,IAAI,CAAC,eAAe,KAAK,eAAe,IAAI,CAAC,MAAM,EAAE,cAAc;IAEvE;IAEA;;GAEC,GACD,AAAO,SAAS,IAAY,EAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,0BAA0B,CAAC;YACjE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA;;GAEC,GACD,AAAO,UAAU,IAAY,EAAQ;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IACjC;IAEA;;GAEC,GACD,AAAO,cAAc,QAA0C,EAAc;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAO,UACL,OAAsB,EACtB,KAAa,EACb,QAA0B,EACd;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,OAAO;QAEtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI;QACvC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAW,GAAG,CAAC;QAEtC,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,OAAO;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;QAEA,8BAA8B;QAC9B,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACzC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5B;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YACzB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;QAEjD,QAAQ,GAAG,CAAC;QAEZ,2DAA2D;QAC3D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,2BAA2B;QAC3B,oBACG,UAAU,GACV,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS;gBACX,QAAQ,GAAG,CACT;YAEF,gEAAgE;YAClE,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,iBAAiB;YACxB;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,iBAAiB;QACxB;IACJ;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,KAAK,MAAM,CAAC,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,CAAE;YACtD,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,OAAO;oBACT,IAAI,CAAC,MAAM,CAAE,EAAE,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAC3D,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QACJ,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEpE,WAAW;YACT,QAAQ,IAAI,CACV,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAsB,EAAQ;QACvD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAE,SAAS;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,IAAI,CAAC,2BAA2B;YACxC,IAAI,CAAC,kBAAkB,CAAC;YAExB,IAAI,WAAW,wBAAwB;gBACrC,6DAA6D;gBAC7D;YACF;YAEA,IAAI,CAAC,iBAAiB;QACxB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAA;YAC9B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,kBAAkB,CAAC;YAExB,2CAA2C;YAC3C,IACE,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,wBACxB,MAAM,OAAO,EAAE,SAAS,iBACxB;gBACA,QAAQ,IAAI,CACV;gBAEF,IAAI,CAAC,yBAAyB;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB;YACxB;QACF;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,yBAAyB;QAChC;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0BAA0B;YACvC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,yBAAyB;QAChC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;QAEjD,oBAAoB,SAAS,CAAC,CAAC,OAAO;YACpC,OAAQ;gBACN,KAAK;oBAA2B;wBAC9B,QAAQ,KAAK,CACX;wBAEF,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,kBAAkB,CAAC;wBACxB;oBACF;gBAEA,KAAK;oBAAkB;wBACrB,QAAQ,KAAK,CACX;wBAEF;oBACF;gBAEA,KAAK;oBAAmB;wBACtB,QAAQ,GAAG,CACT;wBAEF,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,IAAI,CAAC,MAAM,CAAC,UAAU;4BACtB,IAAI,CAAC,MAAM,GAAG;wBAChB;wBACA,6BAA6B;wBAC7B,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI;wBACjC;oBACF;YACF;QACF;IACF;AACF;AAKO,MAAM,sBAAsB,CACjC;IAEA,OAAO,iBAAiB,WAAW,CAAC;AACtC;AAKO,MAAM,oBAAoB;IAC/B,MAAM,UAAU;IAChB,OAAO;QACL,iBAAiB,QAAQ,kBAAkB;QAC3C,aAAa,QAAQ,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 5400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/api/useSmartQuery.ts"], "sourcesContent": ["/**\r\n * @file Smart Query Hook with WebSocket Integration\r\n * Automatically disables polling when WebSocket is connected\r\n * Follows modern best practices for real-time data management\r\n * @module hooks/useSmartQuery\r\n */\r\n\r\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\r\n\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useEffect, useState } from 'react';\r\n\r\nimport type { DomainChannel } from '../../lib/services/WebSocketManager';\r\n\r\nimport { getWebSocketManager } from '../../lib/services/WebSocketManager';\r\n\r\n/**\r\n * Mapping of domain channels to Socket.IO room names\r\n * This ensures the frontend joins the correct rooms that the backend emits events to\r\n */\r\nconst CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {\r\n  crud: 'entity-updates',\r\n  notifications: 'notifications-monitoring',\r\n  reliability: 'reliability-monitoring',\r\n  system: 'system-monitoring',\r\n} as const;\r\n\r\n/**\r\n * Smart query configuration for WebSocket integration\r\n * @template T - The data type returned by the query function\r\n */\r\nexport interface SmartQueryConfig<T = unknown> {\r\n  /**\r\n   * Domain channel for WebSocket events\r\n   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING\r\n   */\r\n  channel: DomainChannel;\r\n  /** Whether to enable fallback polling when WebSocket is disconnected */\r\n  enableFallback?: boolean;\r\n  /** Whether to enable WebSocket integration and room joining */\r\n  enableWebSocket?: boolean;\r\n  /** Events that should trigger data refetch when received via WebSocket */\r\n  events: string[];\r\n  /** Fallback polling interval when WebSocket is disconnected (ms) */\r\n  fallbackInterval?: number;\r\n}\r\n\r\n/**\r\n * Hook for CRUD operations with smart real-time updates\r\n */\r\nexport function useCrudQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  entityType: string,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'crud',\r\n      events: [\r\n        `${entityType}:created`,\r\n        `${entityType}:updated`,\r\n        `${entityType}:deleted`,\r\n        `refresh:${entityType}`,\r\n      ],\r\n      fallbackInterval: 30_000,\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for system notifications with smart real-time updates\r\n */\r\nexport function useNotificationQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'notifications',\r\n      events: ['notification-created', 'notification-updated'],\r\n      fallbackInterval: 60_000, // 1 minute for notifications\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for reliability monitoring with smart real-time updates\r\n */\r\nexport function useReliabilityQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  // Increased intervals to reduce aggressive polling and cancellations\r\n  const intervalMap = {\r\n    alerts: 30_000, // 30 seconds for alerts (was 10s)\r\n    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)\r\n    health: 45_000, // 45 seconds for health (was 15s)\r\n    metrics: 60_000, // 60 seconds for metrics (was 30s)\r\n  };\r\n\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Join reliability monitoring room when WebSocket is connected\r\n  useEffect(() => {\r\n    if (webSocketManager.isConnected()) {\r\n      console.debug(\r\n        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`\r\n      );\r\n      webSocketManager.joinRoom('reliability-monitoring');\r\n    }\r\n\r\n    // Subscribe to connection state changes to join room when connected\r\n    const unsubscribe = webSocketManager.onStateChange(state => {\r\n      if (state === 'connected') {\r\n        console.debug(\r\n          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`\r\n        );\r\n        webSocketManager.joinRoom('reliability-monitoring');\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      // Leave room when component unmounts\r\n      if (webSocketManager.isConnected()) {\r\n        webSocketManager.leaveRoom('reliability-monitoring');\r\n      }\r\n    };\r\n  }, [webSocketManager, monitoringType]);\r\n\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'reliability',\r\n      events: [\r\n        `${monitoringType}-update`,\r\n        `${monitoringType}-created`,\r\n        `${monitoringType}-resolved`,\r\n      ],\r\n      fallbackInterval: intervalMap[monitoringType],\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Smart Query Hook with Socket.IO Room Management\r\n *\r\n * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.\r\n * This hook automatically:\r\n * - Joins the appropriate Socket.IO room based on the domain channel\r\n * - Subscribes to WebSocket events for real-time data updates\r\n * - Switches between WebSocket and polling based on connection state\r\n * - Handles room cleanup when component unmounts\r\n *\r\n * **Room Mapping:**\r\n * - `crud` channel → `entity-updates` room\r\n * - `reliability` channel → `reliability-monitoring` room\r\n * - `notifications` channel → `notifications-monitoring` room\r\n * - `system` channel → `system-monitoring` room\r\n *\r\n * @template T - The data type returned by the query function\r\n * @template E - The error type for failed queries\r\n * @param queryKey - React Query key for caching and invalidation\r\n * @param queryFn - Data fetching function that returns a Promise<T>\r\n * @param config - Smart query configuration including channel and events\r\n * @param options - Additional React Query options (merged with smart defaults)\r\n * @returns Enhanced query result with WebSocket integration and connection state\r\n */\r\nexport function useSmartQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  config: SmartQueryConfig<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n): UseQueryResult<T, E> & {\r\n  isUsingFallback: boolean;\r\n  isWebSocketConnected: boolean;\r\n} {\r\n  const {\r\n    channel,\r\n    enableFallback = true,\r\n    enableWebSocket = true,\r\n    events,\r\n    fallbackInterval = 30_000,\r\n  } = config;\r\n\r\n  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Track WebSocket connection state\r\n  useEffect(() => {\r\n    const updateConnectionState = () => {\r\n      setIsWebSocketConnected(webSocketManager.isConnected());\r\n    };\r\n\r\n    // Initial state\r\n    updateConnectionState();\r\n\r\n    // Subscribe to state changes\r\n    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);\r\n\r\n    return unsubscribe;\r\n  }, [webSocketManager]);\r\n\r\n  // Determine if we should use fallback polling\r\n  const isUsingFallback =\r\n    enableFallback && (!enableWebSocket || !isWebSocketConnected);\r\n\r\n  // Configure React Query options based on WebSocket state\r\n  const queryOptions: UseQueryOptions<T, E> = {\r\n    // Longer cache time for better performance\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    queryFn,\r\n    queryKey,\r\n    // Disable polling when WebSocket is connected\r\n    refetchInterval: isUsingFallback ? fallbackInterval : false,\r\n    refetchOnReconnect: true, // Always refetch on network reconnect\r\n    // Enable background refetch only when using fallback\r\n    refetchOnWindowFocus: isUsingFallback,\r\n    // Shorter stale time when using WebSocket (real-time updates)\r\n    staleTime: isWebSocketConnected ? 0 : 30_000,\r\n    ...options,\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryResult = useQuery<T, E>(queryOptions);\r\n\r\n  // Manage Socket.IO room joining/leaving based on channel\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected) {\r\n      return;\r\n    }\r\n\r\n    const roomName = CHANNEL_ROOM_MAPPING[channel];\r\n    if (!roomName) {\r\n      console.warn(\r\n        `[SmartQuery] No room mapping found for channel: ${channel}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Join the appropriate room for this channel\r\n    try {\r\n      webSocketManager.joinRoom(roomName);\r\n      console.log(\r\n        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`\r\n      );\r\n    } catch (error) {\r\n      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);\r\n    }\r\n\r\n    // Cleanup: leave room when component unmounts or dependencies change\r\n    return () => {\r\n      try {\r\n        webSocketManager.leaveRoom(roomName);\r\n        console.log(\r\n          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`\r\n        );\r\n      } catch (error) {\r\n        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);\r\n      }\r\n    };\r\n  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);\r\n\r\n  // Subscribe to WebSocket events for real-time updates\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const unsubscribers: (() => void)[] = [];\r\n\r\n    // Subscribe to each event\r\n    for (const event of events) {\r\n      const unsubscribe = webSocketManager.subscribe(\r\n        channel,\r\n        event,\r\n        (data: unknown) => {\r\n          console.log(\r\n            `[SmartQuery] WebSocket event received: ${channel}:${event}`,\r\n            data\r\n          );\r\n\r\n          // Invalidate the specific query to trigger refetch\r\n          queryClient.invalidateQueries({ queryKey });\r\n        }\r\n      );\r\n\r\n      unsubscribers.push(unsubscribe);\r\n    }\r\n\r\n    return () => {\r\n      for (const unsubscribe of unsubscribers) unsubscribe();\r\n    };\r\n  }, [\r\n    enableWebSocket,\r\n    isWebSocketConnected,\r\n    events,\r\n    channel,\r\n    webSocketManager,\r\n    queryClient,\r\n    queryKey,\r\n  ]);\r\n\r\n  return {\r\n    ...queryResult,\r\n    isUsingFallback,\r\n    isWebSocketConnected,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for system-wide events with smart real-time updates\r\n */\r\nexport function useSystemQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'system',\r\n      events: ['system-update', 'config-changed'],\r\n      fallbackInterval: 120_000, // 2 minutes for system events\r\n    },\r\n    options\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAID;AAAA;AACA;AAIA;;;;AAEA;;;CAGC,GACD,MAAM,uBAAsD;IAC1D,MAAM;IACN,eAAe;IACf,aAAa;IACb,QAAQ;AACV;AAyBO,SAAS,aACd,QAAmB,EACnB,OAAyB,EACzB,UAAkB,EAClB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,CAAC,QAAQ,EAAE,YAAY;SACxB;QACD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,qBACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAwB;SAAuB;QACxD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,oBACd,QAAmB,EACnB,OAAyB,EACzB,cAAoE,EACpE,OAA6D;IAE7D,qEAAqE;IACrE,MAAM,cAAc;QAClB,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,WAAW,IAAI;YAClC,QAAQ,KAAK,CACX,CAAC,2DAA2D,EAAE,gBAAgB;YAEhF,iBAAiB,QAAQ,CAAC;QAC5B;QAEA,oEAAoE;QACpE,MAAM,cAAc,iBAAiB,aAAa,CAAC,CAAA;YACjD,IAAI,UAAU,aAAa;gBACzB,QAAQ,KAAK,CACX,CAAC,gFAAgF,EAAE,gBAAgB;gBAErG,iBAAiB,QAAQ,CAAC;YAC5B;QACF;QAEA,OAAO;YACL;YACA,qCAAqC;YACrC,IAAI,iBAAiB,WAAW,IAAI;gBAClC,iBAAiB,SAAS,CAAC;YAC7B;QACF;IACF,GAAG;QAAC;QAAkB;KAAe;IAErC,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,eAAe,OAAO,CAAC;YAC1B,GAAG,eAAe,QAAQ,CAAC;YAC3B,GAAG,eAAe,SAAS,CAAC;SAC7B;QACD,kBAAkB,WAAW,CAAC,eAAe;IAC/C,GACA;AAEJ;AA0BO,SAAS,cACd,QAAmB,EACnB,OAAyB,EACzB,MAA2B,EAC3B,OAA6D;IAK7D,MAAM,EACJ,OAAO,EACP,iBAAiB,IAAI,EACrB,kBAAkB,IAAI,EACtB,MAAM,EACN,mBAAmB,MAAM,EAC1B,GAAG;IAEJ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,wBAAwB,iBAAiB,WAAW;QACtD;QAEA,gBAAgB;QAChB;QAEA,6BAA6B;QAC7B,MAAM,cAAc,iBAAiB,aAAa,CAAC;QAEnD,OAAO;IACT,GAAG;QAAC;KAAiB;IAErB,8CAA8C;IAC9C,MAAM,kBACJ,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,oBAAoB;IAE9D,yDAAyD;IACzD,MAAM,eAAsC;QAC1C,2CAA2C;QAC3C,QAAQ,KAAK,KAAK;QAClB;QACA;QACA,8CAA8C;QAC9C,iBAAiB,kBAAkB,mBAAmB;QACtD,oBAAoB;QACpB,qDAAqD;QACrD,sBAAsB;QACtB,8DAA8D;QAC9D,WAAW,uBAAuB,IAAI;QACtC,GAAG,OAAO;IACZ;IAEA,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAQ;IAEnC,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;YAC7C;QACF;QAEA,MAAM,WAAW,oBAAoB,CAAC,QAAQ;QAC9C,IAAI,CAAC,UAAU;YACb,QAAQ,IAAI,CACV,CAAC,gDAAgD,EAAE,SAAS;YAE9D;QACF;QAEA,6CAA6C;QAC7C,IAAI;YACF,iBAAiB,QAAQ,CAAC;YAC1B,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,cAAc,EAAE,SAAS;QAEnE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QACjE;QAEA,qEAAqE;QACrE,OAAO;YACL,IAAI;gBACF,iBAAiB,SAAS,CAAC;gBAC3B,QAAQ,GAAG,CACT,CAAC,wBAAwB,EAAE,SAAS,cAAc,EAAE,SAAS;YAEjE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAClE;QACF;IACF,GAAG;QAAC;QAAiB;QAAsB;QAAS;KAAiB;IAErE,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,OAAO,MAAM,KAAK,GAAG;YACpE;QACF;QAEA,MAAM,gBAAgC,EAAE;QAExC,0BAA0B;QAC1B,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,cAAc,iBAAiB,SAAS,CAC5C,SACA,OACA,CAAC;gBACC,QAAQ,GAAG,CACT,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,OAAO,EAC5D;gBAGF,mDAAmD;gBACnD,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;YAC3C;YAGF,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;YACL,KAAK,MAAM,eAAe,cAAe;QAC3C;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,GAAG,WAAW;QACd;QACA;IACF;AACF;AAKO,SAAS,eACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAiB;SAAiB;QAC3C,kBAAkB;IACpB,GACA;AAEJ", "debugId": null}}, {"offset": {"line": 5612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/ui/useNotifications.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for notification management using Zustand AppStore\r\n * @module hooks/useNotifications\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\nimport { undefinedToNull } from '../../lib/utils/typeHelpers';\r\n\r\n/**\r\n * Custom hook for simplified notification management\r\n * Provides convenient methods for showing different types of notifications\r\n */\r\nexport const useNotifications = () => {\r\n  const addNotification = useAppStore(state => state.addNotification);\r\n  const removeNotification = useAppStore(state => state.removeNotification);\r\n  const clearAllNotifications = useAppStore(\r\n    state => state.clearAllNotifications\r\n  );\r\n  const unreadCount = useAppStore(state => state.unreadNotificationCount);\r\n\r\n  /**\r\n   * Show a success notification\r\n   */\r\n  const showSuccess = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'success',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an error notification\r\n   */\r\n  const showError = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'error',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a warning notification\r\n   */\r\n  const showWarning = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'warning',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an info notification\r\n   */\r\n  const showInfo = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a notification for API operation results\r\n   */\r\n  const showApiResult = useCallback(\r\n    (success: boolean, successMessage: string, errorMessage: string) => {\r\n      if (success) {\r\n        showSuccess(successMessage);\r\n      } else {\r\n        showError(errorMessage);\r\n      }\r\n    },\r\n    [showSuccess, showError]\r\n  );\r\n\r\n  /**\r\n   * Show a notification with auto-dismiss after specified time\r\n   */\r\n  const showTemporary = useCallback(\r\n    (\r\n      type: 'error' | 'info' | 'success' | 'warning',\r\n      message: string,\r\n      dismissAfter = 5000\r\n    ) => {\r\n      addNotification({ message, type });\r\n\r\n      // Auto-dismiss after specified time\r\n      setTimeout(() => {\r\n        // Note: This is a simplified approach. In a real implementation,\r\n        // you might want to store the notification ID and remove specifically that one\r\n        const notifications = useAppStore.getState().notifications;\r\n        const latestNotification = notifications.at(-1);\r\n        if (latestNotification && latestNotification.message === message) {\r\n          removeNotification(latestNotification.id);\r\n        }\r\n      }, dismissAfter);\r\n    },\r\n    [addNotification, removeNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a loading notification that can be updated\r\n   */\r\n  const showLoading = useCallback(\r\n    (message = 'Loading...') => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n\r\n      // Return the notification ID for potential updates\r\n      const notifications = useAppStore.getState().notifications;\r\n      return notifications.at(-1)?.id;\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Update a loading notification to success or error\r\n   */\r\n  const updateLoadingNotification = useCallback(\r\n    (notificationId: string, success: boolean, message: string) => {\r\n      removeNotification(notificationId);\r\n      if (success) {\r\n        showSuccess(message);\r\n      } else {\r\n        showError(message);\r\n      }\r\n    },\r\n    [removeNotification, showSuccess, showError]\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Store methods\r\n    removeNotification,\r\n    // Advanced methods\r\n    showApiResult,\r\n    showError,\r\n\r\n    showInfo,\r\n    showLoading,\r\n    // Basic notification methods\r\n    showSuccess,\r\n    showTemporary,\r\n\r\n    showWarning,\r\n    unreadCount,\r\n    updateLoadingNotification,\r\n  };\r\n};\r\n\r\n/**\r\n * Enhanced notification hook with WorkHub-specific notification types\r\n */\r\nexport const useWorkHubNotifications = () => {\r\n  const {\r\n    clearAllNotifications,\r\n    removeNotification,\r\n    showError,\r\n    showInfo,\r\n    showSuccess,\r\n    showWarning,\r\n    unreadCount,\r\n  } = useNotifications();\r\n\r\n  /**\r\n   * Show delegation-related notifications\r\n   */\r\n  const showDelegationUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'delegation',\r\n        message,\r\n        type: 'delegation-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show vehicle maintenance notifications\r\n   */\r\n  const showVehicleMaintenance = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'vehicle',\r\n        message,\r\n        type: 'vehicle-maintenance',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show task assignment notifications\r\n   */\r\n  const showTaskAssigned = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'task',\r\n        message,\r\n        type: 'task-assigned',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show employee update notifications\r\n   */\r\n  const showEmployeeUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'employee',\r\n        message,\r\n        type: 'employee-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Management\r\n    removeNotification,\r\n    // WorkHub-specific notifications\r\n    showDelegationUpdate,\r\n    showEmployeeUpdate,\r\n\r\n    showError,\r\n    showInfo,\r\n    // Basic notifications\r\n    showSuccess,\r\n    showTaskAssigned,\r\n\r\n    showVehicleMaintenance,\r\n    showWarning,\r\n    unreadCount,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAOO,MAAM,mBAAmB;IAC9B,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,eAAe;IAClE,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,kBAAkB;IACxE,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EACtC,CAAA,QAAS,MAAM,qBAAqB;IAEtC,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,uBAAuB;IAEtE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAkB,gBAAwB;QACzC,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAa;KAAU;IAG1B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CACE,MACA,SACA,eAAe,IAAI;QAEnB,gBAAgB;YAAE;YAAS;QAAK;QAEhC,oCAAoC;QACpC,WAAW;YACT,iEAAiE;YACjE,+EAA+E;YAC/E,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;YAC1D,MAAM,qBAAqB,cAAc,EAAE,CAAC,CAAC;YAC7C,IAAI,sBAAsB,mBAAmB,OAAO,KAAK,SAAS;gBAChE,mBAAmB,mBAAmB,EAAE;YAC1C;QACF,GAAG;IACL,GACA;QAAC;QAAiB;KAAmB;IAGvC;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC,UAAU,YAAY;QACrB,gBAAgB;YACd;YACA,MAAM;QACR;QAEA,mDAAmD;QACnD,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;QAC1D,OAAO,cAAc,EAAE,CAAC,CAAC,IAAI;IAC/B,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,gBAAwB,SAAkB;QACzC,mBAAmB;QACnB,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAoB;QAAa;KAAU;IAG9C,OAAO;QACL;QACA,gBAAgB;QAChB;QACA,mBAAmB;QACnB;QACA;QAEA;QACA;QACA,6BAA6B;QAC7B;QACA;QAEA;QACA;QACA;IACF;AACF;AAKO,MAAM,0BAA0B;IACrC,MAAM,EACJ,qBAAqB,EACrB,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG;IAEJ;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ,OAAO;QACL;QACA,aAAa;QACb;QACA,iCAAiC;QACjC;QACA;QAEA;QACA;QACA,sBAAsB;QACtB;QACA;QAEA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/stores/queries/useEmployees.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Employee-related data.\r\n * These hooks manage fetching, caching, and mutating employee data,\r\n * integrating with the EmployeeApiService and EmployeeTransformer.\r\n * @module stores/queries/useEmployees\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type {\r\n  CreateEmployeeData,\r\n  DriverAvailabilityPrisma,\r\n  Employee,\r\n} from '@/lib/types/domain';\r\n\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { useNotifications } from '@/hooks/ui/useNotifications';\r\nimport { EmployeeTransformer } from '@/lib/transformers/employeeTransformer';\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\nimport { employeeApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\n\r\n/**\r\n * Centralized query keys for employees to ensure consistency.\r\n */\r\nexport const employeeQueryKeys = {\r\n  all: ['employees'] as const,\r\n  detail: (id: string) => ['employees', id] as const,\r\n  // Add other specific query keys as needed\r\n};\r\n\r\n/**\r\n * Custom hook to fetch all employees.\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Employee domain models.\r\n */\r\nexport const useEmployees = (\r\n  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Employee[], Error>(\r\n    [...employeeQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const response = await employeeApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'employee', // entityType\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch employees filtered by role.\r\n * @param role - Optional role filter (e.g., 'driver', 'manager')\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Employee domain models filtered by role.\r\n */\r\nexport const useEmployeesByRole = (\r\n  role?: string,\r\n  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Employee[], Error>(\r\n    role ? ['employees', 'role', role] : [...employeeQueryKeys.all],\r\n    async () => {\r\n      if (role) {\r\n        const response = await employeeApiService.getByRole(role);\r\n        return response;\r\n      } else {\r\n        const response = await employeeApiService.getAll();\r\n        return response.data;\r\n      }\r\n    },\r\n    'employee', // entityType for WebSocket events\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch a single employee by their ID.\r\n * @param id - The ID of the employee to fetch.\r\n * @returns Query result containing a single Employee domain model or undefined.\r\n */\r\nexport const useEmployee = (id: string) => {\r\n  return useCrudQuery<Employee, Error>(\r\n    [...employeeQueryKeys.detail(id)],\r\n    async () => {\r\n      return await employeeApiService.getById(id);\r\n    },\r\n    'employee', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id, // Only run query if id is truthy\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook for creating a new employee.\r\n * Includes optimistic updates and cache invalidation.\r\n * @returns Mutation result for creating an employee.\r\n */\r\nexport const useCreateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  interface CreateContext {\r\n    previousEmployees: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<Employee, Error, CreateEmployeeData, CreateContext>({\r\n    mutationFn: async (employeeData: CreateEmployeeData) => {\r\n      const request = EmployeeTransformer.toCreateRequest(employeeData);\r\n      return await employeeApiService.create(request); // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, _newEmployeeData, context) => {\r\n      if (context?.previousEmployees) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployees\r\n        );\r\n      }\r\n      showError(\r\n        `Failed to create employee: ${err.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onMutate: async newEmployeeData => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      const previousEmployees = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = 'optimistic-' + Date.now().toString();\r\n          const now = new Date().toISOString();\r\n          const optimisticEmployee: Employee = {\r\n            availability: undefinedToNull(newEmployeeData.availability),\r\n            contactEmail: undefinedToNull(newEmployeeData.contactEmail),\r\n            contactInfo: newEmployeeData.contactInfo,\r\n            contactMobile: undefinedToNull(newEmployeeData.contactMobile),\r\n            contactPhone: undefinedToNull(newEmployeeData.contactPhone),\r\n            createdAt: now,\r\n            currentLocation: undefinedToNull(newEmployeeData.currentLocation),\r\n            department: undefinedToNull(newEmployeeData.department),\r\n            employeeId: newEmployeeData.employeeId,\r\n            fullName: newEmployeeData.fullName || newEmployeeData.name,\r\n            generalAssignments: newEmployeeData.generalAssignments || [],\r\n            hireDate: undefinedToNull(newEmployeeData.hireDate),\r\n            id: Number(tempId.replace('optimistic-', '')), // Attempt number ID\r\n            name: newEmployeeData.name,\r\n            notes: undefinedToNull(newEmployeeData.notes),\r\n            position: undefinedToNull(newEmployeeData.position),\r\n            profileImageUrl: undefinedToNull(newEmployeeData.profileImageUrl),\r\n            role: newEmployeeData.role,\r\n            shiftSchedule: undefinedToNull(newEmployeeData.shiftSchedule),\r\n            skills: newEmployeeData.skills || [],\r\n            status: undefinedToNull(newEmployeeData.status),\r\n            updatedAt: now,\r\n            ...(newEmployeeData.workingHours !== undefined && {\r\n              workingHours: undefinedToNull(newEmployeeData.workingHours),\r\n            }),\r\n          };\r\n          return [...old, optimisticEmployee];\r\n        }\r\n      );\r\n\r\n      return { previousEmployees };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n    onSuccess: data => {\r\n      showSuccess(`Employee \"${data.name}\" has been created successfully!`);\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for updating an existing employee.\r\n * Includes optimistic updates and rollback on error.\r\n * @returns Mutation result for updating an employee.\r\n */\r\nexport const useUpdateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateContext {\r\n    previousEmployee: Employee | undefined;\r\n    previousEmployeesList: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Employee,\r\n    Error,\r\n    { data: Partial<CreateEmployeeData>; id: string },\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      const request = EmployeeTransformer.toUpdateRequest(data); // Removed cast\r\n      return await employeeApiService.update(id, request); // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousEmployee) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.detail(variables.id),\r\n          context.previousEmployee\r\n        );\r\n      }\r\n      if (context?.previousEmployeesList) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployeesList\r\n        );\r\n      }\r\n      console.error('Failed to update employee:', err);\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousEmployee = queryClient.getQueryData<Employee>(\r\n        employeeQueryKeys.detail(id)\r\n      );\r\n      const previousEmployeesList = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee>(employeeQueryKeys.detail(id), old => {\r\n        if (!old) return old;\r\n        const now = new Date().toISOString();\r\n\r\n        // Explicitly map updated fields to avoid issues with spread operator on different types\r\n        const updatedOptimistic: Employee = {\r\n          ...old,\r\n          availability: undefinedToNull(\r\n            data.availability === undefined\r\n              ? old.availability\r\n              : data.availability\r\n          ),\r\n          contactEmail: undefinedToNull(\r\n            data.contactEmail === undefined\r\n              ? old.contactEmail\r\n              : data.contactEmail\r\n          ),\r\n          contactInfo: data.contactInfo ?? old.contactInfo,\r\n          contactMobile: undefinedToNull(\r\n            data.contactMobile === undefined\r\n              ? old.contactMobile\r\n              : data.contactMobile\r\n          ),\r\n          contactPhone: undefinedToNull(\r\n            data.contactPhone === undefined\r\n              ? old.contactPhone\r\n              : data.contactPhone\r\n          ),\r\n          currentLocation: undefinedToNull(\r\n            data.currentLocation === undefined\r\n              ? old.currentLocation\r\n              : data.currentLocation\r\n          ),\r\n          department: undefinedToNull(data.department ?? old.department),\r\n          employeeId: data.employeeId ?? old.employeeId,\r\n          fullName: undefinedToNull(data.fullName ?? old.fullName),\r\n          generalAssignments: data.generalAssignments ?? old.generalAssignments,\r\n          hireDate: undefinedToNull(data.hireDate ?? old.hireDate),\r\n          name: data.name ?? old.name,\r\n          notes: undefinedToNull(\r\n            data.notes === undefined ? old.notes : data.notes\r\n          ),\r\n          position: undefinedToNull(data.position ?? old.position),\r\n          profileImageUrl: undefinedToNull(\r\n            data.profileImageUrl === undefined\r\n              ? old.profileImageUrl\r\n              : data.profileImageUrl\r\n          ),\r\n          role: data.role ?? old.role,\r\n          shiftSchedule: undefinedToNull(\r\n            data.shiftSchedule === undefined\r\n              ? old.shiftSchedule\r\n              : data.shiftSchedule\r\n          ),\r\n          skills: data.skills ?? old.skills,\r\n          status: undefinedToNull(data.status ?? old.status),\r\n          updatedAt: now,\r\n          ...(data.workingHours !== undefined && {\r\n            workingHours: undefinedToNull(data.workingHours),\r\n          }),\r\n        };\r\n        return updatedOptimistic;\r\n      });\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => {\r\n          return old.map(employee => {\r\n            if (employee.id === Number(id)) {\r\n              const now = new Date().toISOString();\r\n              return {\r\n                ...employee,\r\n                availability: undefinedToNull(\r\n                  data.availability ?? employee.availability\r\n                ),\r\n                contactEmail: undefinedToNull(\r\n                  data.contactEmail ?? employee.contactEmail\r\n                ),\r\n                contactInfo: data.contactInfo ?? employee.contactInfo,\r\n                contactMobile: undefinedToNull(\r\n                  data.contactMobile ?? employee.contactMobile\r\n                ),\r\n                contactPhone: undefinedToNull(\r\n                  data.contactPhone ?? employee.contactPhone\r\n                ),\r\n                currentLocation: undefinedToNull(\r\n                  data.currentLocation ?? employee.currentLocation\r\n                ),\r\n                department: undefinedToNull(\r\n                  data.department ?? employee.department\r\n                ),\r\n                employeeId: data.employeeId ?? employee.employeeId,\r\n                fullName: undefinedToNull(data.fullName ?? employee.fullName),\r\n                generalAssignments:\r\n                  data.generalAssignments ?? employee.generalAssignments,\r\n                hireDate: undefinedToNull(data.hireDate ?? employee.hireDate),\r\n                name: data.name ?? employee.name,\r\n                notes: undefinedToNull(data.notes ?? employee.notes),\r\n                position: undefinedToNull(data.position ?? employee.position),\r\n                profileImageUrl: undefinedToNull(\r\n                  data.profileImageUrl ?? employee.profileImageUrl\r\n                ),\r\n                role: data.role ?? employee.role,\r\n                shiftSchedule: undefinedToNull(\r\n                  data.shiftSchedule ?? employee.shiftSchedule\r\n                ),\r\n                skills: data.skills ?? employee.skills,\r\n                status: undefinedToNull(data.status ?? employee.status),\r\n                updatedAt: now,\r\n                ...(data.workingHours !== undefined && {\r\n                  workingHours: undefinedToNull(data.workingHours),\r\n                }),\r\n              };\r\n            }\r\n            return employee;\r\n          });\r\n        }\r\n      );\r\n\r\n      return { previousEmployee, previousEmployeesList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: employeeQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for deleting an existing employee.\r\n * Includes cache updates.\r\n * @returns Mutation result for deleting an employee.\r\n */\r\nexport const useDeleteEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface DeleteContext {\r\n    previousEmployeesList: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await employeeApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousEmployeesList) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployeesList\r\n        );\r\n      }\r\n      console.error('Failed to delete employee:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousEmployeesList = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => old.filter(employee => employee.id !== Number(id)) // Compare number ID\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: employeeQueryKeys.detail(id) });\r\n\r\n      return { previousEmployeesList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n// Removed useAssignVehicleToEmployee hook as its functionality is now\r\n// handled by the main update employee mutation.\r\n\r\n/**\r\n * Custom hook for updating an employee's availability status.\r\n * @returns Mutation result for updating availability status.\r\n */\r\nexport const useUpdateEmployeeAvailabilityStatus = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateAvailabilityContext {\r\n    previousEmployee: Employee | undefined;\r\n  }\r\n\r\n  // The input 'status' should align with DriverAvailabilityPrisma\r\n  return useMutation<\r\n    Employee,\r\n    Error,\r\n    { employeeId: string; status: DriverAvailabilityPrisma },\r\n    UpdateAvailabilityContext\r\n  >({\r\n    mutationFn: async ({ employeeId, status }) => {\r\n      // employeeApiService.updateAvailabilityStatus now expects DriverAvailabilityPrisma directly\r\n      const response = await employeeApiService.updateAvailabilityStatus(\r\n        employeeId,\r\n        status\r\n      );\r\n      return response; // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousEmployee) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.detail(variables.employeeId),\r\n          context.previousEmployee\r\n        );\r\n      }\r\n      console.error('Failed to update employee availability status:', err);\r\n    },\r\n    onMutate: async ({ employeeId, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(employeeId),\r\n      });\r\n      const previousEmployee = queryClient.getQueryData<Employee>(\r\n        employeeQueryKeys.detail(employeeId)\r\n      );\r\n\r\n      queryClient.setQueryData<Employee>(\r\n        employeeQueryKeys.detail(employeeId),\r\n        old => {\r\n          // Update the 'availability' field in the domain model\r\n          return old ? { ...old, availability: status } : old;\r\n        }\r\n      );\r\n\r\n      return { previousEmployee };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: employeeQueryKeys.detail(variables.employeeId),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;AAID;AAAA;AAQA,uOAA0D,uBAAuB;AACjF;AACA;AACA;AAEA,gTAA2E,0BAA0B;AAArG;;;;;;;AAKO,MAAM,oBAAoB;IAC/B,KAAK;QAAC;KAAY;IAClB,QAAQ,CAAC,KAAe;YAAC;YAAa;SAAG;AAE3C;AAOO,MAAM,eAAe,CAC1B;IAEA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,kBAAkB,GAAG;KAAC,EAC1B;QACE,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;QAChD,OAAO,SAAS,IAAI;IACtB,GACA,YACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAQO,MAAM,qBAAqB,CAChC,MACA;IAEA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,OAAO;QAAC;QAAa;QAAQ;KAAK,GAAG;WAAI,kBAAkB,GAAG;KAAC,EAC/D;QACE,IAAI,MAAM;YACR,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;YACpD,OAAO;QACT,OAAO;YACL,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;YAChD,OAAO,SAAS,IAAI;QACtB;IACF,GACA,YACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAOO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,kBAAkB,MAAM,CAAC;KAAI,EACjC;QACE,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;IAC1C,GACA,YACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAOO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAMlD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAsD;QACrE,YAAY,OAAO;YACjB,MAAM,UAAU,iJAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;YACpD,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,UAAU,gDAAgD;QACnG;QACA,SAAS,CAAC,KAAK,kBAAkB;YAC/B,IAAI,SAAS,mBAAmB;gBAC9B,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,iBAAiB;YAE7B;YACA,UACE,CAAC,2BAA2B,EAAE,IAAI,OAAO,IAAI,0BAA0B;QAE3E;QACA,UAAU,OAAM;YACd,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;YAClE,MAAM,oBAAoB,YAAY,YAAY,CAChD,kBAAkB,GAAG;YAGvB,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,CAAC,MAAM,EAAE;gBACP,MAAM,SAAS,gBAAgB,KAAK,GAAG,GAAG,QAAQ;gBAClD,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,qBAA+B;oBACnC,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC1D,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC1D,aAAa,gBAAgB,WAAW;oBACxC,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,aAAa;oBAC5D,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC1D,WAAW;oBACX,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe;oBAChE,YAAY,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,UAAU;oBACtD,YAAY,gBAAgB,UAAU;oBACtC,UAAU,gBAAgB,QAAQ,IAAI,gBAAgB,IAAI;oBAC1D,oBAAoB,gBAAgB,kBAAkB,IAAI,EAAE;oBAC5D,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,QAAQ;oBAClD,IAAI,OAAO,OAAO,OAAO,CAAC,eAAe;oBACzC,MAAM,gBAAgB,IAAI;oBAC1B,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,KAAK;oBAC5C,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,QAAQ;oBAClD,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe;oBAChE,MAAM,gBAAgB,IAAI;oBAC1B,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,aAAa;oBAC5D,QAAQ,gBAAgB,MAAM,IAAI,EAAE;oBACpC,QAAQ,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,MAAM;oBAC9C,WAAW;oBACX,GAAI,gBAAgB,YAAY,KAAK,aAAa;wBAChD,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC5D,CAAC;gBACH;gBACA,OAAO;uBAAI;oBAAK;iBAAmB;YACrC;YAGF,OAAO;gBAAE;YAAkB;QAC7B;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;QACA,WAAW,CAAA;YACT,YAAY,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,gCAAgC,CAAC;QACtE;IACF;AACF;AAOO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAOjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YAC7B,MAAM,UAAU,iJAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC,OAAO,eAAe;YAC1E,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,IAAI,UAAU,gDAAgD;QACvG;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,UAAU,EAAE,GACrC,QAAQ,gBAAgB;YAE5B;YACA,IAAI,SAAS,uBAAuB;gBAClC,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,qBAAqB;YAEjC;YACA,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QACA,UAAU,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YAC3B,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;YAClE,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,kBAAkB,MAAM,CAAC;YACrC;YAEA,MAAM,mBAAmB,YAAY,YAAY,CAC/C,kBAAkB,MAAM,CAAC;YAE3B,MAAM,wBAAwB,YAAY,YAAY,CACpD,kBAAkB,GAAG;YAGvB,YAAY,YAAY,CAAW,kBAAkB,MAAM,CAAC,KAAK,CAAA;gBAC/D,IAAI,CAAC,KAAK,OAAO;gBACjB,MAAM,MAAM,IAAI,OAAO,WAAW;gBAElC,wFAAwF;gBACxF,MAAM,oBAA8B;oBAClC,GAAG,GAAG;oBACN,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;oBAEvB,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;oBAEvB,aAAa,KAAK,WAAW,IAAI,IAAI,WAAW;oBAChD,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,KAAK,YACnB,IAAI,aAAa,GACjB,KAAK,aAAa;oBAExB,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;oBAEvB,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,KAAK,YACrB,IAAI,eAAe,GACnB,KAAK,eAAe;oBAE1B,YAAY,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,IAAI,IAAI,UAAU;oBAC7D,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;oBAC7C,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,oBAAoB,KAAK,kBAAkB,IAAI,IAAI,kBAAkB;oBACrE,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI;oBAC3B,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG,KAAK,KAAK;oBAEnD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,KAAK,YACrB,IAAI,eAAe,GACnB,KAAK,eAAe;oBAE1B,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI;oBAC3B,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,KAAK,YACnB,IAAI,aAAa,GACjB,KAAK,aAAa;oBAExB,QAAQ,KAAK,MAAM,IAAI,IAAI,MAAM;oBACjC,QAAQ,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,IAAI,IAAI,MAAM;oBACjD,WAAW;oBACX,GAAI,KAAK,YAAY,KAAK,aAAa;wBACrC,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,YAAY;oBACjD,CAAC;gBACH;gBACA,OAAO;YACT;YAEA,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,CAAC,MAAM,EAAE;gBACP,OAAO,IAAI,GAAG,CAAC,CAAA;oBACb,IAAI,SAAS,EAAE,KAAK,OAAO,KAAK;wBAC9B,MAAM,MAAM,IAAI,OAAO,WAAW;wBAClC,OAAO;4BACL,GAAG,QAAQ;4BACX,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;4BAE5C,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;4BAE5C,aAAa,KAAK,WAAW,IAAI,SAAS,WAAW;4BACrD,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,IAAI,SAAS,aAAa;4BAE9C,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;4BAE5C,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,IAAI,SAAS,eAAe;4BAElD,YAAY,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EACxB,KAAK,UAAU,IAAI,SAAS,UAAU;4BAExC,YAAY,KAAK,UAAU,IAAI,SAAS,UAAU;4BAClD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;4BAC5D,oBACE,KAAK,kBAAkB,IAAI,SAAS,kBAAkB;4BACxD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;4BAC5D,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI;4BAChC,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,SAAS,KAAK;4BACnD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;4BAC5D,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,IAAI,SAAS,eAAe;4BAElD,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI;4BAChC,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,IAAI,SAAS,aAAa;4BAE9C,QAAQ,KAAK,MAAM,IAAI,SAAS,MAAM;4BACtC,QAAQ,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,IAAI,SAAS,MAAM;4BACtD,WAAW;4BACX,GAAI,KAAK,YAAY,KAAK,aAAa;gCACrC,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,YAAY;4BACjD,CAAC;wBACH;oBACF;oBACA,OAAO;gBACT;YACF;YAGF,OAAO;gBAAE;gBAAkB;YAAsB;QACnD;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,kBAAkB,MAAM,CAAC,UAAU,EAAE;YACjD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;IACF;AACF;AAOO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,YAAY,OAAO;YACjB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;YAChC,OAAO;QACT;QACA,SAAS,CAAC,KAAK,KAAK;YAClB,IAAI,SAAS,uBAAuB;gBAClC,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,qBAAqB;YAEjC;YACA,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QACA,UAAU,OAAM;YACd,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;YAClE,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,kBAAkB,MAAM,CAAC;YACrC;YAEA,MAAM,wBAAwB,YAAY,YAAY,CACpD,kBAAkB,GAAG;YAGvB,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO,KAAK,oBAAoB;;YAGvF,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,MAAM,CAAC;YAAI;YAEnE,OAAO;gBAAE;YAAsB;QACjC;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;IACF;AACF;AASO,MAAM,sCAAsC;IACjD,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAMjC,gEAAgE;IAChE,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;YACvC,4FAA4F;YAC5F,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,wBAAwB,CAChE,YACA;YAEF,OAAO,UAAU,gDAAgD;QACnE;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,UAAU,UAAU,GAC7C,QAAQ,gBAAgB;YAE5B;YACA,QAAQ,KAAK,CAAC,kDAAkD;QAClE;QACA,UAAU,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;YACrC,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,kBAAkB,MAAM,CAAC;YACrC;YACA,MAAM,mBAAmB,YAAY,YAAY,CAC/C,kBAAkB,MAAM,CAAC;YAG3B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,aACzB,CAAA;gBACE,sDAAsD;gBACtD,OAAO,MAAM;oBAAE,GAAG,GAAG;oBAAE,cAAc;gBAAO,IAAI;YAClD;YAGF,OAAO;gBAAE;YAAiB;QAC5B;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,kBAAkB,MAAM,CAAC,UAAU,UAAU;YACzD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;IACF;AACF", "debugId": null}}, {"offset": {"line": 6159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/forms/EmployeeSelector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { User } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { Employee } from '@/lib/types/domain';\r\n\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { useEmployees } from '@/lib/stores/queries/useEmployees';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface EmployeeSelectorProps {\r\n  allowClear?: boolean;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  label?: string;\r\n  onValueChange: (value: null | number) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  value?: null | number | string;\r\n}\r\n\r\n// Format employee display name\r\nconst formatEmployeeName = (employee: Employee): string => {\r\n  const name = employee.fullName ?? employee.name ?? 'Unknown';\r\n  const id = employee.employeeId ? ` (ID: ${employee.employeeId})` : '';\r\n  const position = employee.position ? ` - ${employee.position}` : '';\r\n  return `${name}${id}${position}`;\r\n};\r\n\r\n/**\r\n * Enhanced Employee Selector Component\r\n *\r\n * Professional employee selection dropdown with enhanced UX.\r\n * Integrates with the existing employee API and follows the\r\n * established design patterns.\r\n *\r\n * Features:\r\n * - Professional visual design with employee information\r\n * - Loading and error states\r\n * - Clear selection option\r\n * - Responsive design\r\n */\r\nexport function EmployeeSelector({\r\n  allowClear = true,\r\n  className,\r\n  disabled = false,\r\n  error,\r\n  label,\r\n  onValueChange,\r\n  placeholder = 'Select employee...',\r\n  required = false,\r\n  value,\r\n}: EmployeeSelectorProps) {\r\n  const { data: employees = [], error: fetchError, isLoading } = useEmployees();\r\n\r\n  // Find selected employee\r\n  const selectedEmployee = employees.find(emp => emp.id === value);\r\n\r\n  const handleValueChange = (employeeId: string) => {\r\n    if (employeeId === 'clear') {\r\n      onValueChange(null);\r\n      return;\r\n    }\r\n    const numericId = Number.parseInt(employeeId, 10);\r\n    onValueChange(Number.isNaN(numericId) ? null : numericId);\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={cn('space-y-2', className)}>\r\n        {label && (\r\n          <Label className=\"text-sm font-medium\">\r\n            {label}\r\n            {required && <span className=\"ml-1 text-destructive\">*</span>}\r\n          </Label>\r\n        )}\r\n        <Select disabled>\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"Loading employees...\" />\r\n          </SelectTrigger>\r\n        </Select>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (fetchError) {\r\n    return (\r\n      <div className={cn('space-y-2', className)}>\r\n        {label && (\r\n          <Label className=\"text-sm font-medium\">\r\n            {label}\r\n            {required && <span className=\"ml-1 text-destructive\">*</span>}\r\n          </Label>\r\n        )}\r\n        <Select disabled>\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"Error loading employees\" />\r\n          </SelectTrigger>\r\n        </Select>\r\n        <p className=\"text-sm text-destructive\">Failed to load employees</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      {label && (\r\n        <Label className=\"text-sm font-medium\">\r\n          {label}\r\n          {required && <span className=\"ml-1 text-destructive\">*</span>}\r\n        </Label>\r\n      )}\r\n\r\n      <Select\r\n        disabled={disabled}\r\n        onValueChange={handleValueChange}\r\n        value={value?.toString() ?? ''}\r\n      >\r\n        <SelectTrigger\r\n          className={cn(error && 'border-destructive focus:border-destructive')}\r\n        >\r\n          <SelectValue placeholder={placeholder}>\r\n            {selectedEmployee && (\r\n              <div className=\"flex items-center space-x-2\">\r\n                <User className=\"size-4 text-muted-foreground\" />\r\n                <span className=\"truncate\">\r\n                  {formatEmployeeName(selectedEmployee)}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </SelectValue>\r\n        </SelectTrigger>\r\n\r\n        <SelectContent>\r\n          {allowClear && value && (\r\n            <SelectItem className=\"text-muted-foreground\" value=\"clear\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className=\"text-xs\">×</span>\r\n                <span>Clear selection</span>\r\n              </div>\r\n            </SelectItem>\r\n          )}\r\n\r\n          {employees.map(employee => (\r\n            <SelectItem key={employee.id} value={employee.id.toString()}>\r\n              <div className=\"flex w-full items-center space-x-2\">\r\n                <User className=\"size-4 shrink-0 text-muted-foreground\" />\r\n                <div className=\"min-w-0 flex-1\">\r\n                  <div className=\"truncate font-medium\">\r\n                    {employee.fullName ?? employee.name ?? 'Unknown'}\r\n                  </div>\r\n                  <div className=\"truncate text-xs text-muted-foreground\">\r\n                    {employee.employeeId && `ID: ${employee.employeeId}`}\r\n                    {employee.position && ` • ${employee.position}`}\r\n                    {employee.department && ` • ${employee.department}`}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </SelectItem>\r\n          ))}\r\n        </SelectContent>\r\n      </Select>\r\n\r\n      {error && <p className=\"text-sm text-destructive\">{error}</p>}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAKA;AACA;AAOA;AACA;AAAA;AAhBA;;;;;;;AA8BA,+BAA+B;AAC/B,MAAM,qBAAqB,CAAC;IAC1B,MAAM,OAAO,SAAS,QAAQ,IAAI,SAAS,IAAI,IAAI;IACnD,MAAM,KAAK,SAAS,UAAU,GAAG,CAAC,MAAM,EAAE,SAAS,UAAU,CAAC,CAAC,CAAC,GAAG;IACnE,MAAM,WAAW,SAAS,QAAQ,GAAG,CAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,GAAG;IACjE,OAAO,GAAG,OAAO,KAAK,UAAU;AAClC;AAeO,SAAS,iBAAiB,EAC/B,aAAa,IAAI,EACjB,SAAS,EACT,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,aAAa,EACb,cAAc,oBAAoB,EAClC,WAAW,KAAK,EAChB,KAAK,EACiB;IACtB,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,OAAO,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD;IAE1E,yBAAyB;IACzB,MAAM,mBAAmB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAE1D,MAAM,oBAAoB,CAAC;QACzB,IAAI,eAAe,SAAS;YAC1B,cAAc;YACd;QACF;QACA,MAAM,YAAY,OAAO,QAAQ,CAAC,YAAY;QAC9C,cAAc,OAAO,KAAK,CAAC,aAAa,OAAO;IACjD;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;gBAC7B,uBACC,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;wBACd;wBACA,0BAAY,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;8BAGzD,8OAAC,kIAAA,CAAA,SAAM;oBAAC,QAAQ;8BACd,cAAA,8OAAC,kIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;IAKnC;IAEA,IAAI,YAAY;QACd,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;gBAC7B,uBACC,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;wBACd;wBACA,0BAAY,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;8BAGzD,8OAAC,kIAAA,CAAA,SAAM;oBAAC,QAAQ;8BACd,cAAA,8OAAC,kIAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAY;;;;;;;;;;;;;;;;8BAG7B,8OAAC;oBAAE,WAAU;8BAA2B;;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAC7B,uBACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAIzD,8OAAC,kIAAA,CAAA,SAAM;gBACL,UAAU;gBACV,eAAe;gBACf,OAAO,OAAO,cAAc;;kCAE5B,8OAAC,kIAAA,CAAA,gBAAa;wBACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,SAAS;kCAEvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAa;sCACvB,kCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDACb,mBAAmB;;;;;;;;;;;;;;;;;;;;;;kCAO9B,8OAAC,kIAAA,CAAA,gBAAa;;4BACX,cAAc,uBACb,8OAAC,kIAAA,CAAA,aAAU;gCAAC,WAAU;gCAAwB,OAAM;0CAClD,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;sDAAK;;;;;;;;;;;;;;;;;4BAKX,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC,kIAAA,CAAA,aAAU;oCAAmB,OAAO,SAAS,EAAE,CAAC,QAAQ;8CACvD,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,SAAS,QAAQ,IAAI,SAAS,IAAI,IAAI;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;4DACZ,SAAS,UAAU,IAAI,CAAC,IAAI,EAAE,SAAS,UAAU,EAAE;4DACnD,SAAS,QAAQ,IAAI,CAAC,GAAG,EAAE,SAAS,QAAQ,EAAE;4DAC9C,SAAS,UAAU,IAAI,CAAC,GAAG,EAAE,SAAS,UAAU,EAAE;;;;;;;;;;;;;;;;;;;mCAV1C,SAAS,EAAE;;;;;;;;;;;;;;;;;YAmBjC,uBAAS,8OAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 6484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/tables/DataTable.tsx"], "sourcesContent": ["/**\r\n * Generic DataTable Component\r\n *\r\n * A reusable table component built on TanStack Table that provides:\r\n * - Sorting, filtering, and pagination\r\n * - Row selection with bulk actions\r\n * - Column visibility controls\r\n * - Responsive design with shadcn/ui styling\r\n * - Type-safe implementation with generics\r\n *\r\n * Based on the excellent patterns from DelegationTable.tsx\r\n */\r\n\r\n'use client';\r\n\r\nimport type {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  VisibilityState,\r\n} from '@tanstack/react-table';\r\n\r\nimport {\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from '@tanstack/react-table';\r\nimport {\r\n  ChevronDown,\r\n  Settings,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ChevronsLeft,\r\n  ChevronsRight,\r\n} from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface DataTableProps<T> {\r\n  data: T[];\r\n  columns: ColumnDef<T>[];\r\n  className?: string;\r\n  onRowClick?: (row: T) => void;\r\n  searchPlaceholder?: string;\r\n  searchColumn?: string;\r\n  enableRowSelection?: boolean;\r\n  enableColumnVisibility?: boolean;\r\n  enableGlobalFilter?: boolean;\r\n  pageSize?: number;\r\n  emptyMessage?: string;\r\n  // Advanced features from DelegationTable\r\n  enableBulkActions?: boolean;\r\n  bulkActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (selectedRows: T[]) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n  // Professional styling options\r\n  tableClassName?: string;\r\n  headerClassName?: string;\r\n  rowClassName?: string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  data,\r\n  columns,\r\n  className = '',\r\n  onRowClick,\r\n  searchPlaceholder = 'Search...',\r\n  searchColumn,\r\n  enableRowSelection = false,\r\n  enableColumnVisibility = true,\r\n  enableGlobalFilter = true,\r\n  pageSize = 10,\r\n  emptyMessage = 'No results found.',\r\n  enableBulkActions = false,\r\n  bulkActions = [],\r\n  tableClassName = '',\r\n  headerClassName = '',\r\n  rowClassName = '',\r\n}: DataTableProps<T>) {\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\r\n    []\r\n  );\r\n  const [columnVisibility, setColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n  const [rowSelection, setRowSelection] = React.useState({});\r\n  const [globalFilter, setGlobalFilter] = React.useState('');\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onRowSelectionChange: setRowSelection,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      columnVisibility,\r\n      rowSelection,\r\n      globalFilter,\r\n    },\r\n    initialState: {\r\n      pagination: {\r\n        pageSize,\r\n      },\r\n    },\r\n  });\r\n\r\n  // Handle search input for specific column\r\n  const handleSearch = (value: string) => {\r\n    if (searchColumn) {\r\n      table.getColumn(searchColumn)?.setFilterValue(value);\r\n    } else {\r\n      setGlobalFilter(value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn('space-y-4', className)}>\r\n      <Card className=\"shadow-md\">\r\n        {/* Table Controls Header */}\r\n        <div className=\"flex items-center justify-between p-4\">\r\n          {/* Search Input */}\r\n          {enableGlobalFilter && (\r\n            <Input\r\n              placeholder={searchPlaceholder}\r\n              value={\r\n                searchColumn\r\n                  ? ((table\r\n                      .getColumn(searchColumn)\r\n                      ?.getFilterValue() as string) ?? '')\r\n                  : globalFilter\r\n              }\r\n              onChange={event => handleSearch(event.target.value)}\r\n              className=\"max-w-sm\"\r\n            />\r\n          )}\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* Bulk Actions */}\r\n            {enableBulkActions &&\r\n              enableRowSelection &&\r\n              table.getFilteredSelectedRowModel().rows.length > 0 && (\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button variant=\"outline\" size=\"sm\">\r\n                      Actions ({table.getFilteredSelectedRowModel().rows.length}\r\n                      )\r\n                      <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent align=\"end\">\r\n                    {bulkActions.map((action, index) => (\r\n                      <DropdownMenuItem\r\n                        key={index}\r\n                        onClick={() =>\r\n                          action.onClick(\r\n                            table\r\n                              .getFilteredSelectedRowModel()\r\n                              .rows.map(row => row.original)\r\n                          )\r\n                        }\r\n                        className={\r\n                          action.variant === 'destructive'\r\n                            ? 'text-destructive'\r\n                            : ''\r\n                        }\r\n                      >\r\n                        {action.icon && (\r\n                          <action.icon className=\"mr-2 h-4 w-4\" />\r\n                        )}\r\n                        {action.label}\r\n                      </DropdownMenuItem>\r\n                    ))}\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              )}\r\n\r\n            {/* Column Visibility */}\r\n            {enableColumnVisibility && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"outline\">\r\n                    <Settings className=\"mr-2 h-4 w-4\" />\r\n                    Columns\r\n                    <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\" className=\"w-[150px]\">\r\n                  {table\r\n                    .getAllColumns()\r\n                    .filter(column => column.getCanHide())\r\n                    .map(column => {\r\n                      return (\r\n                        <DropdownMenuCheckboxItem\r\n                          key={column.id}\r\n                          className=\"capitalize\"\r\n                          checked={column.getIsVisible()}\r\n                          onCheckedChange={value =>\r\n                            column.toggleVisibility(!!value)\r\n                          }\r\n                        >\r\n                          {column.id}\r\n                        </DropdownMenuCheckboxItem>\r\n                      );\r\n                    })}\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table Content */}\r\n        <CardContent className=\"p-0\">\r\n          <div className={cn('border-t', tableClassName)}>\r\n            <Table>\r\n              <TableHeader>\r\n                {table.getHeaderGroups().map(headerGroup => (\r\n                  <TableRow\r\n                    key={headerGroup.id}\r\n                    className={cn(\r\n                      'border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800',\r\n                      headerClassName\r\n                    )}\r\n                  >\r\n                    {headerGroup.headers.map(header => {\r\n                      return (\r\n                        <TableHead\r\n                          key={header.id}\r\n                          className=\"py-4 font-semibold text-gray-900 dark:text-white\"\r\n                        >\r\n                          {header.isPlaceholder ? null : (\r\n                            <div\r\n                              className={cn(\r\n                                'flex items-center space-x-1',\r\n                                header.column.getCanSort() &&\r\n                                  'cursor-pointer select-none hover:text-gray-600 dark:hover:text-gray-300'\r\n                              )}\r\n                              onClick={header.column.getToggleSortingHandler()}\r\n                            >\r\n                              <span>\r\n                                {flexRender(\r\n                                  header.column.columnDef.header,\r\n                                  header.getContext()\r\n                                )}\r\n                              </span>\r\n                            </div>\r\n                          )}\r\n                        </TableHead>\r\n                      );\r\n                    })}\r\n                  </TableRow>\r\n                ))}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {table.getRowModel().rows?.length ? (\r\n                  table.getRowModel().rows.map(row => (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      data-state={row.getIsSelected() && 'selected'}\r\n                      className={cn(\r\n                        'border-b border-gray-100 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50',\r\n                        onRowClick && 'cursor-pointer',\r\n                        row.getIsSelected() && 'bg-blue-50 dark:bg-blue-900/20',\r\n                        rowClassName\r\n                      )}\r\n                      onClick={() => onRowClick?.(row.original)}\r\n                    >\r\n                      {row.getVisibleCells().map(cell => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={columns.length}\r\n                      className=\"h-24 text-center\"\r\n                    >\r\n                      {emptyMessage}\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Pagination Footer */}\r\n      <Card className=\"flex items-center justify-between border-t p-4\">\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {enableRowSelection &&\r\n          table.getFilteredSelectedRowModel().rows.length > 0 ? (\r\n            <>\r\n              {table.getFilteredSelectedRowModel().rows.length} of{' '}\r\n              {table.getFilteredRowModel().rows.length} row(s) selected.\r\n            </>\r\n          ) : (\r\n            <>\r\n              Showing{' '}\r\n              {table.getState().pagination.pageIndex *\r\n                table.getState().pagination.pageSize +\r\n                1}{' '}\r\n              to{' '}\r\n              {Math.min(\r\n                (table.getState().pagination.pageIndex + 1) *\r\n                  table.getState().pagination.pageSize,\r\n                table.getFilteredRowModel().rows.length\r\n              )}{' '}\r\n              of {table.getFilteredRowModel().rows.length} entries\r\n              {table.getFilteredRowModel().rows.length !== data.length &&\r\n                ` (filtered from ${data.length} total)`}\r\n            </>\r\n          )}\r\n        </div>\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <p className=\"text-sm font-medium\">Rows per page</p>\r\n            <Select\r\n              value={`${table.getState().pagination.pageSize}`}\r\n              onValueChange={value => {\r\n                table.setPageSize(Number(value));\r\n              }}\r\n            >\r\n              <SelectTrigger className=\"h-8 w-[70px]\">\r\n                <SelectValue\r\n                  placeholder={table.getState().pagination.pageSize}\r\n                />\r\n              </SelectTrigger>\r\n              <SelectContent side=\"top\">\r\n                {[10, 20, 30, 40, 50].map(pageSize => (\r\n                  <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                    {pageSize}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\r\n            Page {table.getState().pagination.pageIndex + 1} of{' '}\r\n            {table.getPageCount()}\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n              onClick={() => table.setPageIndex(0)}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to first page</span>\r\n              <ChevronsLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"h-8 w-8 p-0\"\r\n              onClick={() => table.previousPage()}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to previous page</span>\r\n              <ChevronLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"h-8 w-8 p-0\"\r\n              onClick={() => table.nextPage()}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to next page</span>\r\n              <ChevronRight className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n              onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to last page</span>\r\n              <ChevronsRight className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAWD;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAEA;AACA;AACA;AAOA;AACA;AAOA;AAQA;AAAA;AApDA;;;;;;;;;;;;AAgFO,SAAS,UAAa,EAC3B,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,UAAU,EACV,oBAAoB,WAAW,EAC/B,YAAY,EACZ,qBAAqB,KAAK,EAC1B,yBAAyB,IAAI,EAC7B,qBAAqB,IAAI,EACzB,WAAW,EAAE,EACb,eAAe,mBAAmB,EAClC,oBAAoB,KAAK,EACzB,cAAc,EAAE,EAChB,iBAAiB,EAAE,EACnB,kBAAkB,EAAE,EACpB,eAAe,EAAE,EACC;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EACrD,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAmB,CAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,iBAAiB;QACjB,uBAAuB;QACvB,0BAA0B;QAC1B,sBAAsB;QACtB,sBAAsB;QACtB,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;QACA,cAAc;YACZ,YAAY;gBACV;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,IAAI,cAAc;YAChB,MAAM,SAAS,CAAC,eAAe,eAAe;QAChD,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;4BAEZ,oCACC,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAa;gCACb,OACE,eACK,AAAC,MACC,SAAS,CAAC,eACT,oBAA+B,KACnC;gCAEN,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;gCAClD,WAAU;;;;;;0CAId,8OAAC;gCAAI,WAAU;;oCAEZ,qBACC,sBACA,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,mBAChD,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;wDAAK;wDACxB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;wDAAC;sEAE1D,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;0DACxB,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,4IAAA,CAAA,mBAAgB;wDAEf,SAAS,IACP,OAAO,OAAO,CACZ,MACG,2BAA2B,GAC3B,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;wDAGnC,WACE,OAAO,OAAO,KAAK,gBACf,qBACA;;4DAGL,OAAO,IAAI,kBACV,8OAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;4DAExB,OAAO,KAAK;;uDAjBR;;;;;;;;;;;;;;;;oCAyBhB,wCACC,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;sEAErC,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAM,WAAU;0DACxC,MACE,aAAa,GACb,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU,IAClC,GAAG,CAAC,CAAA;oDACH,qBACE,8OAAC,4IAAA,CAAA,2BAAwB;wDAEvB,WAAU;wDACV,SAAS,OAAO,YAAY;wDAC5B,iBAAiB,CAAA,QACf,OAAO,gBAAgB,CAAC,CAAC,CAAC;kEAG3B,OAAO,EAAE;uDAPL,OAAO,EAAE;;;;;gDAUpB;;;;;;;;;;;;;;;;;;;;;;;;kCAQZ,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;sCAC7B,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6EACA;0DAGD,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;oDACvB,qBACE,8OAAC,iIAAA,CAAA,YAAS;wDAER,WAAU;kEAET,OAAO,aAAa,GAAG,qBACtB,8OAAC;4DACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+BACA,OAAO,MAAM,CAAC,UAAU,MACtB;4DAEJ,SAAS,OAAO,MAAM,CAAC,uBAAuB;sEAE9C,cAAA,8OAAC;0EACE,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;;;;;;uDAfpB,OAAO,EAAE;;;;;gDAsBpB;+CA/BK,YAAY,EAAE;;;;;;;;;;kDAmCzB,8OAAC,iIAAA,CAAA,YAAS;kDACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,cAAY,IAAI,aAAa,MAAM;gDACnC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,4FACA,cAAc,kBACd,IAAI,aAAa,MAAM,kCACvB;gDAEF,SAAS,IAAM,aAAa,IAAI,QAAQ;0DAEvC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,8OAAC,iIAAA,CAAA,YAAS;kEACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uDAHH,KAAK,EAAE;;;;;+CAXpB,IAAI,EAAE;;;;sEAqBf,8OAAC,iIAAA,CAAA,WAAQ;sDACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;gDACR,SAAS,QAAQ,MAAM;gDACvB,WAAU;0DAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACZ,sBACD,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,kBAChD;;gCACG,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;gCAAC;gCAAI;gCACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;yDAG3C;;gCAAE;gCACQ;gCACP,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GACpC;gCAAG;gCAAI;gCACN;gCACF,KAAK,GAAG,CACP,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EACtC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCACtC;gCAAI;gCACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;gCAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,IACtD,CAAC,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE;wCAChD,eAAe,CAAA;4CACb,MAAM,WAAW,CAAC,OAAO;wCAC3B;;0DAEA,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDACV,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;0DAGrD,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,MAAK;0DACjB;oDAAC;oDAAI;oDAAI;oDAAI;oDAAI;iDAAG,CAAC,GAAG,CAAC,CAAA,yBACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAgB,OAAO,GAAG,UAAU;kEAC5C;uDADc;;;;;;;;;;;;;;;;;;;;;;0CAOzB,8OAAC;gCAAI,WAAU;;oCAAiE;oCACxE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;oCAAE;oCAAI;oCACnD,MAAM,YAAY;;;;;;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY,CAAC;wCAClC,UAAU,CAAC,MAAM,kBAAkB;;0DAEnC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY;wCACjC,UAAU,CAAC,MAAM,kBAAkB;;0DAEnC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,QAAQ;wCAC7B,UAAU,CAAC,MAAM,cAAc;;0DAE/B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;wCACzD,UAAU,CAAC,MAAM,cAAc;;0DAE/B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 7078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/services/toastService.ts"], "sourcesContent": ["/**\r\n * Unified Generic Toast Service - Following SRP and DRY principles\r\n *\r\n * This service provides a consistent interface for all toast notifications\r\n * across the application, eliminating code duplication and ensuring\r\n * consistent messaging patterns using generics.\r\n */\r\n\r\nimport { toast } from '@/hooks/utils/use-toast';\r\n\r\nexport type ToastVariant = 'default' | 'destructive';\r\n\r\nexport interface ToastOptions {\r\n  title?: string | undefined;\r\n  description?: string | undefined;\r\n  variant?: ToastVariant | undefined;\r\n  duration?: number | undefined;\r\n}\r\n\r\n/**\r\n * Configuration interface for entity-specific toast messages\r\n */\r\nexport interface EntityToastConfig<T = any> {\r\n  entityName: string; // e.g., \"Employee\", \"Vehicle\", \"Service Record\"\r\n  getDisplayName: (entity: T) => string; // Function to get display name from entity\r\n  messages: {\r\n    created: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    updated: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    deleted: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    creationError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    updateError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    deletionError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Base toast service class following SRP\r\n */\r\nclass ToastService {\r\n  /**\r\n   * Show a generic toast notification\r\n   */\r\n  show(options: ToastOptions) {\r\n    return toast({\r\n      title: options.title,\r\n      description: options.description,\r\n      variant: options.variant || 'default',\r\n      ...(options.duration && { duration: options.duration }),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show a success toast notification\r\n   */\r\n  success(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an error toast notification\r\n   */\r\n  error(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'destructive',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an info toast notification\r\n   */\r\n  info(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Generic Entity Toast Service - Works with any entity type T\r\n */\r\nexport class GenericEntityToastService<T = any> extends ToastService {\r\n  private config: EntityToastConfig<T>;\r\n\r\n  constructor(config: EntityToastConfig<T>) {\r\n    super();\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * Show entity created success toast\r\n   */\r\n  entityCreated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.created.title,\r\n      this.config.messages.created.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity updated success toast\r\n   */\r\n  entityUpdated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.updated.title,\r\n      this.config.messages.updated.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deleted success toast\r\n   */\r\n  entityDeleted(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.deleted.title,\r\n      this.config.messages.deleted.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity creation error toast\r\n   */\r\n  entityCreationError(error: string) {\r\n    return this.error(\r\n      this.config.messages.creationError.title,\r\n      this.config.messages.creationError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity update error toast\r\n   */\r\n  entityUpdateError(error: string) {\r\n    return this.error(\r\n      this.config.messages.updateError.title,\r\n      this.config.messages.updateError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deletion error toast\r\n   */\r\n  entityDeletionError(error: string) {\r\n    return this.error(\r\n      this.config.messages.deletionError.title,\r\n      this.config.messages.deletionError.description(error)\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// ENTITY CONFIGURATIONS - Define toast messages for each domain\r\n// =============================================================================\r\n\r\n/**\r\n * Employee entity toast configuration\r\n */\r\nconst employeeToastConfig: EntityToastConfig<{ name: string }> = {\r\n  entityName: 'Employee',\r\n  getDisplayName: employee => employee.name,\r\n  messages: {\r\n    created: {\r\n      title: 'Employee Added',\r\n      description: name =>\r\n        `The employee \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Employee Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Employee Deleted Successfully',\r\n      description: name =>\r\n        `${name} has been permanently removed from the system.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the employee.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the employee.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the employee.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Delegation entity toast configuration\r\n */\r\nconst delegationToastConfig: EntityToastConfig<{\r\n  event?: string;\r\n  location?: string;\r\n}> = {\r\n  entityName: 'Delegation',\r\n  getDisplayName: delegation =>\r\n    delegation.event || delegation.location || 'Delegation',\r\n  messages: {\r\n    created: {\r\n      title: 'Delegation Created',\r\n      description: name =>\r\n        `The delegation \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Delegation Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Delegation Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the delegation.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the delegation.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the delegation.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Vehicle entity toast configuration\r\n */\r\nconst vehicleToastConfig: EntityToastConfig<{ make: string; model: string }> = {\r\n  entityName: 'Vehicle',\r\n  getDisplayName: vehicle => `${vehicle.make} ${vehicle.model}`,\r\n  messages: {\r\n    created: {\r\n      title: 'Vehicle Added',\r\n      description: name =>\r\n        `The vehicle \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Vehicle Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Vehicle Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the vehicle.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the vehicle.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the vehicle.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Task entity toast configuration\r\n */\r\nconst taskToastConfig: EntityToastConfig<{ title?: string; name?: string }> = {\r\n  entityName: 'Task',\r\n  getDisplayName: task => task.title || task.name || 'Task',\r\n  messages: {\r\n    created: {\r\n      title: 'Task Created',\r\n      description: name => `The task \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Task Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Task Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the task.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the task.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the task.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Service Record-specific toast messages following DRY principles\r\n */\r\nexport class ServiceRecordToastService extends ToastService {\r\n  /**\r\n   * Show service record created success toast\r\n   */\r\n  serviceRecordCreated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Added',\r\n      `${serviceType} service for \"${vehicleName}\" has been successfully logged.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record updated success toast\r\n   */\r\n  serviceRecordUpdated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Updated',\r\n      `${serviceType} service for \"${vehicleName}\" has been updated.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deleted success toast\r\n   */\r\n  serviceRecordDeleted(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Deleted',\r\n      `${serviceType} service record for \"${vehicleName}\" has been permanently removed.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record creation error toast\r\n   */\r\n  serviceRecordCreationError(error: string) {\r\n    return this.error(\r\n      'Failed to Log Service Record',\r\n      error || 'An unexpected error occurred while logging the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record update error toast\r\n   */\r\n  serviceRecordUpdateError(error: string) {\r\n    return this.error(\r\n      'Update Failed',\r\n      error || 'An unexpected error occurred while updating the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deletion error toast\r\n   */\r\n  serviceRecordDeletionError(error: string) {\r\n    return this.error(\r\n      'Failed to Delete Service Record',\r\n      error || 'An unexpected error occurred while deleting the service record.'\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// UTILITY FACTORY FUNCTIONS\r\n// =============================================================================\r\n\r\n/**\r\n * Factory function to create a generic entity toast service for any entity type\r\n * Useful for creating toast services for new entities without pre-configuration\r\n */\r\nexport function createEntityToastService<T>(config: EntityToastConfig<T>) {\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n/**\r\n * Factory function to create a simple entity toast service with minimal configuration\r\n * For entities that only need basic CRUD messages\r\n */\r\nexport function createSimpleEntityToastService<T>(\r\n  entityName: string,\r\n  getDisplayName: (entity: T) => string\r\n): GenericEntityToastService<T> {\r\n  const config: EntityToastConfig<T> = {\r\n    entityName,\r\n    getDisplayName,\r\n    messages: {\r\n      created: {\r\n        title: `${entityName} Created`,\r\n        description: (displayName: string) =>\r\n          `The ${entityName.toLowerCase()} \"${displayName}\" has been successfully created.`,\r\n      },\r\n      updated: {\r\n        title: `${entityName} Updated Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been updated.`,\r\n      },\r\n      deleted: {\r\n        title: `${entityName} Deleted Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been permanently removed.`,\r\n      },\r\n      creationError: {\r\n        title: `Failed to Create ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while creating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      updateError: {\r\n        title: 'Update Failed',\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while updating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      deletionError: {\r\n        title: `Failed to Delete ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while deleting the ${entityName.toLowerCase()}.`,\r\n      },\r\n    },\r\n  };\r\n\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n// =============================================================================\r\n// SINGLETON INSTANCES - Pre-configured toast services for each domain\r\n// =============================================================================\r\n\r\n// Base toast service for generic use\r\nexport const toastService = new ToastService();\r\n\r\n// Entity-specific toast services using the generic service\r\nexport const employeeToast = new GenericEntityToastService(employeeToastConfig);\r\nexport const delegationToast = new GenericEntityToastService(\r\n  delegationToastConfig\r\n);\r\nexport const vehicleToast = new GenericEntityToastService(vehicleToastConfig);\r\nexport const taskToast = new GenericEntityToastService(taskToastConfig);\r\nexport const serviceRecordToast = new ServiceRecordToastService();\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;AAED;;AA6CA;;CAEC,GACD,MAAM;IACJ;;GAEC,GACD,KAAK,OAAqB,EAAE;QAC1B,OAAO,CAAA,GAAA,qIAAA,CAAA,QAAK,AAAD,EAAE;YACX,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO,IAAI;YAC5B,GAAI,QAAQ,QAAQ,IAAI;gBAAE,UAAU,QAAQ,QAAQ;YAAC,CAAC;QACxD;IACF;IAEA;;GAEC,GACD,QAAQ,KAAa,EAAE,WAAoB,EAAE;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,MAAM,KAAa,EAAE,WAAoB,EAAE;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,KAAK,KAAa,EAAE,WAAoB,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;AACF;AAKO,MAAM,kCAA2C;IAC9C,OAA6B;IAErC,YAAY,MAA4B,CAAE;QACxC,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;IAEA;;GAEC,GACD,kBAAkB,KAAa,EAAE;QAC/B,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;IAEjD;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;AACF;AAEA,gFAAgF;AAChF,gEAAgE;AAChE,gFAAgF;AAEhF;;CAEC,GACD,MAAM,sBAA2D;IAC/D,YAAY;IACZ,gBAAgB,CAAA,WAAY,SAAS,IAAI;IACzC,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,cAAc,EAAE,KAAK,gCAAgC,CAAC;QAC3D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,GAAG,KAAK,8CAA8C,CAAC;QAC3D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,wBAGD;IACH,YAAY;IACZ,gBAAgB,CAAA,aACd,WAAW,KAAK,IAAI,WAAW,QAAQ,IAAI;IAC7C,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,gBAAgB,EAAE,KAAK,gCAAgC,CAAC;QAC7D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,qBAAyE;IAC7E,YAAY;IACZ,gBAAgB,CAAA,UAAW,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;IAC7D,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,aAAa,EAAE,KAAK,gCAAgC,CAAC;QAC1D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,kBAAwE;IAC5E,YAAY;IACZ,gBAAgB,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI;IACnD,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,CAAC,UAAU,EAAE,KAAK,gCAAgC,CAAC;QAC1E;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAKO,MAAM,kCAAkC;IAC7C;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,wBACA,GAAG,YAAY,cAAc,EAAE,YAAY,+BAA+B,CAAC;IAE/E;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,cAAc,EAAE,YAAY,mBAAmB,CAAC;IAEnE;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,qBAAqB,EAAE,YAAY,+BAA+B,CAAC;IAEtF;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,gCACA,SAAS;IAEb;IAEA;;GAEC,GACD,yBAAyB,KAAa,EAAE;QACtC,OAAO,IAAI,CAAC,KAAK,CACf,iBACA,SAAS;IAEb;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,mCACA,SAAS;IAEb;AACF;AAUO,SAAS,yBAA4B,MAA4B;IACtE,OAAO,IAAI,0BAA6B;AAC1C;AAMO,SAAS,+BACd,UAAkB,EAClB,cAAqC;IAErC,MAAM,SAA+B;QACnC;QACA;QACA,UAAU;YACR,SAAS;gBACP,OAAO,GAAG,WAAW,QAAQ,CAAC;gBAC9B,aAAa,CAAC,cACZ,CAAC,IAAI,EAAE,WAAW,WAAW,GAAG,EAAE,EAAE,YAAY,gCAAgC,CAAC;YACrF;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,kBAAkB,CAAC;YACtC;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,8BAA8B,CAAC;YAClD;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,aAAa;gBACX,OAAO;gBACP,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;QACF;IACF;IAEA,OAAO,IAAI,0BAA6B;AAC1C;AAOO,MAAM,eAAe,IAAI;AAGzB,MAAM,gBAAgB,IAAI,0BAA0B;AACpD,MAAM,kBAAkB,IAAI,0BACjC;AAEK,MAAM,eAAe,IAAI,0BAA0B;AACnD,MAAM,YAAY,IAAI,0BAA0B;AAChD,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 7392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/forms/useFormToast.ts"], "sourcesContent": ["/**\r\n * Form Toast Hook - Integrates generic toast service with form operations\r\n *\r\n * This hook provides a consistent interface for showing toast notifications\r\n * in form components, supporting both generic and entity-specific messaging.\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport {\r\n  type EntityToastConfig,\r\n  type GenericEntityToastService,\r\n  toastService,\r\n  createEntityToastService,\r\n} from '@/lib/services/toastService';\r\n\r\nexport interface FormToastOptions {\r\n  successTitle?: string;\r\n  successDescription?: string;\r\n  errorTitle?: string;\r\n  errorDescription?: string;\r\n}\r\n\r\nexport interface EntityFormToastOptions<T> {\r\n  entityConfig?: EntityToastConfig<T>;\r\n  entityService?: GenericEntityToastService<T>;\r\n}\r\n\r\n/**\r\n * Hook for generic form toast notifications\r\n */\r\nexport function useFormToast() {\r\n  const showSuccess = useCallback((title: string, description?: string) => {\r\n    return toastService.success(title, description);\r\n  }, []);\r\n\r\n  const showError = useCallback((title: string, description?: string) => {\r\n    return toastService.error(title, description);\r\n  }, []);\r\n\r\n  const showInfo = useCallback((title: string, description?: string) => {\r\n    return toastService.info(title, description);\r\n  }, []);\r\n\r\n  const showFormSuccess = useCallback(\r\n    (options?: FormToastOptions) => {\r\n      return showSuccess(\r\n        options?.successTitle || 'Success',\r\n        options?.successDescription || 'Operation completed successfully'\r\n      );\r\n    },\r\n    [showSuccess]\r\n  );\r\n\r\n  const showFormError = useCallback(\r\n    (error: Error | string, options?: FormToastOptions) => {\r\n      const errorMessage = error instanceof Error ? error.message : error;\r\n      return showError(\r\n        options?.errorTitle || 'Error',\r\n        options?.errorDescription ||\r\n          errorMessage ||\r\n          'An unexpected error occurred'\r\n      );\r\n    },\r\n    [showError]\r\n  );\r\n\r\n  return {\r\n    showSuccess,\r\n    showError,\r\n    showInfo,\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for entity-specific form toast notifications\r\n */\r\nexport function useEntityFormToast<T>(\r\n  entityConfig?: EntityToastConfig<T>,\r\n  entityService?: GenericEntityToastService<T>\r\n) {\r\n  const { showFormSuccess, showFormError } = useFormToast();\r\n\r\n  // Create or use provided entity service\r\n  const entityToastService =\r\n    entityService ||\r\n    (entityConfig ? createEntityToastService(entityConfig) : null);\r\n\r\n  const showEntityCreated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityCreated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Created',\r\n        successDescription: 'Item has been created successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityUpdated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityUpdated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Updated',\r\n        successDescription: 'Item has been updated successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityDeleted = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityDeleted(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Deleted',\r\n        successDescription: 'Item has been deleted successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityCreationError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityCreationError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Creation Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityUpdateError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityUpdateError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Update Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityDeletionError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityDeletionError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Deletion Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  return {\r\n    showEntityCreated,\r\n    showEntityUpdated,\r\n    showEntityDeleted,\r\n    showEntityCreationError,\r\n    showEntityUpdateError,\r\n    showEntityDeletionError,\r\n    // Also expose generic methods\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for predefined entity toast services\r\n */\r\nexport function usePredefinedEntityToast(\r\n  entityType: 'employee' | 'vehicle' | 'task' | 'delegation'\r\n) {\r\n  let entityService: GenericEntityToastService<any>;\r\n\r\n  // Lazy import to avoid circular dependencies\r\n  switch (entityType) {\r\n    case 'employee':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').employeeToast;\r\n      break;\r\n    case 'vehicle':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').vehicleToast;\r\n      break;\r\n    case 'task':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').taskToast;\r\n      break;\r\n    case 'delegation':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').delegationToast;\r\n      break;\r\n    default:\r\n      throw new Error(`Unknown entity type: ${entityType}`);\r\n  }\r\n\r\n  return useEntityFormToast(undefined, entityService);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AAEA;;;AAsBO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,OAAO,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,OAAO;IACrC,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC5C,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;IACnC,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC3C,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO;IAClC,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,OAAO,YACL,SAAS,gBAAgB,WACzB,SAAS,sBAAsB;IAEnC,GACA;QAAC;KAAY;IAGf,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,OAAuB;QACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC9D,OAAO,UACL,SAAS,cAAc,SACvB,SAAS,oBACP,gBACA;IAEN,GACA;QAAC;KAAU;IAGb,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,mBACd,YAAmC,EACnC,aAA4C;IAE5C,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG;IAE3C,wCAAwC;IACxC,MAAM,qBACJ,iBACA,CAAC,eAAe,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,gBAAgB,IAAI;IAE/D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,mBAAmB,CAAC;QAChD;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAkB;IAC9D,GACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,iBAAiB,CAAC;QAC9C;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAgB;IAC5D,GACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,mBAAmB,CAAC;QAChD;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAkB;IAC9D,GACA;QAAC;QAAoB;KAAc;IAGrC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,8BAA8B;QAC9B;QACA;IACF;AACF;AAKO,SAAS,yBACd,UAA0D;IAE1D,IAAI;IAEJ,6CAA6C;IAC7C,OAAQ;QACN,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,aAAa;YACpE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,YAAY;YACnE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,SAAS;YAChE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,eAAe;YACtE;QACF;YACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,OAAO,mBAAmB,WAAW;AACvC", "debugId": null}}, {"offset": {"line": 7554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/admin/UserManagement.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\n\r\nimport {\r\n  Activity,\r\n  CheckCircle,\r\n  Edit,\r\n  Eye,\r\n  Loader2,\r\n  MoreHorizontal,\r\n  Plus,\r\n  RefreshCw,\r\n  Shield,\r\n  Trash2,\r\n  UserPlus,\r\n  Users,\r\n  XCircle,\r\n} from 'lucide-react';\r\nimport React, { useCallback, useEffect, useState } from 'react';\r\n\r\nimport type { User } from '@/types';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { EmployeeSelector } from '@/components/ui/forms/EmployeeSelector';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { DataTable } from '@/components/ui/tables/DataTable';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { useFormToast } from '@/hooks/forms/useFormToast';\r\nimport { adminService } from '@/lib/api/services/admin';\r\nimport { getTokenRefreshService } from '@/lib/services/TokenRefreshService';\r\n\r\nexport function UserManagement() {\r\n  const [users, setUsers] = useState<User[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n  const [editingUser, setEditingUser] = useState<null | User>(null);\r\n  const [newUser, setNewUser] = useState({\r\n    email: '',\r\n    emailVerified: false,\r\n    employee_id: '',\r\n    full_name: '',\r\n    isActive: true,\r\n    phone: '',\r\n    role: 'USER',\r\n  });\r\n  const [totalUsers, setTotalUsers] = useState(0);\r\n  const [limit] = useState(10); // Items per page for DataTable\r\n  const { showFormError, showFormSuccess } = useFormToast();\r\n\r\n  // State for Alert Dialogs\r\n  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);\r\n  const [userToDeleteId, setUserToDeleteId] = useState<null | string>(null);\r\n  const [isToggleAlertOpen, setIsToggleAlertOpen] = useState(false);\r\n  const [userToToggleId, setUserToToggleId] = useState<null | string>(null);\r\n  const [userToToggleStatus, setUserToToggleStatus] = useState<boolean | null>(\r\n    null\r\n  );\r\n\r\n  // Authentication error state\r\n  const [authError, setAuthError] = useState<null | string>(null);\r\n  const [isRefreshingAuth, setIsRefreshingAuth] = useState(false);\r\n\r\n  // Client-side email validation\r\n  const isValidEmail = (email: string) => {\r\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\r\n  };\r\n\r\n  // Helper functions for formatting\r\n  const formatDate = (dateString: null | string | undefined) => {\r\n    if (!dateString) return 'Never';\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      day: 'numeric',\r\n      month: 'short',\r\n      year: 'numeric',\r\n    });\r\n  };\r\n\r\n  const getTimeAgo = (dateString: null | string | undefined) => {\r\n    if (!dateString) return 'Never';\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return 'Just now';\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\r\n    if (diffInSeconds < 86_400)\r\n      return `${Math.floor(diffInSeconds / 3600)}h ago`;\r\n    if (diffInSeconds < 2_592_000)\r\n      return `${Math.floor(diffInSeconds / 86_400)}d ago`;\r\n    return formatDate(dateString);\r\n  };\r\n\r\n  const getUserInitials = (email: string, fullName?: string) => {\r\n    if (fullName?.trim()) {\r\n      const names = fullName.trim().split(' ');\r\n      return names.length > 1\r\n        ? `${names[0]?.[0] || ''}${names.at(-1)?.[0] || ''}`.toUpperCase()\r\n        : fullName.slice(0, 2).toUpperCase();\r\n    }\r\n    return email.slice(0, 2).toUpperCase();\r\n  };\r\n\r\n  const fetchUsers = useCallback(async () => {\r\n    setLoading(true);\r\n    setAuthError(null); // Clear previous auth errors\r\n    try {\r\n      const response = await adminService.getAllUsers({\r\n        limit: 100, // Get more users for DataTable to handle pagination\r\n        page: 1,\r\n        search: '',\r\n      });\r\n      // UserService transformer handles the data transformation automatically\r\n      setUsers(response.data || []);\r\n\r\n      // Handle pagination safely\r\n      const pagination = response.pagination || { limit: 100, total: 0 };\r\n      setTotalUsers(pagination.total);\r\n    } catch (error: any) {\r\n      // Check if this is an authentication error\r\n      const isAuthError =\r\n        error?.status === 401 ||\r\n        error?.status === 500 ||\r\n        error?.code === 'NO_TOKEN' ||\r\n        error?.code === 'INVALID_TOKEN' ||\r\n        error?.message?.includes('Authentication failed') ||\r\n        error?.message?.includes('Failed to fetch users'); // Backend returns 500 but frontend sees generic error\r\n\r\n      if (isAuthError) {\r\n        // Try to validate and refresh the session\r\n        try {\r\n          const tokenService = getTokenRefreshService();\r\n          const sessionInfo = await tokenService.getSessionInfo();\r\n\r\n          if (sessionInfo.isValid) {\r\n            // Session seems valid but API call failed - might be a server issue\r\n            setAuthError(\r\n              'Server error occurred. This might be a temporary issue. Try refreshing.'\r\n            );\r\n          } else {\r\n            if (sessionInfo.isExpired) {\r\n              setAuthError(\r\n                'Your session has expired. Click \"Refresh Authentication\" to renew your session.'\r\n              );\r\n            } else {\r\n              setAuthError(\r\n                'Authentication failed. Please refresh the page to sign in again.'\r\n              );\r\n            }\r\n          }\r\n        } catch {\r\n          setAuthError(\r\n            'Authentication system error. Please refresh the page to sign in again.'\r\n          );\r\n        }\r\n      } else {\r\n        // Non-authentication error\r\n        showFormError(error as Error, {\r\n          errorDescription: error.message || 'Failed to load user data.',\r\n          errorTitle: 'Error fetching users',\r\n        });\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [showFormError]);\r\n\r\n  useEffect(() => {\r\n    fetchUsers();\r\n  }, [fetchUsers]);\r\n\r\n  const handleAuthRefresh = async () => {\r\n    setIsRefreshingAuth(true);\r\n    setAuthError(null);\r\n\r\n    try {\r\n      const tokenService = getTokenRefreshService();\r\n      const refreshSuccess = await tokenService.refreshNow();\r\n\r\n      if (refreshSuccess) {\r\n        // Token refreshed successfully, retry the API call\r\n        await fetchUsers();\r\n      } else {\r\n        setAuthError('Failed to refresh authentication. Please sign in again.');\r\n      }\r\n    } catch {\r\n      setAuthError('Authentication refresh failed. Please sign in again.');\r\n    } finally {\r\n      setIsRefreshingAuth(false);\r\n    }\r\n  };\r\n\r\n  const handlePageRefresh = () => {\r\n    globalThis.location.reload();\r\n  };\r\n\r\n  const handleAddUser = async () => {\r\n    if (!isValidEmail(newUser.email)) {\r\n      showFormError(new Error('Please enter a valid email address.'), {\r\n        errorTitle: 'Validation Error',\r\n      });\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const createdUser = await adminService.createUser(newUser);\r\n      showFormSuccess({\r\n        successDescription: `User ${createdUser.email} has been added.`,\r\n        successTitle: 'User created',\r\n      });\r\n      setIsDialogOpen(false);\r\n      setNewUser({\r\n        email: '',\r\n        emailVerified: false,\r\n        employee_id: '',\r\n        full_name: '',\r\n        isActive: true,\r\n        phone: '',\r\n        role: 'USER',\r\n      });\r\n      fetchUsers(); // Refresh list\r\n    } catch (error: any) {\r\n      showFormError(error.message || 'Failed to create user.', {\r\n        errorTitle: 'Error creating user',\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleEditUser = async () => {\r\n    if (!editingUser) return;\r\n\r\n    if (!isValidEmail(editingUser.email || '')) {\r\n      showFormError('Please enter a valid email address.', {\r\n        errorTitle: 'Validation Error',\r\n      });\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const updatedUser = await adminService.updateUser(editingUser.id, {\r\n        email: editingUser.email,\r\n        emailVerified: editingUser.email_confirmed_at ? true : false,\r\n        isActive: editingUser.isActive,\r\n        role: editingUser.role,\r\n      });\r\n      showFormSuccess({\r\n        successDescription: `User ${(updatedUser as any).email} has been updated.`,\r\n        successTitle: 'User updated',\r\n      });\r\n      setIsDialogOpen(false);\r\n      setEditingUser(null);\r\n      fetchUsers(); // Refresh list\r\n    } catch (error: any) {\r\n      showFormError(error.message || 'Failed to update user.', {\r\n        errorTitle: 'Error updating user',\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const confirmDeleteUser = async () => {\r\n    if (!userToDeleteId) return;\r\n    setLoading(true);\r\n    try {\r\n      await adminService.deleteUser(userToDeleteId);\r\n      showFormSuccess({\r\n        successDescription: 'User has been successfully deleted.',\r\n        successTitle: 'User deleted',\r\n      });\r\n      fetchUsers(); // Refresh list\r\n    } catch (error: any) {\r\n      showFormError(error.message || 'Failed to delete user.', {\r\n        errorTitle: 'Error deleting user',\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setIsDeleteAlertOpen(false);\r\n      setUserToDeleteId(null);\r\n    }\r\n  };\r\n\r\n  const confirmToggleActivation = async () => {\r\n    if (!userToToggleId || userToToggleStatus === null) return;\r\n    setLoading(true);\r\n    try {\r\n      const updatedUser = await adminService.toggleUserActivation(\r\n        userToToggleId,\r\n        !userToToggleStatus\r\n      );\r\n      showFormSuccess({\r\n        successDescription: `User ${(updatedUser as any).email} is now ${\r\n          (updatedUser as any).isActive ? 'active' : 'inactive'\r\n        }.`,\r\n        successTitle: 'User status updated',\r\n      });\r\n      fetchUsers(); // Refresh list\r\n    } catch (error: any) {\r\n      showFormError(error.message || 'Failed to toggle user activation.', {\r\n        errorTitle: 'Error updating status',\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n      setIsToggleAlertOpen(false);\r\n      setUserToToggleId(null);\r\n      setUserToToggleStatus(null);\r\n    }\r\n  };\r\n\r\n  const getRoleBadgeColor = (role: string) => {\r\n    switch (role) {\r\n      case 'ADMIN': {\r\n        return 'bg-purple-500 hover:bg-purple-600 text-white';\r\n      }\r\n      case 'MANAGER': {\r\n        return 'bg-blue-500 hover:bg-blue-600 text-white';\r\n      }\r\n      case 'READONLY': {\r\n        return 'bg-yellow-500 hover:bg-yellow-600 text-white';\r\n      }\r\n      case 'SUPER_ADMIN': {\r\n        return 'bg-red-500 hover:bg-red-600 text-white';\r\n      }\r\n      case 'USER': {\r\n        return 'bg-green-500 hover:bg-green-600 text-white';\r\n      }\r\n      default: {\r\n        return 'bg-gray-500 hover:bg-gray-600 text-white';\r\n      }\r\n    }\r\n  };\r\n\r\n  // Define columns for DataTable\r\n  const columns: ColumnDef<User>[] = [\r\n    {\r\n      accessorKey: 'email',\r\n      cell: ({ row }) => {\r\n        const user = row.original;\r\n        return (\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Avatar className=\"size-10\">\r\n              <AvatarImage alt={user.email} src=\"\" />\r\n              <AvatarFallback className=\"text-sm\">\r\n                {getUserInitials(user.email, user.full_name)}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"min-w-0 flex-1\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <p className=\"truncate text-sm font-medium\">\r\n                  {user.full_name || user.email.split('@')[0]}\r\n                </p>\r\n                {user.email_confirmed_at && (\r\n                  <Shield className=\"size-3 text-green-500\" />\r\n                )}\r\n              </div>\r\n              <p className=\"truncate text-xs text-muted-foreground\">\r\n                {user.email}\r\n              </p>\r\n              {user.employee_id && (\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  ID: {user.employee_id}\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      header: 'User',\r\n    },\r\n    {\r\n      accessorKey: 'role',\r\n      cell: ({ row }) => {\r\n        const user = row.original;\r\n        return (\r\n          <Badge className={getRoleBadgeColor(user.role)}>\r\n            {(user.role || 'USER').replace('_', ' ')}\r\n          </Badge>\r\n        );\r\n      },\r\n      header: 'Role',\r\n    },\r\n    {\r\n      accessorKey: 'isActive',\r\n      cell: ({ row }) => {\r\n        const user = row.original;\r\n        return (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Badge variant={user.isActive ? 'default' : 'destructive'}>\r\n              {user.isActive ? 'Active' : 'Inactive'}\r\n            </Badge>\r\n            {user.email_confirmed_at ? (\r\n              <CheckCircle className=\"size-4 text-green-500\" />\r\n            ) : (\r\n              <XCircle className=\"size-4 text-red-500\" />\r\n            )}\r\n          </div>\r\n        );\r\n      },\r\n      header: 'Status',\r\n    },\r\n    {\r\n      accessorKey: 'last_sign_in_at',\r\n      cell: ({ row }) => {\r\n        const user = row.original;\r\n        return (\r\n          <div className=\"text-sm\">{getTimeAgo(user.last_sign_in_at)}</div>\r\n        );\r\n      },\r\n      header: 'Last Activity',\r\n    },\r\n    {\r\n      accessorKey: 'created_at',\r\n      cell: ({ row }) => {\r\n        const user = row.original;\r\n        return <div className=\"text-sm\">{formatDate(user.created_at)}</div>;\r\n      },\r\n      header: 'Joined',\r\n    },\r\n    {\r\n      cell: ({ row }) => {\r\n        const user = row.original;\r\n        return (\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button className=\"size-8 p-0\" variant=\"ghost\">\r\n                <span className=\"sr-only\">Open menu</span>\r\n                <MoreHorizontal className=\"size-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n              <DropdownMenuItem\r\n                onClick={() => {\r\n                  setEditingUser(user);\r\n                  setIsDialogOpen(true);\r\n                }}\r\n              >\r\n                <Edit className=\"mr-2 size-4\" />\r\n                Edit user\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => {\r\n                  setUserToToggleId(user.id);\r\n                  setUserToToggleStatus(user.isActive);\r\n                  setIsToggleAlertOpen(true);\r\n                }}\r\n              >\r\n                <Activity className=\"mr-2 size-4\" />\r\n                {user.isActive ? 'Deactivate' : 'Activate'}\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                className=\"text-red-600 focus:text-red-600\"\r\n                onClick={() => {\r\n                  setUserToDeleteId(user.id);\r\n                  setIsDeleteAlertOpen(true);\r\n                }}\r\n              >\r\n                <Trash2 className=\"mr-2 size-4\" />\r\n                Delete user\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        );\r\n      },\r\n      header: 'Actions',\r\n      id: 'actions',\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header Section with Statistics */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h2 className=\"text-3xl font-bold tracking-tight\">\r\n              User Management\r\n            </h2>\r\n            <p className=\"text-muted-foreground\">\r\n              Manage user accounts, roles, and permissions\r\n            </p>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Button onClick={fetchUsers} size=\"sm\" variant=\"outline\">\r\n              <RefreshCw className=\"mr-2 size-4\" />\r\n              Refresh\r\n            </Button>\r\n            <Dialog onOpenChange={setIsDialogOpen} open={isDialogOpen}>\r\n              <DialogTrigger asChild>\r\n                <Button\r\n                  onClick={() => {\r\n                    setEditingUser(null);\r\n                    setNewUser({\r\n                      email: '',\r\n                      emailVerified: false,\r\n                      employee_id: '',\r\n                      full_name: '',\r\n                      isActive: true,\r\n                      phone: '',\r\n                      role: 'USER',\r\n                    });\r\n                  }}\r\n                >\r\n                  <Plus className=\"mr-2 size-4\" /> Add User\r\n                </Button>\r\n              </DialogTrigger>\r\n              <DialogContent className=\"sm:max-w-[600px]\">\r\n                <DialogHeader>\r\n                  <DialogTitle className=\"flex items-center gap-2\">\r\n                    {editingUser ? (\r\n                      <>\r\n                        <Edit className=\"size-5\" />\r\n                        Edit User Profile\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <UserPlus className=\"size-5\" />\r\n                        Add New User\r\n                      </>\r\n                    )}\r\n                  </DialogTitle>\r\n                  <DialogDescription>\r\n                    {editingUser\r\n                      ? 'Update user information and permissions.'\r\n                      : 'Create a new user account with role and permissions.'}\r\n                  </DialogDescription>\r\n                </DialogHeader>\r\n\r\n                <Tabs className=\"w-full\" defaultValue=\"basic\">\r\n                  <TabsList className=\"grid w-full grid-cols-2\">\r\n                    <TabsTrigger value=\"basic\">Basic Info</TabsTrigger>\r\n                    <TabsTrigger value=\"advanced\">Advanced</TabsTrigger>\r\n                  </TabsList>\r\n\r\n                  <TabsContent className=\"mt-4 space-y-4\" value=\"basic\">\r\n                    <div className=\"grid gap-4\">\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <Label className=\"text-right\" htmlFor=\"full_name\">\r\n                          Full Name\r\n                        </Label>\r\n                        <Input\r\n                          className=\"col-span-3\"\r\n                          id=\"full_name\"\r\n                          onChange={e =>\r\n                            editingUser\r\n                              ? setEditingUser({\r\n                                  ...editingUser,\r\n                                  full_name: e.target.value,\r\n                                })\r\n                              : setNewUser({\r\n                                  ...newUser,\r\n                                  full_name: e.target.value,\r\n                                })\r\n                          }\r\n                          placeholder=\"Enter full name\"\r\n                          value={\r\n                            editingUser\r\n                              ? editingUser.full_name || ''\r\n                              : newUser.full_name\r\n                          }\r\n                        />\r\n                      </div>\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <Label className=\"text-right\" htmlFor=\"email\">\r\n                          Email *\r\n                        </Label>\r\n                        <Input\r\n                          className=\"col-span-3\"\r\n                          id=\"email\"\r\n                          onChange={e =>\r\n                            editingUser\r\n                              ? setEditingUser({\r\n                                  ...editingUser,\r\n                                  email: e.target.value,\r\n                                })\r\n                              : setNewUser({\r\n                                  ...newUser,\r\n                                  email: e.target.value,\r\n                                })\r\n                          }\r\n                          placeholder=\"<EMAIL>\"\r\n                          type=\"email\"\r\n                          value={\r\n                            editingUser ? editingUser.email : newUser.email\r\n                          }\r\n                        />\r\n                      </div>\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <Label className=\"text-right\" htmlFor=\"phone\">\r\n                          Phone\r\n                        </Label>\r\n                        <Input\r\n                          className=\"col-span-3\"\r\n                          id=\"phone\"\r\n                          onChange={e =>\r\n                            editingUser\r\n                              ? setEditingUser({\r\n                                  ...editingUser,\r\n                                  phone: e.target.value,\r\n                                })\r\n                              : setNewUser({\r\n                                  ...newUser,\r\n                                  phone: e.target.value,\r\n                                })\r\n                          }\r\n                          placeholder=\"+****************\"\r\n                          type=\"tel\"\r\n                          value={\r\n                            editingUser\r\n                              ? editingUser.phone || ''\r\n                              : newUser.phone\r\n                          }\r\n                        />\r\n                      </div>\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <Label className=\"text-right\" htmlFor=\"role\">\r\n                          Role *\r\n                        </Label>\r\n                        <Select\r\n                          onValueChange={value =>\r\n                            editingUser\r\n                              ? setEditingUser({ ...editingUser, role: value })\r\n                              : setNewUser({ ...newUser, role: value })\r\n                          }\r\n                          value={editingUser ? editingUser.role : newUser.role}\r\n                        >\r\n                          <SelectTrigger className=\"col-span-3\">\r\n                            <SelectValue placeholder=\"Select a role\" />\r\n                          </SelectTrigger>\r\n                          <SelectContent>\r\n                            <SelectItem value=\"SUPER_ADMIN\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <Shield className=\"size-4 text-red-500\" />\r\n                                Super Admin\r\n                              </div>\r\n                            </SelectItem>\r\n                            <SelectItem value=\"ADMIN\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <Shield className=\"size-4 text-purple-500\" />\r\n                                Admin\r\n                              </div>\r\n                            </SelectItem>\r\n                            <SelectItem value=\"MANAGER\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <Users className=\"size-4 text-blue-500\" />\r\n                                Manager\r\n                              </div>\r\n                            </SelectItem>\r\n                            <SelectItem value=\"USER\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <Users className=\"size-4 text-green-500\" />\r\n                                User\r\n                              </div>\r\n                            </SelectItem>\r\n                            <SelectItem value=\"READONLY\">\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <Eye className=\"size-4 text-yellow-500\" />\r\n                                Read Only\r\n                              </div>\r\n                            </SelectItem>\r\n                          </SelectContent>\r\n                        </Select>\r\n                      </div>\r\n                    </div>\r\n                  </TabsContent>\r\n\r\n                  <TabsContent className=\"mt-4 space-y-4\" value=\"advanced\">\r\n                    <div className=\"grid gap-4\">\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <div className=\"col-span-4\">\r\n                          <EmployeeSelector\r\n                            allowClear={true}\r\n                            className=\"w-full\"\r\n                            label=\"Link to Employee (Optional)\"\r\n                            onValueChange={employeeId =>\r\n                              editingUser\r\n                                ? setEditingUser({\r\n                                    ...editingUser,\r\n                                    employee_id: employeeId,\r\n                                  })\r\n                                : setNewUser({\r\n                                    ...newUser,\r\n                                    employee_id: employeeId?.toString() || '',\r\n                                  })\r\n                            }\r\n                            placeholder=\"Select an employee to link this user account...\"\r\n                            value={\r\n                              editingUser\r\n                                ? (editingUser.employee_id ?? null)\r\n                                : newUser.employee_id\r\n                                  ? Number.parseInt(newUser.employee_id)\r\n                                  : null\r\n                            }\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <Label className=\"text-right\" htmlFor=\"isActive\">\r\n                          Account Status\r\n                        </Label>\r\n                        <div className=\"col-span-3 flex items-center space-x-2\">\r\n                          <Checkbox\r\n                            checked={\r\n                              editingUser\r\n                                ? editingUser.isActive\r\n                                : newUser.isActive\r\n                            }\r\n                            id=\"isActive\"\r\n                            onCheckedChange={checked =>\r\n                              editingUser\r\n                                ? setEditingUser({\r\n                                    ...editingUser,\r\n                                    isActive: Boolean(checked),\r\n                                  })\r\n                                : setNewUser({\r\n                                    ...newUser,\r\n                                    isActive: Boolean(checked),\r\n                                  })\r\n                            }\r\n                          />\r\n                          <Label className=\"text-sm\" htmlFor=\"isActive\">\r\n                            Active account\r\n                          </Label>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"grid grid-cols-4 items-center gap-4\">\r\n                        <Label className=\"text-right\" htmlFor=\"emailVerified\">\r\n                          Email Verification\r\n                        </Label>\r\n                        <div className=\"col-span-3 flex items-center space-x-2\">\r\n                          <Checkbox\r\n                            checked={\r\n                              editingUser\r\n                                ? editingUser.email_confirmed_at\r\n                                  ? true\r\n                                  : false\r\n                                : newUser.emailVerified\r\n                            }\r\n                            id=\"emailVerified\"\r\n                            onCheckedChange={checked =>\r\n                              editingUser\r\n                                ? setEditingUser({\r\n                                    ...editingUser,\r\n                                    email_confirmed_at: checked\r\n                                      ? new Date().toISOString()\r\n                                      : null,\r\n                                  })\r\n                                : setNewUser({\r\n                                    ...newUser,\r\n                                    emailVerified: Boolean(checked),\r\n                                  })\r\n                            }\r\n                          />\r\n                          <Label className=\"text-sm\" htmlFor=\"emailVerified\">\r\n                            Email verified\r\n                          </Label>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </TabsContent>\r\n                </Tabs>\r\n                <DialogFooter>\r\n                  <Button\r\n                    disabled={loading}\r\n                    onClick={editingUser ? handleEditUser : handleAddUser}\r\n                    type=\"submit\"\r\n                  >\r\n                    {loading && (\r\n                      <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n                    )}\r\n                    {editingUser ? 'Save changes' : 'Add User'}\r\n                  </Button>\r\n                </DialogFooter>\r\n              </DialogContent>\r\n            </Dialog>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Statistics Cards */}\r\n        <div className=\"grid gap-4 md:grid-cols-4\">\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Total Users</CardTitle>\r\n              <Users className=\"size-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{totalUsers}</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                {users.filter(u => u.isActive).length} active\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Active Users\r\n              </CardTitle>\r\n              <Activity className=\"size-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {users.filter(u => u.isActive).length}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                {Math.round(\r\n                  (users.filter(u => u.isActive).length /\r\n                    Math.max(users.length, 1)) *\r\n                    100\r\n                )}\r\n                % of total\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Verified Emails\r\n              </CardTitle>\r\n              <Shield className=\"size-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {users.filter(u => u.email_confirmed_at).length}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                {Math.round(\r\n                  (users.filter(u => u.email_confirmed_at).length /\r\n                    Math.max(users.length, 1)) *\r\n                    100\r\n                )}\r\n                % verified\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Admins</CardTitle>\r\n              <Shield className=\"size-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {\r\n                  users.filter(\r\n                    u => u.role === 'ADMIN' || u.role === 'SUPER_ADMIN'\r\n                  ).length\r\n                }\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                System administrators\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n\r\n      {authError ? (\r\n        <div className=\"rounded-md border border-red-200 bg-red-50 p-4\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <XCircle className=\"size-5 text-red-500\" />\r\n            <div>\r\n              <h3 className=\"font-medium text-red-800\">Authentication Error</h3>\r\n              <p className=\"text-sm text-red-700\">{authError}</p>\r\n              <div className=\"mt-3 flex space-x-2\">\r\n                <Button\r\n                  disabled={isRefreshingAuth}\r\n                  onClick={handleAuthRefresh}\r\n                  size=\"sm\"\r\n                >\r\n                  {isRefreshingAuth ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n                      Refreshing...\r\n                    </>\r\n                  ) : (\r\n                    'Refresh Authentication'\r\n                  )}\r\n                </Button>\r\n                <Button onClick={handlePageRefresh} size=\"sm\" variant=\"outline\">\r\n                  Refresh Page\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ) : loading ? (\r\n        <div className=\"flex h-64 items-center justify-center\">\r\n          <Loader2 className=\"size-8 animate-spin text-primary\" />\r\n          <span className=\"ml-2 text-lg\">Loading users...</span>\r\n        </div>\r\n      ) : (\r\n        <DataTable\r\n          className=\"w-full\"\r\n          columns={columns}\r\n          data={users}\r\n          emptyMessage=\"No users found.\"\r\n          enableColumnVisibility={true}\r\n          enableGlobalFilter={true}\r\n          enableRowSelection={false}\r\n          pageSize={limit}\r\n          searchColumn=\"email\"\r\n          searchPlaceholder=\"Search users by email or role...\"\r\n        />\r\n      )}\r\n\r\n      {/* Alert Dialogs (moved outside of TableRow) */}\r\n      <AlertDialog onOpenChange={setIsToggleAlertOpen} open={isToggleAlertOpen}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>\r\n              {userToToggleStatus ? 'Deactivate User' : 'Activate User'}\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to{' '}\r\n              {userToToggleStatus ? 'deactivate' : 'activate'} user{' '}\r\n              <span className=\"font-bold\">\r\n                {users.find(u => u.id === userToToggleId)?.email}\r\n              </span>\r\n              ?\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction onClick={confirmToggleActivation}>\r\n              {userToToggleStatus ? 'Deactivate' : 'Activate'}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      <AlertDialog onOpenChange={setIsDeleteAlertOpen} open={isDeleteAlertOpen}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Delete User</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to permanently delete user{' '}\r\n              <span className=\"font-bold\">\r\n                {users.find(u => u.id === userToDeleteId)?.email}\r\n              </span>\r\n              ? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction onClick={confirmDeleteUser}>\r\n              Delete\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAIA;AAUA;AACA;AACA;AACA;AACA;AACA;AASA;AAQA;AACA;AACA;AACA;AAOA;AACA;AAEA;AACA;AAAA;AACA;AAtEA;;;;;;;;;;;;;;;;;;;;;AAwEO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,eAAe;QACf,aAAa;QACb,WAAW;QACX,UAAU;QACV,OAAO;QACP,MAAM;IACR;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,+BAA+B;IAC7D,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAEtD,0BAA0B;IAC1B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzD;IAGF,6BAA6B;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,+BAA+B;IAC/B,MAAM,eAAe,CAAC;QACpB,OAAO,6BAA6B,IAAI,CAAC;IAC3C;IAEA,kCAAkC;IAClC,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,IAAI,gBAAgB,QAClB,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;QACnD,IAAI,gBAAgB,WAClB,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,QAAQ,KAAK,CAAC;QACrD,OAAO,WAAW;IACpB;IAEA,MAAM,kBAAkB,CAAC,OAAe;QACtC,IAAI,UAAU,QAAQ;YACpB,MAAM,QAAQ,SAAS,IAAI,GAAG,KAAK,CAAC;YACpC,OAAO,MAAM,MAAM,GAAG,IAClB,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW,KAC9D,SAAS,KAAK,CAAC,GAAG,GAAG,WAAW;QACtC;QACA,OAAO,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;IACtC;IAEA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,WAAW;QACX,aAAa,OAAO,6BAA6B;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,sJAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBAC9C,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;YACA,wEAAwE;YACxE,SAAS,SAAS,IAAI,IAAI,EAAE;YAE5B,2BAA2B;YAC3B,MAAM,aAAa,SAAS,UAAU,IAAI;gBAAE,OAAO;gBAAK,OAAO;YAAE;YACjE,cAAc,WAAW,KAAK;QAChC,EAAE,OAAO,OAAY;YACnB,2CAA2C;YAC3C,MAAM,cACJ,OAAO,WAAW,OAClB,OAAO,WAAW,OAClB,OAAO,SAAS,cAChB,OAAO,SAAS,mBAChB,OAAO,SAAS,SAAS,4BACzB,OAAO,SAAS,SAAS,0BAA0B,sDAAsD;YAE3G,IAAI,aAAa;gBACf,0CAA0C;gBAC1C,IAAI;oBACF,MAAM,eAAe,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;oBAC1C,MAAM,cAAc,MAAM,aAAa,cAAc;oBAErD,IAAI,YAAY,OAAO,EAAE;wBACvB,oEAAoE;wBACpE,aACE;oBAEJ,OAAO;wBACL,IAAI,YAAY,SAAS,EAAE;4BACzB,aACE;wBAEJ,OAAO;4BACL,aACE;wBAEJ;oBACF;gBACF,EAAE,OAAM;oBACN,aACE;gBAEJ;YACF,OAAO;gBACL,2BAA2B;gBAC3B,cAAc,OAAgB;oBAC5B,kBAAkB,MAAM,OAAO,IAAI;oBACnC,YAAY;gBACd;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,oBAAoB;QACxB,oBAAoB;QACpB,aAAa;QAEb,IAAI;YACF,MAAM,eAAe,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;YAC1C,MAAM,iBAAiB,MAAM,aAAa,UAAU;YAEpD,IAAI,gBAAgB;gBAClB,mDAAmD;gBACnD,MAAM;YACR,OAAO;gBACL,aAAa;YACf;QACF,EAAE,OAAM;YACN,aAAa;QACf,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW,QAAQ,CAAC,MAAM;IAC5B;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,aAAa,QAAQ,KAAK,GAAG;YAChC,cAAc,IAAI,MAAM,wCAAwC;gBAC9D,YAAY;YACd;YACA;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,cAAc,MAAM,sJAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YAClD,gBAAgB;gBACd,oBAAoB,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,gBAAgB,CAAC;gBAC/D,cAAc;YAChB;YACA,gBAAgB;YAChB,WAAW;gBACT,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;YACA,cAAc,eAAe;QAC/B,EAAE,OAAO,OAAY;YACnB,cAAc,MAAM,OAAO,IAAI,0BAA0B;gBACvD,YAAY;YACd;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,aAAa;QAElB,IAAI,CAAC,aAAa,YAAY,KAAK,IAAI,KAAK;YAC1C,cAAc,uCAAuC;gBACnD,YAAY;YACd;YACA;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,cAAc,MAAM,sJAAA,CAAA,eAAY,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE;gBAChE,OAAO,YAAY,KAAK;gBACxB,eAAe,YAAY,kBAAkB,GAAG,OAAO;gBACvD,UAAU,YAAY,QAAQ;gBAC9B,MAAM,YAAY,IAAI;YACxB;YACA,gBAAgB;gBACd,oBAAoB,CAAC,KAAK,EAAE,AAAC,YAAoB,KAAK,CAAC,kBAAkB,CAAC;gBAC1E,cAAc;YAChB;YACA,gBAAgB;YAChB,eAAe;YACf,cAAc,eAAe;QAC/B,EAAE,OAAO,OAAY;YACnB,cAAc,MAAM,OAAO,IAAI,0BAA0B;gBACvD,YAAY;YACd;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB;QACrB,WAAW;QACX,IAAI;YACF,MAAM,sJAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YAC9B,gBAAgB;gBACd,oBAAoB;gBACpB,cAAc;YAChB;YACA,cAAc,eAAe;QAC/B,EAAE,OAAO,OAAY;YACnB,cAAc,MAAM,OAAO,IAAI,0BAA0B;gBACvD,YAAY;YACd;QACF,SAAU;YACR,WAAW;YACX,qBAAqB;YACrB,kBAAkB;QACpB;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI,CAAC,kBAAkB,uBAAuB,MAAM;QACpD,WAAW;QACX,IAAI;YACF,MAAM,cAAc,MAAM,sJAAA,CAAA,eAAY,CAAC,oBAAoB,CACzD,gBACA,CAAC;YAEH,gBAAgB;gBACd,oBAAoB,CAAC,KAAK,EAAE,AAAC,YAAoB,KAAK,CAAC,QAAQ,EAC7D,AAAC,YAAoB,QAAQ,GAAG,WAAW,WAC5C,CAAC,CAAC;gBACH,cAAc;YAChB;YACA,cAAc,eAAe;QAC/B,EAAE,OAAO,OAAY;YACnB,cAAc,MAAM,OAAO,IAAI,qCAAqC;gBAClE,YAAY;YACd;QACF,SAAU;YACR,WAAW;YACX,qBAAqB;YACrB,kBAAkB;YAClB,sBAAsB;QACxB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAS;oBACZ,OAAO;gBACT;YACA,KAAK;gBAAW;oBACd,OAAO;gBACT;YACA,KAAK;gBAAY;oBACf,OAAO;gBACT;YACA,KAAK;gBAAe;oBAClB,OAAO;gBACT;YACA,KAAK;gBAAQ;oBACX,OAAO;gBACT;YACA;gBAAS;oBACP,OAAO;gBACT;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,UAA6B;QACjC;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,KAAK,KAAK,KAAK;oCAAE,KAAI;;;;;;8CAClC,8OAAC,kIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,gBAAgB,KAAK,KAAK,EAAE,KAAK,SAAS;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,KAAK,SAAS,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;wCAE5C,KAAK,kBAAkB,kBACtB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;8CAGtB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,WAAW,kBACf,8OAAC;oCAAE,WAAU;;wCAAgC;wCACtC,KAAK,WAAW;;;;;;;;;;;;;;;;;;;YAMjC;YACA,QAAQ;QACV;QACA;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAW,kBAAkB,KAAK,IAAI;8BAC1C,CAAC,KAAK,IAAI,IAAI,MAAM,EAAE,OAAO,CAAC,KAAK;;;;;;YAG1C;YACA,QAAQ;QACV;QACA;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;sCACzC,KAAK,QAAQ,GAAG,WAAW;;;;;;wBAE7B,KAAK,kBAAkB,iBACtB,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;YAI3B;YACA,QAAQ;QACV;QACA;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,8OAAC;oBAAI,WAAU;8BAAW,WAAW,KAAK,eAAe;;;;;;YAE7D;YACA,QAAQ;QACV;QACA;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBAAO,8OAAC;oBAAI,WAAU;8BAAW,WAAW,KAAK,UAAU;;;;;;YAC7D;YACA,QAAQ;QACV;QACA;YACE,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ;gBACzB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;sCACX,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAa,SAAQ;;kDACrC,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAM;;8CACzB,8OAAC,4IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,4IAAA,CAAA,mBAAgB;oCACf,SAAS;wCACP,eAAe;wCACf,gBAAgB;oCAClB;;sDAEA,8OAAC,2MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;8CAGlC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8CACtB,8OAAC,4IAAA,CAAA,mBAAgB;oCACf,SAAS;wCACP,kBAAkB,KAAK,EAAE;wCACzB,sBAAsB,KAAK,QAAQ;wCACnC,qBAAqB;oCACvB;;sDAEA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,KAAK,QAAQ,GAAG,eAAe;;;;;;;8CAElC,8OAAC,4IAAA,CAAA,mBAAgB;oCACf,WAAU;oCACV,SAAS;wCACP,kBAAkB,KAAK,EAAE;wCACzB,qBAAqB;oCACvB;;sDAEA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;;;;;;;;;;;;;YAM5C;YACA,QAAQ;YACR,IAAI;QACN;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAY,MAAK;wCAAK,SAAQ;;0DAC7C,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;kDAGvC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,cAAc;wCAAiB,MAAM;;0DAC3C,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;wDACP,eAAe;wDACf,WAAW;4DACT,OAAO;4DACP,eAAe;4DACf,aAAa;4DACb,WAAW;4DACX,UAAU;4DACV,OAAO;4DACP,MAAM;wDACR;oDACF;;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAgB;;;;;;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,kIAAA,CAAA,eAAY;;0EACX,8OAAC,kIAAA,CAAA,cAAW;gEAAC,WAAU;0EACpB,4BACC;;sFACE,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAW;;iGAI7B;;sFACE,8OAAC,8MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAW;;;;;;;;0EAKrC,8OAAC,kIAAA,CAAA,oBAAiB;0EACf,cACG,6CACA;;;;;;;;;;;;kEAIR,8OAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;wDAAS,cAAa;;0EACpC,8OAAC,gIAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,8OAAC,gIAAA,CAAA,cAAW;wEAAC,OAAM;kFAAQ;;;;;;kFAC3B,8OAAC,gIAAA,CAAA,cAAW;wEAAC,OAAM;kFAAW;;;;;;;;;;;;0EAGhC,8OAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;gEAAiB,OAAM;0EAC5C,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;oFAAa,SAAQ;8FAAY;;;;;;8FAGlD,8OAAC,iIAAA,CAAA,QAAK;oFACJ,WAAU;oFACV,IAAG;oFACH,UAAU,CAAA,IACR,cACI,eAAe;4FACb,GAAG,WAAW;4FACd,WAAW,EAAE,MAAM,CAAC,KAAK;wFAC3B,KACA,WAAW;4FACT,GAAG,OAAO;4FACV,WAAW,EAAE,MAAM,CAAC,KAAK;wFAC3B;oFAEN,aAAY;oFACZ,OACE,cACI,YAAY,SAAS,IAAI,KACzB,QAAQ,SAAS;;;;;;;;;;;;sFAI3B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;oFAAa,SAAQ;8FAAQ;;;;;;8FAG9C,8OAAC,iIAAA,CAAA,QAAK;oFACJ,WAAU;oFACV,IAAG;oFACH,UAAU,CAAA,IACR,cACI,eAAe;4FACb,GAAG,WAAW;4FACd,OAAO,EAAE,MAAM,CAAC,KAAK;wFACvB,KACA,WAAW;4FACT,GAAG,OAAO;4FACV,OAAO,EAAE,MAAM,CAAC,KAAK;wFACvB;oFAEN,aAAY;oFACZ,MAAK;oFACL,OACE,cAAc,YAAY,KAAK,GAAG,QAAQ,KAAK;;;;;;;;;;;;sFAIrD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;oFAAa,SAAQ;8FAAQ;;;;;;8FAG9C,8OAAC,iIAAA,CAAA,QAAK;oFACJ,WAAU;oFACV,IAAG;oFACH,UAAU,CAAA,IACR,cACI,eAAe;4FACb,GAAG,WAAW;4FACd,OAAO,EAAE,MAAM,CAAC,KAAK;wFACvB,KACA,WAAW;4FACT,GAAG,OAAO;4FACV,OAAO,EAAE,MAAM,CAAC,KAAK;wFACvB;oFAEN,aAAY;oFACZ,MAAK;oFACL,OACE,cACI,YAAY,KAAK,IAAI,KACrB,QAAQ,KAAK;;;;;;;;;;;;sFAIvB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;oFAAa,SAAQ;8FAAO;;;;;;8FAG7C,8OAAC,kIAAA,CAAA,SAAM;oFACL,eAAe,CAAA,QACb,cACI,eAAe;4FAAE,GAAG,WAAW;4FAAE,MAAM;wFAAM,KAC7C,WAAW;4FAAE,GAAG,OAAO;4FAAE,MAAM;wFAAM;oFAE3C,OAAO,cAAc,YAAY,IAAI,GAAG,QAAQ,IAAI;;sGAEpD,8OAAC,kIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gGAAC,aAAY;;;;;;;;;;;sGAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8GACZ,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAChB,cAAA,8OAAC;wGAAI,WAAU;;0HACb,8OAAC,sMAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;4GAAwB;;;;;;;;;;;;8GAI9C,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAChB,cAAA,8OAAC;wGAAI,WAAU;;0HACb,8OAAC,sMAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;4GAA2B;;;;;;;;;;;;8GAIjD,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAChB,cAAA,8OAAC;wGAAI,WAAU;;0HACb,8OAAC,oMAAA,CAAA,QAAK;gHAAC,WAAU;;;;;;4GAAyB;;;;;;;;;;;;8GAI9C,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAChB,cAAA,8OAAC;wGAAI,WAAU;;0HACb,8OAAC,oMAAA,CAAA,QAAK;gHAAC,WAAU;;;;;;4GAA0B;;;;;;;;;;;;8GAI/C,8OAAC,kIAAA,CAAA,aAAU;oGAAC,OAAM;8GAChB,cAAA,8OAAC;wGAAI,WAAU;;0HACb,8OAAC,gMAAA,CAAA,MAAG;gHAAC,WAAU;;;;;;4GAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAUxD,8OAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;gEAAiB,OAAM;0EAC5C,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC,qJAAA,CAAA,mBAAgB;oFACf,YAAY;oFACZ,WAAU;oFACV,OAAM;oFACN,eAAe,CAAA,aACb,cACI,eAAe;4FACb,GAAG,WAAW;4FACd,aAAa;wFACf,KACA,WAAW;4FACT,GAAG,OAAO;4FACV,aAAa,YAAY,cAAc;wFACzC;oFAEN,aAAY;oFACZ,OACE,cACK,YAAY,WAAW,IAAI,OAC5B,QAAQ,WAAW,GACjB,OAAO,QAAQ,CAAC,QAAQ,WAAW,IACnC;;;;;;;;;;;;;;;;sFAKd,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;oFAAa,SAAQ;8FAAW;;;;;;8FAGjD,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,oIAAA,CAAA,WAAQ;4FACP,SACE,cACI,YAAY,QAAQ,GACpB,QAAQ,QAAQ;4FAEtB,IAAG;4FACH,iBAAiB,CAAA,UACf,cACI,eAAe;oGACb,GAAG,WAAW;oGACd,UAAU,QAAQ;gGACpB,KACA,WAAW;oGACT,GAAG,OAAO;oGACV,UAAU,QAAQ;gGACpB;;;;;;sGAGR,8OAAC,iIAAA,CAAA,QAAK;4FAAC,WAAU;4FAAU,SAAQ;sGAAW;;;;;;;;;;;;;;;;;;sFAKlD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;oFAAa,SAAQ;8FAAgB;;;;;;8FAGtD,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,oIAAA,CAAA,WAAQ;4FACP,SACE,cACI,YAAY,kBAAkB,GAC5B,OACA,QACF,QAAQ,aAAa;4FAE3B,IAAG;4FACH,iBAAiB,CAAA,UACf,cACI,eAAe;oGACb,GAAG,WAAW;oGACd,oBAAoB,UAChB,IAAI,OAAO,WAAW,KACtB;gGACN,KACA,WAAW;oGACT,GAAG,OAAO;oGACV,eAAe,QAAQ;gGACzB;;;;;;sGAGR,8OAAC,iIAAA,CAAA,QAAK;4FAAC,WAAU;4FAAU,SAAQ;sGAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAQ7D,8OAAC,kIAAA,CAAA,eAAY;kEACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,UAAU;4DACV,SAAS,cAAc,iBAAiB;4DACxC,MAAK;;gEAEJ,yBACC,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAEpB,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAAsB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;;oDACV,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAI5C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;0DAEvC,8OAAC;gDAAE,WAAU;;oDACV,KAAK,KAAK,CACT,AAAC,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GACnC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,KACvB;oDACF;;;;;;;;;;;;;;;;;;;0CAKR,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAG3C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;kDAEpB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,kBAAkB,EAAE,MAAM;;;;;;0DAEjD,8OAAC;gDAAE,WAAU;;oDACV,KAAK,KAAK,CACT,AAAC,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,kBAAkB,EAAE,MAAM,GAC7C,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,KACvB;oDACF;;;;;;;;;;;;;;;;;;;0CAKR,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;kDAEpB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAEX,MAAM,MAAM,CACV,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,eACtC,MAAM;;;;;;0DAGZ,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpD,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,UAAU;4CACV,SAAS;4CACT,MAAK;sDAEJ,iCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA6B;;+DAIlD;;;;;;sDAGJ,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAmB,MAAK;4CAAK,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAOtE,wBACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;qCAGjC,8OAAC,+IAAA,CAAA,YAAS;gBACR,WAAU;gBACV,SAAS;gBACT,MAAM;gBACN,cAAa;gBACb,wBAAwB;gBACxB,oBAAoB;gBACpB,oBAAoB;gBACpB,UAAU;gBACV,cAAa;gBACb,mBAAkB;;;;;;0BAKtB,8OAAC,2IAAA,CAAA,cAAW;gBAAC,cAAc;gBAAsB,MAAM;0BACrD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CACd,qBAAqB,oBAAoB;;;;;;8CAE5C,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACG;wCACxB,qBAAqB,eAAe;wCAAW;wCAAM;sDACtD,8OAAC;4CAAK,WAAU;sDACb,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;;;;;;wCACtC;;;;;;;;;;;;;sCAIX,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,SAAS;8CACzB,qBAAqB,eAAe;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC,2IAAA,CAAA,cAAW;gBAAC,cAAc;gBAAsB,MAAM;0BACrD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCAC2B;sDACjD,8OAAC;4CAAK,WAAU;sDACb,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;;;;;;wCACtC;;;;;;;;;;;;;sCAIX,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,SAAS;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D", "debugId": null}}, {"offset": {"line": 9398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Info, Settings } from 'lucide-react';\r\n\r\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\r\nimport { AuditLogViewer } from '@/components/features/admin/auditLogViewer';\r\nimport { SupabaseDiagnostics } from '@/components/features/admin/SupabaseDiagnostics';\r\nimport { UserManagement } from '@/components/features/admin/UserManagement';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\n\r\nexport default function AdminPage() {\r\n  return (\r\n    <ProtectedRoute allowedRoles={['ADMIN', 'SUPER_ADMIN']}>\r\n      <div className=\"space-y-6\">\r\n        <div className=\"mb-6 flex items-center space-x-2\">\r\n          <Settings className=\"size-8 text-primary\" />\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-primary\">Admin Dashboard</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              System administration and diagnostics\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <Alert>\r\n          <Info className=\"size-4\" />\r\n          <AlertTitle>Information</AlertTitle>\r\n          <AlertDescription>\r\n            This admin dashboard provides system diagnostics and monitoring\r\n            tools. Access is restricted to ADMIN and SUPER_ADMIN roles.\r\n          </AlertDescription>\r\n        </Alert>\r\n\r\n        <Tabs className=\"w-full\" defaultValue=\"system-diagnostics\">\r\n          <TabsList className=\"grid w-full grid-cols-3\">\r\n            <TabsTrigger value=\"system-diagnostics\">\r\n              System Diagnostics\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"user-management\">User Management</TabsTrigger>\r\n            <TabsTrigger value=\"audit-logs\">Audit Logs</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent className=\"mt-6\" value=\"system-diagnostics\">\r\n            <SupabaseDiagnostics />\r\n          </TabsContent>\r\n\r\n          <TabsContent className=\"mt-6\" value=\"user-management\">\r\n            <UserManagement />\r\n          </TabsContent>\r\n\r\n          <TabsContent className=\"mt-6\" value=\"audit-logs\">\r\n            <AuditLogViewer />\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </ProtectedRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,iBAAc;QAAC,cAAc;YAAC;YAAS;SAAc;kBACpD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAMzC,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;sCACZ,8OAAC,iIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;8BAMpB,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;oBAAS,cAAa;;sCACpC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAqB;;;;;;8CAGxC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAkB;;;;;;8CACrC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;;;;;;;sCAGlC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;4BAAO,OAAM;sCAClC,cAAA,8OAAC,8JAAA,CAAA,sBAAmB;;;;;;;;;;sCAGtB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;4BAAO,OAAM;sCAClC,cAAA,8OAAC,yJAAA,CAAA,iBAAc;;;;;;;;;;sCAGjB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;4BAAO,OAAM;sCAClC,cAAA,8OAAC,yJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}]}