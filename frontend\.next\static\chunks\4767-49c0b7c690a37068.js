"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4767],{976:(e,t,i)=>{i.d(t,{y:()=>o});var r=i(31203),s=i(25982);let n={fromApi:e=>r.G.fromApi(e),toApi:e=>e},a={toCreateRequest:e=>e};class o extends s.v{async getByStatus(e){return(await this.getAll({status:e})).data}async manageFlightDetails(e,t){return this.executeWithInfrastructure(null,async()=>{let i=a.toCreateRequest(t),s=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/flight-details"),i);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.G.fromApi(s)})}async setDelegates(e,t){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/delegates"),{delegates:t});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.G.fromApi(i)})}async setDrivers(e,t){return this.executeWithInfrastructure(null,async()=>{let i=t.map(e=>({employeeId:e})),s=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/drivers"),{drivers:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.G.fromApi(s)})}async setEscorts(e,t){return this.executeWithInfrastructure(null,async()=>{let i=t.map(e=>({employeeId:e})),s=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/escorts"),{escorts:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.G.fromApi(s)})}async setVehicles(e,t){return this.executeWithInfrastructure(null,async()=>{let i=t.map(e=>({assignedDate:new Date().toISOString(),vehicleId:e})),s=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/vehicles"),{vehicleAssignments:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.G.fromApi(s)})}async updateStatus(e,t,i){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.put("".concat(this.endpoint,"/").concat(e),{status:t,statusChangeReason:i});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.G.fromApi(s)})}constructor(e,t){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/delegations",this.transformer=n}}},1955:(e,t,i)=>{var r=i(25982);r.v},3619:(e,t,i)=>{var r=i(25982);r.v},8264:(e,t,i)=>{i.d(t,{b:()=>n});var r=i(12115),s=i(34389);function n(){let[e,t]=(0,r.useState)({csrfToken:null,isTokenValid:!1,tokenExpiresAt:null,isInitialized:!1}),i=(0,r.useCallback)(i=>{if(!s.k.isProtectionRequired(i))return i;let r=s.k.getCurrentToken();return r?(r.token!==e.csrfToken&&t(e=>({...e,csrfToken:r.token,isTokenValid:r.isValid,tokenExpiresAt:r.expiresAt})),s.k.attachToRequest(i)):(console.warn("No valid CSRF token available for request"),i)},[e.csrfToken]),n=(0,r.useCallback)(e=>s.k.validateToken(e),[]),a=(0,r.useCallback)(()=>{let e=s.k.refreshToken();return t(t=>({...t,csrfToken:e.token,isTokenValid:e.isValid,tokenExpiresAt:e.expiresAt})),e},[]),o=(0,r.useCallback)(()=>{s.k.clearToken(),t(e=>({...e,csrfToken:null,isTokenValid:!1,tokenExpiresAt:null}))},[]),l=(0,r.useCallback)(e=>s.k.isProtectionRequired(e),[]);return(0,r.useEffect)(()=>{try{let e=s.k.initialize();t({csrfToken:e.token,isTokenValid:e.isValid,tokenExpiresAt:e.expiresAt,isInitialized:!0})}catch(e){console.error("Failed to initialize CSRF protection:",e),t(e=>({...e,isInitialized:!0}))}},[]),(0,r.useEffect)(()=>{if(!e.isInitialized||!e.tokenExpiresAt)return;let t=()=>{let t=new Date;e.tokenExpiresAt.getTime()-t.getTime()<=3e5&&a()},i=setInterval(t,6e4);return t(),()=>clearInterval(i)},[e.isInitialized,e.tokenExpiresAt,a]),(0,r.useEffect)(()=>{if(!e.csrfToken)return;let t=setInterval(()=>{!s.k.validateToken(e.csrfToken).isValid&&e.isTokenValid&&a()},3e5);return()=>clearInterval(t)},[e.csrfToken,e.isTokenValid,a]),{csrfToken:e.csrfToken,isTokenValid:e.isTokenValid,tokenExpiresAt:e.tokenExpiresAt,isInitialized:e.isInitialized,attachCSRF:i,validateCSRF:n,refreshCSRFToken:a,clearCSRFToken:o,isProtectionRequired:l}}},10431:(e,t,i)=>{i.d(t,{$f:()=>p,lo:()=>m,E9:()=>h,Cv:()=>o.SessionManager,tC:()=>u});var r=i(34389),s=i(52474),n=i(38750);class a{static setClientSideCookie(e,t,i){try{let r={...this.DEFAULT_COOKIE_OPTIONS,...i},s="".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(t));if(r.maxAge&&(s+="; Max-Age=".concat(r.maxAge)),r.path&&(s+="; Path=".concat(r.path)),r.domain&&(s+="; Domain=".concat(r.domain)),r.secure&&(s+="; Secure"),r.sameSite&&(s+="; SameSite=".concat(r.sameSite)),r.httpOnly)throw Error("Cannot set httpOnly cookies from client-side. Use server-side cookie setting.");return document.cookie=s,{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to set client-side cookie"}}}static getSecureItem(e){try{return document.cookie.split(";").reduce((e,t)=>{let[i,r]=t.trim().split("=");return i&&r&&(e[decodeURIComponent(i)]=decodeURIComponent(r)),e},{})[e]||null}catch(e){return console.error("Failed to get secure item:",e),null}}static removeSecureItem(e,t){try{let i={...this.DEFAULT_COOKIE_OPTIONS,...t},r="".concat(encodeURIComponent(e),"=; expires=Thu, 01 Jan 1970 00:00:00 GMT");return i.path&&(r+="; Path=".concat(i.path)),i.domain&&(r+="; Domain=".concat(i.domain)),document.cookie=r,{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to remove secure item"}}}static isAvailable(){try{let e="__secure_storage_test__",t="test";this.setClientSideCookie(e,t,{httpOnly:!1});let i=this.getSecureItem(e);return this.removeSecureItem(e),i===t}catch(e){return!1}}static clearAllCookies(){try{for(let e of document.cookie.split(";")){let t=e.indexOf("="),i=t>-1?e.substring(0,t).trim():e.trim();i&&(document.cookie="".concat(i,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/"),document.cookie="".concat(i,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=").concat(window.location.hostname))}return{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to clear cookies"}}}static getAllCookies(){try{return document.cookie.split(";").reduce((e,t)=>{let[i,r]=t.trim().split("=");return i&&r&&(e[decodeURIComponent(i)]=decodeURIComponent(r)),e},{})}catch(e){return{}}}static hasCookie(e){return null!==this.getSecureItem(e)}static getCookieExpiration(e){return null}}a.DEFAULT_COOKIE_OPTIONS={httpOnly:!0,secure:!0,sameSite:"lax",path:"/"};var o=i(89699),l=i(29159),c=i(54120);class u{static extractTokenFromHeader(e){if(!e)return null;let t=e.split(" ");return 2!==t.length||"Bearer"!==t[0]?null:(0,c.d$)(t[1])}static extractTokenFromCookie(e){var t;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"sb-access-token";if(!e)return null;let r={};return e.split(";").forEach(e=>{let[t,i]=e.trim().split("=");t&&i&&(r[t]=decodeURIComponent(i))}),null!=(t=r[i])?t:null}static validateToken(e){try{if(!e||"string"!=typeof e)return{isValid:!1,isExpired:!0,payload:null,error:"Invalid token format"};let t=(0,l.s)(e),i=Math.floor(Date.now()/1e3),r=t.exp<i,s={isValid:!r,isExpired:r,payload:t};return r&&(s.error="Token expired"),s}catch(e){return{isValid:!1,isExpired:!0,payload:null,error:e instanceof Error?e.message:"Token validation failed"}}}static extractUserRole(e){var t,i;let r=this.validateToken(e);if(!r.isValid||!r.payload)return null;let s=null==(t=r.payload.custom_claims)?void 0:t.user_role;return s||(null!=(i=r.payload.user_role)?i:"USER")}static extractEmployeeId(e){var t,i,r;let s=this.validateToken(e);return s.isValid&&s.payload&&null!=(r=null!=(i=null==(t=s.payload.custom_claims)?void 0:t.employee_id)?i:s.payload.employee_id)?r:null}static isUserActive(e){var t,i,r;let s=this.validateToken(e);return!!s.isValid&&!!s.payload&&(null==(r=null!=(i=null==(t=s.payload.custom_claims)?void 0:t.is_active)?i:s.payload.is_active)||r)}static getTokenExpiration(e){let t=this.validateToken(e);return t.isValid&&t.payload?new Date(1e3*t.payload.exp):null}static willExpireSoon(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,i=this.getTokenExpiration(e);return!i||i<=new Date(Date.now()+60*t*1e3)}static hashTokenForLogging(e){return e?e.length<16?"short-token":"".concat(e.substring(0,8),"...").concat(e.substring(e.length-8)):"no-token"}static isValidTokenFormat(e){if(!e||"string"!=typeof e)return!1;let t=e.split(".");return 3===t.length&&t.every(e=>e.length>0)}}let d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0},h={attachCSRFToRequest:r.k.attachToRequest,clearAllCookies:a.clearAllCookies,detectTimeout:o.SessionManager.detectTimeout,escapeForDisplay:s.B.escapeForDisplay,extractEmployeeId:u.extractEmployeeId,extractUserRole:u.extractUserRole,generateCSRFToken:r.k.generateToken,getCurrentSessionId:o.SessionManager.getCurrentSessionId,getPermissionsForRole:n.B.getPermissionsForRole,getSecureItem:a.getSecureItem,hasMinimumRole:n.B.hasMinimumRole,hasPermission:n.B.hasPermission,isCSRFRequired:r.k.isProtectionRequired,isStorageAvailable:a.isAvailable,isUserActive:u.isUserActive,removeSecureItem:a.removeSecureItem,sanitizeForXSS:s.B.sanitizeForXSS,setClientSideCookie:a.setClientSideCookie,updateActivity:o.SessionManager.updateActivity,validateCSRFToken:r.k.validateToken,validateEmail:s.B.validateEmail,validateObject:s.B.validateObject,validatePhone:s.B.validatePhone,validateToken:u.validateToken,validateURL:s.B.validateURL,validateUUID:s.B.validateUUID,validateValue:s.B.validateValue,willExpireSoon:u.willExpireSoon,canPerformSecurityCheck(){let e=Date.now();return(d.isOpen&&e-d.lastAttemptTime>p.CIRCUIT_BREAKER_RESET_TIMEOUT&&(d.isOpen=!1,d.attempts=0,console.log("\uD83D\uDD04 Circuit breaker reset - security operations enabled")),d.isOpen)?(console.warn("\uD83D\uDD12 Circuit breaker OPEN - preventing security verification loop"),!1):!(e-d.lastOperationTime<p.SECURITY_OPERATION_COOLDOWN)||(console.debug("⏳ Security operation cooldown active"),!1)},recordSecurityAttempt(){let e=Date.now();d.attempts++,d.lastAttemptTime=e;try{localStorage.setItem(p.VERIFICATION_LOOP_STORAGE_KEY,JSON.stringify({attempts:d.attempts,lastAttemptTime:e}))}catch(e){console.warn("Failed to store verification attempts:",e)}d.attempts>=p.CIRCUIT_BREAKER_MAX_ATTEMPTS&&(d.isOpen=!0,console.error("\uD83D\uDEA8 Security verification loop detected - circuit breaker activated"),this.forceSecurityReset())},recordSecuritySuccess(){d.attempts=0,d.isOpen=!1,d.lastOperationTime=Date.now();try{localStorage.removeItem(p.VERIFICATION_LOOP_STORAGE_KEY)}catch(e){console.warn("Failed to clear verification attempts:",e)}},startSecurityOperation(e){if(!this.canPerformSecurityCheck())return!1;if(d.activeOperations.has(e))return console.debug("\uD83D\uDD04 Security operation ".concat(e," already in progress")),!1;d.activeOperations.add(e),d.lastOperationTime=Date.now();try{localStorage.setItem(p.SECURITY_OPERATIONS_STORAGE_KEY,JSON.stringify(Array.from(d.activeOperations)))}catch(e){console.warn("Failed to store active operations:",e)}return console.debug("\uD83D\uDD10 Started security operation: ".concat(e)),!0},endSecurityOperation(e){d.activeOperations.delete(e);try{0===d.activeOperations.size?localStorage.removeItem(p.SECURITY_OPERATIONS_STORAGE_KEY):localStorage.setItem(p.SECURITY_OPERATIONS_STORAGE_KEY,JSON.stringify(Array.from(d.activeOperations)))}catch(e){console.warn("Failed to update active operations:",e)}console.debug("✅ Ended security operation: ".concat(e))},isCircuitOpen:()=>d.isOpen,getCircuitBreakerState:()=>({attemptCount:d.attempts,lastAttempt:d.lastAttemptTime,isOpen:d.isOpen,activeOperations:Array.from(d.activeOperations),lastOperationTime:d.lastOperationTime}),forceSecurityReset(){console.warn("\uD83D\uDD27 Forcing security state reset to break verification loop");try{o.SessionManager.clearSessionState(),localStorage.removeItem(p.VERIFICATION_LOOP_STORAGE_KEY),localStorage.removeItem(p.SECURITY_OPERATIONS_STORAGE_KEY),localStorage.removeItem(p.LOGOUT_EVENT_KEY),d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0},console.log("✅ Security state reset complete"),setTimeout(()=>{window.location.href="/auth-test"},1e3)}catch(e){console.error("❌ Failed to reset security state:",e),window.location.reload()}},resetCircuitBreakerForTesting(){d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0};try{localStorage.removeItem(p.VERIFICATION_LOOP_STORAGE_KEY),localStorage.removeItem(p.SECURITY_OPERATIONS_STORAGE_KEY)}catch(e){}},initializeCircuitBreaker(){try{let e=localStorage.getItem(p.VERIFICATION_LOOP_STORAGE_KEY);if(e){let{attempts:t,lastAttemptTime:i}=JSON.parse(e);Date.now()-i<p.CIRCUIT_BREAKER_RESET_TIMEOUT?(d.attempts=t,d.lastAttemptTime=i,t>=p.CIRCUIT_BREAKER_MAX_ATTEMPTS&&(d.isOpen=!0,console.warn("\uD83D\uDD12 Circuit breaker restored in OPEN state"))):localStorage.removeItem(p.VERIFICATION_LOOP_STORAGE_KEY)}localStorage.getItem(p.SECURITY_OPERATIONS_STORAGE_KEY)&&(localStorage.removeItem(p.SECURITY_OPERATIONS_STORAGE_KEY),d.activeOperations.clear()),console.log("\uD83D\uDD10 Circuit breaker initialized")}catch(e){console.warn("Failed to initialize circuit breaker:",e),d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0}}}},p={DEFAULT_COOKIE_NAME:"sb-access-token",LOGOUT_EVENT_KEY:"workhub-logout-event",MAX_CONCURRENT_SESSIONS:5,REFRESH_COOKIE_NAME:"sb-refresh-token",SESSION_TIMEOUT_MINUTES:30,TOKEN_EXPIRY_THRESHOLD_MINUTES:5,CIRCUIT_BREAKER_MAX_ATTEMPTS:3,CIRCUIT_BREAKER_RESET_TIMEOUT:3e4,SECURITY_OPERATION_COOLDOWN:5e3,VERIFICATION_LOOP_STORAGE_KEY:"workhub_verification_attempts",SECURITY_OPERATIONS_STORAGE_KEY:"workhub_active_operations"},m={CROSS_TAB_LOGOUT:"cross_tab_logout",SECURITY_VIOLATION:"security_violation",SESSION_TIMEOUT:"session_timeout",TOKEN_EXPIRED:"token_expired",TOKEN_REFRESH_FAILED:"token_refresh_failed",UNAUTHORIZED_ACCESS:"unauthorized_access"}},12430:(e,t,i)=>{i.d(t,{Q:()=>a});var r=i(38069),s=i(25982);let n={fromApi:e=>r.A.fromApi(e),toApi:e=>e};class a extends s.v{async getByRole(e){return(await this.getAll({role:e})).data}async updateAvailabilityStatus(e,t){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/availability"),{availability:t});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.A.fromApi(i)})}constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/employees",this.transformer=n}}},14163:(e,t,i)=>{i.d(t,{N:()=>a});var r=i(34982);let s="https://abylqjnpaegeqwktcukn.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o";if(!s||!n)throw Error("Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file");let a=(0,r.UU)(s,n)},20249:(e,t,i)=>{i.d(t,{C:()=>a});var r=i(12115),s=i(10431),n=i(28113);function a(e){let[t,i]=(0,r.useState)({isTokenExpired:!1,isTokenValid:!1,lastValidation:null,tokenError:null,willExpireSoon:!1}),a=(0,r.useMemo)(()=>(0,n.Q)(),[]),o=(0,r.useCallback)(()=>{if(!e)return null;if(!s.E9.canPerformSecurityCheck())return console.debug("\uD83D\uDD12 Token validation blocked by circuit breaker"),null;let t="token-validation";if(!s.E9.startSecurityOperation(t))return console.debug("\uD83D\uDD04 Token validation already in progress"),null;try{let t=s.tC.validateToken(e);return t.isValid?(s.E9.recordSecuritySuccess(),s.Cv.handleSessionValidation(!0)):(console.warn("❌ Token validation failed:",t.error),s.E9.recordSecurityAttempt(),s.Cv.handleSessionValidation(!1,{error:t.error})),i(i=>({...i,isTokenExpired:t.isExpired,isTokenValid:t.isValid,lastValidation:new Date,tokenError:t.error||null,willExpireSoon:!!t.isValid&&s.tC.willExpireSoon(e)})),t}catch(t){let e=t instanceof Error?t.message:"Token validation failed";return console.error("❌ Token validation error:",e),s.E9.recordSecurityAttempt(),i(t=>({...t,isTokenValid:!1,tokenError:e,lastValidation:new Date})),null}finally{s.E9.endSecurityOperation(t)}},[e]),l=(0,r.useCallback)(async()=>{if(!s.E9.canPerformSecurityCheck())return console.debug("\uD83D\uDD12 Token refresh blocked by circuit breaker"),!1;let e="token-refresh";if(!s.E9.startSecurityOperation(e))return console.debug("\uD83D\uDD04 Token refresh already in progress"),!1;try{i(e=>({...e,tokenError:null})),console.log("\uD83D\uDD04 Starting token refresh...");let e=await a.refreshNow();return e?(console.log("✅ Token refresh successful"),s.E9.recordSecuritySuccess(),s.Cv.handleTokenRefresh(!0),i(e=>({...e,isTokenValid:!0,isTokenExpired:!1,tokenError:null,lastValidation:new Date}))):(console.warn("❌ Token refresh failed"),s.E9.recordSecurityAttempt(),s.Cv.handleTokenRefresh(!1),i(e=>({...e,isTokenValid:!1,tokenError:"Token refresh failed"}))),e}catch(t){let e=t instanceof Error?t.message:"Token refresh failed";return console.error("❌ Token refresh error:",e),s.E9.recordSecurityAttempt(),s.Cv.handleTokenRefresh(!1,{error:e}),i(t=>({...t,isTokenValid:!1,tokenError:e})),!1}finally{s.E9.endSecurityOperation(e)}},[a]),c=(0,r.useCallback)(()=>{i({isTokenExpired:!1,isTokenValid:!1,lastValidation:null,tokenError:null,willExpireSoon:!1})},[]),u=(0,r.useCallback)(()=>!e||s.tC.willExpireSoon(e,s.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES),[e]),d=(0,r.useCallback)(()=>e?s.tC.getTokenExpiration(e):null,[e]);return(0,r.useEffect)(()=>{e?o():c()},[e,o,c]),(0,r.useEffect)(()=>{if(!e||!t.isTokenValid)return;let i=()=>{if(!s.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Auto token refresh blocked by circuit breaker");let t="auto-token-refresh-check";if(!s.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Auto token refresh check already in progress");try{s.tC.willExpireSoon(e,2)&&(console.log("⏰ Token will expire soon, triggering auto-refresh"),l())}catch(e){console.error("❌ Auto token refresh check failed:",e),s.E9.recordSecurityAttempt()}finally{s.E9.endSecurityOperation(t)}},r=setInterval(i,12e4);return setTimeout(i,5e3),()=>clearInterval(r)},[e,t.isTokenValid,l]),{checkTokenExpiry:u,clearToken:c,getTokenExpiration:d,isTokenExpired:t.isTokenExpired,isTokenValid:t.isTokenValid,lastValidation:t.lastValidation,refreshToken:l,tokenError:t.tokenError,validateCurrentToken:o,willExpireSoon:t.willExpireSoon}}},23505:(e,t,i)=>{i.d(t,{z:()=>l});var r=i(10431),s=i(55411),n=i(3695),a=i(46265);class o extends s.O{getSecurityStatus(){let e={lastSecurityCheck:this.lastSecurityCheck,securityConstants:r.$f,securityFeaturesEnabled:this.securityConfig,securityFeaturesInitialized:this.securityInitialized,sessionInfo:this.getSessionInfo(),userInfo:this.extractUserFromToken()};if(this.securityComposer){let t=this.securityComposer.getSecurityStatus();return{...e,hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,threatLevel:this.assessThreatLevel(t)}}return{...e,hasValidToken:!1,isAuthenticated:!1,threatLevel:"critical"}}initializeSecurity(e){try{this.securityConfig.validateSecurityFeatures&&this.validateSecurityFeatures(e),this.securityFeatures=e,this.securityComposer?(this.securityComposer.updateSecurityFeatures(e),this.securityComposer.updateConfig(this.enhancedSecurityConfig)):this.securityComposer=new a.B(e,this.enhancedSecurityConfig),this.securityInitialized=!0,this.lastSecurityCheck=new Date,console.log("\uD83D\uDD10 SecureApiClient: Security features initialized with SecurityUtils integration",{featuresInitialized:Object.keys(e).filter(t=>e[t]),securityConfig:this.enhancedSecurityConfig,timestamp:this.lastSecurityCheck.toISOString()})}catch(e){throw console.error("SecureApiClient: Failed to initialize security features:",e),Error("Security initialization failed: ".concat(e instanceof Error?e.message:"Unknown error"))}}refreshSecurityFeatures(){this.securityFeatures&&this.securityComposer?(this.securityComposer.updateSecurityFeatures(this.securityFeatures),this.lastSecurityCheck=new Date,console.log("\uD83D\uDD04 SecureApiClient: Security features refreshed")):console.warn("SecureApiClient: Cannot refresh - security features not initialized")}async secureRequest(e,t,i,r){var s,a,o,l,c,u;if(!this.securityInitialized||!this.securityComposer)throw new n.v3("Security features not initialized. Call initializeSecurity() first.","SECURITY_NOT_INITIALIZED");this.lastSecurityCheck=new Date;let d={hasValidToken:null!=(c=null==(a=this.securityFeatures)||null==(s=a.tokenManagement)?void 0:s.isTokenValid)&&c,isAuthenticated:null!=(u=null==(l=this.securityFeatures)||null==(o=l.sessionSecurity)?void 0:o.isSessionActive)&&u,session:this.getSessionInfo(),timestamp:this.lastSecurityCheck,user:this.extractUserFromToken()},h={...r,body:i,method:e,url:t};try{await this.performPreRequestSecurityChecks(d);let{body:i,...r}=await this.securityComposer.processRequest(h,d),s=await super[e.toLowerCase()](t,i,r);return await this.performPostRequestSecurityChecks(d),s}catch(e){throw await this.handleSecurityError(e,d),e}}updateSecurityConfig(e){this.securityComposer?(this.securityComposer.updateConfig(e),console.log("\uD83D\uDD10 SecureApiClient: Security configuration updated",e)):console.warn("SecureApiClient: Cannot update config - SecurityComposer not initialized")}assessThreatLevel(e){try{if(!this.securityInitialized||!e.securityFeaturesInitialized)return"critical";if(!e.isAuthenticated||!e.hasValidToken)return"high";let t=r.E9.getSecureItem("auth_token");if(r.E9.detectTimeout()||t&&r.E9.willExpireSoon(t))return"medium";return"low"}catch(e){return console.warn("SecureApiClient: Failed to assess threat level:",e),"medium"}}extractUserFromToken(){try{var e,t;if(!(null==(t=this.securityFeatures)||null==(e=t.tokenManagement)?void 0:e.isTokenValid))return;let i=r.E9.getSecureItem("auth_token");if(!i)return;let s=r.E9.extractEmployeeId(i),n=r.E9.extractUserRole(i);return s||n?{employeeId:s,userRole:n}:void 0}catch(e){console.warn("SecureApiClient: Failed to extract user from token:",e);return}}getSessionInfo(){try{let e=r.E9.getCurrentSessionId(),t=r.E9.detectTimeout();return e?{isTimeout:t,sessionId:e}:void 0}catch(e){console.warn("SecureApiClient: Failed to get session info:",e);return}}async handleSecurityError(e,t){if(!r.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Security error handling blocked by circuit breaker");let i="api-error-handling";if(!r.E9.startSecurityOperation(i))return void console.debug("\uD83D\uDD04 Security error handling already in progress");try{this.securityComposer&&await this.securityComposer.handleError(e,t),e instanceof Error&&((e.message.includes("401")||e.message.includes("Unauthorized")||e.message.includes("Authentication"))&&(console.warn("\uD83D\uDD10 SecureApiClient: Authentication error detected"),r.E9.recordSecurityAttempt(),await this.attemptSessionRecovery()||(r.E9.clearAllCookies(),console.log("\uD83E\uDDF9 Cleared secure storage due to auth failure"))),(e.message.includes("CSRF")||e.message.includes("403"))&&(console.warn("\uD83D\uDEE1️ SecureApiClient: CSRF error detected"),r.E9.recordSecurityAttempt()),(e.message.includes("Network")||e.message.includes("fetch"))&&console.warn("\uD83C\uDF10 SecureApiClient: Network error detected"),(e.message.includes("timeout")||e.message.includes("Timeout"))&&(console.warn("⏰ SecureApiClient: Timeout error detected"),r.E9.recordSecurityAttempt()))}catch(e){console.error("SecureApiClient: Error in security error handling:",e),r.E9.recordSecurityAttempt()}finally{r.E9.endSecurityOperation(i)}}async performPostRequestSecurityChecks(e){try{var t;e.isAuthenticated&&(null==(t=this.securityFeatures)?void 0:t.sessionSecurity)&&this.securityFeatures.sessionSecurity.updateActivity()}catch(e){console.warn("SecureApiClient: Post-request security check failed:",e)}}async performPreRequestSecurityChecks(e){if(!r.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Pre-request security checks blocked by circuit breaker");let t="pre-request-security-check";if(!r.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Pre-request security check already in progress");try{var i;if(r.E9.detectTimeout()&&(console.warn("⏰ Session timeout detected in pre-request check"),r.E9.recordSecurityAttempt(),!await this.attemptSessionRecovery()))throw new n.v3("Session has timed out","SESSION_TIMEOUT");if(e.hasValidToken&&(null==(i=this.securityFeatures)?void 0:i.tokenManagement)){let{isTokenExpired:e}=this.securityFeatures.tokenManagement,t=r.E9.getSecureItem("auth_token");if(e||t&&r.E9.willExpireSoon(t)){console.warn("\uD83D\uDD04 SecureApiClient: Token will expire soon, attempting refresh");try{await this.securityFeatures.tokenManagement.refreshToken()?(console.log("✅ Token refreshed successfully"),r.E9.recordSecuritySuccess()):(console.warn("❌ Token refresh failed"),r.E9.recordSecurityAttempt())}catch(e){console.error("❌ Token refresh error:",e),r.E9.recordSecurityAttempt()}}}r.E9.recordSecuritySuccess()}catch(e){throw console.error("SecureApiClient: Pre-request security check failed:",e),r.E9.recordSecurityAttempt(),e}finally{r.E9.endSecurityOperation(t)}}async attemptSessionRecovery(){try{console.log("\uD83D\uDD27 Attempting session recovery...");let{SessionManager:e}=await Promise.resolve().then(i.bind(i,89699));if(await e.performIntegrityCheck())return console.log("✅ Session integrity check passed"),!0;if(e.recoverFromCorruptedState())return console.log("✅ Session state recovered successfully"),!0;return console.warn("❌ Session recovery failed"),!1}catch(e){return console.error("❌ Session recovery error:",e),!1}}validateSecurityFeatures(e){let t=[];if(this.securityConfig.enableTokenValidation&&e.tokenManagement){let{isTokenExpired:i,isTokenValid:r}=e.tokenManagement;("boolean"!=typeof r||"boolean"!=typeof i)&&t.push("Token management features must provide boolean status indicators")}if(this.securityConfig.enableCSRF&&e.csrfProtection&&"function"!=typeof e.csrfProtection.attachCSRF&&t.push("CSRF protection must provide attachCSRF function"),this.securityConfig.enableInputSanitization&&e.inputValidation&&"function"!=typeof e.inputValidation.sanitizeInput&&t.push("Input validation must provide sanitizeInput function"),this.securityConfig.enableAutoLogout&&e.sessionSecurity){let{clearSession:i,isSessionActive:r}=e.sessionSecurity;("boolean"!=typeof r||"function"!=typeof i)&&t.push("Session security must provide boolean status and clearSession function")}if(t.length>0)throw Error("Security feature validation failed: ".concat(t.join(", ")))}constructor(e,t){var i,s,n,a,o,l;super(e),this.lastSecurityCheck=new Date,this.securityInitialized=!1,this.securityConfig={enableAutoLogout:null==(i=e.enableAutoLogout)||i,enableCSRF:null==(s=e.enableCSRF)||s,enableInputSanitization:null==(n=e.enableInputSanitization)||n,enableTokenValidation:null==(a=e.enableTokenValidation)||a,securityConfig:null!=(o=e.securityConfig)?o:{},validateSecurityFeatures:null==(l=e.validateSecurityFeatures)||l},this.enhancedSecurityConfig={authentication:{autoLogout:!0,enabled:this.securityConfig.enableAutoLogout,redirectOnFailure:!0},csrf:{enabled:this.securityConfig.enableCSRF,excludePaths:[],tokenHeader:"X-CSRF-Token"},http:{baseURL:e.baseURL||"/api",retryAttempts:e.retryAttempts||3,timeout:e.timeout||1e4},inputSanitization:{enabled:this.securityConfig.enableInputSanitization,sanitizers:["xss","sql"]},tokenValidation:{autoRefresh:!0,enabled:this.securityConfig.enableTokenValidation,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES},...e.securityConfig},this.securityComposer=t,console.log("\uD83D\uDD10 SecureApiClient: Initialized with SecurityUtils integration",{constants:{sessionTimeout:r.$f.SESSION_TIMEOUT_MINUTES,tokenThreshold:r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES},securityFeatures:Object.keys(this.securityConfig).filter(e=>this.securityConfig[e])})}}function l(e,t){let i,s={authentication:{autoLogout:!0,enabled:!0,redirectOnFailure:!0},csrf:{enabled:!0,excludePaths:[],tokenHeader:"X-CSRF-Token"},http:{baseURL:"/api",retryAttempts:3,timeout:1e4},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},tokenValidation:{autoRefresh:!0,enabled:!0,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES}},n={enableAutoLogout:!0,enableCSRF:!0,enableInputSanitization:!0,enableTokenValidation:!0,validateSecurityFeatures:!0,...e,securityConfig:{...s,...e.securityConfig}};t&&(i=new a.B(t,n.securityConfig));let l=new o(n,i);return console.log("\uD83C\uDFED SecureApiClient Factory: Created enhanced secure API client",{config:{autoLogout:n.enableAutoLogout,csrf:n.enableCSRF,inputSanitization:n.enableInputSanitization,tokenValidation:n.enableTokenValidation},securityConstants:r.$f,securityFeatures:t?Object.keys(t):[]}),l}},25982:(e,t,i)=>{i.d(t,{v:()=>a});var r=i(3695);class s{async execute(e){if("OPEN"===this.state)if(Date.now()-this.lastFailureTime>this.timeout)this.state="HALF_OPEN";else throw new r._7("Circuit breaker is OPEN","CIRCUIT_BREAKER_OPEN");try{let t=await e();return this.onSuccess(),t}catch(e){throw this.onFailure(),e}}getState(){return{failures:this.failures,lastFailureTime:this.lastFailureTime,name:this.name,state:this.state}}onFailure(){this.failures++,this.lastFailureTime=Date.now(),this.failures>=this.threshold&&(this.state="OPEN")}onSuccess(){this.failures=0,this.state="CLOSED"}constructor(e,t=5,i=6e4){this.name=e,this.threshold=t,this.timeout=i,this.failures=0,this.lastFailureTime=0,this.state="CLOSED"}}class n{clear(){this.cache.clear()}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiry?(this.cache.delete(e),null):t.data:null}getStats(){return{keys:[...this.cache.keys()],size:this.cache.size}}invalidate(e){this.cache.delete(e)}invalidatePattern(e){for(let t of this.cache.keys())e.test(t)&&this.cache.delete(t)}set(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5;this.cache.set(e,{data:t,expiry:Date.now()+i})}constructor(){this.cache=new Map}}class a{clearCache(){this.cache.clear()}async create(e){return this.executeWithInfrastructure(null,async()=>{let t=this.transformer.toApi?this.transformer.toApi(e):e,i=await this.apiClient.post(this.endpoint,t);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.transformer.fromApi?this.transformer.fromApi(i):i})}async delete(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("".concat(this.endpoint,"/").concat(e)),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e))})}async getAll(e){let t="".concat(this.endpoint,":getAll:").concat(JSON.stringify(e||{}));return this.executeWithInfrastructure(t,async()=>{let t,i=new URLSearchParams;if(e)for(let[t,r]of Object.entries(e))null!=r&&i.append(t,String(r));let r=i.toString(),s=r?"".concat(this.endpoint,"?").concat(r):this.endpoint,n=await this.apiClient.get(s),a={};if(n&&"success"===n.status&&n.data){var o,l,c,u,d,h,p,m,S;let e=n.data;Array.isArray(e)?(t=e,n.pagination&&(a={pagination:{hasNext:null!=(o=n.pagination.hasNext)&&o,hasPrevious:null!=(l=n.pagination.hasPrevious)&&l,limit:n.pagination.limit,page:n.pagination.page,total:n.pagination.total,totalPages:null!=(c=n.pagination.totalPages)?c:Math.ceil(n.pagination.total/n.pagination.limit)}})):e&&Array.isArray(e.data)?(t=e.data,e.pagination?a={pagination:{hasNext:null!=(u=e.pagination.hasNext)&&u,hasPrevious:null!=(d=e.pagination.hasPrevious)&&d,limit:e.pagination.limit,page:e.pagination.page,total:e.pagination.total,totalPages:null!=(h=e.pagination.totalPages)?h:Math.ceil(e.pagination.total/e.pagination.limit)}}:n.pagination&&(a={pagination:{hasNext:null!=(p=n.pagination.hasNext)&&p,hasPrevious:null!=(m=n.pagination.hasPrevious)&&m,limit:n.pagination.limit,page:n.pagination.page,total:n.pagination.total,totalPages:null!=(S=n.pagination.totalPages)?S:Math.ceil(n.pagination.total/n.pagination.limit)}})):t=[e]}else if(Array.isArray(n))t=n;else if(n&&(n.error||"error"===n.status))throw Error(n.message||n.error||"API request failed");else if(n&&"object"==typeof n)t=[n];else throw Error("Invalid response format from API: ".concat(JSON.stringify(n)));return{data:t.map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e),...a}})}async getById(e){let t="".concat(this.endpoint,":getById:").concat(e);return this.executeWithInfrastructure(t,async()=>{let t=await this.apiClient.get("".concat(this.endpoint,"/").concat(e));return this.transformer.fromApi?this.transformer.fromApi(t):t})}getHealthStatus(){return{cacheStats:this.cache.getStats(),circuitBreakerState:this.circuitBreaker.getState(),endpoint:this.endpoint,metrics:this.metrics,service:this.constructor.name}}resetMetrics(){this.metrics={averageResponseTime:0,cacheHitRatio:0,errorCount:0,requestCount:0}}async update(e,t){return this.executeWithInfrastructure(null,async()=>{let i=this.transformer.toApi?this.transformer.toApi(t):t,r=await this.apiClient.put("".concat(this.endpoint,"/").concat(e),i);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),this.transformer.fromApi?this.transformer.fromApi(r):r})}async executeWithInfrastructure(e,t){let i=Date.now();try{if(this.metrics.requestCount++,e){let t=this.cache.get(e);if(t)return this.metrics.cacheHitRatio=(this.metrics.cacheHitRatio*(this.metrics.requestCount-1)+1)/this.metrics.requestCount,t}let r=await this.circuitBreaker.execute(async()=>o(t,this.config.retryAttempts));e&&r&&this.cache.set(e,r,this.config.cacheDuration);let s=Date.now()-i;return this.metrics.averageResponseTime=(this.metrics.averageResponseTime*(this.metrics.requestCount-1)+s)/this.metrics.requestCount,r}catch(e){var s;if(this.metrics.errorCount++,console.error("Service error in ".concat(this.constructor.name,":"),{endpoint:this.endpoint,errorDetails:e instanceof Error?{message:e.message,name:e.name,stack:e.stack}:e,errorMessage:e instanceof Error?e.message:String(e),errorType:(null==e||null==(s=e.constructor)?void 0:s.name)||typeof e,timestamp:new Date().toISOString()}),e instanceof r._7)throw e;if(e instanceof Error){if(e.message.includes("fetch")||e.message.includes("network"))throw new r._7("Network connection failed. Please check your internet connection and try again.","NETWORK_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name});if(e.message.includes("500")||e.message.includes("Internal Server Error"))throw new r._7("Server error occurred. Please try again later.","SERVER_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name})}throw new r._7(e instanceof Error?e.message:"Unknown service error","SERVICE_ERROR",void 0,{endpoint:this.endpoint,service:this.constructor.name})}}constructor(e,t={}){this.apiClient=e,this.config={cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t},this.circuitBreaker=new s("".concat(this.constructor.name),this.config.circuitBreakerThreshold),this.cache=new n,this.metrics={averageResponseTime:0,cacheHitRatio:0,errorCount:0,requestCount:0}}}async function o(e){let t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let s=1;s<=i;s++)try{return await e()}catch(e){if(t=e instanceof Error?e:Error("Unknown error"),s===i)throw t;await new Promise(e=>setTimeout(e,r*s))}throw t}},28113:(e,t,i)=>{i.d(t,{Q:()=>l});var r=i(38549),s=i(89699),n=i(14163);let a={baseRetryDelay:1e3,enableDebugLogging:!1,maxRetryAttempts:3,refreshBeforeExpiryMinutes:5};class o{static getInstance(e){return o.instance||(o.instance=new o(e)),o.instance}async getSessionInfo(){try{var e;let{data:{session:t},error:i}=await n.N.auth.getSession();if(i)return this.log("Error getting session info",{error:i}),s.SessionManager.handleSessionValidation(!1,{error:i.message}),{error:i.message,isValid:!1};if(!t)return this.log("No active session found"),s.SessionManager.handleSessionValidation(!1,{error:"No active session"}),{error:"No active session",isValid:!1};let r=Math.floor(Date.now()/1e3),a=!!t.expires_at&&t.expires_at<r,o={isExpired:a,isValid:!a,user:{id:t.user.id}};return t.expires_at&&(o.expiresAt=t.expires_at),t.user.email&&(o.user.email=t.user.email),(null==(e=t.user.user_metadata)?void 0:e.role)&&(o.user.role=t.user.user_metadata.role),o}catch(e){return this.log("Exception in getSessionInfo",{error:e}),{error:e instanceof Error?e.message:"Unknown error",isValid:!1}}}async refreshNow(){return this.isRefreshing?(this.log("Refresh already in progress, skipping"),!1):this.performRefresh()}stop(){this.clearScheduledRefresh(),this.clearRetryTimeout(),this.callbacks.clear(),this.currentSession=null,this.isRefreshing=!1,this.retryAttempts=0,this.log("TokenRefreshService stopped")}subscribe(e){return this.callbacks.add(e),()=>this.callbacks.delete(e)}updateSession(e){this.currentSession=e,e?(this.scheduleRefresh(e),this.log("Session updated, refresh scheduled",{expiresAt:new Date(1e3*e.expires_at).toISOString()})):(this.clearScheduledRefresh(),this.log("Session cleared, refresh cancelled"))}clearRetryTimeout(){this.retryTimeout&&(clearTimeout(this.retryTimeout),this.retryTimeout=null)}clearScheduledRefresh(){this.refreshTimeout&&(clearTimeout(this.refreshTimeout),this.refreshTimeout=null)}emitEvent(e,t){for(let i of this.callbacks)try{i(e,t)}catch(e){console.error("Error in token refresh callback:",e)}}handleRefreshFailure(e){if(this.retryAttempts++,this.retryAttempts>=this.config.maxRetryAttempts){this.log("Max retry attempts reached, giving up and signaling critical failure"),this.emitEvent("critical_refresh_failed",{attempts:this.retryAttempts,error:"Max retry attempts exceeded, session unrecoverable",...e});return}let t=this.config.baseRetryDelay*Math.pow(2,this.retryAttempts-1);this.log("Scheduling retry",{attempt:this.retryAttempts,delayMs:t}),this.retryTimeout=setTimeout(()=>{this.performRefresh()},t)}log(e,t){this.config.enableDebugLogging&&console.log("\uD83D\uDD04 TokenRefreshService: ".concat(e),t||"")}async performRefresh(){if(this.isRefreshing)return!1;this.isRefreshing=!0,this.log("Starting token refresh");try{var e,t;let i={"Content-Type":"application/json"};(null==(e=this.currentSession)?void 0:e.access_token)&&(i.Authorization="Bearer ".concat(this.currentSession.access_token));let a={};(null==(t=this.currentSession)?void 0:t.refresh_token)?(a.refresh_token=this.currentSession.refresh_token,this.log("Including refresh token in request body")):this.log("Warning: No refresh token available in current session");let o=(0,r.Qq)().apiBaseUrl,l=await fetch("".concat(o,"/auth/refresh"),{credentials:"include",headers:i,method:"POST",body:JSON.stringify(a)});if(l.ok){let e=await l.json();this.log("Token refresh successful",{expiresIn:e.expiresIn}),this.retryAttempts=0,this.clearRetryTimeout();try{let{newTokens:t}=e;(null==t?void 0:t.session)&&(null==t?void 0:t.user)?(await n.N.auth.setSession({access_token:t.session.access_token,refresh_token:t.session.refresh_token}),this.currentSession=t.session,this.log("Supabase session explicitly updated with new tokens")):this.log("Warning: New tokens from backend did not contain full session/user data",{data:e})}catch(e){this.log("Error explicitly updating Supabase session after refresh",{sessionUpdateError:e})}return this.emitEvent("refresh_success",e),s.SessionManager.handleTokenRefresh(!0,e),!0}{let e=await l.json().catch(()=>({}));return this.log("Token refresh failed",{error:e,status:l.status}),this.handleRefreshFailure(e),s.SessionManager.handleTokenRefresh(!1,e),!1}}catch(e){return this.log("Token refresh error",{error:e instanceof Error?e.message:String(e)}),this.handleRefreshFailure({error:"Network error"}),s.SessionManager.handleTokenRefresh(!1,{error:"Network error"}),!1}finally{this.isRefreshing=!1}}scheduleRefresh(e){if(this.clearScheduledRefresh(),!e.expires_at)return void this.log("No expiration time in session, cannot schedule refresh");let t=1e3*e.expires_at-60*this.config.refreshBeforeExpiryMinutes*1e3,i=Math.max(0,t-Date.now());if(0===i){this.log("Token expired or about to expire, refreshing immediately"),this.performRefresh();return}this.refreshTimeout=setTimeout(()=>{this.performRefresh()},i),this.log("Refresh scheduled",{delayMinutes:Math.round(i/6e4),refreshAt:new Date(t).toISOString()}),this.emitEvent("refresh_scheduled",{delay:i,refreshAt:t})}setupVisibilityHandling(){"undefined"!=typeof document&&document.addEventListener("visibilitychange",()=>{if(this.isTabVisible=!document.hidden,this.isTabVisible&&this.currentSession){let e=Date.now();1e3*(this.currentSession.expires_at||0)-e<=60*this.config.refreshBeforeExpiryMinutes*1e3&&(this.log("Tab visible and token needs refresh"),this.performRefresh())}})}constructor(e={}){this.callbacks=new Set,this.currentSession=null,this.isRefreshing=!1,this.isTabVisible=!0,this.refreshTimeout=null,this.retryAttempts=0,this.retryTimeout=null,this.config={...a,...e},this.setupVisibilityHandling(),this.log("TokenRefreshService initialized")}}o.instance=null;let l=e=>o.getInstance(e)},30285:(e,t,i)=>{i.d(t,{$:()=>c,r:()=>l});var r=i(95155),s=i(12115),n=i(99708),a=i(74466),o=i(54036);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:i,variant:s,size:a,asChild:c=!1,...u}=e,d=c?n.DX:"button";return(0,r.jsx)(d,{className:(0,o.cn)(l({variant:s,size:a,className:i})),ref:t,...u})});c.displayName="Button"},31203:(e,t,i)=>{i.d(t,{G:()=>a});let r={toDelegateApiStructure(e){var t;return{name:e.name,notes:null!=(t=e.notes)?t:null,title:e.title}},toDriverApiStructure(e){var t;return{employeeId:e.employeeId,notes:null!=(t=e.notes)?t:null}},toEscortApiStructure(e){var t;return{employeeId:e.employeeId,notes:null!=(t=e.notes)?t:null}},toVehicleAssignmentApiStructure:e=>e},s={fromApi(e){var t,i,r,s,n,a,o,l,c,u,d,h;return{availability:e.availability,contactEmail:null!=(t=e.contactEmail)?t:null,contactInfo:e.contactInfo,contactMobile:null!=(i=e.contactMobile)?i:null,contactPhone:null!=(r=e.contactPhone)?r:null,createdAt:e.createdAt,currentLocation:null!=(s=e.currentLocation)?s:null,department:null!=(n=e.department)?n:null,employeeId:e.employeeId,fullName:null!=(a=e.fullName)?a:null,generalAssignments:e.generalAssignments,hireDate:null!=(o=e.hireDate)?o:null,id:e.id,name:e.name,notes:null!=(l=e.notes)?l:null,position:null!=(c=e.position)?c:null,profileImageUrl:null!=(u=e.profileImageUrl)?u:null,role:e.role,shiftSchedule:null!=(d=e.shiftSchedule)?d:null,skills:e.skills,status:e.status,updatedAt:e.updatedAt,workingHours:null!=(h=e.workingHours)?h:null}}},n={fromApi:e=>({color:e.color,createdAt:e.createdAt,id:e.id,imageUrl:e.imageUrl,initialOdometer:e.initialOdometer,licensePlate:e.licensePlate,make:e.make,model:e.model,ownerContact:e.ownerContact,ownerName:e.ownerName,serviceHistory:[],updatedAt:e.updatedAt,vin:e.vin,year:e.year})},a={fromApi(e){var t,i,r,a,l,c,u;return{arrivalFlight:e.flightArrivalDetails?o.fromApi(e.flightArrivalDetails):null,createdAt:e.createdAt,delegates:e.delegates||[],departureFlight:e.flightDepartureDetails?o.fromApi(e.flightDepartureDetails):null,drivers:(null==(t=e.drivers)?void 0:t.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,employee:s.fromApi(t),employeeId:t.id,id:"driver-".concat(e.id,"-").concat(t.id),updatedAt:e.updatedAt})))||[],durationFrom:e.durationFrom,durationTo:e.durationTo,escorts:(null==(i=e.escorts)?void 0:i.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,employee:s.fromApi(t),employeeId:t.id,id:"escort-".concat(e.id,"-").concat(t.id),updatedAt:e.updatedAt})))||[],eventName:e.eventName,id:e.id,imageUrl:null!=(a=e.imageUrl)?a:null,invitationFrom:null!=(l=e.invitationFrom)?l:null,invitationTo:null!=(c=e.invitationTo)?c:null,location:e.location,notes:null!=(u=e.notes)?u:null,status:e.status,statusHistory:e.statusHistory||[],updatedAt:e.updatedAt,vehicles:(null==(r=e.vehicles)?void 0:r.map(t=>({createdAt:e.createdAt,createdBy:null,delegationId:e.id,id:"vehicle-".concat(e.id,"-").concat(t.id),updatedAt:e.updatedAt,vehicle:n.fromApi(t),vehicleId:t.id})))||[]}},toCreateRequest(e){var t,i,s,n,a,l,c,u,d,h,p,m;return{delegates:null!=(a=null==(t=e.delegates)?void 0:t.map(e=>r.toDelegateApiStructure(e)))?a:[],driverEmployeeIds:null!=(l=null==(i=e.drivers)?void 0:i.map(e=>e.employeeId))?l:[],durationFrom:e.durationFrom,durationTo:e.durationTo,escortEmployeeIds:null!=(c=null==(s=e.escorts)?void 0:s.map(e=>e.employeeId))?c:[],eventName:e.eventName,flightArrivalDetails:e.flightArrivalDetails?o.toApiStructureForCreate(e.flightArrivalDetails):void 0,flightDepartureDetails:e.flightDepartureDetails?o.toApiStructureForCreate(e.flightDepartureDetails):void 0,imageUrl:null!=(u=e.imageUrl)?u:null,invitationFrom:null!=(d=e.invitationFrom)?d:null,invitationTo:null!=(h=e.invitationTo)?h:null,location:e.location,notes:null!=(p=e.notes)?p:null,status:e.status,vehicleIds:null!=(m=null==(n=e.vehicles)?void 0:n.map(e=>e.vehicleId))?m:[]}},toUpdateRequest(e){var t,i;let r={};return void 0!==e.eventName&&(r.eventName=e.eventName),void 0!==e.location&&(r.location=e.location),void 0!==e.durationFrom&&(r.durationFrom=e.durationFrom),void 0!==e.durationTo&&(r.durationTo=e.durationTo),void 0!==e.status&&(r.status=e.status),void 0!==e.notes&&(r.notes=e.notes),void 0!==e.imageUrl&&(r.imageUrl=e.imageUrl),void 0!==e.invitationFrom&&(r.invitationFrom=e.invitationFrom),void 0!==e.invitationTo&&(r.invitationTo=e.invitationTo),void 0!==e.flightArrivalDetails&&(r.flightArrivalDetails=null!=(t=e.flightArrivalDetails)?t:void 0),void 0!==e.flightDepartureDetails&&(r.flightDepartureDetails=null!=(i=e.flightDepartureDetails)?i:void 0),void 0!==e.escorts&&(r.escortEmployeeIds=e.escorts.map(e=>e.employeeId)),void 0!==e.drivers&&(r.driverEmployeeIds=e.drivers.map(e=>e.employeeId)),void 0!==e.vehicles&&(r.vehicleIds=e.vehicles.map(e=>e.vehicleId)),r}},o={fromApi:e=>e?{airport:e.airport,dateTime:e.dateTime,flightNumber:e.flightNumber,id:e.id,notes:e.notes||null,terminal:e.terminal||null}:null,toApiStructureForCreate:e=>e,toCreateRequest(e){var t,i;return{airport:e.airport,dateTime:e.dateTime,flightNumber:e.flightNumber,notes:null!=(t=e.notes)?t:null,terminal:null!=(i=e.terminal)?i:null}}}},34389:(e,t,i)=>{i.d(t,{k:()=>r});class r{static generateToken(){let e=new Uint8Array(32);if(window.crypto)window.crypto.getRandomValues(e);else for(let t=0;t<e.length;t++)e[t]=Math.floor(256*Math.random());return btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}static validateToken(e){if(!e||"string"!=typeof e||!/^[A-Za-z0-9_-]+$/.test(e))return{isValid:!1,error:"Invalid token format"};if(e.length<32)return{isValid:!1,error:"Token too short"};let t=this.getStoredToken();return t?!t.isValid||t.expiresAt<new Date?{isValid:!1,error:"Stored token expired"}:e!==t.token?{isValid:!1,error:"Token mismatch"}:{isValid:!0,token:e}:{isValid:!1,error:"No stored token found"}}static attachToRequest(e){if(!["POST","PUT","PATCH","DELETE"].includes((e.method||"GET").toUpperCase()))return e;let t=this.getCurrentToken();if(!t)return console.warn("No CSRF token available for request"),e;let i={...e.headers,[this.CSRF_HEADER_NAME]:t.token};return{...e,headers:i}}static getCurrentToken(){let e=this.getStoredToken();return!e||!e.isValid||e.expiresAt<new Date?this.refreshToken():e}static refreshToken(){let e={token:this.generateToken(),expiresAt:new Date(Date.now()+60*this.TOKEN_LIFETIME_MINUTES*1e3),isValid:!0};return this.storeToken(e),e}static clearToken(){try{localStorage.removeItem(this.CSRF_STORAGE_KEY),document.cookie="".concat(this.CSRF_COOKIE_NAME,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}catch(e){console.error("Failed to clear CSRF token:",e)}}static initialize(){return this.refreshToken()}static isProtectionRequired(e){return["POST","PUT","PATCH","DELETE"].includes((e.method||"GET").toUpperCase())}static extractTokenFromResponse(e){return e[this.CSRF_HEADER_NAME.toLowerCase()]||e[this.CSRF_HEADER_NAME]||null}static getStoredToken(){try{let e=localStorage.getItem(this.CSRF_STORAGE_KEY);if(!e)return null;let t=JSON.parse(e);return{...t,expiresAt:new Date(t.expiresAt)}}catch(e){return console.error("Failed to get stored CSRF token:",e),null}}static storeToken(e){try{localStorage.setItem(this.CSRF_STORAGE_KEY,JSON.stringify(e))}catch(e){console.error("Failed to store CSRF token:",e)}}}r.CSRF_HEADER_NAME="X-CSRF-Token",r.CSRF_COOKIE_NAME="csrf-token",r.CSRF_STORAGE_KEY="workhub_csrf_token",r.TOKEN_LIFETIME_MINUTES=60},38069:(e,t,i)=>{i.d(t,{A:()=>s});var r=i(21876);let s={fromApi(e){var t,i,r,s,n,a,o,l,c,u,d,h;return{availability:e.availability,contactEmail:null!=(t=e.contactEmail)?t:null,contactInfo:e.contactInfo,contactMobile:null!=(i=e.contactMobile)?i:null,contactPhone:null!=(r=e.contactPhone)?r:null,createdAt:e.createdAt,currentLocation:null!=(s=e.currentLocation)?s:null,department:null!=(n=e.department)?n:null,employeeId:e.employeeId,fullName:null!=(a=e.fullName)?a:null,generalAssignments:e.generalAssignments,hireDate:null!=(o=e.hireDate)?o:null,id:e.id,name:e.name||"",notes:null!=(l=e.notes)?l:null,position:null!=(c=e.position)?c:null,profileImageUrl:null!=(u=e.profileImageUrl)?u:null,role:e.role,shiftSchedule:null!=(d=e.shiftSchedule)?d:null,skills:e.skills,status:e.status,updatedAt:e.updatedAt,workingHours:null!=(h=e.workingHours)?h:null}},toCreateRequest(e){var t,i,s,n,a,o,l,c,u,d,h,p,m,S,g,f,y,v,E;return{availability:e.availability,contactEmail:null!=(u=null==(t=e.contactEmail)?void 0:t.trim())?u:null,contactInfo:e.contactInfo.trim(),contactMobile:null!=(d=null==(i=e.contactMobile)?void 0:i.trim())?d:null,contactPhone:null!=(h=null==(s=e.contactPhone)?void 0:s.trim())?h:null,currentLocation:null!=(p=e.currentLocation)?p:null,department:null!=(m=null==(n=e.department)?void 0:n.trim())?m:null,employeeId:e.employeeId,fullName:null!=(S=null==(a=e.fullName)?void 0:a.trim())?S:null,generalAssignments:e.generalAssignments,hireDate:e.hireDate?(0,r.B7)(e.hireDate):null,name:e.name.trim(),notes:null!=(g=null==(o=e.notes)?void 0:o.trim())?g:null,position:null!=(f=null==(l=e.position)?void 0:l.trim())?f:null,profileImageUrl:null!=(y=null==(c=e.profileImageUrl)?void 0:c.trim())?y:null,role:e.role,shiftSchedule:null!=(v=e.shiftSchedule)?v:null,skills:e.skills,status:e.status,workingHours:null!=(E=e.workingHours)?E:null}},toUpdateRequest(e){var t,i,s,n,a,o,l,c,u,d,h,p,m,S,g,f,y,v,E,T;let k={};return void 0!==e.name&&(k.name=null!=(h=null==(t=e.name)?void 0:t.trim())?h:null),void 0!==e.employeeId&&(k.employeeId=e.employeeId),void 0!==e.contactInfo&&(k.contactInfo=null!=(p=null==(i=e.contactInfo)?void 0:i.trim())?p:null),void 0!==e.contactEmail&&(k.contactEmail=null!=(m=null==(s=e.contactEmail)?void 0:s.trim())?m:null),void 0!==e.contactMobile&&(k.contactMobile=null!=(S=null==(n=e.contactMobile)?void 0:n.trim())?S:null),void 0!==e.contactPhone&&(k.contactPhone=null!=(g=null==(a=e.contactPhone)?void 0:a.trim())?g:null),void 0!==e.position&&(k.position=null!=(f=null==(o=e.position)?void 0:o.trim())?f:null),void 0!==e.department&&(k.department=null!=(y=null==(l=e.department)?void 0:l.trim())?y:null),void 0!==e.hireDate&&(k.hireDate=e.hireDate?(0,r.B7)(e.hireDate):null),void 0!==e.fullName&&(k.fullName=null!=(v=null==(c=e.fullName)?void 0:c.trim())?v:null),void 0!==e.role&&(k.role=e.role),void 0!==e.status&&(k.status=e.status),void 0!==e.availability&&(k.availability=e.availability),void 0!==e.currentLocation&&(k.currentLocation=e.currentLocation),void 0!==e.workingHours&&(k.workingHours=e.workingHours),void 0!==e.generalAssignments&&(k.generalAssignments=e.generalAssignments),void 0!==e.notes&&(k.notes=null!=(E=null==(u=e.notes)?void 0:u.trim())?E:null),void 0!==e.profileImageUrl&&(k.profileImageUrl=null!=(T=null==(d=e.profileImageUrl)?void 0:d.trim())?T:null),void 0!==e.shiftSchedule&&(k.shiftSchedule=e.shiftSchedule),void 0!==e.skills&&(k.skills=e.skills),k}}},38549:(e,t,i)=>{i.d(t,{Qq:()=>s});let r=function(){let e=function(){let e=window.location.hostname;return(window.location.port,e.includes(".vercel.app")||e.includes(".netlify.app")||e.includes(".herokuapp.com")||e.includes("cloudworkstations.dev"))?"cloud":"localhost"===e||"127.0.0.1"===e||e.startsWith("192.168.")?e.startsWith("192.168.")||e.startsWith("10.")||e.startsWith("172.")?"network":"localhost":"cloud"}(),{apiUrl:t,apiBaseUrl:i,wsUrl:r}={apiUrl:"http://localhost:3001",apiBaseUrl:"http://localhost:3001/api",wsUrl:"ws://localhost:3001"};return{apiUrl:t,apiBaseUrl:i,wsUrl:r,environment:"production",deploymentContext:e,isProduction:!0,isDevelopment:!1,enableDebugLogging:!0}}();function s(){return r}},38750:(e,t,i)=>{i.d(t,{B:()=>r});class r{static hasPermission(e,t){let i=this.normalizeRole(e);return i?this.ROLE_PERMISSIONS[i].includes(t)||this.checkHierarchicalPermission(i,t)?{hasPermission:!0}:{hasPermission:!1,reason:"Access denied. Required role: ".concat(i||"Unknown"),requiredRole:i||"USER"}:{hasPermission:!1,reason:"Invalid user role"}}static hasMinimumRole(e,t){let i=this.normalizeRole(e);return!!i&&this.ROLE_HIERARCHY[i]>=this.ROLE_HIERARCHY[t]}static getPermissionsForRole(e){let t=this.normalizeRole(e);return t?[...this.ROLE_PERMISSIONS[t]]:[]}static hasAllPermissions(e,t){for(let i of t){let t=this.hasPermission(e,i);if(!t.hasPermission)return{hasPermission:!1,reason:"Missing permission: ".concat(i),requiredRole:t.requiredRole||"USER"}}return{hasPermission:!0}}static hasAnyPermission(e,t){for(let i of t)if(this.hasPermission(e,i).hasPermission)return{hasPermission:!0};return{hasPermission:!1,reason:"None of the required permissions found: ".concat(t.join(", "))}}static normalizeRole(e){if(!e||"string"!=typeof e)return null;let t=e.toUpperCase();return Object.keys(this.ROLE_HIERARCHY).includes(t)?t:null}static checkHierarchicalPermission(e,t){let i=this.ROLE_HIERARCHY[e];for(let[e,r]of Object.entries(this.ROLE_HIERARCHY))if(r>i&&this.ROLE_PERMISSIONS[e].includes(t))break;return!1}static getMinimumRoleForPermission(e){for(let[t,i]of Object.entries(this.ROLE_PERMISSIONS))if(i.includes(e))return t}}r.ROLE_HIERARCHY={READONLY:0,USER:1,MANAGER:2,ADMIN:3,SUPER_ADMIN:4},r.ROLE_PERMISSIONS={READONLY:["read","employees:read","vehicles:read","delegations:read","tasks:read","reports:read"],USER:["read","employees:read","vehicles:read","delegations:read","tasks:read","reports:read","settings:read"],MANAGER:["read","write","employees:read","employees:write","vehicles:read","vehicles:write","delegations:read","delegations:write","tasks:read","tasks:write","reports:read","reports:write","settings:read"],ADMIN:["read","write","delete","employees:read","employees:write","employees:delete","vehicles:read","vehicles:write","vehicles:delete","delegations:read","delegations:write","delegations:delete","tasks:read","tasks:write","tasks:delete","reports:read","reports:write","settings:read","settings:write"],SUPER_ADMIN:["read","write","delete","admin","employees:read","employees:write","employees:delete","employees:admin","vehicles:read","vehicles:write","vehicles:delete","vehicles:admin","delegations:read","delegations:write","delegations:delete","delegations:admin","tasks:read","tasks:write","tasks:delete","tasks:admin","reports:read","reports:write","reports:admin","settings:read","settings:write","settings:admin","system:admin","system:audit","system:backup"]}},39807:(e,t,i)=>{i.d(t,{G:()=>d});var r=i(12115),s=i(23505),n=i(40283),a=i(87085),o=i(8264),l=i(20249),c=i(67612),u=i(53953);function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoInitialize:t=!0,enableLogging:i=!0,onSecurityError:d,onSecurityStatusChange:h,...p}=e,{session:m,user:S,loading:g,signOut:f}=(0,n.useAuthContext)(),{config:y,isConfigValid:v}=(0,a.KW)(),E=(0,o.b)(),T=(0,l.C)(null==m?void 0:m.access_token),k=(0,c.j8)(),A=(0,u.o)(),C=(0,r.useRef)(null),I=(0,r.useRef)(null),R=(0,r.useMemo)(()=>!!S&&!!(null==m?void 0:m.access_token)&&!g,[S,null==m?void 0:m.access_token,g]),w=(0,r.useMemo)(()=>R&&T.isTokenValid&&!T.isTokenExpired,[R,T.isTokenValid,T.isTokenExpired]),b=(0,r.useMemo)(()=>({csrfProtection:E,tokenManagement:T,inputValidation:k,sessionSecurity:A}),[E,T,k,A]),_=(0,r.useMemo)(()=>({baseURL:y.http.baseURL,timeout:y.http.timeout,retryAttempts:y.http.retryAttempts,getAuthToken:()=>(null==m?void 0:m.access_token)||null,enableCSRF:y.csrf.enabled,enableInputSanitization:y.inputSanitization.enabled,enableTokenValidation:y.tokenValidation.enabled,enableAutoLogout:y.authentication.autoLogout,securityConfig:y,validateSecurityFeatures:!0,...p}),[y,null==m?void 0:m.access_token,p]),O=(0,r.useMemo)(()=>{try{if(!v)throw Error("Invalid security configuration");let e=(0,s.z)(_,b);return t&&b&&e.initializeSecurity(b),C.current=e,I.current=null,i&&console.log("\uD83D\uDD10 useSecureHttpClient: Client initialized successfully",{isAuthenticated:R,hasValidToken:w,securityFeatures:Object.keys(b).filter(e=>b[e])}),e}catch(t){let e=t instanceof Error?t:Error("Failed to initialize secure client");throw I.current=e,null==d||d(e),console.error("useSecureHttpClient: Failed to initialize client:",e),e}},[_,b,t,v,R,w,i,d]),N=(0,r.useMemo)(()=>{try{return(null==O?void 0:O.getSecurityStatus())||null}catch(e){return console.warn("useSecureHttpClient: Failed to get security status:",e),null}},[O]);(0,r.useEffect)(()=>{N&&h&&h(N)},[N,h]);let D=(0,r.useCallback)(()=>{try{O&&b&&(O.refreshSecurityFeatures(),i&&console.log("\uD83D\uDD04 useSecureHttpClient: Security features refreshed"))}catch(e){console.error("useSecureHttpClient: Failed to refresh security features:",e),null==d||d(e instanceof Error?e:Error("Refresh failed"))}},[O,b,i,d]),P=(0,r.useCallback)(e=>{try{O&&(O.updateSecurityConfig(e),i&&console.log("\uD83D\uDD27 useSecureHttpClient: Security configuration updated",e))}catch(e){console.error("useSecureHttpClient: Failed to update security config:",e),null==d||d(e instanceof Error?e:Error("Config update failed"))}},[O,i,d]),F=(0,r.useCallback)(e=>k.sanitizeInput(e),[k]);return{client:O,isAuthenticated:R,hasValidToken:w,securityStatus:N,refreshToken:(0,r.useCallback)(async()=>{try{return await T.refreshToken()}catch(e){return console.error("useSecureHttpClient: Token refresh failed:",e),null==d||d(e instanceof Error?e:Error("Token refresh failed")),!1}},[T,d]),refreshSecurityFeatures:D,updateSecurityConfig:P,sanitizeInput:F,isInitialized:!!O&&!I.current,isLoading:g,error:I.current}}},40283:(e,t,i)=>{i.d(t,{AuthProvider:()=>d,useAuthContext:()=>h});var r=i(95155),s=i(12115),n=i(72248),a=i(10431),o=i(28113),l=i(14163);let c="workhub-logout-event",u=(0,s.createContext)(void 0);function d(e){var t,i,d,h;let{children:p}=e,[m,S]=(0,s.useState)(null),[g,f]=(0,s.useState)(null),[y,v]=(0,s.useState)(!0),[E,T]=(0,s.useState)(null),[k,A]=(0,s.useState)(!1),C=e=>{var t;return{app_metadata:e.app_metadata,created_at:e.created_at,email:e.email,email_confirmed_at:e.email_confirmed_at,id:e.id,is_anonymous:e.is_anonymous,is_sso_user:(null==(t=e.app_metadata)?void 0:t.provider)!=="email",last_sign_in_at:e.last_sign_in_at,updated_at:e.updated_at,user_metadata:e.user_metadata}};(0,s.useEffect)(()=>{let e=!0;(async()=>{if(a.E9.initializeCircuitBreaker(),!a.E9.canPerformSecurityCheck()){console.debug("\uD83D\uDD12 Auth initialization blocked by circuit breaker"),e&&(v(!1),A(!0),T("Authentication system temporarily unavailable"));return}let t="auth-initialization";if(!a.E9.startSecurityOperation(t))return console.debug("\uD83D\uDD04 Auth initialization already in progress");try{let{data:{session:t},error:i}=await l.N.auth.getSession();i?(console.error("Error getting initial session:",i),a.E9.recordSecurityAttempt(),T(i.message)):e&&(console.log("✅ Auth initialization successful"),a.E9.recordSecuritySuccess(),S(t),f((null==t?void 0:t.user)?C(t.user):null),t&&(a.Cv.updateActivity(),setTimeout(async()=>{try{await a.Cv.performIntegrityCheck()?console.log("✅ Session integrity check passed after auth initialization"):(console.log("\uD83D\uDCCA Session integrity check failed - automatic recovery will handle this"),a.Cv.recoverFromCorruptedState()||console.warn("⚠️ Session recovery completed with warnings after auth initialization"))}catch(e){console.warn("Session integrity check error:",e)}},1e3)))}catch(t){console.error("Error initializing auth:",t),a.E9.recordSecurityAttempt(),e&&T("Failed to initialize authentication")}finally{a.E9.endSecurityOperation(t),e&&(v(!1),A(!0))}})();let{data:{subscription:t}}=l.N.auth.onAuthStateChange(async(t,i)=>{var r;e&&(console.log("Auth state changed:",t,null==i||null==(r=i.user)?void 0:r.email),S(i),f((null==i?void 0:i.user)?C(i.user):null),v(!1),A(!0),"SIGNED_OUT"===t&&T(null))});return()=>{e=!1,t.unsubscribe()}},[]);let I=async(e,t)=>{try{v(!0),T(null);let{data:i,error:r}=await l.N.auth.signInWithPassword({email:e,password:t});if(r)return T(r.message),{error:r.message};return{}}catch(t){let e=t instanceof Error?t.message:"An unexpected error occurred";return T(e),{error:e}}finally{v(!1)}},R=async()=>{if(!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Sign out blocked by circuit breaker");let e="auth-signout";if(!a.E9.startSecurityOperation(e))return void console.debug("\uD83D\uDD04 Sign out already in progress");try{v(!0),console.log("\uD83D\uDD10 Starting sign out process..."),a.Cv.clearSessionState(),a.E9.clearAllCookies();let{error:e}=await l.N.auth.signOut();e?(console.error("❌ Supabase sign out error:",e),a.E9.recordSecurityAttempt(),T(e.message)):(console.log("✅ Sign out successful"),a.E9.recordSecuritySuccess(),S(null),f(null),T(null))}catch(e){console.error("Sign out error:",e),a.E9.recordSecurityAttempt(),T("Sign out failed")}finally{a.E9.endSecurityOperation(e),v(!1)}},w={clearError:()=>{T(null)},error:E,isInitialized:k,loading:y,session:m?{access_token:m.access_token,user:null!=g?g:null}:null,signIn:I,signOut:R,user:g,userRole:g?(null==(d=g.user_metadata)?void 0:d.role)||(null==(h=g.app_metadata)?void 0:h.role)||"USER":null},b=(0,s.useRef)(!1),_=(0,o.Q)();(0,s.useEffect)(()=>{(0,n.y2)(()=>{var e;return(null==(e=w.session)?void 0:e.access_token)||null})},[null==(t=w.session)?void 0:t.access_token]);let O=(0,s.useCallback)(e=>{if(e.key===c&&"true"===e.newValue){if(console.log("\uD83D\uDD10 Cross-tab logout detected. Signing out..."),!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Cross-tab logout blocked by circuit breaker");if(!b.current){b.current=!0;let e="cross-tab-logout";a.E9.startSecurityOperation(e)?w.signOut().finally(()=>{a.E9.endSecurityOperation(e),b.current=!1,localStorage.removeItem(c)}):(console.debug("\uD83D\uDD04 Cross-tab logout already in progress"),b.current=!1)}}},[w.signOut]);return(0,s.useEffect)(()=>{let e=()=>{if(console.warn("\uD83D\uDD10 Critical token refresh failure detected, signing out user"),!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Critical refresh failure handling blocked by circuit breaker");if(!b.current){b.current=!0;let e="critical-refresh-failure";a.E9.startSecurityOperation(e)?(a.E9.recordSecurityAttempt(),w.signOut().finally(()=>{a.E9.endSecurityOperation(e),b.current=!1})):(console.debug("\uD83D\uDD04 Critical refresh failure handling already in progress"),b.current=!1)}};return _.subscribe(t=>{"critical_refresh_failed"===t&&e()}),()=>{}},[w.signOut,_]),(0,s.useEffect)(()=>{var e;null==(e=w.session)||e.access_token,globalThis.addEventListener("storage",O),w.session||w.loading||b.current?w.session&&"true"===localStorage.getItem(c)&&localStorage.removeItem(c):localStorage.setItem(c,"true")},[null==(i=w.session)?void 0:i.access_token,w.user,w.loading,O]),(0,s.useEffect)(()=>()=>{globalThis.removeEventListener("storage",O)},[O]),(0,r.jsx)(u.Provider,{value:w,children:p})}function h(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e}},46265:(e,t,i)=>{i.d(t,{B:()=>s});var r=i(10431);class s{async processRequest(e,t){let i={...e};try{var r,s,n,a,o;if((null==(r=this.config.authentication)?void 0:r.enabled)&&this.securityFeatures.sessionSecurity&&!this.securityFeatures.sessionSecurity.isSessionActive)throw Error("Authentication required for secure API calls");if((null==(s=this.config.tokenValidation)?void 0:s.enabled)&&this.securityFeatures.tokenManagement){let{isTokenValid:e,isTokenExpired:t,refreshToken:i}=this.securityFeatures.tokenManagement;if(!e||t){if(console.log("\uD83D\uDD04 SecurityComposer: Token invalid/expired, attempting refresh..."),!await i())throw Error("Token refresh failed - authentication required");console.log("✅ SecurityComposer: Token refreshed successfully")}}if((null==(n=this.config.inputSanitization)?void 0:n.enabled)&&this.securityFeatures.inputValidation&&i.body){let{sanitizeInput:e}=this.securityFeatures.inputValidation;i.body=e(i.body),console.debug("\uD83E\uDDF9 SecurityComposer: Input sanitized using moved hooks")}if((null==(a=this.config.csrf)?void 0:a.enabled)&&this.securityFeatures.csrfProtection){let e=null==(o=i.method)?void 0:o.toUpperCase();if(["POST","PUT","PATCH","DELETE"].includes(e||"")){let{attachCSRF:e}=this.securityFeatures.csrfProtection,t={url:i.url||"",method:i.method||"GET",headers:i.headers||{},body:i.body},r=e(t);i={...i,...r},console.debug("\uD83D\uDEE1️ SecurityComposer: CSRF protection applied using moved hooks")}}return i}catch(e){throw console.error("SecurityComposer: Error processing request:",e),e}}async handleError(e,t){try{var i;(null==(i=this.config.authentication)?void 0:i.autoLogout)&&this.securityFeatures.sessionSecurity&&e instanceof Error&&(e.message.includes("401")||e.message.includes("Authentication")||e.message.includes("Unauthorized"))&&(console.warn("\uD83D\uDD10 SecurityComposer: Authentication error detected, clearing session..."),this.securityFeatures.sessionSecurity.clearSession()),this.securityFeatures.sessionSecurity&&this.securityFeatures.sessionSecurity.updateActivity()}catch(e){console.error("SecurityComposer: Error in error handling:",e)}}getSecurityStatus(){var e,t,i,r,s,n;return{isAuthenticated:null!=(r=null==(e=this.securityFeatures.sessionSecurity)?void 0:e.isSessionActive)&&r,hasValidToken:null!=(s=null==(t=this.securityFeatures.tokenManagement)?void 0:t.isTokenValid)&&s,sessionActive:null!=(n=null==(i=this.securityFeatures.sessionSecurity)?void 0:i.isSessionActive)&&n,securityFeaturesEnabled:this.config,securityFeaturesInitialized:!!(this.securityFeatures.csrfProtection||this.securityFeatures.tokenManagement||this.securityFeatures.inputValidation||this.securityFeatures.sessionSecurity)}}updateConfig(e){this.config={...this.config,...e}}updateSecurityFeatures(e){this.securityFeatures={...this.securityFeatures,...e}}constructor(e,t={}){this.securityFeatures=e,this.config={csrf:{enabled:!0,tokenHeader:"X-CSRF-Token",excludePaths:[]},tokenValidation:{enabled:!0,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES,autoRefresh:!0},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},authentication:{enabled:!0,autoLogout:!0,redirectOnFailure:!0},...t}}}},50080:(e,t,i)=>{i.d(t,{n:()=>a});var r=i(12115),s=i(54983);class n extends Error{constructor({code:e,details:t,message:i,status:r}){super(i),this.name="ApiError",this.status=r||0,this.code=e||"",this.details=t}}function a(){let e=(0,s.Z8)({enableLogging:!0}),t=(0,r.useCallback)(async t=>{try{return await e.secureRequest({url:t.url,method:t.method||"GET",data:t.data,headers:t.headers||{},timeout:t.timeout||1e4})}catch(e){if(e instanceof Error)throw new n({code:"REQUEST_FAILED",message:e.message,details:e});throw e}},[e]);return{hasValidToken:e.hasValidToken,isAuthenticated:e.isAuthenticated,refreshToken:e.refreshToken,sanitizeInput:e.sanitizeInput,secureRequest:t}}},52474:(e,t,i)=>{i.d(t,{B:()=>r});class r{static validateValue(e,t){let i=[],r=e;return(t.sanitizer&&(r=t.sanitizer(e)),t.required&&(null==e||""===e))?(i.push("This field is required"),{isValid:!1,errors:i}):t.required||null!=e&&""!==e?("string"==typeof e&&(t.minLength&&e.length<t.minLength&&i.push("Minimum length is ".concat(t.minLength," characters")),t.maxLength&&e.length>t.maxLength&&i.push("Maximum length is ".concat(t.maxLength," characters")),t.pattern&&!t.pattern.test(e)&&i.push("Invalid format"),this.containsDangerousContent(e)&&i.push("Contains potentially dangerous content")),t.customValidator&&!t.customValidator(r)&&i.push("Custom validation failed"),{isValid:0===i.length,errors:i,sanitizedValue:r}):{isValid:!0,errors:[],sanitizedValue:r}}static validateObject(e,t){let i=[],r={};for(let[s,n]of Object.entries(t)){let t=e[s],a=this.validateValue(t,n);a.isValid?r[s]=a.sanitizedValue:i.push(...a.errors.map(e=>"".concat(s,": ").concat(e)))}return{isValid:0===i.length,errors:i,sanitizedValue:r}}static sanitizeForXSS(e){return"string"!=typeof e?e:e.replace(this.PATTERNS.XSS,"").replace(/javascript:/gi,"").replace(/vbscript:/gi,"").replace(/data:text\/html/gi,"data:text/plain").replace(/on\w+\s*=/gi,"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}static escapeForDisplay(e){return"string"!=typeof e?e:e.replace(/'/g,"''").replace(/;/g,"").replace(/--/g,"").replace(/\/\*/g,"").replace(/\*\//g,"").replace(/\b(UNION|SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b/gi,"")}static validateEmail(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.EMAIL,maxLength:254,sanitizer:e=>e.toLowerCase().trim()})}static validatePhone(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.PHONE,sanitizer:e=>e.replace(/\s/g,"")})}static validateURL(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.URL,customValidator:e=>{try{return new URL(e),!0}catch(e){return!1}}})}static validateUUID(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.UUID})}static containsDangerousContent(e){let t=e.toLowerCase();for(let e of this.DANGEROUS_STRINGS)if(t.includes(e.toLowerCase()))return!0;return!!(this.PATTERNS.SQL_INJECTION.test(e)||this.PATTERNS.XSS.test(e))}static createEmployeeValidationSchema(){return{firstName:{required:!0,minLength:2,maxLength:50,pattern:this.PATTERNS.ALPHA,sanitizer:this.sanitizeForXSS},lastName:{required:!0,minLength:2,maxLength:50,pattern:this.PATTERNS.ALPHA,sanitizer:this.sanitizeForXSS},email:{required:!0,pattern:this.PATTERNS.EMAIL,maxLength:254,sanitizer:e=>this.sanitizeForXSS(e.toLowerCase().trim())},phone:{required:!1,pattern:this.PATTERNS.PHONE,sanitizer:e=>(null==e?void 0:e.replace(/\s/g,""))||""},position:{required:!0,minLength:2,maxLength:100,sanitizer:this.sanitizeForXSS}}}static createVehicleValidationSchema(){return{make:{required:!0,minLength:2,maxLength:50,sanitizer:this.sanitizeForXSS},model:{required:!0,minLength:1,maxLength:50,sanitizer:this.sanitizeForXSS},year:{required:!0,pattern:/^\d{4}$/,customValidator:e=>{let t=parseInt(e.toString());return t>=1900&&t<=new Date().getFullYear()+1}},licensePlate:{required:!0,minLength:2,maxLength:20,sanitizer:e=>this.sanitizeForXSS(e.toUpperCase().trim())},vin:{required:!1,minLength:17,maxLength:17,pattern:/^[A-HJ-NPR-Z0-9]{17}$/i,sanitizer:e=>(null==e?void 0:e.toUpperCase().trim())||""}}}}r.PATTERNS={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,PHONE:/^\+?[\d\s\-\(\)]{10,}$/,URL:/^https?:\/\/[^\s/$.?#].[^\s]*$/,ALPHANUMERIC:/^[a-zA-Z0-9]+$/,ALPHA:/^[a-zA-Z]+$/,NUMERIC:/^\d+$/,UUID:/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,SQL_INJECTION:/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/i,XSS:/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,HTML_TAGS:/<[^>]*>/g},r.DANGEROUS_STRINGS=["javascript:","vbscript:","onload=","onerror=","onclick=","onmouseover=","onfocus=","onblur=","onchange=","onsubmit=","data:text/html","eval(","expression(","setTimeout(","setInterval("]},52747:(e,t,i)=>{i.d(t,{sf:()=>l.sf,Sk:()=>a,a8:()=>o.a8}),i(8264),i(67612);var r=i(12115),s=i(40283),n=i(38750);function a(){let{user:e,userRole:t,loading:i}=(0,s.useAuthContext)(),a=(0,r.useMemo)(()=>!!e&&!i,[e,i]),o=(0,r.useMemo)(()=>{if(!a||!t)return null;let e=t.toUpperCase();return["USER","ADMIN","SUPER_ADMIN"].includes(e)?e:"USER"},[a,t]),l=(0,r.useCallback)(e=>!!o&&n.B.hasPermission(o,e).hasPermission,[o]),c=(0,r.useCallback)(e=>!!o&&n.B.hasAllPermissions(o,e).hasPermission,[o]),u=(0,r.useCallback)(e=>!!o&&n.B.hasAnyPermission(o,e).hasPermission,[o]),d=(0,r.useCallback)(e=>!!o&&n.B.hasMinimumRole(o,e),[o]),h=(0,r.useCallback)(e=>o?n.B.hasPermission(o,e):{hasPermission:!1,reason:"User not authenticated"},[o]),p=(0,r.useCallback)(()=>o?n.B.getPermissionsForRole(o):[],[o]),m=(0,r.useMemo)(()=>"USER"===o,[o]),S=(0,r.useMemo)(()=>"ADMIN"===o,[o]),g=(0,r.useMemo)(()=>"SUPER_ADMIN"===o,[o]);return{userRole:o,isAuthenticated:a,hasPermission:l,hasAllPermissions:c,hasAnyPermission:u,hasMinimumRole:d,getPermissionCheck:h,getAllPermissions:p,isUser:m,isAdmin:S,isSuperAdmin:g}}i(50080);var o=i(54983);i(39807),i(10431);i(53953),i(20249),i(28113);var l=i(87085);i(46265),i(23505)},53953:(e,t,i)=>{i.d(t,{o:()=>a});var r=i(12115),s=i(40283),n=i(10431);function a(){let{signOut:e}=(0,s.useAuthContext)(),[t,i]=(0,r.useState)({concurrentSessions:[],isSessionActive:!0,isSessionExpired:!1,lastActivity:null,sessionId:"",sessionWarning:!1}),a=(0,r.useCallback)(()=>{n.Cv.updateActivity(),i(e=>({...e,isSessionActive:!0,lastActivity:new Date,sessionWarning:!1}))},[]),o=(0,r.useCallback)(()=>{n.Cv.handleCrossTabLogout(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0}))},[]),l=(0,r.useCallback)(()=>{n.Cv.clearSessionState(),i({concurrentSessions:[],isSessionActive:!1,isSessionExpired:!0,lastActivity:null,sessionId:"",sessionWarning:!1})},[]),c=(0,r.useCallback)(()=>{let e=n.Cv.getSessionState(),t=n.Cv.getCurrentSessionId();e&&i(i=>({...i,isSessionActive:e.isActive,isSessionExpired:!1,lastActivity:e.lastActivity,sessionId:t,sessionWarning:!1})),n.Cv.manageConcurrentSessions()},[]);return(0,r.useEffect)(()=>(n.E9.initializeCircuitBreaker(),n.Cv.initialize(),(async()=>{try{if(!await n.Cv.performIntegrityCheck()&&(console.warn("\uD83D\uDCCA Initial session integrity check failed, attempting recovery..."),!n.Cv.recoverFromCorruptedState())){console.error("❌ Initial session recovery failed"),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0}));return}let e=n.Cv.getSessionState(),t=n.Cv.getCurrentSessionId();i(i=>{var r,s;return{...i,isSessionActive:null==(r=null==e?void 0:e.isActive)||r,lastActivity:null!=(s=null==e?void 0:e.lastActivity)?s:new Date,sessionId:t}}),console.log("✅ Session security initialized successfully")}catch(e){console.error("❌ Session security initialization failed:",e),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0}))}})(),()=>{n.Cv.cleanup()}),[]),(0,r.useEffect)(()=>n.Cv.addSessionEventListener(t=>{if(!n.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Session event handling blocked by circuit breaker");let r="session-event-".concat(t.type);if(!n.E9.startSecurityOperation(r))return void console.debug("\uD83D\uDD04 Session event ".concat(t.type," already being handled"));try{switch(t.type){case n.lo.CROSS_TAB_LOGOUT:console.log("\uD83D\uDD04 Cross-tab logout event received"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),setTimeout(()=>e(),100);break;case n.lo.SESSION_TIMEOUT:console.log("⏰ Session timeout event received"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),setTimeout(()=>e(),100);break;case"session_validated":console.log("✅ Session validated event received"),n.E9.recordSecuritySuccess(),i(e=>({...e,isSessionActive:!0,isSessionExpired:!1,sessionWarning:!1}));break;case"token_refresh_success":console.log("\uD83D\uDD04 Token refresh success event received"),n.E9.recordSecuritySuccess(),c();break;case n.lo.TOKEN_REFRESH_FAILED:console.warn("❌ Token refresh failed event received"),n.E9.recordSecurityAttempt(),i(e=>({...e,sessionWarning:!0}));break;default:console.debug("\uD83D\uDD0D Unknown session event: ".concat(t.type))}}catch(e){console.error("❌ Error handling session event ".concat(t.type,":"),e),n.E9.recordSecurityAttempt()}finally{n.E9.endSecurityOperation(r)}}),[e,c]),(0,r.useEffect)(()=>{let t=()=>{if(!n.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Session timeout check blocked by circuit breaker");let t="session-timeout-check";if(!n.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Session timeout check already in progress");try{if(!n.Cv.validateSessionConsistency()&&(console.warn("\uD83D\uDCCA Session state inconsistent, attempting recovery..."),!n.Cv.recoverFromCorruptedState())){console.error("❌ Session recovery failed, forcing logout"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),e();return}if(n.Cv.detectTimeout()){console.log("⏰ Session timeout detected"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),setTimeout(()=>e(),100);return}let t=n.Cv.getSessionState();(null==t?void 0:t.lastActivity)&&Date.now()-t.lastActivity.getTime()>15e5&&i(e=>({...e,sessionWarning:!0})),n.E9.recordSecuritySuccess()}catch(e){console.error("❌ Session timeout check failed:",e),n.E9.recordSecurityAttempt()}finally{n.E9.endSecurityOperation(t)}},r=setInterval(t,12e4);return setTimeout(t,1e3),()=>clearInterval(r)},[e]),(0,r.useEffect)(()=>{let e=setInterval(()=>{n.Cv.manageConcurrentSessions()},3e5);return()=>clearInterval(e)},[]),{clearSession:l,concurrentSessions:t.concurrentSessions,handleCrossTabLogout:o,isSessionActive:t.isSessionActive,isSessionExpired:t.isSessionExpired,lastActivity:t.lastActivity,refreshSession:c,sessionId:t.sessionId,sessionWarning:t.sessionWarning,updateActivity:a}}},54120:(e,t,i)=>{function r(e){return void 0===e?null:e}i.d(t,{d$:()=>r})},54983:(e,t,i)=>{i.d(t,{Z8:()=>a,a8:()=>n});var r=i(12115),s=i(39807);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{client:t,isAuthenticated:i,hasValidToken:n,securityStatus:a,refreshToken:o,refreshSecurityFeatures:l,updateSecurityConfig:c,sanitizeInput:u,isInitialized:d,isLoading:h,error:p}=(0,s.G)(e);return{hasValidToken:n,isAuthenticated:i,refreshToken:o,sanitizeInput:u,secureRequest:(0,r.useCallback)(async e=>{let{data:i,headers:r={},method:s="GET",timeout:n,url:a}=e;try{let e,o={headers:r,timeout:n||1e4};switch(s.toUpperCase()){case"GET":e=await t.get(a,o);break;case"POST":e=await t.post(a,i,o);break;case"PUT":e=await t.put(a,i,o);break;case"PATCH":e=await t.patch(a,i,o);break;case"DELETE":e=await t.delete(a,o);break;default:throw Error("Unsupported HTTP method: ".concat(s))}return{data:e,headers:{},status:200,statusText:"OK"}}catch(e){if(e instanceof Error)throw e;throw Error("Request failed")}},[t]),client:t,securityStatus:a,refreshSecurityFeatures:l,updateSecurityConfig:c,isInitialized:d,isLoading:h,error:p}}function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=n(e);return{hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,refreshToken:t.refreshToken,sanitizeInput:t.sanitizeInput,secureRequest:t.secureRequest}}},55411:(e,t,i)=>{i.d(t,{O:()=>o});var r=i(54120),s=i(3695);let n=e=>new Promise(t=>setTimeout(t,e)),a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return Math.min(t*Math.pow(2,e),3e4)};class o{async delete(e,t){return this.request("DELETE",e,void 0,t)}async get(e,t){return this.request("GET",e,void 0,t)}async patch(e,t,i){return this.request("PATCH",e,t,i)}async post(e,t,i){return this.request("POST",e,t,i)}async put(e,t,i){return this.request("PUT",e,t,i)}async request(e,t,i,o){let l="".concat(this.baseURL).concat(t),c={...this.defaultHeaders,...null==o?void 0:o.headers};if(this.getAuthToken){let e=this.getAuthToken();e&&(c.Authorization="Bearer ".concat(e))}let u={body:i?JSON.stringify(i):null,credentials:"include",headers:c,method:e,signal:(0,r.d$)(null==o?void 0:o.signal)},d=new AbortController,h=setTimeout(()=>d.abort(),(null==o?void 0:o.timeout)||this.timeout);u.signal=(null==o?void 0:o.signal)||d.signal;for(let e=0;e<this.retryAttempts;e++)try{let e=await fetch(l,u);clearTimeout(h);let t=await e.json().catch(()=>null);if(!e.ok){if(t&&"error"===t.status)throw new s.hD(t.message,{code:t.code,details:t.error,status:e.status});let i=(null==t?void 0:t.message)||e.statusText;switch(e.status){case 400:throw new s.v7(i,t);case 401:throw new s.v3(i,t);case 404:throw new s.m_(i,t);case 500:throw new s.PO(i,t);default:throw new s.hD(i,{details:t,status:e.status})}}if(t&&"object"==typeof t&&"success"===t.status)return t.data;if(204===e.status)return;return t}catch(i){if(i instanceof s.hD)throw i;if(e===this.retryAttempts-1){let e=i instanceof Error?i.message:String(i);throw new s.Dr("Request failed after ".concat(this.retryAttempts," attempts: ").concat(e))}let t=a(e);await n(t)}throw new s.Dr("Request failed after multiple retries.")}constructor(e){this.baseURL=e.baseURL,this.timeout=e.timeout||1e4,this.retryAttempts=e.retryAttempts||3,this.getAuthToken=e.getAuthToken,this.defaultHeaders={"Content-Type":"application/json",...e.headers}}}},62494:(e,t,i)=>{i.d(t,{J:()=>a});var r=i(38069),s=i(99605);let n={fromApi:e=>({completed:e.completed,id:e.id,taskId:e.taskId,title:e.title}),toApiRequest:e=>({completed:void 0!==e.completed&&e.completed,taskId:e.taskId,title:e.title.trim()})},a={fromApi(e){var t,i,a,o;return{createdAt:e.createdAt,dateTime:e.dateTime,deadline:null!=(t=e.deadline)?t:null,description:e.description,driverEmployee:e.Employee_Task_driverEmployeeIdToEmployee?r.A.fromApi(e.Employee_Task_driverEmployeeIdToEmployee):null,driverEmployeeId:null!=(i=e.driverEmployeeId)?i:null,estimatedDuration:e.estimatedDuration,id:e.id,location:e.location,notes:null!=(a=e.notes)?a:null,priority:e.priority,requiredSkills:e.requiredSkills,staffEmployee:e.Employee_Task_staffEmployeeIdToEmployee?r.A.fromApi(e.Employee_Task_staffEmployeeIdToEmployee):null,staffEmployeeId:e.staffEmployeeId,status:e.status,subtasks:Array.isArray(e.SubTask)?e.SubTask.map(e=>n.fromApi(e)):[],updatedAt:e.updatedAt,vehicle:e.Vehicle?s.M.fromApi(e.Vehicle):null,vehicleId:null!=(o=e.vehicleId)?o:null}},toCreateRequest(e){var t,i,r,s,a,o;return{dateTime:e.dateTime,deadline:null!=(i=e.deadline)?i:null,description:e.description,driverEmployeeId:null!=(r=e.driverEmployeeId)?r:null,estimatedDuration:e.estimatedDuration,location:e.location,notes:null!=(s=e.notes)?s:null,priority:e.priority,requiredSkills:e.requiredSkills,staffEmployeeId:e.staffEmployeeId,status:e.status,subTasks:null!=(a=null==(t=e.subtasks)?void 0:t.map(n.toApiRequest))?a:[],vehicleId:null!=(o=e.vehicleId)?o:null}},toUpdateRequest(e){let t={};return void 0!==e.description&&(t.description=e.description),void 0!==e.notes&&(t.notes=e.notes),void 0!==e.location&&(t.location=e.location),void 0!==e.dateTime&&(t.dateTime=e.dateTime),void 0!==e.estimatedDuration&&(t.estimatedDuration=e.estimatedDuration),void 0!==e.priority&&(t.priority=e.priority),void 0!==e.status&&(t.status=e.status),void 0!==e.deadline&&(t.deadline=e.deadline),void 0!==e.requiredSkills&&(t.requiredSkills=e.requiredSkills),void 0!==e.vehicleId&&(t.vehicleId=e.vehicleId),void 0!==e.staffEmployeeId&&(t.staffEmployeeId=e.staffEmployeeId),void 0!==e.driverEmployeeId&&(t.driverEmployeeId=e.driverEmployeeId),void 0!==e.subtasks&&(t.subTasks=e.subtasks.map(n.toApiRequest)),t}}},67612:(e,t,i)=>{i.d(t,{j8:()=>n});var r=i(12115),s=i(52474);function n(e){let[t,i]=(0,r.useState)({isValid:!0,errors:{},touched:{},isValidating:!1}),n=(0,r.useCallback)((e,t,r)=>{i(e=>({...e,isValidating:!0}));let n=s.B.validateValue(t,r);return i(t=>({...t,isValidating:!1,errors:{...t.errors,[e]:n.isValid?[]:n.errors},isValid:n.isValid&&Object.values({...t.errors,[e]:n.isValid?[]:n.errors}).every(e=>0===e.length)})),n},[]),a=(0,r.useCallback)((e,t)=>{i(e=>({...e,isValidating:!0}));let r=s.B.validateObject(e,t),n={};return r.errors.forEach(e=>{let[t,...i]=e.split(": "),r=i.join(": ");t&&(n[t]||(n[t]=[]),n[t].push(r))}),i(e=>({...e,isValidating:!1,errors:n,isValid:r.isValid})),r},[]),o=(0,r.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];i(i=>({...i,touched:{...i.touched,[e]:t}}))},[]),l=(0,r.useCallback)(e=>{i(t=>({...t,errors:{...t.errors,[e]:[]},isValid:Object.values({...t.errors,[e]:[]}).every(e=>0===e.length)}))},[]),c=(0,r.useCallback)(()=>{i(e=>({...e,errors:{},isValid:!0}))},[]),u=(0,r.useCallback)(()=>{i({isValid:!0,errors:{},touched:{},isValidating:!1})},[]),d=(0,r.useCallback)(e=>{if("string"==typeof e)return s.B.sanitizeForXSS(e);if(Array.isArray(e))return e.map(e=>d(e));if(e&&"object"==typeof e){let t={};for(let[i,r]of Object.entries(e))t[i]=d(r);return t}return e},[]),h=(0,r.useCallback)(e=>{let i=t.errors[e];return i&&i.length>0&&i[0]||null},[t.errors]),p=(0,r.useCallback)(e=>{let i=t.errors[e];return!!i&&i.length>0},[t.errors]),m=(0,r.useCallback)(e=>t.touched[e]||!1,[t.touched]);return{isValid:t.isValid,errors:t.errors,touched:t.touched,isValidating:t.isValidating,validateField:n,validateForm:a,setFieldTouched:o,clearFieldErrors:l,clearAllErrors:c,resetValidation:u,sanitizeInput:d,getFieldError:h,hasFieldError:p,isFieldTouched:m}}},72248:(e,t,i)=>{i.d(t,{Sk:()=>o,getGlobalAuthTokenProvider:()=>l,uE:()=>u,y2:()=>a});var r=i(55411);i(25982),i(3695);var s=i(38549);i(52747),i(976),i(12430),i(90137),i(97966),i(3619),i(1955);let n=null;function a(e){n=e}function o(){return n}function l(){return console.warn("⚠️ getGlobalAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead."),n}let c=(0,s.Qq)(),u=new r.O({baseURL:c.apiBaseUrl,getAuthToken:function(){if(!n)return null;try{return n()}catch(e){return console.error("❌ Error getting auth token from secure provider:",e),null}},headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4})},87085:(e,t,i)=>{i.d(t,{KW:()=>u,sf:()=>c});var r=i(95155),s=i(12115),n=i(38549),a=i(10431);let o=(()=>{let e=(0,n.Qq)();return{csrf:{enabled:!0,tokenHeader:"X-CSRF-Token",excludePaths:["/api/health","/api/status"]},tokenValidation:{enabled:!0,refreshThreshold:60*a.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES,autoRefresh:!0},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},authentication:{enabled:!0,autoLogout:!0,redirectOnFailure:!0},http:{baseURL:e.apiBaseUrl,timeout:1e4,retryAttempts:3}}})(),l=(0,s.createContext)(null);function c(e){let{children:t,initialConfig:i={},configVersion:n="1.0.0",onConfigChange:c,validateConfig:u=!0}=e,d=(0,s.useMemo)(()=>({...o,...i,csrf:{...o.csrf,...i.csrf},tokenValidation:{...o.tokenValidation,...i.tokenValidation},inputSanitization:{...o.inputSanitization,...i.inputSanitization},authentication:{...o.authentication,...i.authentication},http:{...o.http,...i.http}}),[i]),h=(0,s.useMemo)(()=>{if(!u)return!0;try{if(!d.http.baseURL||d.http.timeout<=0||d.http.retryAttempts<0||d.tokenValidation.refreshThreshold<=0||d.csrf.enabled&&!d.csrf.tokenHeader||d.inputSanitization.enabled&&0===d.inputSanitization.sanitizers.length)return!1;return!0}catch(e){return console.error("SecurityConfigProvider: Configuration validation failed:",e),!1}},[d,u]),p=(0,s.useMemo)(()=>e=>{let t={...d,...e,csrf:{...d.csrf,...e.csrf},tokenValidation:{...d.tokenValidation,...e.tokenValidation},inputSanitization:{...d.inputSanitization,...e.inputSanitization},authentication:{...d.authentication,...e.authentication},http:{...d.http,...e.http}};null==c||c(t)},[d,c]),m=(0,s.useMemo)(()=>()=>{null==c||c(o)},[c]),S=(0,s.useMemo)(()=>({config:d,updateConfig:p,resetConfig:m,isConfigValid:h,configVersion:n}),[d,p,m,h,n]);return s.useEffect(()=>{console.log("\uD83D\uDD27 SecurityConfigProvider: Configuration initialized",{isValid:h,version:n,constants:a.$f,config:{csrf:d.csrf.enabled,tokenValidation:d.tokenValidation.enabled,inputSanitization:d.inputSanitization.enabled,authentication:d.authentication.enabled}})},[d,h,n]),(0,r.jsx)(l.Provider,{value:S,children:t})}function u(){let e=(0,s.useContext)(l);if(!e)throw Error("useSecurityConfig must be used within a SecurityConfigProvider");return e}},89699:(e,t,i)=>{i.d(t,{SessionManager:()=>a});var r=i(38549);let s={MAX_CONCURRENT_SESSIONS:5,SESSION_TIMEOUT_MINUTES:30},n={CROSS_TAB_LOGOUT:"cross_tab_logout",SESSION_INVALID:"session_invalid",SESSION_TIMEOUT:"session_timeout",SESSION_VALIDATED:"session_validated",TOKEN_REFRESH_FAILED:"token_refresh_failed",TOKEN_REFRESH_SUCCESS:"token_refresh_success"};class a{static addSessionEventListener(e){var t;if(void 0===globalThis.window)return()=>{};let i=t=>{e(t.data)};return null==(t=this.broadcastChannel)||t.addEventListener("message",i),()=>{var e;null==(e=this.broadcastChannel)||e.removeEventListener("message",i)}}static cleanup(){var e;for(let t of(this.sessionCheckInterval&&(clearInterval(this.sessionCheckInterval),this.sessionCheckInterval=null),null==(e=this.broadcastChannel)||e.close(),this.broadcastChannel=null,this.activityListeners))t();this.activityListeners=[]}static clearSessionState(){if(void 0!==globalThis.window)for(let e of Object.values(this.STORAGE_KEYS))localStorage.removeItem(e)}static detectAndResolveConflicts(){if(void 0===globalThis.window)return!0;try{let e=this.getSessionState(),t=this.getLastActivity(),i=this.getConcurrentSessions();if(e&&t&&Math.abs(e.lastActivity.getTime()-t.getTime())>3e5){console.warn("⚠️ Session timestamp conflict detected, resolving...");let i=e.lastActivity.getTime()>t.getTime()?e.lastActivity:t;localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY,i.toISOString()),this.setSessionState({...e,lastActivity:i}),console.log("✅ Session timestamp conflict resolved")}let r=this.getCurrentSessionId(),s=i.filter(e=>e.sessionId===r);if(s.length>1){console.warn("⚠️ Duplicate session entries detected, cleaning up...");let e=s.reduce((e,t)=>t.lastActivity>e.lastActivity?t:e),t=i.filter(e=>e.sessionId!==r);t.push(e),this.setConcurrentSessions(t),console.log("✅ Duplicate sessions cleaned up")}return!0}catch(e){return console.error("Failed to detect and resolve conflicts:",e),!1}}static detectTimeout(){if(void 0===globalThis.window)return!1;let e=this.getLastActivity();if(!e)return!0;let t=60*s.SESSION_TIMEOUT_MINUTES*1e3;return Date.now()-e.getTime()>t}static getCurrentSessionId(){if(void 0===globalThis.window)return"";let e=localStorage.getItem(this.STORAGE_KEYS.SESSION_ID);return e||(e=this.generateSessionId(),localStorage.setItem(this.STORAGE_KEYS.SESSION_ID,e)),e}static getSessionState(){if(void 0===globalThis.window)return null;try{let e=localStorage.getItem(this.STORAGE_KEYS.SESSION_STATE);if(!e)return null;let t=JSON.parse(e);return{...t,expiresAt:new Date(t.expiresAt),lastActivity:new Date(t.lastActivity)}}catch(e){return null}}static handleCrossTabLogout(){if(void 0===globalThis.window)return;let e={sessionId:this.getCurrentSessionId(),timestamp:new Date,type:n.CROSS_TAB_LOGOUT};this.broadcastSessionEvent(e),this.clearSessionState()}static handleSessionValidation(e,t){if(void 0===globalThis.window)return;let i={data:t,sessionId:this.getCurrentSessionId(),timestamp:new Date,type:e?n.SESSION_VALIDATED:n.SESSION_INVALID};this.broadcastSessionEvent(i),e?this.updateActivity():this.clearSessionState()}static handleTokenRefresh(e,t){if(void 0===globalThis.window)return;let i={data:t,sessionId:this.getCurrentSessionId(),timestamp:new Date,type:e?n.TOKEN_REFRESH_SUCCESS:n.TOKEN_REFRESH_FAILED};if(this.broadcastSessionEvent(i),e){this.updateActivity();let e=this.getSessionState();if(e){let t=new Date,i=new Date(t.getTime()+60*s.SESSION_TIMEOUT_MINUTES*1e3);this.setSessionState({...e,expiresAt:i,lastActivity:t})}}}static initialize(){void 0!==globalThis.window&&(this.initializeBroadcastChannel(),this.startSessionMonitoring(),this.setupActivityTracking(),this.initializeSessionState())}static manageConcurrentSessions(){if(void 0===globalThis.window)return;let e=this.getConcurrentSessions(),t=this.getCurrentSessionId();if(!e.find(e=>e.sessionId===t)){let i={lastActivity:new Date,sessionId:t,startTime:new Date,userAgent:navigator.userAgent};e.push(i)}let i=e.find(e=>e.sessionId===t);i&&(i.lastActivity=new Date);let r=e.filter(e=>{let t=60*s.SESSION_TIMEOUT_MINUTES*1e3;return Date.now()-e.lastActivity.getTime()<=t});r.length>s.MAX_CONCURRENT_SESSIONS&&(r.sort((e,t)=>t.lastActivity.getTime()-e.lastActivity.getTime()),r.splice(s.MAX_CONCURRENT_SESSIONS)),this.setConcurrentSessions(r)}static async performIntegrityCheck(){if(void 0===globalThis.window)return!0;try{if(!this.validateSessionConsistency())return console.warn("\uD83D\uDCCA Local session state is inconsistent"),!1;if(!await this.validateWithBackend())return console.warn("\uD83D\uDD17 Backend session validation failed"),!1;return this.cleanupStaleSessions(),console.log("✅ Session integrity check passed"),!0}catch(e){return console.error("❌ Session integrity check failed:",e),!1}}static recoverFromCorruptedState(){if(void 0===globalThis.window)return!0;try{if(console.log("\uD83D\uDD27 Attempting session state recovery..."),this.validateSessionConsistency())return console.log("✅ Session state is already consistent"),!0;let e=this.preserveNonSecurityData();return this.clearSessionState(),this.restorePreservedData(e),this.initializeSessionState(),console.log("✅ Session state recovery completed"),!0}catch(e){return console.error("❌ Session state recovery failed:",e),!1}}static setSessionState(e){if(void 0!==globalThis.window)try{localStorage.setItem(this.STORAGE_KEYS.SESSION_STATE,JSON.stringify(e))}catch(e){console.error("Failed to set session state:",e)}}static updateActivity(){if(void 0===globalThis.window)return;let e=new Date;localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY,e.toISOString()),this.manageConcurrentSessions()}static validateSessionConsistency(){if(void 0===globalThis.window)return!0;try{let e=this.getSessionState(),t=this.getLastActivity(),i=this.getCurrentSessionId();if(e&&!t)return console.warn("\uD83D\uDD0D Session state exists but no last activity found"),!1;if(t&&!i)return console.warn("\uD83D\uDD0D Last activity exists but no session ID found"),!1;if(e&&e.sessionId!==i)return console.warn("\uD83D\uDD0D Session state ID mismatch with current session ID"),!1;if(e&&e.expiresAt<new Date)return console.warn("\uD83D\uDD0D Session state has expired"),!1;return!0}catch(e){return console.error("Failed to validate session consistency:",e),!1}}static broadcastSessionEvent(e){var t;null==(t=this.broadcastChannel)||t.postMessage(e)}static cleanupStaleSessions(){try{let e=this.getConcurrentSessions(),t=Date.now(),i=60*s.SESSION_TIMEOUT_MINUTES*1e3,r=e.filter(e=>t-e.lastActivity.getTime()<=i);r.length!==e.length&&(console.log("\uD83E\uDDF9 Cleaned up ".concat(e.length-r.length," stale sessions")),this.setConcurrentSessions(r))}catch(e){console.warn("Failed to cleanup stale sessions:",e)}}static generateSessionId(){return"session_".concat(Date.now(),"_").concat(Math.random().toString(36).slice(2,11))}static getConcurrentSessions(){try{let e=localStorage.getItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS);if(!e)return[];return JSON.parse(e).map(e=>({...e,lastActivity:new Date(e.lastActivity),startTime:new Date(e.startTime)}))}catch(e){return[]}}static getLastActivity(){let e=localStorage.getItem(this.STORAGE_KEYS.LAST_ACTIVITY);return e?new Date(e):null}static handleSessionTimeout(){let e={sessionId:this.getCurrentSessionId(),timestamp:new Date,type:n.SESSION_TIMEOUT};this.broadcastSessionEvent(e),this.clearSessionState()}static initializeBroadcastChannel(){"undefined"!=typeof BroadcastChannel&&(this.broadcastChannel=new BroadcastChannel(this.BROADCAST_CHANNEL_NAME))}static initializeSessionState(){let e=this.getCurrentSessionId(),t=new Date,i=new Date(t.getTime()+60*s.SESSION_TIMEOUT_MINUTES*1e3);this.setSessionState({expiresAt:i,isActive:!0,lastActivity:t,sessionId:e}),this.updateActivity()}static preserveNonSecurityData(){let e={};for(let t of["workhub-app-store","workhub_user_preferences"])try{e[t]=localStorage.getItem(t)}catch(e){console.warn("Failed to preserve data for key ".concat(t,":"),e)}return e}static restorePreservedData(e){for(let[t,i]of Object.entries(e))if(null!==i)try{localStorage.setItem(t,i)}catch(e){console.warn("Failed to restore data for key ".concat(t,":"),e)}}static setConcurrentSessions(e){try{localStorage.setItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS,JSON.stringify(e))}catch(e){console.error("Failed to set concurrent sessions:",e)}}static setupActivityTracking(){let e=()=>{this.updateActivity()};for(let t of["mousedown","mousemove","keypress","scroll","touchstart","click"])document.addEventListener(t,e,{passive:!0}),this.activityListeners.push(()=>{document.removeEventListener(t,e)})}static startSessionMonitoring(){this.sessionCheckInterval=setInterval(()=>{this.detectTimeout()&&this.handleSessionTimeout()},6e4)}static async validateWithBackend(){try{let e=(0,r.Qq)().apiBaseUrl;await new Promise(e=>setTimeout(e,200));let t=await fetch("".concat(e,"/health"),{method:"GET",headers:{"Content-Type":"application/json"},cache:"no-cache"});if(t.ok)return console.log("✅ Backend connectivity validation successful"),!0;return console.log("\uD83D\uDD0D Backend connectivity check failed with status: ".concat(t.status)),!1}catch(e){return console.warn("Backend validation failed:",e),!0}}}a.activityListeners=[],a.BROADCAST_CHANNEL_NAME="workhub_session_events",a.broadcastChannel=null,a.sessionCheckInterval=null,a.STORAGE_KEYS={CONCURRENT_SESSIONS:"workhub_concurrent_sessions",LAST_ACTIVITY:"workhub_last_activity",SESSION_ID:"workhub_session_id",SESSION_STATE:"workhub_session_state"}},90137:(e,t,i)=>{i.d(t,{D:()=>a});var r=i(62494),s=i(25982);let n={fromApi:e=>r.J.fromApi(e),toApi:e=>e};class a extends s.v{async getByStatus(e){return(await this.getAll({status:e})).data}async updateTaskStatus(e,t){return this.executeWithInfrastructure(null,async()=>{let i=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/status"),{status:t});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),r.J.fromApi(i)})}constructor(e,t){super(e,{cacheDuration:18e4,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/tasks",this.transformer=n}}},97966:(e,t,i)=>{i.d(t,{C:()=>a});var r=i(25982),s=i(99605);let n={fromApi:e=>s.M.fromApi(e),toApi:e=>e};class a extends r.v{async getAvailableVehicles(e,t){return(await this.getAll({available:!0,endDate:t.toISOString(),startDate:e.toISOString()})).data}async getByStatus(e){return(await this.getAll({status:e})).data}constructor(e,t){super(e,{cacheDuration:6e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/vehicles",this.transformer=n}}},99605:(e,t,i)=>{i.d(t,{M:()=>s});let r={fromApi:e=>({cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId})},s={fromApi(e){var t,i,s,n;return{color:null!=(t=e.color)?t:null,createdAt:e.createdAt,id:e.id,imageUrl:null!=(i=e.imageUrl)?i:null,initialOdometer:null!=(s=e.initialOdometer)?s:null,licensePlate:e.licensePlate,make:e.make,model:e.model,ownerContact:e.ownerContact,ownerName:e.ownerName,serviceHistory:(()=>{let t=e.serviceHistory||e.ServiceRecord;return Array.isArray(t)?t.map(r.fromApi):[]})(),updatedAt:e.updatedAt,vin:null!=(n=e.vin)?n:null,year:e.year}},toCreateRequest(e){var t,i,r,s;let n=(null==(t=e.vin)?void 0:t.trim())||this.generateDefaultVin(e),a=(null==(i=e.ownerContact)?void 0:i.trim())||"<EMAIL>",o=(null==(r=e.ownerName)?void 0:r.trim())||"WorkHub Fleet Management",l={color:e.color?e.color.trim():null,imageUrl:e.imageUrl?e.imageUrl.trim():"",initialOdometer:null!=(s=e.initialOdometer)?s:null,licensePlate:e.licensePlate.trim(),make:e.make.trim(),model:e.model.trim(),ownerContact:a,ownerName:o,vin:n,year:e.year};if(!l.make||!l.model||!l.year||!l.licensePlate)throw Error("Missing required fields for creating a vehicle (make, model, year, licensePlate)");if(!/^[A-HJ-NPR-Z0-9]{17}$/.test(l.vin))throw Error("VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)");return l},generateDefaultVin(e){let t="ABCDEFGHJKLMNPRSTUVWXYZ0123456789",i=e.make.substring(0,3).toUpperCase().replace(/[IOQ]/g,"X").padEnd(3,"X"),r=e.model.substring(0,2).toUpperCase().replace(/[IOQ]/g,"X").padEnd(2,"X"),s=e.year.toString().substring(2),n="";for(let e=0;e<10;e++)n+=t.charAt(Math.floor(Math.random()*t.length));return"".concat(i).concat(r).concat(s).concat(n).substring(0,17).padEnd(17,"X")},toUpdateRequest(e){let t={};return void 0!==e.make&&(t.make=e.make.trim()),void 0!==e.model&&(t.model=e.model.trim()),void 0!==e.year&&(t.year=e.year),void 0!==e.vin&&(t.vin=e.vin.trim()),void 0!==e.licensePlate&&(t.licensePlate=e.licensePlate.trim()),void 0!==e.ownerName&&(t.ownerName=e.ownerName.trim()),void 0!==e.ownerContact&&(t.ownerContact=e.ownerContact.trim()),void 0!==e.color&&(t.color=e.color?e.color.trim():null),void 0!==e.initialOdometer&&(t.initialOdometer=e.initialOdometer),void 0!==e.imageUrl&&(t.imageUrl=e.imageUrl?e.imageUrl.trim():""),t}}}}]);