{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { Check, ChevronDown, ChevronUp } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ children, className, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    className={cn(\r\n      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"size-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"size-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"size-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ children, className, position = 'popper', ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      className={cn(\r\n        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\r\n        position === 'popper' &&\r\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n        className\r\n      )}\r\n      position={position}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          'p-1',\r\n          position === 'popper' &&\r\n            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.memo(\r\n  React.forwardRef<\r\n    React.ElementRef<typeof SelectPrimitive.Item>,\r\n    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n  >(({ children, className, ...props }, forwardedRef) => {\r\n    const composedRefs = React.useCallback(\r\n      (node: any) => {\r\n        if (typeof forwardedRef === 'function') {\r\n          forwardedRef(node);\r\n        } else if (forwardedRef) {\r\n          (forwardedRef as React.MutableRefObject<any>).current = node;\r\n        }\r\n      },\r\n      [forwardedRef]\r\n    );\r\n\r\n    return (\r\n      <SelectPrimitive.Item\r\n        className={cn(\r\n          'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n          className\r\n        )}\r\n        ref={composedRefs}\r\n        {...props}\r\n      >\r\n        <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\r\n          <SelectPrimitive.ItemIndicator>\r\n            <Check className=\"size-4\" />\r\n          </SelectPrimitive.ItemIndicator>\r\n        </span>\r\n\r\n        <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n      </SelectPrimitive.Item>\r\n    );\r\n  })\r\n);\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;;;AANA;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACV,KAAK;YACJ,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAU,AAAD,sBAC1B,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WAGb,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IACpC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDACnC,CAAC;YACC,IAAI,OAAO,iBAAiB,YAAY;gBACtC,aAAa;YACf,OAAO,IAAI,cAAc;gBACtB,aAA6C,OAAO,GAAG;YAC1D;QACF;+CACA;QAAC;KAAa;IAGhB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;;AAEF,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/StatusUpdateModal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nimport type { DelegationStatusPrisma } from '../lib/types/domain'; // Adjust path as needed\r\n\r\nimport { formatDelegationStatusForDisplay } from '../lib/utils/formattingUtils';\r\nimport { Button } from './ui/button'; // Assuming shadcn/ui button component\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from './ui/dialog'; // Assuming shadcn/ui dialog components\r\nimport { Input } from './ui/input'; // Assuming shadcn/ui input component\r\nimport { Label } from './ui/label'; // Assuming shadcn/ui label component\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from './ui/select'; // Add Select components\r\n\r\ninterface StatusUpdateModalProps {\r\n  currentStatus: DelegationStatusPrisma;\r\n  delegationId: string;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: (status: DelegationStatusPrisma, reason: string) => void;\r\n}\r\n\r\nconst StatusUpdateModal: React.FC<StatusUpdateModalProps> = ({\r\n  currentStatus,\r\n  delegationId,\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n}) => {\r\n  const [newStatus, setNewStatus] =\r\n    useState<DelegationStatusPrisma>(currentStatus);\r\n  const [reason, setReason] = useState<string>('');\r\n  const [error, setError] = useState<null | string>(null);\r\n\r\n  const handleConfirm = () => {\r\n    if (!reason.trim()) {\r\n      setError('Reason for status change is required.');\r\n      return;\r\n    }\r\n    if (newStatus === currentStatus) {\r\n      setError('Please select a different status.');\r\n      return;\r\n    }\r\n    setError(null);\r\n    onConfirm(newStatus, reason);\r\n  };\r\n\r\n  // Available status options\r\n  const statusOptions: DelegationStatusPrisma[] = [\r\n    'Planned',\r\n    'Confirmed',\r\n    'In_Progress',\r\n    'Completed',\r\n    'Cancelled',\r\n    'No_details',\r\n  ];\r\n\r\n  return (\r\n    <Dialog onOpenChange={onClose} open={isOpen}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Update Delegation Status</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"grid gap-4 py-4\">\r\n          <div className=\"grid grid-cols-4 items-center gap-4\">\r\n            <Label className=\"text-right\" htmlFor=\"current-status\">\r\n              Current Status\r\n            </Label>\r\n            <Input\r\n              className=\"col-span-3\"\r\n              id=\"current-status\"\r\n              readOnly\r\n              value={formatDelegationStatusForDisplay(currentStatus)}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-4 items-center gap-4\">\r\n            <Label className=\"text-right\" htmlFor=\"new-status\">\r\n              New Status\r\n            </Label>\r\n            <Select\r\n              onValueChange={(value: DelegationStatusPrisma) =>\r\n                setNewStatus(value)\r\n              }\r\n              value={newStatus}\r\n            >\r\n              <SelectTrigger className=\"col-span-3\">\r\n                <SelectValue placeholder=\"Select new status\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {statusOptions.map(status => (\r\n                  <SelectItem\r\n                    disabled={status === currentStatus}\r\n                    key={status}\r\n                    value={status}\r\n                  >\r\n                    {formatDelegationStatusForDisplay(status)}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-4 items-center gap-4\">\r\n            <Label className=\"text-right\" htmlFor=\"reason\">\r\n              Reason\r\n            </Label>\r\n            <Input\r\n              className=\"col-span-3\"\r\n              id=\"reason\"\r\n              onChange={e => setReason(e.target.value)}\r\n              placeholder=\"Enter reason for status change\"\r\n              value={reason}\r\n            />\r\n          </div>\r\n          {error && (\r\n            <p className=\"col-span-4 text-center text-sm text-red-500\">\r\n              {error}\r\n            </p>\r\n          )}\r\n        </div>\r\n        <DialogFooter>\r\n          <Button onClick={onClose} variant=\"outline\">\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleConfirm}>Confirm</Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default StatusUpdateModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA,yOAAsC,sCAAsC;AAC5E,yOAMsB,uCAAuC;AAC7D,uOAAoC,qCAAqC;AACzE,uOAAoC,qCAAqC;AACzE,yOAMsB,wBAAwB;;;;;;;;;;AAU9C,MAAM,oBAAsD,CAAC,EAC3D,aAAa,EACb,YAAY,EACZ,MAAM,EACN,OAAO,EACP,SAAS,EACV;;IACC,MAAM,CAAC,WAAW,aAAa,GAC7B,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,gBAAgB;QACpB,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,SAAS;YACT;QACF;QACA,IAAI,cAAc,eAAe;YAC/B,SAAS;YACT;QACF;QACA,SAAS;QACT,UAAU,WAAW;IACvB;IAEA,2BAA2B;IAC3B,MAAM,gBAA0C;QAC9C;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,cAAc;QAAS,MAAM;kBACnC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAa,SAAQ;8CAAiB;;;;;;8CAGvD,6LAAC,oIAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,IAAG;oCACH,QAAQ;oCACR,OAAO,CAAA,GAAA,yIAAA,CAAA,mCAAgC,AAAD,EAAE;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAa,SAAQ;8CAAa;;;;;;8CAGnD,6LAAC,qIAAA,CAAA,SAAM;oCACL,eAAe,CAAC,QACd,aAAa;oCAEf,OAAO;;sDAEP,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;sDACX,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC,qIAAA,CAAA,aAAU;oDACT,UAAU,WAAW;oDAErB,OAAO;8DAEN,CAAA,GAAA,yIAAA,CAAA,mCAAgC,AAAD,EAAE;mDAH7B;;;;;;;;;;;;;;;;;;;;;;sCAUf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAa,SAAQ;8CAAS;;;;;;8CAG/C,6LAAC,oIAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,IAAG;oCACH,UAAU,CAAA,IAAK,UAAU,EAAE,MAAM,CAAC,KAAK;oCACvC,aAAY;oCACZ,OAAO;;;;;;;;;;;;wBAGV,uBACC,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAIP,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAS,SAAQ;sCAAU;;;;;;sCAG5C,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAK1C;GA5GM;KAAA;uCA8GS", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst TooltipProvider = TooltipPrimitive.Provider;\r\n\r\nconst Tooltip = TooltipPrimitive.Root;\r\n\r\nconst TooltipTrigger = TooltipPrimitive.Trigger;\r\n\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <TooltipPrimitive.Content\r\n    className={cn(\r\n      'z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    sideOffset={sideOffset}\r\n    {...props}\r\n  />\r\n));\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName;\r\n\r\nexport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAEF,KAAK;QACL,YAAY;QACX,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/StatusHistoryCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { format, parseISO } from 'date-fns';\r\nimport {\r\n  AlertCircle,\r\n  Calendar,\r\n  CheckCircle2,\r\n  ClipboardList,\r\n  Clock,\r\n  History,\r\n  PlayCircle,\r\n  Plus,\r\n  RefreshCw,\r\n  TrendingUp,\r\n  Trophy,\r\n  User,\r\n  XCircle,\r\n} from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport type {\r\n  DelegationStatusEntry,\r\n  DelegationStatusPrisma,\r\n} from '@/lib/types/domain';\r\n\r\nimport StatusUpdateModal from '@/components/StatusUpdateModal';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip';\r\nimport { cn } from '@/lib/utils';\r\nimport { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';\r\n\r\ninterface StatusHistoryCardProps {\r\n  currentStatus: DelegationStatusPrisma;\r\n  delegationId: string;\r\n  isUpdating?: boolean;\r\n  onStatusUpdate: (\r\n    status: DelegationStatusPrisma,\r\n    reason: string\r\n  ) => Promise<void>;\r\n  statusHistory: DelegationStatusEntry[];\r\n}\r\n\r\n// Enhanced status color function with modern gradients\r\nconst getStatusColor = (status: DelegationStatusPrisma | undefined) => {\r\n  switch (status) {\r\n    case 'Cancelled': {\r\n      return 'bg-gradient-to-r from-red-500/20 to-rose-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:from-red-500/10 dark:to-rose-500/10 dark:border-red-500/20';\r\n    }\r\n    case 'Completed': {\r\n      return 'bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:from-purple-500/10 dark:to-violet-500/10 dark:border-purple-500/20';\r\n    }\r\n    case 'Confirmed': {\r\n      return 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:from-green-500/10 dark:to-emerald-500/10 dark:border-green-500/20';\r\n    }\r\n    case 'In_Progress': {\r\n      return 'bg-gradient-to-r from-yellow-500/20 to-amber-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:from-yellow-500/10 dark:to-amber-500/10 dark:border-yellow-500/20';\r\n    }\r\n    case 'Planned': {\r\n      return 'bg-gradient-to-r from-blue-500/20 to-blue-600/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:from-blue-500/10 dark:to-blue-600/10 dark:border-blue-500/20';\r\n    }\r\n    case 'No_details':\r\n    default: {\r\n      return 'bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:from-gray-500/10 dark:to-slate-500/10 dark:border-gray-500/20';\r\n    }\r\n  }\r\n};\r\n\r\n// Function to get the appropriate icon for each status\r\nconst getStatusIcon = (status: DelegationStatusPrisma | undefined) => {\r\n  switch (status) {\r\n    case 'Cancelled': {\r\n      return XCircle;\r\n    }\r\n    case 'Completed': {\r\n      return Trophy;\r\n    }\r\n    case 'Confirmed': {\r\n      return CheckCircle2;\r\n    }\r\n    case 'In_Progress': {\r\n      return PlayCircle;\r\n    }\r\n    case 'Planned': {\r\n      return ClipboardList;\r\n    }\r\n    case 'No_details':\r\n    default: {\r\n      return AlertCircle;\r\n    }\r\n  }\r\n};\r\n\r\n// Function to get the background color for status icons\r\nconst getStatusIconBg = (status: DelegationStatusPrisma | undefined) => {\r\n  switch (status) {\r\n    case 'Cancelled': {\r\n      return 'from-red-500/20 to-rose-500/20 border-red-500/30';\r\n    }\r\n    case 'Completed': {\r\n      return 'from-purple-500/20 to-violet-500/20 border-purple-500/30';\r\n    }\r\n    case 'Confirmed': {\r\n      return 'from-green-500/20 to-emerald-500/20 border-green-500/30';\r\n    }\r\n    case 'In_Progress': {\r\n      return 'from-yellow-500/20 to-amber-500/20 border-yellow-500/30';\r\n    }\r\n    case 'Planned': {\r\n      return 'from-blue-500/20 to-blue-600/20 border-blue-500/30';\r\n    }\r\n    case 'No_details':\r\n    default: {\r\n      return 'from-gray-500/20 to-slate-500/20 border-gray-500/30';\r\n    }\r\n  }\r\n};\r\n\r\nconst formatDate = (dateString: string | undefined, includeTime = false) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    return format(\r\n      parseISO(dateString),\r\n      includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'\r\n    );\r\n  } catch {\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n\r\nexport default function StatusHistoryCard({\r\n  currentStatus,\r\n  delegationId,\r\n  isUpdating = false,\r\n  onStatusUpdate,\r\n  statusHistory,\r\n}: StatusHistoryCardProps) {\r\n  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);\r\n\r\n  const handleStatusUpdateClick = () => {\r\n    setIsStatusModalOpen(true);\r\n  };\r\n\r\n  const handleStatusUpdateConfirm = async (\r\n    status: DelegationStatusPrisma,\r\n    reason: string\r\n  ) => {\r\n    try {\r\n      await onStatusUpdate(status, reason);\r\n      setIsStatusModalOpen(false);\r\n    } catch (error) {\r\n      // Error handling is done in the parent component\r\n      console.error('Status update failed:', error);\r\n    }\r\n  };\r\n\r\n  const sortedHistory =\r\n    statusHistory\r\n      ?.slice()\r\n      .sort(\r\n        (a, b) =>\r\n          new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()\r\n      ) || [];\r\n\r\n  return (\r\n    <>\r\n      <Card className=\"border-border/60 bg-gradient-to-br from-card to-card/95 shadow-lg backdrop-blur-sm\">\r\n        <CardHeader className=\"pb-4\">\r\n          <div className=\"flex items-center justify-between gap-4\">\r\n            <div className=\"flex min-w-0 flex-1 items-center gap-3\">\r\n              <div className=\"shrink-0 rounded-lg bg-primary/10 p-2 text-primary\">\r\n                <History className=\"size-5\" />\r\n              </div>\r\n              <div className=\"flex min-w-0 items-center gap-2\">\r\n                <CardTitle className=\"whitespace-nowrap text-xl font-semibold text-primary\">\r\n                  Status History\r\n                </CardTitle>\r\n                <Badge className=\"shrink-0 text-xs\" variant=\"secondary\">\r\n                  {sortedHistory.length}{' '}\r\n                  {sortedHistory.length === 1 ? 'entry' : 'entries'}\r\n                </Badge>\r\n              </div>\r\n            </div>\r\n\r\n            <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <Button\r\n                    className=\"shrink-0 gap-2 whitespace-nowrap bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-md transition-all duration-200 hover:from-primary/90 hover:to-primary hover:shadow-lg\"\r\n                    disabled={isUpdating}\r\n                    onClick={handleStatusUpdateClick}\r\n                    size=\"sm\"\r\n                  >\r\n                    {isUpdating ? (\r\n                      <RefreshCw className=\"size-4 animate-spin\" />\r\n                    ) : (\r\n                      <Plus className=\"size-4\" />\r\n                    )}\r\n                  </Button>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Change delegation status</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        <CardContent className=\"pt-0\">\r\n          {sortedHistory.length > 0 ? (\r\n            <div className=\"scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent max-h-96 space-y-4 overflow-y-auto pr-2\">\r\n              {sortedHistory.map((entry, index) => (\r\n                <div key={entry.id || index}>\r\n                  <div className=\"group relative rounded-xl border border-border/50 bg-gradient-to-r from-background/50 to-background/30 p-4 transition-all duration-200 hover:border-border\">\r\n                    {/* Timeline connector */}\r\n                    {index < sortedHistory.length - 1 && (\r\n                      <div className=\"absolute left-7 top-16 h-8 w-0.5 bg-gradient-to-b from-border via-border/50 to-transparent\" />\r\n                    )}\r\n\r\n                    <div className=\"flex items-start gap-4\">\r\n                      {/* Status indicator with specific icon */}\r\n                      <div className=\"relative\">\r\n                        {(() => {\r\n                          const StatusIcon = getStatusIcon(\r\n                            entry.status as DelegationStatusPrisma\r\n                          );\r\n                          const iconBgClasses = getStatusIconBg(\r\n                            entry.status as DelegationStatusPrisma\r\n                          );\r\n                          return (\r\n                            <div\r\n                              className={cn(\r\n                                'p-2.5 rounded-full bg-gradient-to-br border-2 transition-all duration-200 group-hover:scale-105',\r\n                                iconBgClasses\r\n                              )}\r\n                            >\r\n                              <StatusIcon className=\"size-4\" />\r\n                            </div>\r\n                          );\r\n                        })()}\r\n                        {index === 0 && (\r\n                          <div className=\"absolute -right-1 -top-1 size-3 animate-pulse rounded-full border-2 border-background bg-green-500 shadow-lg\" />\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"min-w-0 flex-1\">\r\n                        <div className=\"mb-2 flex items-center justify-between gap-3\">\r\n                          <Badge\r\n                            className={cn(\r\n                              'text-sm py-1.5 px-3 font-medium border shadow-sm flex items-center gap-1.5 flex-shrink-0 whitespace-nowrap',\r\n                              getStatusColor(entry.status)\r\n                            )}\r\n                          >\r\n                            {(() => {\r\n                              const StatusIcon = getStatusIcon(\r\n                                entry.status as DelegationStatusPrisma\r\n                              );\r\n                              return (\r\n                                <StatusIcon className=\"size-3.5 shrink-0\" />\r\n                              );\r\n                            })()}\r\n                            <span className=\"whitespace-nowrap\">\r\n                              {formatDelegationStatusForDisplay(\r\n                                entry.status as DelegationStatusPrisma\r\n                              )}\r\n                            </span>\r\n                          </Badge>\r\n\r\n                          <div className=\"flex shrink-0 items-center gap-2 text-xs text-muted-foreground\">\r\n                            <Calendar className=\"size-3\" />\r\n                            <span className=\"whitespace-nowrap\">\r\n                              {formatDate(entry.changedAt, true)}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {entry.reason && (\r\n                          <div className=\"mt-2 rounded-lg border border-border/30 bg-muted/30 p-3\">\r\n                            <p className=\"flex items-start gap-2 text-sm italic text-muted-foreground\">\r\n                              <User className=\"mt-0.5 size-3 shrink-0\" />\r\n                              <span className=\"shrink-0 font-medium\">\r\n                                Reason:\r\n                              </span>\r\n                              <span className=\"break-words\">\r\n                                {entry.reason}\r\n                              </span>\r\n                            </p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {index < sortedHistory.length - 1 && (\r\n                    <Separator className=\"my-2 bg-gradient-to-r from-transparent via-border to-transparent\" />\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"py-12 text-center\">\r\n              <div className=\"mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-muted/30 p-4\">\r\n                <Clock className=\"size-8 text-muted-foreground\" />\r\n              </div>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                No status history available.\r\n              </p>\r\n              <p className=\"mt-1 text-xs text-muted-foreground\">\r\n                Status changes will appear here.\r\n              </p>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Status Update Modal */}\r\n      <StatusUpdateModal\r\n        currentStatus={currentStatus}\r\n        delegationId={delegationId}\r\n        isOpen={isStatusModalOpen}\r\n        onClose={() => setIsStatusModalOpen(false)}\r\n        onConfirm={handleStatusUpdateConfirm}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAOA;AACA;AACA;AACA;AACA;AACA;AAMA;AAAA;AACA;;;AArCA;;;;;;;;;;;;AAkDA,uDAAuD;AACvD,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAe;gBAClB,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;QACL;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAEA,uDAAuD;AACvD,MAAM,gBAAgB,CAAC;IACrB,OAAQ;QACN,KAAK;YAAa;gBAChB,OAAO,+MAAA,CAAA,UAAO;YAChB;QACA,KAAK;YAAa;gBAChB,OAAO,yMAAA,CAAA,SAAM;YACf;QACA,KAAK;YAAa;gBAChB,OAAO,wNAAA,CAAA,eAAY;YACrB;QACA,KAAK;YAAe;gBAClB,OAAO,qNAAA,CAAA,aAAU;YACnB;QACA,KAAK;YAAW;gBACd,OAAO,2NAAA,CAAA,gBAAa;YACtB;QACA,KAAK;QACL;YAAS;gBACP,OAAO,uNAAA,CAAA,cAAW;YACpB;IACF;AACF;AAEA,wDAAwD;AACxD,MAAM,kBAAkB,CAAC;IACvB,OAAQ;QACN,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAe;gBAClB,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;QACL;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAEA,MAAM,aAAa,CAAC,YAAgC,cAAc,KAAK;IACrE,IAAI,CAAC,YAAY,OAAO;IACxB,IAAI;QACF,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EACV,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,aACT,cAAc,uBAAuB;IAEzC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEe,SAAS,kBAAkB,EACxC,aAAa,EACb,YAAY,EACZ,aAAa,KAAK,EAClB,cAAc,EACd,aAAa,EACU;;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,0BAA0B;QAC9B,qBAAqB;IACvB;IAEA,MAAM,4BAA4B,OAChC,QACA;QAEA,IAAI;YACF,MAAM,eAAe,QAAQ;YAC7B,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,iDAAiD;YACjD,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,gBACJ,eACI,QACD,KACC,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,OAC9D,EAAE;IAEX,qBACE;;0BACE,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAuD;;;;;;8DAG5E,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;oDAAmB,SAAQ;;wDACzC,cAAc,MAAM;wDAAE;wDACtB,cAAc,MAAM,KAAK,IAAI,UAAU;;;;;;;;;;;;;;;;;;;8CAK9C,6LAAC,sIAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;0DACN,6LAAC,sIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,UAAU;oDACV,SAAS;oDACT,MAAK;8DAEJ,2BACC,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;6EAErB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAItB,6LAAC,sIAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOb,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,cAAc,MAAM,GAAG,kBACtB,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;gDAEZ,QAAQ,cAAc,MAAM,GAAG,mBAC9B,6LAAC;oDAAI,WAAU;;;;;;8DAGjB,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;gEACZ,CAAC;oEACA,MAAM,aAAa,cACjB,MAAM,MAAM;oEAEd,MAAM,gBAAgB,gBACpB,MAAM,MAAM;oEAEd,qBACE,6LAAC;wEACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mGACA;kFAGF,cAAA,6LAAC;4EAAW,WAAU;;;;;;;;;;;gEAG5B,CAAC;gEACA,UAAU,mBACT,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAInB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EACJ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8GACA,eAAe,MAAM,MAAM;;gFAG5B,CAAC;oFACA,MAAM,aAAa,cACjB,MAAM,MAAM;oFAEd,qBACE,6LAAC;wFAAW,WAAU;;;;;;gFAE1B,CAAC;8FACD,6LAAC;oFAAK,WAAU;8FACb,CAAA,GAAA,yIAAA,CAAA,mCAAgC,AAAD,EAC9B,MAAM,MAAM;;;;;;;;;;;;sFAKlB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;8FACpB,6LAAC;oFAAK,WAAU;8FACb,WAAW,MAAM,SAAS,EAAE;;;;;;;;;;;;;;;;;;gEAKlC,MAAM,MAAM,kBACX,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;0FACX,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAuB;;;;;;0FAGvC,6LAAC;gFAAK,WAAU;0FACb,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAS1B,QAAQ,cAAc,MAAM,GAAG,mBAC9B,6LAAC,wIAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;mCAlFf,MAAM,EAAE,IAAI;;;;;;;;;iDAwF1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAG7C,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;0BAS1D,6LAAC,0IAAA,CAAA,UAAiB;gBAChB,eAAe;gBACf,cAAc;gBACd,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,WAAW;;;;;;;;AAInB;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/aspect-ratio.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\"\r\n\r\nconst AspectRatio = AspectRatioPrimitive.Root\r\n\r\nexport { AspectRatio }\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,cAAc,8KAAA,CAAA,OAAyB", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/common/DetailItem.tsx"], "sourcesContent": ["/**\r\n * @file DetailItem component for displaying labeled information with icons\r\n * @module components/delegations/common/DetailItem\r\n */\r\n\r\nimport React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface DetailItemProps {\r\n  children?: React.ReactNode;\r\n  icon: React.ElementType;\r\n  label: string;\r\n  value?: null | number | string;\r\n  valueClassName?: string;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * DetailItem component for displaying labeled information with icons\r\n * Used throughout delegation detail views for consistent information display\r\n */\r\nexport function DetailItem({\r\n  children,\r\n  icon: Icon,\r\n  label,\r\n  value,\r\n  valueClassName,\r\n  className,\r\n}: DetailItemProps) {\r\n  return (\r\n    <div className={cn('flex items-start space-x-3', className)}>\r\n      <div className=\"mt-0.5 rounded-full bg-blue-50 dark:bg-blue-900/30 p-2\">\r\n        <Icon className=\"size-4 text-blue-600 dark:text-blue-400\" />\r\n      </div>\r\n      <div className=\"flex-1 min-w-0\">\r\n        <div className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1\">\r\n          {label}\r\n        </div>\r\n        <div className={cn('font-medium text-gray-900 dark:text-white', valueClassName)}>\r\n          {children || value || 'N/A'}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default DetailItem;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AAAA;;;AAeO,SAAS,WAAW,EACzB,QAAQ,EACR,MAAM,IAAI,EACV,KAAK,EACL,KAAK,EACL,cAAc,EACd,SAAS,EACO;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;;0BAC/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,6LAAC;wBAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;kCAC7D,YAAY,SAAS;;;;;;;;;;;;;;;;;;AAKhC;KAvBgB;uCAyBD", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/DelegationOverviewCard.tsx"], "sourcesContent": ["/**\r\n * @file DelegationOverviewCard component for displaying delegation overview\r\n * @module components/delegations/detail/DelegationOverviewCard\r\n */\r\n\r\nimport React from 'react';\r\nimport Image from 'next/image';\r\nimport { MapPin, CalendarDays, Users, Info } from 'lucide-react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { AspectRatio } from '@/components/ui/aspect-ratio';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { DetailItem } from '../common/DetailItem';\r\nimport { getSafeDelegationImageUrl } from '@/lib/utils/imageUtils';\r\nimport type { Delegation } from '@/lib/types/domain';\r\nimport { format, parseISO } from 'date-fns';\r\n\r\ninterface DelegationOverviewCardProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\nconst formatDate = (dateString: string | undefined) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    return format(parseISO(dateString), 'MMM d, yyyy');\r\n  } catch {\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n\r\n/**\r\n * DelegationOverviewCard component for displaying delegation overview\r\n * Shows main delegation information with image and key details\r\n */\r\nexport function DelegationOverviewCard({ delegation, className }: DelegationOverviewCardProps) {\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center space-x-2\">\r\n          <MapPin className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n          <span>Event Overview</span>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Image Section */}\r\n        <AspectRatio ratio={16 / 9} className=\"overflow-hidden rounded-lg\">\r\n          <Image\r\n            src={getSafeDelegationImageUrl(\r\n              delegation.imageUrl,\r\n              delegation.id,\r\n              'detail'\r\n            )}\r\n            alt={delegation.eventName}\r\n            className=\"object-cover transition-transform hover:scale-105\"\r\n            fill\r\n            priority\r\n          />\r\n        </AspectRatio>\r\n        \r\n        <Separator />\r\n        \r\n        {/* Details Grid */}\r\n        <div className=\"grid gap-4 md:grid-cols-2\">\r\n          <DetailItem\r\n            icon={CalendarDays}\r\n            label=\"Duration\"\r\n            value={`${formatDate(delegation.durationFrom)} - ${formatDate(delegation.durationTo)}`}\r\n          />\r\n          <DetailItem\r\n            icon={MapPin}\r\n            label=\"Location\"\r\n            value={delegation.location}\r\n          />\r\n          {delegation.invitationFrom && (\r\n            <DetailItem\r\n              icon={Users}\r\n              label=\"Invitation From\"\r\n              value={delegation.invitationFrom}\r\n            />\r\n          )}\r\n          {delegation.invitationTo && (\r\n            <DetailItem\r\n              icon={Users}\r\n              label=\"Invitation To\"\r\n              value={delegation.invitationTo}\r\n            />\r\n          )}\r\n        </div>\r\n        \r\n        {delegation.notes && (\r\n          <>\r\n            <Separator />\r\n            <div className=\"space-y-2\">\r\n              <DetailItem\r\n                icon={Info}\r\n                label=\"General Notes\"\r\n                valueClassName=\"whitespace-pre-wrap\"\r\n                value={delegation.notes}\r\n              />\r\n            </div>\r\n          </>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DelegationOverviewCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;;;;;;;;AAOA,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,YAAY,OAAO;IACxB,IAAI;QACF,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IACtC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,uBAAuB,EAAE,UAAU,EAAE,SAAS,EAA+B;IAC3F,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC,8IAAA,CAAA,cAAW;wBAAC,OAAO,KAAK;wBAAG,WAAU;kCACpC,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD,EAC3B,WAAW,QAAQ,EACnB,WAAW,EAAE,EACb;4BAEF,KAAK,WAAW,SAAS;4BACzB,WAAU;4BACV,IAAI;4BACJ,QAAQ;;;;;;;;;;;kCAIZ,6LAAC,wIAAA,CAAA,YAAS;;;;;kCAGV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wKAAA,CAAA,aAAU;gCACT,MAAM,yNAAA,CAAA,eAAY;gCAClB,OAAM;gCACN,OAAO,GAAG,WAAW,WAAW,YAAY,EAAE,GAAG,EAAE,WAAW,WAAW,UAAU,GAAG;;;;;;0CAExF,6LAAC,wKAAA,CAAA,aAAU;gCACT,MAAM,6MAAA,CAAA,SAAM;gCACZ,OAAM;gCACN,OAAO,WAAW,QAAQ;;;;;;4BAE3B,WAAW,cAAc,kBACxB,6LAAC,wKAAA,CAAA,aAAU;gCACT,MAAM,uMAAA,CAAA,QAAK;gCACX,OAAM;gCACN,OAAO,WAAW,cAAc;;;;;;4BAGnC,WAAW,YAAY,kBACtB,6LAAC,wKAAA,CAAA,aAAU;gCACT,MAAM,uMAAA,CAAA,QAAK;gCACX,OAAM;gCACN,OAAO,WAAW,YAAY;;;;;;;;;;;;oBAKnC,WAAW,KAAK,kBACf;;0CACE,6LAAC,wIAAA,CAAA,YAAS;;;;;0CACV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wKAAA,CAAA,aAAU;oCACT,MAAM,qMAAA,CAAA,OAAI;oCACV,OAAM;oCACN,gBAAe;oCACf,OAAO,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;KAvEgB;uCAyED", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/FlightDetailsCard.tsx"], "sourcesContent": ["/**\r\n * @file FlightDetailsCard component for displaying flight information\r\n * @module components/delegations/detail/FlightDetailsCard\r\n */\r\n\r\nimport React from 'react';\r\nimport {\r\n  Plane,\r\n  PlaneLanding,\r\n  PlaneTakeoff,\r\n  Clock,\r\n  MapPin,\r\n  Info,\r\n} from 'lucide-react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { DetailItem } from '../common/DetailItem';\r\nimport type { Delegation } from '@/lib/types/domain';\r\nimport { format, parseISO } from 'date-fns';\r\n\r\ninterface FlightDetailsCardProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\nconst formatFlightDate = (dateString: string | undefined) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    return format(parseISO(dateString), 'MMM d, yyyy, HH:mm');\r\n  } catch {\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n\r\n/**\r\n * FlightDetailsCard component for displaying flight information\r\n * Shows arrival and departure flight details in a professional layout\r\n */\r\nexport function FlightDetailsCard({\r\n  delegation,\r\n  className,\r\n}: FlightDetailsCardProps) {\r\n  // Flight data is now correctly loaded via fixed transformer\r\n\r\n  const hasFlights = delegation.arrivalFlight || delegation.departureFlight;\r\n\r\n  if (!hasFlights) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center space-x-2\">\r\n            <Plane className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n            <span>Flight Information</span>\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n            <div className=\"mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\">\r\n              <Plane className=\"h-6 w-6 text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\r\n              No Flight Details\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">\r\n              No flight information has been provided for this delegation.\r\n            </p>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Plane className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n            <span>Flight Information</span>\r\n          </div>\r\n          <Badge\r\n            variant=\"secondary\"\r\n            className=\"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800\"\r\n          >\r\n            {\r\n              [delegation.arrivalFlight, delegation.departureFlight].filter(\r\n                Boolean\r\n              ).length\r\n            }{' '}\r\n            flight\r\n            {[delegation.arrivalFlight, delegation.departureFlight].filter(\r\n              Boolean\r\n            ).length !== 1\r\n              ? 's'\r\n              : ''}\r\n          </Badge>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {delegation.arrivalFlight && (\r\n          <div className=\"rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20\">\r\n            <div className=\"flex items-center space-x-2 mb-4\">\r\n              <PlaneLanding className=\"h-5 w-5 text-green-600 dark:text-green-400\" />\r\n              <h3 className=\"text-lg font-semibold text-green-900 dark:text-green-100\">\r\n                Arrival Flight\r\n              </h3>\r\n            </div>\r\n            <div className=\"grid gap-3 md:grid-cols-2\">\r\n              <DetailItem\r\n                icon={Plane}\r\n                label=\"Flight Number\"\r\n                value={delegation.arrivalFlight.flightNumber}\r\n              />\r\n              <DetailItem\r\n                icon={Clock}\r\n                label=\"Date & Time\"\r\n                value={formatFlightDate(delegation.arrivalFlight.dateTime)}\r\n              />\r\n              <DetailItem\r\n                icon={MapPin}\r\n                label=\"Airport\"\r\n                value={delegation.arrivalFlight.airport}\r\n              />\r\n              {delegation.arrivalFlight.terminal && (\r\n                <DetailItem\r\n                  icon={MapPin}\r\n                  label=\"Terminal\"\r\n                  value={delegation.arrivalFlight.terminal}\r\n                />\r\n              )}\r\n            </div>\r\n            {delegation.arrivalFlight.notes && (\r\n              <>\r\n                <Separator className=\"my-3\" />\r\n                <DetailItem\r\n                  icon={Info}\r\n                  label=\"Notes\"\r\n                  value={delegation.arrivalFlight.notes}\r\n                  valueClassName=\"whitespace-pre-wrap\"\r\n                />\r\n              </>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {delegation.departureFlight && (\r\n          <div className=\"rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-900/20\">\r\n            <div className=\"flex items-center space-x-2 mb-4\">\r\n              <PlaneTakeoff className=\"h-5 w-5 text-orange-600 dark:text-orange-400\" />\r\n              <h3 className=\"text-lg font-semibold text-orange-900 dark:text-orange-100\">\r\n                Departure Flight\r\n              </h3>\r\n            </div>\r\n            <div className=\"grid gap-3 md:grid-cols-2\">\r\n              <DetailItem\r\n                icon={Plane}\r\n                label=\"Flight Number\"\r\n                value={delegation.departureFlight.flightNumber}\r\n              />\r\n              <DetailItem\r\n                icon={Clock}\r\n                label=\"Date & Time\"\r\n                value={formatFlightDate(delegation.departureFlight.dateTime)}\r\n              />\r\n              <DetailItem\r\n                icon={MapPin}\r\n                label=\"Airport\"\r\n                value={delegation.departureFlight.airport}\r\n              />\r\n              {delegation.departureFlight.terminal && (\r\n                <DetailItem\r\n                  icon={MapPin}\r\n                  label=\"Terminal\"\r\n                  value={delegation.departureFlight.terminal}\r\n                />\r\n              )}\r\n            </div>\r\n            {delegation.departureFlight.notes && (\r\n              <>\r\n                <Separator className=\"my-3\" />\r\n                <DetailItem\r\n                  icon={Info}\r\n                  label=\"Notes\"\r\n                  value={delegation.departureFlight.notes}\r\n                  valueClassName=\"whitespace-pre-wrap\"\r\n                />\r\n              </>\r\n            )}\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default FlightDetailsCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AAEA;AAAA;;;;;;;;AAOA,MAAM,mBAAmB,CAAC;IACxB,IAAI,CAAC,YAAY,OAAO;IACxB,IAAI;QACF,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IACtC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,kBAAkB,EAChC,UAAU,EACV,SAAS,EACc;IACvB,4DAA4D;IAE5D,MAAM,aAAa,WAAW,aAAa,IAAI,WAAW,eAAe;IAEzE,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;IAO1D;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;;gCAGR;oCAAC,WAAW,aAAa;oCAAE,WAAW,eAAe;iCAAC,CAAC,MAAM,CAC3D,SACA,MAAM;gCACR;gCAAI;gCAEL;oCAAC,WAAW,aAAa;oCAAE,WAAW,eAAe;iCAAC,CAAC,MAAM,CAC5D,SACA,MAAM,KAAK,IACT,MACA;;;;;;;;;;;;;;;;;;0BAIV,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,WAAW,aAAa,kBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;;;;;;;0CAI3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,uMAAA,CAAA,QAAK;wCACX,OAAM;wCACN,OAAO,WAAW,aAAa,CAAC,YAAY;;;;;;kDAE9C,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,uMAAA,CAAA,QAAK;wCACX,OAAM;wCACN,OAAO,iBAAiB,WAAW,aAAa,CAAC,QAAQ;;;;;;kDAE3D,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,6MAAA,CAAA,SAAM;wCACZ,OAAM;wCACN,OAAO,WAAW,aAAa,CAAC,OAAO;;;;;;oCAExC,WAAW,aAAa,CAAC,QAAQ,kBAChC,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,6MAAA,CAAA,SAAM;wCACZ,OAAM;wCACN,OAAO,WAAW,aAAa,CAAC,QAAQ;;;;;;;;;;;;4BAI7C,WAAW,aAAa,CAAC,KAAK,kBAC7B;;kDACE,6LAAC,wIAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,qMAAA,CAAA,OAAI;wCACV,OAAM;wCACN,OAAO,WAAW,aAAa,CAAC,KAAK;wCACrC,gBAAe;;;;;;;;;;;;;;oBAOxB,WAAW,eAAe,kBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAG,WAAU;kDAA6D;;;;;;;;;;;;0CAI7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,uMAAA,CAAA,QAAK;wCACX,OAAM;wCACN,OAAO,WAAW,eAAe,CAAC,YAAY;;;;;;kDAEhD,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,uMAAA,CAAA,QAAK;wCACX,OAAM;wCACN,OAAO,iBAAiB,WAAW,eAAe,CAAC,QAAQ;;;;;;kDAE7D,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,6MAAA,CAAA,SAAM;wCACZ,OAAM;wCACN,OAAO,WAAW,eAAe,CAAC,OAAO;;;;;;oCAE1C,WAAW,eAAe,CAAC,QAAQ,kBAClC,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,6MAAA,CAAA,SAAM;wCACZ,OAAM;wCACN,OAAO,WAAW,eAAe,CAAC,QAAQ;;;;;;;;;;;;4BAI/C,WAAW,eAAe,CAAC,KAAK,kBAC/B;;kDACE,6LAAC,wIAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC,wKAAA,CAAA,aAAU;wCACT,MAAM,qMAAA,CAAA,OAAI;wCACV,OAAM;wCACN,OAAO,WAAW,eAAe,CAAC,KAAK;wCACvC,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;AASjC;KA3JgB;uCA6JD", "debugId": null}}, {"offset": {"line": 1781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/AssignmentSection.tsx"], "sourcesContent": ["/**\r\n * @file Generic AssignmentSection component for displaying assignment lists\r\n * @module components/delegations/detail/assignments/AssignmentSection\r\n */\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface AssignmentSectionProps<T> {\r\n  title: string;\r\n  icon: React.ElementType;\r\n  items: T[];\r\n  renderItem: (item: T, index: number) => React.ReactNode;\r\n  emptyMessage: string;\r\n  className?: string | undefined;\r\n}\r\n\r\n/**\r\n * Generic AssignmentSection component for displaying assignment lists\r\n * Provides consistent styling and structure for delegates, escorts, drivers, vehicles\r\n */\r\nexport function AssignmentSection<T>({\r\n  title,\r\n  icon: Icon,\r\n  items,\r\n  renderItem,\r\n  emptyMessage,\r\n  className,\r\n}: AssignmentSectionProps<T>) {\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Icon className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n            <span>{title}</span>\r\n          </div>\r\n          <Badge\r\n            variant=\"secondary\"\r\n            className=\"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800\"\r\n          >\r\n            {items.length}\r\n          </Badge>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        {items.length > 0 ? (\r\n          <div className=\"space-y-3\">{items.map(renderItem)}</div>\r\n        ) : (\r\n          <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n            <div className=\"rounded-full bg-gray-100 dark:bg-gray-800 p-3 mb-3\">\r\n              <Icon className=\"h-6 w-6 text-gray-400\" />\r\n            </div>\r\n            <p className=\"text-gray-600 dark:text-gray-400 font-medium\">\r\n              {emptyMessage}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default AssignmentSection;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;;;;AAeO,SAAS,kBAAqB,EACnC,KAAK,EACL,MAAM,IAAI,EACV,KAAK,EACL,UAAU,EACV,YAAY,EACZ,SAAS,EACiB;IAC1B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM;;;;;;;;;;;;sCAET,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,MAAM,MAAM;;;;;;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACT,MAAM,MAAM,GAAG,kBACd,6LAAC;oBAAI,WAAU;8BAAa,MAAM,GAAG,CAAC;;;;;yCAEtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOf;KAxCgB;uCA0CD", "debugId": null}}, {"offset": {"line": 1909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/delegatesCard.tsx"], "sourcesContent": ["/**\r\n * @file DelegatesCard component for displaying delegation delegates\r\n * @module components/delegations/detail/assignments/DelegatesCard\r\n */\r\n\r\nimport { Users } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\nimport { AssignmentSection } from './AssignmentSection';\r\n\r\ninterface DelegatesCardProps {\r\n  className?: string;\r\n  delegation: Delegation;\r\n}\r\n\r\n/**\r\n * DelegatesCard component for displaying delegation delegates\r\n * Uses the generic AssignmentSection for consistent styling\r\n */\r\nexport function DelegatesCard({ className, delegation }: DelegatesCardProps) {\r\n  return (\r\n    <AssignmentSection\r\n      className={className ?? ''}\r\n      emptyMessage=\"No delegates assigned to this delegation.\"\r\n      icon={Users}\r\n      items={delegation.delegates ?? []}\r\n      renderItem={(delegate, index) => (\r\n        <div\r\n          className=\"space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800\"\r\n          key={delegate.id ?? index}\r\n        >\r\n          <h4 className=\"font-semibold text-gray-900 dark:text-white\">\r\n            {delegate.name}\r\n          </h4>\r\n          {delegate.title && (\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              {delegate.title}\r\n            </p>\r\n          )}\r\n          {delegate.notes && (\r\n            <p className=\"rounded bg-gray-50 p-2 text-xs italic text-gray-500 dark:bg-gray-700 dark:text-gray-500\">\r\n              {delegate.notes}\r\n            </p>\r\n          )}\r\n        </div>\r\n      )}\r\n      title=\"Delegates\"\r\n    />\r\n  );\r\n}\r\n\r\nexport default DelegatesCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAKA;;;;AAWO,SAAS,cAAc,EAAE,SAAS,EAAE,UAAU,EAAsB;IACzE,qBACE,6LAAC,8LAAA,CAAA,oBAAiB;QAChB,WAAW,aAAa;QACxB,cAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO,WAAW,SAAS,IAAI,EAAE;QACjC,YAAY,CAAC,UAAU,sBACrB,6LAAC;gBACC,WAAU;;kCAGV,6LAAC;wBAAG,WAAU;kCACX,SAAS,IAAI;;;;;;oBAEf,SAAS,KAAK,kBACb,6LAAC;wBAAE,WAAU;kCACV,SAAS,KAAK;;;;;;oBAGlB,SAAS,KAAK,kBACb,6LAAC;wBAAE,WAAU;kCACV,SAAS,KAAK;;;;;;;eAZd,SAAS,EAAE,IAAI;;;;;QAiBxB,OAAM;;;;;;AAGZ;KA9BgB;uCAgCD", "debugId": null}}, {"offset": {"line": 1981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/driversCard.tsx"], "sourcesContent": ["/**\r\n * @file DriversCard component for displaying delegation drivers\r\n * @module components/delegations/detail/assignments/DriversCard\r\n */\r\n\r\nimport { User } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\nimport {\r\n  formatEmployeeName,\r\n  formatEmployeeRole,\r\n} from '@/lib/utils/formattingUtils';\r\n\r\nimport { AssignmentSection } from './AssignmentSection';\r\n\r\ninterface DriversCardProps {\r\n  className?: string;\r\n  delegation: Delegation;\r\n}\r\n\r\n/**\r\n * DriversCard component for displaying delegation drivers\r\n * Uses the generic AssignmentSection for consistent styling\r\n */\r\nexport function DriversCard({ className, delegation }: DriversCardProps) {\r\n  return (\r\n    <AssignmentSection\r\n      className={className ?? ''}\r\n      emptyMessage=\"No drivers assigned to this delegation.\"\r\n      icon={User}\r\n      items={delegation.drivers ?? []}\r\n      renderItem={(driver, index) => (\r\n        <div\r\n          className=\"space-y-2 rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-sm dark:border-gray-700 dark:bg-gray-800\"\r\n          key={driver.employeeId || index}\r\n        >\r\n          <h4 className=\"font-semibold text-gray-900 dark:text-white\">\r\n            {driver.employee\r\n              ? formatEmployeeName(driver.employee)\r\n              : `Employee ID: ${driver.employeeId}`}\r\n          </h4>\r\n          {driver.employee?.role && (\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              {formatEmployeeRole(driver.employee.role)}\r\n            </p>\r\n          )}\r\n          {driver.employee?.contactEmail && (\r\n            <p className=\"rounded bg-gray-50 p-2 text-xs text-gray-500 dark:bg-gray-700 dark:text-gray-500\">\r\n              📧 {driver.employee.contactEmail}\r\n            </p>\r\n          )}\r\n        </div>\r\n      )}\r\n      title=\"Drivers\"\r\n    />\r\n  );\r\n}\r\n\r\nexport default DriversCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAKA;AAKA;;;;;AAWO,SAAS,YAAY,EAAE,SAAS,EAAE,UAAU,EAAoB;IACrE,qBACE,6LAAC,8LAAA,CAAA,oBAAiB;QAChB,WAAW,aAAa;QACxB,cAAa;QACb,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO,WAAW,OAAO,IAAI,EAAE;QAC/B,YAAY,CAAC,QAAQ,sBACnB,6LAAC;gBACC,WAAU;;kCAGV,6LAAC;wBAAG,WAAU;kCACX,OAAO,QAAQ,GACZ,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,QAAQ,IAClC,CAAC,aAAa,EAAE,OAAO,UAAU,EAAE;;;;;;oBAExC,OAAO,QAAQ,EAAE,sBAChB,6LAAC;wBAAE,WAAU;kCACV,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,QAAQ,CAAC,IAAI;;;;;;oBAG3C,OAAO,QAAQ,EAAE,8BAChB,6LAAC;wBAAE,WAAU;;4BAAmF;4BAC1F,OAAO,QAAQ,CAAC,YAAY;;;;;;;;eAd/B,OAAO,UAAU,IAAI;;;;;QAmB9B,OAAM;;;;;;AAGZ;KAhCgB;uCAkCD", "debugId": null}}, {"offset": {"line": 2058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/EscortsCard.tsx"], "sourcesContent": ["/**\r\n * @file EscortsCard component for displaying delegation escorts\r\n * @module components/delegations/detail/assignments/EscortsCard\r\n */\r\n\r\nimport React from 'react';\r\nimport { Shield } from 'lucide-react';\r\nimport { AssignmentSection } from './AssignmentSection';\r\nimport {\r\n  formatEmployeeName,\r\n  formatEmployeeRole,\r\n} from '@/lib/utils/formattingUtils';\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\ninterface EscortsCardProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * EscortsCard component for displaying delegation escorts\r\n * Uses the generic AssignmentSection for consistent styling\r\n */\r\nexport function EscortsCard({ delegation, className }: EscortsCardProps) {\r\n  return (\r\n    <AssignmentSection\r\n      title=\"Escorts\"\r\n      icon={Shield}\r\n      items={delegation.escorts ?? []}\r\n      renderItem={(escort, index) => (\r\n        <div\r\n          key={escort.employeeId || index}\r\n          className=\"rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800\"\r\n        >\r\n          <h4 className=\"font-semibold text-gray-900 dark:text-white\">\r\n            {escort.employee\r\n              ? formatEmployeeName(escort.employee)\r\n              : `Employee ID: ${escort.employeeId}`}\r\n          </h4>\r\n          {escort.employee?.role && (\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              {formatEmployeeRole(escort.employee.role)}\r\n            </p>\r\n          )}\r\n          {escort.employee?.contactEmail && (\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded\">\r\n              📧 {escort.employee.contactEmail}\r\n            </p>\r\n          )}\r\n        </div>\r\n      )}\r\n      emptyMessage=\"No escorts assigned to this delegation.\"\r\n      className={className || undefined}\r\n    />\r\n  );\r\n}\r\n\r\nexport default EscortsCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;;;;;AAeO,SAAS,YAAY,EAAE,UAAU,EAAE,SAAS,EAAoB;IACrE,qBACE,6LAAC,8LAAA,CAAA,oBAAiB;QAChB,OAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO,WAAW,OAAO,IAAI,EAAE;QAC/B,YAAY,CAAC,QAAQ,sBACnB,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCACX,OAAO,QAAQ,GACZ,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,QAAQ,IAClC,CAAC,aAAa,EAAE,OAAO,UAAU,EAAE;;;;;;oBAExC,OAAO,QAAQ,EAAE,sBAChB,6LAAC;wBAAE,WAAU;kCACV,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,QAAQ,CAAC,IAAI;;;;;;oBAG3C,OAAO,QAAQ,EAAE,8BAChB,6LAAC;wBAAE,WAAU;;4BAAmF;4BAC1F,OAAO,QAAQ,CAAC,YAAY;;;;;;;;eAf/B,OAAO,UAAU,IAAI;;;;;QAoB9B,cAAa;QACb,WAAW,aAAa;;;;;;AAG9B;KAhCgB;uCAkCD", "debugId": null}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/SearchableAssignmentSection.tsx"], "sourcesContent": ["/**\r\n * @file SearchableAssignmentSection component with search and filter capabilities\r\n * @module components/delegations/detail/assignments/SearchableAssignmentSection\r\n */\r\n\r\nimport React, { useState, useMemo } from 'react';\r\nimport { Search, Filter, X } from 'lucide-react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\n\r\ninterface SearchableAssignmentSectionProps<T> {\r\n  title: string;\r\n  icon: React.ElementType;\r\n  items: T[];\r\n  renderItem: (item: T, index: number) => React.ReactNode;\r\n  emptyMessage: string;\r\n  searchFields: (keyof T)[];\r\n  filterOptions?: { label: string; value: string; filter: (item: T) => boolean }[];\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * SearchableAssignmentSection component with search and filter capabilities\r\n * Enhanced version of AssignmentSection with search and filtering functionality\r\n */\r\nexport function SearchableAssignmentSection<T>({\r\n  title,\r\n  icon: Icon,\r\n  items,\r\n  renderItem,\r\n  emptyMessage,\r\n  searchFields,\r\n  filterOptions = [],\r\n  className,\r\n}: SearchableAssignmentSectionProps<T>) {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [activeFilter, setActiveFilter] = useState<string | null>(null);\r\n\r\n  const filteredItems = useMemo(() => {\r\n    let filtered = items;\r\n\r\n    // Apply search filter\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(item =>\r\n        searchFields.some(field => {\r\n          const value = item[field];\r\n          if (typeof value === 'string') {\r\n            return value.toLowerCase().includes(searchTerm.toLowerCase());\r\n          }\r\n          if (typeof value === 'object' && value !== null) {\r\n            return JSON.stringify(value).toLowerCase().includes(searchTerm.toLowerCase());\r\n          }\r\n          return false;\r\n        })\r\n      );\r\n    }\r\n\r\n    // Apply active filter\r\n    if (activeFilter) {\r\n      const filterOption = filterOptions.find(option => option.value === activeFilter);\r\n      if (filterOption) {\r\n        filtered = filtered.filter(filterOption.filter);\r\n      }\r\n    }\r\n\r\n    return filtered;\r\n  }, [items, searchTerm, activeFilter, searchFields, filterOptions]);\r\n\r\n  const clearFilters = () => {\r\n    setSearchTerm('');\r\n    setActiveFilter(null);\r\n  };\r\n\r\n  const hasActiveFilters = searchTerm || activeFilter;\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Icon className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n            <span>{title}</span>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            {hasActiveFilters && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={clearFilters}\r\n                className=\"h-8 px-2 text-xs\"\r\n              >\r\n                <X className=\"h-3 w-3 mr-1\" />\r\n                Clear\r\n              </Button>\r\n            )}\r\n            <Badge variant=\"secondary\" className=\"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800\">\r\n              {filteredItems.length} of {items.length}\r\n            </Badge>\r\n          </div>\r\n        </CardTitle>\r\n        \r\n        {/* Search and Filter Controls */}\r\n        {(items.length > 3 || filterOptions.length > 0) && (\r\n          <div className=\"flex items-center space-x-2 mt-4\">\r\n            <div className=\"relative flex-1\">\r\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\r\n              <Input\r\n                placeholder={`Search ${title.toLowerCase()}...`}\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-10\"\r\n              />\r\n            </div>\r\n            \r\n            {filterOptions.length > 0 && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"outline\" size=\"sm\" className=\"shrink-0\">\r\n                    <Filter className=\"h-4 w-4 mr-2\" />\r\n                    Filter\r\n                    {activeFilter && (\r\n                      <Badge variant=\"secondary\" className=\"ml-2 h-5 px-1 text-xs\">\r\n                        1\r\n                      </Badge>\r\n                    )}\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\">\r\n                  <DropdownMenuItem\r\n                    onClick={() => setActiveFilter(null)}\r\n                    className={!activeFilter ? 'bg-blue-50 dark:bg-blue-900/30' : ''}\r\n                  >\r\n                    All {title}\r\n                  </DropdownMenuItem>\r\n                  {filterOptions.map((option) => (\r\n                    <DropdownMenuItem\r\n                      key={option.value}\r\n                      onClick={() => setActiveFilter(option.value)}\r\n                      className={activeFilter === option.value ? 'bg-blue-50 dark:bg-blue-900/30' : ''}\r\n                    >\r\n                      {option.label}\r\n                    </DropdownMenuItem>\r\n                  ))}\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </div>\r\n        )}\r\n      </CardHeader>\r\n      \r\n      <CardContent>\r\n        {filteredItems.length > 0 ? (\r\n          <div className=\"space-y-3\">\r\n            {filteredItems.map(renderItem)}\r\n          </div>\r\n        ) : (\r\n          <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n            <div className=\"rounded-full bg-gray-100 dark:bg-gray-800 p-3 mb-3\">\r\n              {hasActiveFilters ? (\r\n                <Search className=\"h-6 w-6 text-gray-400\" />\r\n              ) : (\r\n                <Icon className=\"h-6 w-6 text-gray-400\" />\r\n              )}\r\n            </div>\r\n            <p className=\"text-gray-600 dark:text-gray-400 font-medium\">\r\n              {hasActiveFilters ? `No ${title.toLowerCase()} match your search` : emptyMessage}\r\n            </p>\r\n            {hasActiveFilters && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={clearFilters}\r\n                className=\"mt-2\"\r\n              >\r\n                Clear filters\r\n              </Button>\r\n            )}\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default SearchableAssignmentSection;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAsBO,SAAS,4BAA+B,EAC7C,KAAK,EACL,MAAM,IAAI,EACV,KAAK,EACL,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAAE,EAClB,SAAS,EAC2B;;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8DAAE;YAC5B,IAAI,WAAW;YAEf,sBAAsB;YACtB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;0EAAC,CAAA,OACzB,aAAa,IAAI;kFAAC,CAAA;gCAChB,MAAM,QAAQ,IAAI,CAAC,MAAM;gCACzB,IAAI,OAAO,UAAU,UAAU;oCAC7B,OAAO,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gCAC5D;gCACA,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;oCAC/C,OAAO,KAAK,SAAS,CAAC,OAAO,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gCAC5E;gCACA,OAAO;4BACT;;;YAEJ;YAEA,sBAAsB;YACtB,IAAI,cAAc;gBAChB,MAAM,eAAe,cAAc,IAAI;uFAAC,CAAA,SAAU,OAAO,KAAK,KAAK;;gBACnE,IAAI,cAAc;oBAChB,WAAW,SAAS,MAAM,CAAC,aAAa,MAAM;gBAChD;YACF;YAEA,OAAO;QACT;6DAAG;QAAC;QAAO;QAAY;QAAc;QAAc;KAAc;IAEjE,MAAM,eAAe;QACnB,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,cAAc;IAEvC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM;;;;;;;;;;;;0CAET,6LAAC;gCAAI,WAAU;;oCACZ,kCACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIlC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;4CAClC,cAAc,MAAM;4CAAC;4CAAK,MAAM,MAAM;;;;;;;;;;;;;;;;;;;oBAM5C,CAAC,MAAM,MAAM,GAAG,KAAK,cAAc,MAAM,GAAG,CAAC,mBAC5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAa,CAAC,OAAO,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC;wCAC/C,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;4BAIb,cAAc,MAAM,GAAG,mBACtB,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;;8DAC5C,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;gDAElC,8BACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAwB;;;;;;;;;;;;;;;;;kDAMnE,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,6LAAC,+IAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,gBAAgB;gDAC/B,WAAW,CAAC,eAAe,mCAAmC;;oDAC/D;oDACM;;;;;;;4CAEN,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,+IAAA,CAAA,mBAAgB;oDAEf,SAAS,IAAM,gBAAgB,OAAO,KAAK;oDAC3C,WAAW,iBAAiB,OAAO,KAAK,GAAG,mCAAmC;8DAE7E,OAAO,KAAK;mDAJR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcjC,6LAAC,mIAAA,CAAA,cAAW;0BACT,cAAc,MAAM,GAAG,kBACtB,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC;;;;;yCAGrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,iCACC,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;qDAElB,6LAAC;gCAAK,WAAU;;;;;;;;;;;sCAGpB,6LAAC;4BAAE,WAAU;sCACV,mBAAmB,CAAC,GAAG,EAAE,MAAM,WAAW,GAAG,kBAAkB,CAAC,GAAG;;;;;;wBAErE,kCACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf;GA7JgB;KAAA;uCA+JD", "debugId": null}}, {"offset": {"line": 2483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/VehiclesCard.tsx"], "sourcesContent": ["/**\r\n * @file VehiclesCard component for displaying delegation vehicles\r\n * @module components/delegations/detail/assignments/VehiclesCard\r\n */\r\n\r\nimport React from 'react';\r\nimport { Car } from 'lucide-react';\r\nimport { AssignmentSection } from './AssignmentSection';\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\ninterface VehiclesCardProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * VehiclesCard component for displaying delegation vehicles\r\n * Uses the generic AssignmentSection for consistent styling\r\n */\r\nexport function VehiclesCard({ delegation, className }: VehiclesCardProps) {\r\n  return (\r\n    <AssignmentSection\r\n      title=\"Vehicles\"\r\n      icon={Car}\r\n      items={delegation.vehicles ?? []}\r\n      renderItem={(vehicle, index) => (\r\n        <div\r\n          key={vehicle.vehicleId || index}\r\n          className=\"rounded-lg border border-gray-200 bg-white p-4 space-y-2 hover:shadow-sm transition-shadow dark:border-gray-700 dark:bg-gray-800\"\r\n        >\r\n          <h4 className=\"font-semibold text-gray-900 dark:text-white\">\r\n            {vehicle.vehicle\r\n              ? `${vehicle.vehicle.make} ${vehicle.vehicle.model} (${vehicle.vehicle.year})`\r\n              : `Vehicle ID: ${vehicle.vehicleId}`}\r\n          </h4>\r\n          {vehicle.vehicle?.licensePlate && (\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              🚗 License Plate: {vehicle.vehicle.licensePlate}\r\n            </p>\r\n          )}\r\n          {vehicle.vehicle?.ownerName && (\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-500 bg-gray-50 dark:bg-gray-700 p-2 rounded\">\r\n              👤 Owner: {vehicle.vehicle.ownerName}\r\n            </p>\r\n          )}\r\n        </div>\r\n      )}\r\n      emptyMessage=\"No vehicles assigned to this delegation.\"\r\n      className={className || undefined}\r\n    />\r\n  );\r\n}\r\n\r\nexport default VehiclesCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;;;;AAYO,SAAS,aAAa,EAAE,UAAU,EAAE,SAAS,EAAqB;IACvE,qBACE,6LAAC,8LAAA,CAAA,oBAAiB;QAChB,OAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO,WAAW,QAAQ,IAAI,EAAE;QAChC,YAAY,CAAC,SAAS,sBACpB,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCACX,QAAQ,OAAO,GACZ,GAAG,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAC5E,CAAC,YAAY,EAAE,QAAQ,SAAS,EAAE;;;;;;oBAEvC,QAAQ,OAAO,EAAE,8BAChB,6LAAC;wBAAE,WAAU;;4BAA2C;4BACnC,QAAQ,OAAO,CAAC,YAAY;;;;;;;oBAGlD,QAAQ,OAAO,EAAE,2BAChB,6LAAC;wBAAE,WAAU;;4BAAmF;4BACnF,QAAQ,OAAO,CAAC,SAAS;;;;;;;;eAfnC,QAAQ,SAAS,IAAI;;;;;QAoB9B,cAAa;QACb,WAAW,aAAa;;;;;;AAG9B;KAhCgB;uCAkCD", "debugId": null}}, {"offset": {"line": 2561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/assignments/index.ts"], "sourcesContent": ["/**\r\n * @file Assignment components barrel export\r\n * @module components/delegations/detail/assignments\r\n */\r\n\r\nexport {\r\n  AssignmentSection,\r\n  default as AssignmentSectionDefault,\r\n} from './AssignmentSection';\r\nexport {\r\n  DelegatesCard,\r\n  default as DelegatesCardDefault,\r\n} from './delegatesCard';\r\nexport { DriversCard, default as DriversCardDefault } from './driversCard';\r\nexport { EscortsCard, default as EscortsCardDefault } from './EscortsCard';\r\nexport {\r\n  SearchableAssignmentSection,\r\n  default as SearchableAssignmentSectionDefault,\r\n} from './SearchableAssignmentSection';\r\nexport { VehiclesCard, default as VehiclesCardDefault } from './VehiclesCard';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;AAED;AAIA;AAIA;AACA;AACA;AAIA", "debugId": null}}, {"offset": {"line": 2600, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/DelegationTabs.tsx"], "sourcesContent": ["/**\r\n * @file DelegationTabs component for organizing delegation detail content\r\n * @module components/delegations/detail/DelegationTabs\r\n */\r\n\r\nimport React from 'react';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';\r\nimport { DelegationOverviewCard } from './DelegationOverviewCard';\r\nimport { FlightDetailsCard } from './FlightDetailsCard';\r\nimport {\r\n  DelegatesCard,\r\n  EscortsCard,\r\n  DriversCard,\r\n  VehiclesCard,\r\n} from './assignments';\r\nimport StatusHistoryCard from './StatusHistoryCard';\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\ninterface DelegationTabsProps {\r\n  delegation: Delegation;\r\n  onStatusUpdate?: (status: any, reason: string) => Promise<void>;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * DelegationTabs component for organizing delegation detail content\r\n * Provides tab-based navigation for different sections of delegation information\r\n */\r\nexport function DelegationTabs({\r\n  delegation,\r\n  onStatusUpdate,\r\n  className,\r\n}: DelegationTabsProps) {\r\n  return (\r\n    <Tabs defaultValue=\"overview\" className={className}>\r\n      <TabsList className=\"grid w-full grid-cols-4 mb-6\">\r\n        <TabsTrigger value=\"overview\" className=\"text-sm\">\r\n          Overview\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"assignments\" className=\"text-sm\">\r\n          Assignments\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"flights\" className=\"text-sm\">\r\n          Flights\r\n        </TabsTrigger>\r\n        <TabsTrigger value=\"history\" className=\"text-sm\">\r\n          History\r\n        </TabsTrigger>\r\n      </TabsList>\r\n\r\n      <TabsContent value=\"overview\" className=\"space-y-6 mt-0\">\r\n        <DelegationOverviewCard delegation={delegation} />\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"assignments\" className=\"space-y-6 mt-0\">\r\n        <div className=\"grid gap-6 lg:grid-cols-2\">\r\n          <DelegatesCard delegation={delegation} />\r\n          <EscortsCard delegation={delegation} />\r\n          <DriversCard delegation={delegation} />\r\n          <VehiclesCard delegation={delegation} />\r\n        </div>\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"flights\" className=\"space-y-6 mt-0\">\r\n        <FlightDetailsCard delegation={delegation} />\r\n      </TabsContent>\r\n\r\n      <TabsContent value=\"history\" className=\"space-y-6 mt-0\">\r\n        {onStatusUpdate ? (\r\n          <StatusHistoryCard\r\n            currentStatus={delegation.status as any}\r\n            delegationId={delegation.id}\r\n            statusHistory={delegation.statusHistory || []}\r\n            onStatusUpdate={onStatusUpdate}\r\n          />\r\n        ) : (\r\n          <div className=\"rounded-lg border border-gray-200 bg-white p-8 text-center dark:border-gray-700 dark:bg-gray-800\">\r\n            <div className=\"mx-auto mb-4 h-12 w-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\">\r\n              <span className=\"text-2xl\">📊</span>\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\r\n              Status History\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400\">\r\n              Status history will be displayed here when available.\r\n            </p>\r\n          </div>\r\n        )}\r\n      </TabsContent>\r\n    </Tabs>\r\n  );\r\n}\r\n\r\nexport default DelegationTabs;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAMA;;;;;;;AAaO,SAAS,eAAe,EAC7B,UAAU,EACV,cAAc,EACd,SAAS,EACW;IACpB,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,cAAa;QAAW,WAAW;;0BACvC,6LAAC,mIAAA,CAAA,WAAQ;gBAAC,WAAU;;kCAClB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCAAU;;;;;;kCAGlD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;kCAAU;;;;;;kCAGrD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCAAU;;;;;;kCAGjD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCAAU;;;;;;;;;;;;0BAKnD,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAW,WAAU;0BACtC,cAAA,6LAAC,oLAAA,CAAA,yBAAsB;oBAAC,YAAY;;;;;;;;;;;0BAGtC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAc,WAAU;0BACzC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0LAAA,CAAA,gBAAa;4BAAC,YAAY;;;;;;sCAC3B,6LAAC,wLAAA,CAAA,cAAW;4BAAC,YAAY;;;;;;sCACzB,6LAAC,wLAAA,CAAA,cAAW;4BAAC,YAAY;;;;;;sCACzB,6LAAC,yLAAA,CAAA,eAAY;4BAAC,YAAY;;;;;;;;;;;;;;;;;0BAI9B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAU,WAAU;0BACrC,cAAA,6LAAC,+KAAA,CAAA,oBAAiB;oBAAC,YAAY;;;;;;;;;;;0BAGjC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,OAAM;gBAAU,WAAU;0BACpC,+BACC,6LAAC,+KAAA,CAAA,UAAiB;oBAChB,eAAe,WAAW,MAAM;oBAChC,cAAc,WAAW,EAAE;oBAC3B,eAAe,WAAW,aAAa,IAAI,EAAE;oBAC7C,gBAAgB;;;;;yCAGlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;sCAE7B,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;KA/DgB;uCAiED", "debugId": null}}, {"offset": {"line": 2825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\nimport { X } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Sheet = SheetPrimitive.Root;\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger;\r\n\r\nconst SheetClose = SheetPrimitive.Close;\r\n\r\nconst SheetPortal = SheetPrimitive.Portal;\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n));\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName;\r\n\r\nconst sheetVariants = cva(\r\n  'fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out',\r\n  {\r\n    defaultVariants: {\r\n      side: 'right',\r\n    },\r\n    variants: {\r\n      side: {\r\n        bottom:\r\n          'inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom',\r\n        left: 'inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm',\r\n        right:\r\n          'inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm',\r\n        top: 'inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top',\r\n      },\r\n    },\r\n  }\r\n);\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ children, className, side = 'right', ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      className={cn(sheetVariants({ side }), className)}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"size-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n));\r\nSheetContent.displayName = SheetPrimitive.Content.displayName;\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col space-y-2 text-center sm:text-left',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nSheetHeader.displayName = 'SheetHeader';\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nSheetFooter.displayName = 'SheetFooter';\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    className={cn('text-lg font-semibold text-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName;\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    className={cn('text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Sheet,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetFooter,\r\n  SheetHeader,\r\n  SheetOverlay,\r\n  SheetPortal,\r\n  SheetTitle,\r\n  SheetTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAPA;;;;;;;AASA,MAAM,QAAQ,qKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,qKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,qKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,qKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,iBAAiB;QACf,MAAM;IACR;IACA,UAAU;QACR,MAAM;YACJ,QACE;YACF,MAAM;YACN,OACE;YACF,KAAK;QACP;IACF;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACvC,KAAK;gBACJ,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAoB;QACnB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACvD,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,qKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA0B;QACzB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,qKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/action-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { Loader2 } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { ButtonProps } from '@/components/ui/button';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface ActionButtonProps extends Omit<ButtonProps, 'variant'> {\r\n  /**\r\n   * The type of action this button represents\r\n   * - primary: Main actions (Create, Save, Submit)\r\n   * - secondary: Alternative actions (View, Edit)\r\n   * - tertiary: Optional actions (Cancel, Back)\r\n   * - danger: Destructive actions (Delete, Remove)\r\n   */\r\n  actionType?: ActionType;\r\n\r\n  /**\r\n   * Icon to display before the button text\r\n   * Should be a Lucide icon with consistent sizing (h-4 w-4)\r\n   */\r\n  icon?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether the button is in a loading state\r\n   */\r\n  isLoading?: boolean;\r\n\r\n  /**\r\n   * Text to display when button is loading\r\n   * If not provided, will use children\r\n   */\r\n  loadingText?: string;\r\n}\r\n\r\nexport type ActionType = 'danger' | 'primary' | 'secondary' | 'tertiary';\r\n\r\n/**\r\n * ActionButton component for consistent action styling across the application\r\n *\r\n * @example\r\n * <ActionButton actionType=\"primary\" icon={<PlusCircle />}>\r\n *   Add New\r\n * </ActionButton>\r\n */\r\nexport const ActionButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  ActionButtonProps\r\n>(\r\n  (\r\n    {\r\n      actionType = 'primary',\r\n      asChild = false,\r\n      children,\r\n      className,\r\n      disabled,\r\n      icon,\r\n      isLoading = false,\r\n      loadingText,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    // Map action types to shadcn/ui button variants and additional styling\r\n    const actionStyles: Record<\r\n      ActionType,\r\n      { className: string; variant: ButtonProps['variant']; }\r\n    > = {\r\n      danger: {\r\n        className: 'shadow-md',\r\n        variant: 'destructive',\r\n      },\r\n      primary: {\r\n        className: 'shadow-md',\r\n        variant: 'default',\r\n      },\r\n      secondary: {\r\n        className: '',\r\n        variant: 'secondary',\r\n      },\r\n      tertiary: {\r\n        className: '',\r\n        variant: 'outline',\r\n      },\r\n    };\r\n\r\n    const { className: actionClassName, variant } = actionStyles[actionType];\r\n\r\n    // const Comp = asChild ? Slot : \"button\"; // This was for an older structure, Button handles asChild now\r\n\r\n    return (\r\n      <Button\r\n        asChild={asChild} // This is passed to the underlying shadcn Button\r\n        className={cn(actionClassName, className)}\r\n        disabled={isLoading || disabled}\r\n        ref={ref}\r\n        variant={variant}\r\n        {...props}\r\n      >\r\n        {isLoading ? (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n            {loadingText || children}\r\n          </span>\r\n        ) : (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            {icon && <span className=\"mr-2\">{icon}</span>}\r\n            {children}\r\n          </span>\r\n        )}\r\n      </Button>\r\n    );\r\n  }\r\n);\r\n\r\nActionButton.displayName = 'ActionButton';\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAIA;AACA;AAAA;AATA;;;;;;AAiDO,MAAM,6BAAe,6JAAA,CAAA,UAAK,CAAC,UAAU,MAI1C,CACE,EACE,aAAa,SAAS,EACtB,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,YAAY,KAAK,EACjB,WAAW,EACX,GAAG,OACJ,EACD;IAEA,uEAAuE;IACvE,MAAM,eAGF;QACF,QAAQ;YACN,WAAW;YACX,SAAS;QACX;QACA,SAAS;YACP,WAAW;YACX,SAAS;QACX;QACA,WAAW;YACT,WAAW;YACX,SAAS;QACX;QACA,UAAU;YACR,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,EAAE,WAAW,eAAe,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,WAAW;IAExE,yGAAyG;IAEzG,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,UAAU,aAAa;QACvB,KAAK;QACL,SAAS;QACR,GAAG,KAAK;kBAER,0BACC,6LAAC;YAAK,WAAU;;gBACb;8BAED,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAClB,eAAe;;;;;;iCAGlB,6LAAC;YAAK,WAAU;;gBACb;gBAEA,sBAAQ,6LAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;;;;;AAKX;;AAGF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/reports/ViewReportButton.tsx"], "sourcesContent": ["import { ExternalLink, FileText } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport React from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\n\r\ninterface ViewReportButtonProps {\r\n  /**\r\n   * Additional CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Handler function that returns the URL to the report page (for list reports with dynamic parameters)\r\n   */\r\n  getReportUrl?: () => string;\r\n\r\n  /**\r\n   * Direct URL to the report page (for individual item reports)\r\n   */\r\n  href?: string;\r\n\r\n  /**\r\n   * Whether this button is for a list report (affects button text)\r\n   */\r\n  isList?: boolean;\r\n}\r\n\r\n/**\r\n * Standardized button for navigating to report pages.\r\n *\r\n * For individual item reports (e.g., Vehicle Report, Delegation Report):\r\n * ```tsx\r\n * <ViewReportButton href={`/vehicles/${vehicle.id}/report`} />\r\n * ```\r\n *\r\n * For list reports with dynamic parameters (e.g., Tasks Report, Delegations List Report):\r\n * ```tsx\r\n * <ViewReportButton\r\n *   isList={true}\r\n *   getReportUrl={() => {\r\n *     const params = new URLSearchParams({ searchTerm, status });\r\n *     return `/tasks/report?${params}`;\r\n *   }}\r\n * />\r\n * ```\r\n */\r\nexport function ViewReportButton({\r\n  className,\r\n  getReportUrl,\r\n  href,\r\n  isList = false,\r\n}: ViewReportButtonProps) {\r\n  if (!href && !getReportUrl) {\r\n    console.error('ViewReportButton requires either href or getReportUrl prop');\r\n    return null;\r\n  }\r\n\r\n  const buttonText = isList ? 'View List Report' : 'View Report';\r\n\r\n  // For direct links (individual item reports)\r\n  if (href) {\r\n    return (\r\n      <ActionButton\r\n        actionType=\"secondary\"\r\n        asChild\r\n        className={className}\r\n        icon={<FileText className=\"size-4\" />}\r\n      >\r\n        <Link href={href} rel=\"noopener noreferrer\" target=\"_blank\">\r\n          {buttonText}\r\n          <ExternalLink\r\n            aria-hidden=\"true\"\r\n            className=\"ml-1.5 inline-block size-3\"\r\n          />\r\n          <span className=\"sr-only\">(opens in new tab)</span>\r\n        </Link>\r\n      </ActionButton>\r\n    );\r\n  }\r\n\r\n  // For dynamic links (list reports)\r\n  const handleClick = () => {\r\n    if (getReportUrl) {\r\n      const reportUrl = getReportUrl();\r\n      window.open(reportUrl, '_blank', 'noopener,noreferrer');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ActionButton\r\n      actionType=\"secondary\"\r\n      className={className}\r\n      icon={<FileText className=\"size-4\" />}\r\n      onClick={handleClick}\r\n    >\r\n      {buttonText}\r\n      <ExternalLink\r\n        aria-hidden=\"true\"\r\n        className=\"ml-1.5 inline-block size-3\"\r\n      />\r\n    </ActionButton>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAGA;;;;;AA2CO,SAAS,iBAAiB,EAC/B,SAAS,EACT,YAAY,EACZ,IAAI,EACJ,SAAS,KAAK,EACQ;IACtB,IAAI,CAAC,QAAQ,CAAC,cAAc;QAC1B,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA,MAAM,aAAa,SAAS,qBAAqB;IAEjD,6CAA6C;IAC7C,IAAI,MAAM;QACR,qBACE,6LAAC,+IAAA,CAAA,eAAY;YACX,YAAW;YACX,OAAO;YACP,WAAW;YACX,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;sBAE1B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM;gBAAM,KAAI;gBAAsB,QAAO;;oBAChD;kCACD,6LAAC,yNAAA,CAAA,eAAY;wBACX,eAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIlC;IAEA,mCAAmC;IACnC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,MAAM,YAAY;YAClB,OAAO,IAAI,CAAC,WAAW,UAAU;QACnC;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;QACX,YAAW;QACX,WAAW;QACX,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,SAAS;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBACX,eAAY;gBACZ,WAAU;;;;;;;;;;;;AAIlB;KAxDgB", "debugId": null}}, {"offset": {"line": 3206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\r\nimport * as React from 'react';\r\n\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root;\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger;\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal;\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n));\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName;\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      className={cn(\r\n        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n));\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName;\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col space-y-2 text-center sm:text-left',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nAlertDialogHeader.displayName = 'AlertDialogHeader';\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nAlertDialogFooter.displayName = 'AlertDialogFooter';\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    className={cn('text-lg font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName;\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    className={cn('text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName;\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    className={cn(buttonVariants(), className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName;\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    className={cn(\r\n      buttonVariants({ variant: 'outline' }),\r\n      'mt-2 sm:mt-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName;\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogOverlay,\r\n  AlertDialogPortal,\r\n  AlertDialogTitle,\r\n  AlertDialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AAAA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACvC,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAChC,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/DelegationDetailHeader.tsx"], "sourcesContent": ["/**\r\n * @file DelegationDetailHeader component for delegation detail page header\r\n * @module components/delegations/detail/DelegationDetailHeader\r\n */\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { ArrowLeft, Edit, Trash2, MoreHorizontal, FileText, Printer } from 'lucide-react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { ViewReportButton } from '@/components/reports/ViewReportButton';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n  AlertDialogTrigger,\r\n} from '@/components/ui/alert-dialog';\r\nimport { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';\r\nimport { cn } from '@/lib/utils';\r\nimport type { Delegation, DelegationStatusPrisma } from '@/lib/types/domain';\r\n\r\ninterface DelegationDetailHeaderProps {\r\n  delegation: Delegation;\r\n  onDelete?: () => void;\r\n  className?: string;\r\n}\r\n\r\nconst getStatusColor = (status: DelegationStatusPrisma | undefined) => {\r\n  switch (status) {\r\n    case 'Cancelled': {\r\n      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';\r\n    }\r\n    case 'Completed': {\r\n      return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';\r\n    }\r\n    case 'Confirmed': {\r\n      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';\r\n    }\r\n    case 'In_Progress': {\r\n      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';\r\n    }\r\n    case 'Planned': {\r\n      return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';\r\n    }\r\n    case 'No_details':\r\n    default: {\r\n      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * DelegationDetailHeader component for delegation detail page header\r\n * Provides responsive header with actions and navigation\r\n */\r\nexport function DelegationDetailHeader({ delegation, onDelete, className }: DelegationDetailHeaderProps) {\r\n  const router = useRouter();\r\n\r\n  const ActionButtons = () => (\r\n    <>\r\n      <ActionButton\r\n        actionType=\"tertiary\"\r\n        icon={<ArrowLeft className=\"size-4\" />}\r\n        onClick={() => router.push('/delegations')}\r\n        size=\"sm\"\r\n      >\r\n        <span className=\"hidden sm:inline\">Back to List</span>\r\n        <span className=\"sm:hidden\">Back</span>\r\n      </ActionButton>\r\n      <ActionButton\r\n        actionType=\"secondary\"\r\n        asChild\r\n        icon={<Edit className=\"size-4\" />}\r\n        size=\"sm\"\r\n      >\r\n        <Link href={`/delegations/${delegation.id}/edit`}>\r\n          Edit\r\n        </Link>\r\n      </ActionButton>\r\n      <ViewReportButton\r\n        href={`/delegations/${delegation.id}/report`}\r\n      />\r\n      {onDelete && (\r\n        <AlertDialog>\r\n          <AlertDialogTrigger asChild>\r\n            <ActionButton\r\n              actionType=\"danger\"\r\n              icon={<Trash2 className=\"size-4\" />}\r\n              size=\"sm\"\r\n            >\r\n              <span className=\"hidden sm:inline\">Delete Delegation</span>\r\n              <span className=\"sm:hidden\">Delete</span>\r\n            </ActionButton>\r\n          </AlertDialogTrigger>\r\n          <AlertDialogContent>\r\n            <AlertDialogHeader>\r\n              <AlertDialogTitle>Are you sure?</AlertDialogTitle>\r\n              <AlertDialogDescription>\r\n                This action cannot be undone. This will permanently delete the delegation and all its related information.\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <AlertDialogFooter>\r\n              <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n              <AlertDialogAction\r\n                className=\"bg-destructive hover:bg-destructive/90\"\r\n                onClick={onDelete}\r\n              >\r\n                Delete\r\n              </AlertDialogAction>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n      )}\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <div className={cn('border-b bg-white dark:bg-gray-800', className)}>\r\n      <div className=\"container mx-auto px-4 py-6\">\r\n        {/* Mobile Layout */}\r\n        <div className=\"flex items-center justify-between lg:hidden\">\r\n          <div className=\"min-w-0 flex-1\">\r\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white truncate\">\r\n              {delegation.eventName}\r\n            </h1>\r\n            <div className=\"mt-2 flex items-center space-x-2\">\r\n              <Badge\r\n                className={cn(\r\n                  'text-sm py-1 px-3 font-semibold',\r\n                  getStatusColor(delegation.status)\r\n                )}\r\n              >\r\n                {formatDelegationStatusForDisplay(delegation.status as DelegationStatusPrisma)}\r\n              </Badge>\r\n              {delegation.location && (\r\n                <>\r\n                  <Separator orientation=\"vertical\" className=\"h-4\" />\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400 truncate\">\r\n                    {delegation.location}\r\n                  </span>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Sheet>\r\n            <SheetTrigger asChild>\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                <MoreHorizontal className=\"h-4 w-4\" />\r\n              </Button>\r\n            </SheetTrigger>\r\n            <SheetContent side=\"bottom\" className=\"h-[80vh]\">\r\n              <SheetHeader>\r\n                <SheetTitle>Delegation Actions</SheetTitle>\r\n              </SheetHeader>\r\n              <div className=\"grid gap-4 py-4\">\r\n                <ActionButtons />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        {/* Desktop Layout */}\r\n        <div className=\"hidden lg:flex lg:items-center lg:justify-between\">\r\n          <div className=\"min-w-0 flex-1\">\r\n            <h1 className=\"text-3xl font-bold tracking-tight text-gray-900 dark:text-white\">\r\n              {delegation.eventName}\r\n            </h1>\r\n            <div className=\"mt-2 flex items-center space-x-4\">\r\n              <Badge\r\n                className={cn(\r\n                  'text-sm py-1 px-3 font-semibold',\r\n                  getStatusColor(delegation.status)\r\n                )}\r\n              >\r\n                {formatDelegationStatusForDisplay(delegation.status as DelegationStatusPrisma)}\r\n              </Badge>\r\n              {delegation.location && (\r\n                <>\r\n                  <Separator orientation=\"vertical\" className=\"h-4\" />\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {delegation.location}\r\n                  </span>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <ActionButtons />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default DelegationDetailHeader;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;AAAA;;;;;;;;;;;;;;;AASA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAe;gBAClB,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;QACL;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAMO,SAAS,uBAAuB,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAA+B;;IACrG,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB,kBACpB;;8BACE,6LAAC,+IAAA,CAAA,eAAY;oBACX,YAAW;oBACX,oBAAM,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAC3B,SAAS,IAAM,OAAO,IAAI,CAAC;oBAC3B,MAAK;;sCAEL,6LAAC;4BAAK,WAAU;sCAAmB;;;;;;sCACnC,6LAAC;4BAAK,WAAU;sCAAY;;;;;;;;;;;;8BAE9B,6LAAC,+IAAA,CAAA,eAAY;oBACX,YAAW;oBACX,OAAO;oBACP,oBAAM,6LAAC,8MAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,MAAK;8BAEL,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC;kCAAE;;;;;;;;;;;8BAIpD,6LAAC,oJAAA,CAAA,mBAAgB;oBACf,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC;;;;;;gBAE7C,0BACC,6LAAC,8IAAA,CAAA,cAAW;;sCACV,6LAAC,8IAAA,CAAA,qBAAkB;4BAAC,OAAO;sCACzB,cAAA,6LAAC,+IAAA,CAAA,eAAY;gCACX,YAAW;gCACX,oBAAM,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCACxB,MAAK;;kDAEL,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6LAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAGhC,6LAAC,8IAAA,CAAA,qBAAkB;;8CACjB,6LAAC,8IAAA,CAAA,oBAAiB;;sDAChB,6LAAC,8IAAA,CAAA,mBAAgB;sDAAC;;;;;;sDAClB,6LAAC,8IAAA,CAAA,yBAAsB;sDAAC;;;;;;;;;;;;8CAI1B,6LAAC,8IAAA,CAAA,oBAAiB;;sDAChB,6LAAC,8IAAA,CAAA,oBAAiB;sDAAC;;;;;;sDACnB,6LAAC,8IAAA,CAAA,oBAAiB;4CAChB,WAAU;4CACV,SAAS;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;IAUb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;kBACvD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,WAAW,SAAS;;;;;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mCACA,eAAe,WAAW,MAAM;sDAGjC,CAAA,GAAA,yIAAA,CAAA,mCAAgC,AAAD,EAAE,WAAW,MAAM;;;;;;wCAEpD,WAAW,QAAQ,kBAClB;;8DACE,6LAAC,wIAAA,CAAA,YAAS;oDAAC,aAAY;oDAAW,WAAU;;;;;;8DAC5C,6LAAC;oDAAK,WAAU;8DACb,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;sCAM9B,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAC7B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG9B,6LAAC,oIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAS,WAAU;;sDACpC,6LAAC,oIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,aAAU;0DAAC;;;;;;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOT,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,WAAW,SAAS;;;;;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mCACA,eAAe,WAAW,MAAM;sDAGjC,CAAA,GAAA,yIAAA,CAAA,mCAAgC,AAAD,EAAE,WAAW,MAAM;;;;;;wCAEpD,WAAW,QAAQ,kBAClB;;8DACE,6LAAC,wIAAA,CAAA,YAAS;oDAAC,aAAY;oDAAW,WAAU;;;;;;8DAC5C,6LAAC;oDAAK,WAAU;8DACb,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;sCAM9B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA1IgB;;QACC,qIAAA,CAAA,YAAS;;;KADV;uCA4ID", "debugId": null}}, {"offset": {"line": 3838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/common/DelegationMetrics.tsx"], "sourcesContent": ["/**\r\n * @file DelegationMetrics component for displaying delegation statistics\r\n * @module components/delegations/common/DelegationMetrics\r\n */\r\n\r\nimport React from 'react';\r\nimport { Users, Shield, Car, User, Clock, Calendar, MapPin, Plane } from 'lucide-react';\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport type { Delegation } from '@/lib/types/domain';\r\nimport { differenceInDays, parseISO } from 'date-fns';\r\n\r\ninterface DelegationMetricsProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\ninterface MetricItemProps {\r\n  icon: React.ElementType;\r\n  label: string;\r\n  value: string | number;\r\n  color: string;\r\n  bgColor: string;\r\n}\r\n\r\nfunction MetricItem({ icon: Icon, label, value, color, bgColor }: MetricItemProps) {\r\n  return (\r\n    <div className=\"flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700\">\r\n      <div className=\"flex items-center space-x-3\">\r\n        <div className={`p-2 rounded-full ${bgColor}`}>\r\n          <Icon className={`h-4 w-4 ${color}`} />\r\n        </div>\r\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n          {label}\r\n        </span>\r\n      </div>\r\n      <Badge variant=\"secondary\" className=\"font-semibold\">\r\n        {value}\r\n      </Badge>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst calculateDuration = (startDate: string | undefined, endDate: string | undefined): string => {\r\n  if (!startDate || !endDate) return 'N/A';\r\n  try {\r\n    const start = parseISO(startDate);\r\n    const end = parseISO(endDate);\r\n    const days = differenceInDays(end, start) + 1;\r\n    return `${days} day${days !== 1 ? 's' : ''}`;\r\n  } catch {\r\n    return 'Invalid';\r\n  }\r\n};\r\n\r\n/**\r\n * DelegationMetrics component for displaying delegation statistics\r\n * Shows key metrics and counts in a compact, visual format\r\n */\r\nexport function DelegationMetrics({ delegation, className }: DelegationMetricsProps) {\r\n  const metrics = [\r\n    {\r\n      icon: Users,\r\n      label: 'Delegates',\r\n      value: delegation.delegates?.length || 0,\r\n      color: 'text-blue-600 dark:text-blue-400',\r\n      bgColor: 'bg-blue-50 dark:bg-blue-900/30',\r\n    },\r\n    {\r\n      icon: Shield,\r\n      label: 'Escorts',\r\n      value: delegation.escorts?.length || 0,\r\n      color: 'text-green-600 dark:text-green-400',\r\n      bgColor: 'bg-green-50 dark:bg-green-900/30',\r\n    },\r\n    {\r\n      icon: User,\r\n      label: 'Drivers',\r\n      value: delegation.drivers?.length || 0,\r\n      color: 'text-purple-600 dark:text-purple-400',\r\n      bgColor: 'bg-purple-50 dark:bg-purple-900/30',\r\n    },\r\n    {\r\n      icon: Car,\r\n      label: 'Vehicles',\r\n      value: delegation.vehicles?.length || 0,\r\n      color: 'text-orange-600 dark:text-orange-400',\r\n      bgColor: 'bg-orange-50 dark:bg-orange-900/30',\r\n    },\r\n    {\r\n      icon: Plane,\r\n      label: 'Flights',\r\n      value: [delegation.arrivalFlight, delegation.departureFlight].filter(Boolean).length,\r\n      color: 'text-indigo-600 dark:text-indigo-400',\r\n      bgColor: 'bg-indigo-50 dark:bg-indigo-900/30',\r\n    },\r\n    {\r\n      icon: Clock,\r\n      label: 'Duration',\r\n      value: calculateDuration(delegation.durationFrom, delegation.durationTo),\r\n      color: 'text-gray-600 dark:text-gray-400',\r\n      bgColor: 'bg-gray-50 dark:bg-gray-900/30',\r\n    },\r\n  ];\r\n\r\n  const totalPeople = (delegation.delegates?.length || 0) + \r\n                     (delegation.escorts?.length || 0) + \r\n                     (delegation.drivers?.length || 0);\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Calendar className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\r\n            <span>Delegation Metrics</span>\r\n          </div>\r\n          <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800\">\r\n            {totalPeople} people total\r\n          </Badge>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-3\">\r\n        {metrics.map((metric, index) => (\r\n          <MetricItem\r\n            key={index}\r\n            icon={metric.icon}\r\n            label={metric.label}\r\n            value={metric.value}\r\n            color={metric.color}\r\n            bgColor={metric.bgColor}\r\n          />\r\n        ))}\r\n        \r\n        {delegation.location && (\r\n          <div className=\"mt-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <MapPin className=\"h-4 w-4 text-gray-600 dark:text-gray-400\" />\r\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Location\r\n              </span>\r\n            </div>\r\n            <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400 ml-6\">\r\n              {delegation.location}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n\r\nexport default DelegationMetrics;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAeA,SAAS,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAmB;IAC/E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,SAAS;kCAC3C,cAAA,6LAAC;4BAAK,WAAW,CAAC,QAAQ,EAAE,OAAO;;;;;;;;;;;kCAErC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;0BAGL,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAY,WAAU;0BAClC;;;;;;;;;;;;AAIT;KAhBS;AAkBT,MAAM,oBAAoB,CAAC,WAA+B;IACxD,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IACnC,IAAI;QACF,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE;QACvB,MAAM,MAAM,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE;QACrB,MAAM,OAAO,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,SAAS;QAC5C,OAAO,GAAG,KAAK,IAAI,EAAE,SAAS,IAAI,MAAM,IAAI;IAC9C,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAMO,SAAS,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAA0B;IACjF,MAAM,UAAU;QACd;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,WAAW,SAAS,EAAE,UAAU;YACvC,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO,WAAW,OAAO,EAAE,UAAU;YACrC,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO,WAAW,OAAO,EAAE,UAAU;YACrC,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,OAAO,WAAW,QAAQ,EAAE,UAAU;YACtC,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;gBAAC,WAAW,aAAa;gBAAE,WAAW,eAAe;aAAC,CAAC,MAAM,CAAC,SAAS,MAAM;YACpF,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,kBAAkB,WAAW,YAAY,EAAE,WAAW,UAAU;YACvE,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,cAAc,CAAC,WAAW,SAAS,EAAE,UAAU,CAAC,IACnC,CAAC,WAAW,OAAO,EAAE,UAAU,CAAC,IAChC,CAAC,WAAW,OAAO,EAAE,UAAU,CAAC;IAEnD,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;gCAChC;gCAAY;;;;;;;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4BAEC,MAAM,OAAO,IAAI;4BACjB,OAAO,OAAO,KAAK;4BACnB,OAAO,OAAO,KAAK;4BACnB,OAAO,OAAO,KAAK;4BACnB,SAAS,OAAO,OAAO;2BALlB;;;;;oBASR,WAAW,QAAQ,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAIzE,6LAAC;gCAAE,WAAU;0CACV,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;MA3FgB;uCA6FD", "debugId": null}}, {"offset": {"line": 4110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/DelegationSidebar.tsx"], "sourcesContent": ["/**\r\n * @file DelegationSidebar component for delegation detail page sidebar\r\n * @module components/delegations/detail/DelegationSidebar\r\n */\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { Edit, FileText, Printer } from 'lucide-react';\r\nimport { DelegationMetrics } from '../common/DelegationMetrics';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport type { Delegation } from '@/lib/types/domain';\r\nimport { format, parseISO } from 'date-fns';\r\n\r\ninterface DelegationSidebarProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * DelegationSidebar component for delegation detail page sidebar\r\n * Provides quick actions and key metrics\r\n */\r\nexport function DelegationSidebar({\r\n  delegation,\r\n  className,\r\n}: DelegationSidebarProps) {\r\n  const handlePrint = () => {\r\n    if (typeof window !== 'undefined') {\r\n      window.print();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <div className=\"space-y-6\">\r\n        {/* Quick Actions */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-lg\">Quick Actions</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-3\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start\"\r\n              size=\"sm\"\r\n              asChild\r\n            >\r\n              <Link href={`/delegations/${delegation.id}/edit`}>\r\n                <Edit className=\"mr-2 h-4 w-4\" />\r\n                Edit Delegation\r\n              </Link>\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start\"\r\n              size=\"sm\"\r\n              asChild\r\n            >\r\n              <Link\r\n                href={`/delegations/${delegation.id}/report`}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n              >\r\n                <FileText className=\"mr-2 h-4 w-4\" />\r\n                View Report\r\n              </Link>\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start\"\r\n              size=\"sm\"\r\n              onClick={handlePrint}\r\n            >\r\n              <Printer className=\"mr-2 h-4 w-4\" />\r\n              Print Details\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Delegation Metrics */}\r\n        <DelegationMetrics delegation={delegation} />\r\n\r\n        {/* Status Information */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-lg\">Status Information</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-3\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-gray-900 dark:text-white mb-1\">\r\n                {delegation.status?.replace('_', ' ') || 'No Status'}\r\n              </div>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Current Status\r\n              </p>\r\n            </div>\r\n\r\n            {delegation.durationFrom && (\r\n              <>\r\n                <Separator />\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white mb-1\">\r\n                    {format(parseISO(delegation.durationFrom), 'MMM d, yyyy')}\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400\">\r\n                    Start Date\r\n                  </p>\r\n                </div>\r\n              </>\r\n            )}\r\n\r\n            {delegation.durationTo && (\r\n              <>\r\n                <Separator />\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white mb-1\">\r\n                    {format(parseISO(delegation.durationTo), 'MMM d, yyyy')}\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400\">\r\n                    End Date\r\n                  </p>\r\n                </div>\r\n              </>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default DelegationSidebar;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;;;;;;;;;AAWO,SAAS,kBAAkB,EAChC,UAAU,EACV,SAAS,EACc;IACvB,MAAM,cAAc;QAClB,wCAAmC;YACjC,OAAO,KAAK;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,MAAK;oCACL,OAAO;8CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC;;0DAC9C,6LAAC,8MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIrC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,MAAK;oCACL,OAAO;8CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,OAAO,CAAC;wCAC5C,QAAO;wCACP,KAAI;;0DAEJ,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIzC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,MAAK;oCACL,SAAS;;sDAET,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAO1C,6LAAC,+KAAA,CAAA,oBAAiB;oBAAC,YAAY;;;;;;8BAG/B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,WAAW,MAAM,EAAE,QAAQ,KAAK,QAAQ;;;;;;sDAE3C,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;gCAKzD,WAAW,YAAY,kBACtB;;sDACE,6LAAC,wIAAA,CAAA,YAAS;;;;;sDACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,YAAY,GAAG;;;;;;8DAE7C,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;gCAO7D,WAAW,UAAU,kBACpB;;sDACE,6LAAC,wIAAA,CAAA,YAAS;;;;;sDACV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,UAAU,GAAG;;;;;;8DAE3C,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1E;KA3GgB;uCA6GD", "debugId": null}}, {"offset": {"line": 4410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/features/delegations/detail/index.ts"], "sourcesContent": ["/**\r\n * @file Detail view components barrel export\r\n * @module components/delegations/detail\r\n */\r\n\r\nexport { default as StatusHistoryCard } from './StatusHistoryCard';\r\nexport {\r\n  DelegationOverviewCard,\r\n  default as DelegationOverviewCardDefault,\r\n} from './DelegationOverviewCard';\r\nexport {\r\n  DelegationTabs,\r\n  default as DelegationTabsDefault,\r\n} from './DelegationTabs';\r\nexport {\r\n  DelegationDetailHeader,\r\n  default as DelegationDetailHeaderDefault,\r\n} from './DelegationDetailHeader';\r\nexport {\r\n  DelegationSidebar,\r\n  default as DelegationSidebarDefault,\r\n} from './DelegationSidebar';\r\nexport {\r\n  FlightDetailsCard,\r\n  default as FlightDetailsCardDefault,\r\n} from './FlightDetailsCard';\r\nexport * from './assignments';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;AAED;AACA;AAIA;AAIA;AAIA;AAIA;AAIA", "debugId": null}}, {"offset": {"line": 4452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Breadcrumb = React.forwardRef<\r\n  HTMLElement,\r\n  React.HTMLAttributes<HTMLElement>\r\n>(({ className, ...props }, ref) => (\r\n  <nav\r\n    aria-label=\"breadcrumb\"\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumb.displayName = 'Breadcrumb';\r\n\r\nconst BreadcrumbList = React.forwardRef<\r\n  HTMLOListElement,\r\n  React.OlHTMLAttributes<HTMLOListElement>\r\n>(({ className, ...props }, ref) => (\r\n  <ol\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbList.displayName = 'BreadcrumbList';\r\n\r\nconst BreadcrumbItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.LiHTMLAttributes<HTMLLIElement>\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    className={cn('inline-flex items-center gap-1.5', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbItem.displayName = 'BreadcrumbItem';\r\n\r\nconst BreadcrumbLink = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.AnchorHTMLAttributes<HTMLAnchorElement> & {\r\n    asChild?: boolean;\r\n  }\r\n>(({ asChild, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      className={cn('transition-colors hover:text-foreground', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nBreadcrumbLink.displayName = 'BreadcrumbLink';\r\n\r\nconst BreadcrumbPage = React.forwardRef<\r\n  HTMLSpanElement,\r\n  React.HTMLAttributes<HTMLSpanElement>\r\n>(({ className, ...props }, ref) => (\r\n  <span\r\n    aria-current=\"page\"\r\n    aria-disabled=\"true\"\r\n    className={cn('font-normal text-foreground', className)}\r\n    ref={ref}\r\n    role=\"link\"\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbPage.displayName = 'BreadcrumbPage';\r\n\r\nconst BreadcrumbSeparator = ({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('[&>svg]:size-3.5', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    {children ?? <ChevronRight className=\"size-4\" />}\r\n  </span>\r\n);\r\nBreadcrumbSeparator.displayName = 'BreadcrumbSeparator';\r\n\r\nconst BreadcrumbEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"size-4\" />\r\n    <span className=\"sr-only\">More</span>\r\n  </span>\r\n);\r\nBreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbEllipsis,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACzD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,gBAAa;QACb,iBAAc;QACd,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC7C,KAAK;QACL,MAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACmC,iBACtC,6LAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QAClC,MAAK;QACJ,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;;;;;;OAXnC;AAcN,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACmC,iBACtC,6LAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,MAAK;QACJ,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;OAXxB;AAcN,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/app-breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport React from 'react';\r\n\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface AppBreadcrumbProps {\r\n  className?: string;\r\n  homeHref?: string;\r\n  homeLabel?: string;\r\n  showContainer?: boolean;\r\n}\r\n\r\n/**\r\n * Enhanced AppBreadcrumb Component\r\n *\r\n * Professional breadcrumb navigation with improved styling, accessibility,\r\n * and responsive design. Automatically generates breadcrumbs from the current\r\n * pathname with intelligent segment formatting.\r\n *\r\n * Features:\r\n * - Professional visual design with subtle container styling\r\n * - Responsive layout that adapts to screen size\r\n * - Intelligent path segment formatting (handles IDs, special cases)\r\n * - Enhanced accessibility with proper ARIA labels\r\n * - Consistent integration with design system\r\n * - Smooth hover transitions and visual feedback\r\n */\r\nexport function AppBreadcrumb({\r\n  className,\r\n  homeHref = '/',\r\n  homeLabel = 'Dashboard',\r\n  showContainer = true,\r\n}: AppBreadcrumbProps) {\r\n  const pathname = usePathname();\r\n  const pathSegments = pathname ? pathname.split('/').filter(Boolean) : [];\r\n\r\n  /**\r\n   * Format path segments with intelligent handling of different segment types\r\n   */\r\n  const formatSegment = (segment: string): string => {\r\n    // Handle numeric IDs (don't capitalize, show as \"ID: 123\")\r\n    if (/^\\d+$/.test(segment)) {\r\n      return `ID: ${segment}`;\r\n    }\r\n\r\n    // Handle UUIDs or long alphanumeric strings (show as \"Details\")\r\n    if (segment.length > 10 && /^[a-zA-Z0-9-]+$/.test(segment)) {\r\n      return 'Details';\r\n    }\r\n\r\n    // Handle special cases\r\n    const specialCases: Record<string, string> = {\r\n      add: 'Add New',\r\n      admin: 'Administration',\r\n      edit: 'Edit',\r\n      reports: 'Reports',\r\n      'service-history': 'Service History',\r\n      settings: 'Settings',\r\n    };\r\n\r\n    if (specialCases[segment]) {\r\n      return specialCases[segment];\r\n    }\r\n\r\n    // Default formatting: capitalize and replace dashes with spaces\r\n    return segment\r\n      .split('-')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ');\r\n  };\r\n\r\n  const breadcrumbItems = pathSegments.map((segment, index) => {\r\n    const href = '/' + pathSegments.slice(0, index + 1).join('/');\r\n    const isLast = index === pathSegments.length - 1;\r\n    const displaySegment = formatSegment(segment);\r\n\r\n    return (\r\n      <React.Fragment key={href}>\r\n        <BreadcrumbItem>\r\n          {isLast ? (\r\n            <BreadcrumbPage className=\"font-medium text-foreground\">\r\n              {displaySegment}\r\n            </BreadcrumbPage>\r\n          ) : (\r\n            <BreadcrumbLink asChild>\r\n              <Link\r\n                className=\"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2\"\r\n                href={href}\r\n              >\r\n                {displaySegment}\r\n              </Link>\r\n            </BreadcrumbLink>\r\n          )}\r\n        </BreadcrumbItem>\r\n        {!isLast && <BreadcrumbSeparator />}\r\n      </React.Fragment>\r\n    );\r\n  });\r\n\r\n  const breadcrumbContent = (\r\n    <Breadcrumb className={cn('text-sm', className)}>\r\n      <BreadcrumbList className=\"flex-wrap\">\r\n        <BreadcrumbItem>\r\n          <BreadcrumbLink asChild>\r\n            <Link\r\n              className=\"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2\"\r\n              href={homeHref}\r\n            >\r\n              {homeLabel}\r\n            </Link>\r\n          </BreadcrumbLink>\r\n        </BreadcrumbItem>\r\n        {pathSegments.length > 0 && <BreadcrumbSeparator />}\r\n        {breadcrumbItems}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  );\r\n\r\n  if (!showContainer) {\r\n    return breadcrumbContent;\r\n  }\r\n\r\n  return (\r\n    <div className=\"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm\">\r\n      <div className=\"flex items-center\">{breadcrumbContent}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAQA;AAAA;;;AAdA;;;;;;AAsCO,SAAS,cAAc,EAC5B,SAAS,EACT,WAAW,GAAG,EACd,YAAY,WAAW,EACvB,gBAAgB,IAAI,EACD;;IACnB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC,WAAW,EAAE;IAExE;;GAEC,GACD,MAAM,gBAAgB,CAAC;QACrB,2DAA2D;QAC3D,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO,CAAC,IAAI,EAAE,SAAS;QACzB;QAEA,gEAAgE;QAChE,IAAI,QAAQ,MAAM,GAAG,MAAM,kBAAkB,IAAI,CAAC,UAAU;YAC1D,OAAO;QACT;QAEA,uBAAuB;QACvB,MAAM,eAAuC;YAC3C,KAAK;YACL,OAAO;YACP,MAAM;YACN,SAAS;YACT,mBAAmB;YACnB,UAAU;QACZ;QAEA,IAAI,YAAY,CAAC,QAAQ,EAAE;YACzB,OAAO,YAAY,CAAC,QAAQ;QAC9B;QAEA,gEAAgE;QAChE,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC,SAAS;QACjD,MAAM,OAAO,MAAM,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;QACzD,MAAM,SAAS,UAAU,aAAa,MAAM,GAAG;QAC/C,MAAM,iBAAiB,cAAc;QAErC,qBACE,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;8BACb,6LAAC,yIAAA,CAAA,iBAAc;8BACZ,uBACC,6LAAC,yIAAA,CAAA,iBAAc;wBAAC,WAAU;kCACvB;;;;;6CAGH,6LAAC,yIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,WAAU;4BACV,MAAM;sCAEL;;;;;;;;;;;;;;;;gBAKR,CAAC,wBAAU,6LAAC,yIAAA,CAAA,sBAAmB;;;;;;WAjBb;;;;;IAoBzB;IAEA,MAAM,kCACJ,6LAAC,yIAAA,CAAA,aAAU;QAAC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;kBACnC,cAAA,6LAAC,yIAAA,CAAA,iBAAc;YAAC,WAAU;;8BACxB,6LAAC,yIAAA,CAAA,iBAAc;8BACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,WAAU;4BACV,MAAM;sCAEL;;;;;;;;;;;;;;;;gBAIN,aAAa,MAAM,GAAG,mBAAK,6LAAC,yIAAA,CAAA,sBAAmB;;;;;gBAC/C;;;;;;;;;;;;IAKP,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBAAqB;;;;;;;;;;;AAG1C;GApGgB;;QAMG,qIAAA,CAAA,cAAW;;;KANd", "debugId": null}}, {"offset": {"line": 4782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn('animate-pulse rounded-md bg-muted', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 4813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>cle, Loader2, LucideIcon } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Size variants for the spinner\r\nexport type SpinnerSize = 'lg' | 'md' | 'sm' | 'xl';\r\n\r\n// Size mappings for the spinner\r\nconst spinnerSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'h-8 w-8',\r\n  md: 'h-6 w-6',\r\n  sm: 'h-4 w-4',\r\n  xl: 'h-12 w-12',\r\n};\r\n\r\n// Text size mappings for the spinner\r\nconst spinnerTextSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'text-base',\r\n  md: 'text-sm',\r\n  sm: 'text-xs',\r\n  xl: 'text-lg',\r\n};\r\n\r\nexport interface DataLoaderProps<T> {\r\n  /**\r\n   * Render function for the data\r\n   */\r\n  children: (data: T) => React.ReactNode;\r\n\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * The data to render\r\n   */\r\n  data: null | T | undefined;\r\n\r\n  /**\r\n   * Custom empty state component\r\n   */\r\n  emptyComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Error message, if any\r\n   */\r\n  error?: null | string;\r\n\r\n  /**\r\n   * Custom error component\r\n   */\r\n  errorComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether data is currently loading\r\n   */\r\n  isLoading: boolean;\r\n\r\n  /**\r\n   * Custom loading component\r\n   */\r\n  loadingComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Function to retry loading data\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface EmptyStateProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Description text to display\r\n   */\r\n  description?: string;\r\n\r\n  /**\r\n   * Icon to display (Lucide icon component)\r\n   */\r\n  icon?: LucideIcon;\r\n\r\n  /**\r\n   * Primary action button\r\n   */\r\n  primaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Secondary action button\r\n   */\r\n  secondaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Title text to display\r\n   */\r\n  title: string;\r\n}\r\n\r\nexport interface ErrorDisplayProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Error message to display\r\n   */\r\n  message: string;\r\n\r\n  /**\r\n   * Function to retry the operation\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface LoadingSpinnerProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Whether to display as a full-page overlay\r\n   */\r\n  fullPage?: boolean;\r\n\r\n  /**\r\n   * Size of the spinner\r\n   */\r\n  size?: SpinnerSize;\r\n\r\n  /**\r\n   * Text to display below the spinner\r\n   */\r\n  text?: string;\r\n}\r\n\r\nexport interface SkeletonLoaderProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Number of skeleton items to display\r\n   */\r\n  count?: number;\r\n\r\n  /**\r\n   * Test ID for testing\r\n   */\r\n  testId?: string;\r\n\r\n  /**\r\n   * Type of content to show a skeleton for\r\n   */\r\n  variant?: SkeletonVariant;\r\n}\r\n\r\n// Skeleton variants\r\nexport type SkeletonVariant = 'card' | 'default' | 'list' | 'stats' | 'table';\r\n\r\n/**\r\n * DataLoader component for handling loading, error, and empty states\r\n *\r\n * @example\r\n * <DataLoader\r\n *   isLoading={isLoading}\r\n *   error={error}\r\n *   data={vehicles}\r\n *   onRetry={refetch}\r\n *   loadingComponent={<SkeletonLoader variant=\"card\" count={3} />}\r\n * >\r\n *   {(vehicles) => (\r\n *     <div className=\"grid grid-cols-3 gap-4\">\r\n *       {vehicles.map(vehicle => (\r\n *         <VehicleCard key={vehicle.id} vehicle={vehicle} />\r\n *       ))}\r\n *     </div>\r\n *   )}\r\n * </DataLoader>\r\n */\r\nexport function DataLoader<T>({\r\n  children,\r\n  className,\r\n  data,\r\n  emptyComponent,\r\n  error,\r\n  errorComponent,\r\n  isLoading,\r\n  loadingComponent,\r\n  onRetry,\r\n}: DataLoaderProps<T>) {\r\n  if (isLoading) {\r\n    return (\r\n      loadingComponent || (\r\n        <LoadingSpinner {...(className && { className })} text=\"Loading...\" />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      errorComponent || (\r\n        <ErrorDisplay\r\n          {...(className && { className })}\r\n          message={error}\r\n          {...(onRetry && { onRetry })}\r\n        />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!data || (Array.isArray(data) && data.length === 0)) {\r\n    return (\r\n      emptyComponent || (\r\n        <div className={cn('text-center py-8', className)}>\r\n          <p className=\"text-muted-foreground\">No data available</p>\r\n        </div>\r\n      )\r\n    );\r\n  }\r\n\r\n  return <div className={className}>{children(data)}</div>;\r\n}\r\n\r\n/**\r\n * Unified empty state component following the design pattern from delegations page\r\n *\r\n * @example\r\n * <EmptyState\r\n *   title=\"No Service Records Found\"\r\n *   description=\"There are no service records matching your current filters.\"\r\n *   icon={History}\r\n *   primaryAction={{\r\n *     label: \"Log New Service\",\r\n *     href: \"/vehicles\",\r\n *     icon: <PlusCircle className=\"size-4\" />\r\n *   }}\r\n *   secondaryAction={{\r\n *     label: \"Clear Filters\",\r\n *     onClick: clearFilters\r\n *   }}\r\n * />\r\n */\r\nexport function EmptyState({\r\n  className,\r\n  description,\r\n  icon: Icon,\r\n  primaryAction,\r\n  secondaryAction,\r\n  title,\r\n}: EmptyStateProps) {\r\n  return (\r\n    <div className={cn('space-y-6 text-center py-12', className)}>\r\n      {Icon && (\r\n        <div className=\"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted\">\r\n          <Icon className=\"h-10 w-10 text-muted-foreground\" />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-2\">\r\n        <h3 className=\"text-2xl font-semibold text-foreground\">{title}</h3>\r\n        {description && (\r\n          <p className=\"text-muted-foreground max-w-md mx-auto\">\r\n            {description}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\r\n        {primaryAction && (\r\n          <ActionButton\r\n            actionType=\"primary\"\r\n            asChild={!!primaryAction.href}\r\n            icon={primaryAction.icon}\r\n            onClick={primaryAction.onClick}\r\n          >\r\n            {primaryAction.href ? (\r\n              <a href={primaryAction.href}>{primaryAction.label}</a>\r\n            ) : (\r\n              primaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n\r\n        {secondaryAction && (\r\n          <ActionButton\r\n            actionType=\"tertiary\"\r\n            asChild={!!secondaryAction.href}\r\n            icon={secondaryAction.icon}\r\n            onClick={secondaryAction.onClick}\r\n          >\r\n            {secondaryAction.href ? (\r\n              <a href={secondaryAction.href}>{secondaryAction.label}</a>\r\n            ) : (\r\n              secondaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified error display component\r\n *\r\n * @example\r\n * <ErrorDisplay message=\"Failed to load data\" onRetry={refetch} />\r\n */\r\nexport function ErrorDisplay({\r\n  className,\r\n  message,\r\n  onRetry,\r\n}: ErrorDisplayProps) {\r\n  return (\r\n    <Alert className={cn('my-4', className)} variant=\"destructive\">\r\n      <AlertCircle className=\"size-4\" />\r\n      <AlertTitle>Error</AlertTitle>\r\n      <AlertDescription>\r\n        <div className=\"mt-2\">\r\n          <p className=\"mb-4 text-sm text-muted-foreground\">{message}</p>\r\n          {onRetry && (\r\n            <ActionButton\r\n              actionType=\"tertiary\"\r\n              icon={<Loader2 className=\"size-4\" />}\r\n              onClick={onRetry}\r\n              size=\"sm\"\r\n            >\r\n              Try Again\r\n            </ActionButton>\r\n          )}\r\n        </div>\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified loading spinner component\r\n *\r\n * @example\r\n * <LoadingSpinner size=\"md\" text=\"Loading data...\" />\r\n */\r\nexport function LoadingSpinner({\r\n  className,\r\n  fullPage = false,\r\n  size = 'md',\r\n  text,\r\n}: LoadingSpinnerProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex items-center justify-center',\r\n        fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',\r\n        className\r\n      )}\r\n    >\r\n      <div className=\"flex flex-col items-center\">\r\n        <Loader2\r\n          className={cn('animate-spin text-primary', spinnerSizeClasses[size])}\r\n        />\r\n        {text && (\r\n          <span\r\n            className={cn(\r\n              'mt-2 text-muted-foreground',\r\n              spinnerTextSizeClasses[size]\r\n            )}\r\n          >\r\n            {text}\r\n          </span>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified skeleton loader component\r\n *\r\n * @example\r\n * <SkeletonLoader variant=\"card\" count={3} />\r\n */\r\nexport function SkeletonLoader({\r\n  className,\r\n  count = 1,\r\n  testId = 'loading-skeleton',\r\n  variant = 'default',\r\n}: SkeletonLoaderProps) {\r\n  // Render card skeleton (for entity cards like vehicles, employees)\r\n  if (variant === 'card') {\r\n    return (\r\n      <div\r\n        className={cn(\r\n          'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',\r\n          className\r\n        )}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div\r\n            className=\"overflow-hidden rounded-lg border bg-card shadow-md\"\r\n            key={i}\r\n          >\r\n            <Skeleton className=\"aspect-[16/10] w-full\" />\r\n            <div className=\"p-5\">\r\n              <Skeleton className=\"mb-1 h-7 w-3/4\" />\r\n              <Skeleton className=\"mb-3 h-4 w-1/2\" />\r\n              <Skeleton className=\"my-3 h-px w-full\" />\r\n              <div className=\"space-y-2.5\">\r\n                {Array.from({ length: 3 })\r\n                  .fill(0)\r\n                  .map((_, j) => (\r\n                    <div className=\"flex items-center\" key={j}>\r\n                      <Skeleton className=\"mr-2.5 size-5 rounded-full\" />\r\n                      <Skeleton className=\"h-5 w-2/3\" />\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render table skeleton\r\n  if (variant === 'table') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        <div className=\"flex gap-4\">\r\n          {Array.from({ length: 3 })\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Skeleton className=\"h-8 flex-1\" key={i} />\r\n            ))}\r\n        </div>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex gap-4\" key={i}>\r\n            {Array.from({ length: 3 })\r\n              .fill(0)\r\n              .map((_, j) => (\r\n                <Skeleton className=\"h-6 flex-1\" key={j} />\r\n              ))}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render list skeleton\r\n  if (variant === 'list') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex items-center gap-4\" key={i}>\r\n            <Skeleton className=\"size-12 rounded-full\" />\r\n            <div className=\"flex-1 space-y-2\">\r\n              <Skeleton className=\"h-4 w-1/3\" />\r\n              <Skeleton className=\"h-4 w-full\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render stats skeleton\r\n  if (variant === 'stats') {\r\n    return (\r\n      <div\r\n        className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"rounded-lg border bg-card p-5 shadow-sm\" key={i}>\r\n            <div className=\"flex justify-between\">\r\n              <Skeleton className=\"h-5 w-1/3\" />\r\n              <Skeleton className=\"size-5 rounded-full\" />\r\n            </div>\r\n            <Skeleton className=\"mt-3 h-8 w-1/2\" />\r\n            <Skeleton className=\"mt-2 h-4 w-2/3\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Default skeleton\r\n  return (\r\n    <div className={cn('space-y-2', className)} data-testid={testId}>\r\n      {new Array(count).fill(0).map((_, i) => (\r\n        <Skeleton className=\"h-5 w-full\" key={i} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AARA;;;;;;;AAaA,gCAAgC;AAChC,MAAM,qBAAkD;IACtD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,qCAAqC;AACrC,MAAM,yBAAsD;IAC1D,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AA+KO,SAAS,WAAc,EAC5B,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,cAAc,EACd,KAAK,EACL,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,OAAO,EACY;IACnB,IAAI,WAAW;QACb,OACE,kCACE,6LAAC;YAAgB,GAAI,aAAa;gBAAE;YAAU,CAAC;YAAG,MAAK;;;;;;IAG7D;IAEA,IAAI,OAAO;QACT,OACE,gCACE,6LAAC;YACE,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC/B,SAAS;YACR,GAAI,WAAW;gBAAE;YAAQ,CAAC;;;;;;IAInC;IAEA,IAAI,CAAC,QAAS,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,GAAI;QACvD,OACE,gCACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;sBACrC,cAAA,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAI7C;IAEA,qBAAO,6LAAC;QAAI,WAAW;kBAAY,SAAS;;;;;;AAC9C;KA1CgB;AA+DT,SAAS,WAAW,EACzB,SAAS,EACT,WAAW,EACX,MAAM,IAAI,EACV,aAAa,EACb,eAAe,EACf,KAAK,EACW;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;YAC/C,sBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;;;;;;;;;;0BAIpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;oBACvD,6BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;;oBACZ,+BACC,6LAAC,+IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,cAAc,IAAI;wBAC7B,MAAM,cAAc,IAAI;wBACxB,SAAS,cAAc,OAAO;kCAE7B,cAAc,IAAI,iBACjB,6LAAC;4BAAE,MAAM,cAAc,IAAI;sCAAG,cAAc,KAAK;;;;;mCAEjD,cAAc,KAAK;;;;;;oBAKxB,iCACC,6LAAC,+IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,gBAAgB,IAAI;wBAC/B,MAAM,gBAAgB,IAAI;wBAC1B,SAAS,gBAAgB,OAAO;kCAE/B,gBAAgB,IAAI,iBACnB,6LAAC;4BAAE,MAAM,gBAAgB,IAAI;sCAAG,gBAAgB,KAAK;;;;;mCAErD,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;AAOnC;MA1DgB;AAkET,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,OAAO,EACW;IAClB,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,SAAQ;;0BAC/C,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,6LAAC,oIAAA,CAAA,aAAU;0BAAC;;;;;;0BACZ,6LAAC,oIAAA,CAAA,mBAAgB;0BACf,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,yBACC,6LAAC,+IAAA,CAAA,eAAY;4BACX,YAAW;4BACX,oBAAM,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BACzB,SAAS;4BACT,MAAK;sCACN;;;;;;;;;;;;;;;;;;;;;;;AAQb;MA1BgB;AAkCT,SAAS,eAAe,EAC7B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,IAAI,EACX,IAAI,EACgB;IACpB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oCACA,YAAY,wDACZ;kBAGF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oNAAA,CAAA,UAAO;oBACN,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B,kBAAkB,CAAC,KAAK;;;;;;gBAEpE,sBACC,6LAAC;oBACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8BACA,sBAAsB,CAAC,KAAK;8BAG7B;;;;;;;;;;;;;;;;;AAMb;MA/BgB;AAuCT,SAAS,eAAe,EAC7B,SAAS,EACT,QAAQ,CAAC,EACT,SAAS,kBAAkB,EAC3B,UAAU,SAAS,EACC;IACpB,mEAAmE;IACnE,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wDACA;YAEF,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,6LAAC;oBACC,WAAU;;sCAGV,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;2CAFkB;;;;;;;;;;;;;;;;;mBAX3C;;;;;;;;;;IAsBf;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;;8BACvD,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;2BAAkB;;;;;;;;;;gBAG3C,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;+BAAkB;;;;;uBAJX;;;;;;;;;;;IAUzC;IAEA,uBAAuB;IACvB,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;sBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;mBAJsB;;;;;;;;;;IAUtD;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;YAC1D,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;mBANwC;;;;;;;;;;IAWtE;IAEA,mBAAmB;IACnB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAY,eAAa;kBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,6LAAC,uIAAA,CAAA,WAAQ;gBAAC,WAAU;eAAkB;;;;;;;;;;AAI9C;MAjHgB", "debugId": null}}, {"offset": {"line": 5376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/components/ui/PageHeader.tsx"], "sourcesContent": ["import type { LucideIcon } from 'lucide-react';\r\n\r\nimport React from 'react';\r\n\r\ninterface PageHeaderProps {\r\n  children?: React.ReactNode; // For action buttons like \"Add New\"\r\n  description?: string;\r\n  icon?: LucideIcon;\r\n  title: string;\r\n}\r\n\r\nexport function PageHeader({\r\n  children,\r\n  description,\r\n  icon: Icon,\r\n  title,\r\n}: PageHeaderProps) {\r\n  return (\r\n    <div className=\"mb-6 flex items-center justify-between border-b border-border/50 pb-4\">\r\n      <div>\r\n        <div className=\"flex items-center gap-3\">\r\n          {Icon && <Icon className=\"size-8 text-primary\" />}\r\n          <h1 className=\"text-3xl font-bold tracking-tight text-foreground\">\r\n            {title}\r\n          </h1>\r\n        </div>\r\n        {description && (\r\n          <p className=\"mt-1 text-muted-foreground\">{description}</p>\r\n        )}\r\n      </div>\r\n      {children && <div className=\"flex items-center gap-2\">{children}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAWO,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACW;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;4BACZ,sBAAQ,6LAAC;gCAAK,WAAU;;;;;;0CACzB,6LAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;;oBAGJ,6BACC,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;YAG9C,0BAAY,6LAAC;gBAAI,WAAU;0BAA2B;;;;;;;;;;;;AAG7D;KAtBgB", "debugId": null}}, {"offset": {"line": 5452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/services/toastService.ts"], "sourcesContent": ["/**\r\n * Unified Generic Toast Service - Following SRP and DRY principles\r\n *\r\n * This service provides a consistent interface for all toast notifications\r\n * across the application, eliminating code duplication and ensuring\r\n * consistent messaging patterns using generics.\r\n */\r\n\r\nimport { toast } from '@/hooks/utils/use-toast';\r\n\r\nexport type ToastVariant = 'default' | 'destructive';\r\n\r\nexport interface ToastOptions {\r\n  title?: string | undefined;\r\n  description?: string | undefined;\r\n  variant?: ToastVariant | undefined;\r\n  duration?: number | undefined;\r\n}\r\n\r\n/**\r\n * Configuration interface for entity-specific toast messages\r\n */\r\nexport interface EntityToastConfig<T = any> {\r\n  entityName: string; // e.g., \"Employee\", \"Vehicle\", \"Service Record\"\r\n  getDisplayName: (entity: T) => string; // Function to get display name from entity\r\n  messages: {\r\n    created: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    updated: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    deleted: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    creationError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    updateError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    deletionError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Base toast service class following SRP\r\n */\r\nclass ToastService {\r\n  /**\r\n   * Show a generic toast notification\r\n   */\r\n  show(options: ToastOptions) {\r\n    return toast({\r\n      title: options.title,\r\n      description: options.description,\r\n      variant: options.variant || 'default',\r\n      ...(options.duration && { duration: options.duration }),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show a success toast notification\r\n   */\r\n  success(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an error toast notification\r\n   */\r\n  error(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'destructive',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an info toast notification\r\n   */\r\n  info(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Generic Entity Toast Service - Works with any entity type T\r\n */\r\nexport class GenericEntityToastService<T = any> extends ToastService {\r\n  private config: EntityToastConfig<T>;\r\n\r\n  constructor(config: EntityToastConfig<T>) {\r\n    super();\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * Show entity created success toast\r\n   */\r\n  entityCreated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.created.title,\r\n      this.config.messages.created.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity updated success toast\r\n   */\r\n  entityUpdated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.updated.title,\r\n      this.config.messages.updated.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deleted success toast\r\n   */\r\n  entityDeleted(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.deleted.title,\r\n      this.config.messages.deleted.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity creation error toast\r\n   */\r\n  entityCreationError(error: string) {\r\n    return this.error(\r\n      this.config.messages.creationError.title,\r\n      this.config.messages.creationError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity update error toast\r\n   */\r\n  entityUpdateError(error: string) {\r\n    return this.error(\r\n      this.config.messages.updateError.title,\r\n      this.config.messages.updateError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deletion error toast\r\n   */\r\n  entityDeletionError(error: string) {\r\n    return this.error(\r\n      this.config.messages.deletionError.title,\r\n      this.config.messages.deletionError.description(error)\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// ENTITY CONFIGURATIONS - Define toast messages for each domain\r\n// =============================================================================\r\n\r\n/**\r\n * Employee entity toast configuration\r\n */\r\nconst employeeToastConfig: EntityToastConfig<{ name: string }> = {\r\n  entityName: 'Employee',\r\n  getDisplayName: employee => employee.name,\r\n  messages: {\r\n    created: {\r\n      title: 'Employee Added',\r\n      description: name =>\r\n        `The employee \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Employee Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Employee Deleted Successfully',\r\n      description: name =>\r\n        `${name} has been permanently removed from the system.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the employee.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the employee.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the employee.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Delegation entity toast configuration\r\n */\r\nconst delegationToastConfig: EntityToastConfig<{\r\n  event?: string;\r\n  location?: string;\r\n}> = {\r\n  entityName: 'Delegation',\r\n  getDisplayName: delegation =>\r\n    delegation.event || delegation.location || 'Delegation',\r\n  messages: {\r\n    created: {\r\n      title: 'Delegation Created',\r\n      description: name =>\r\n        `The delegation \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Delegation Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Delegation Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the delegation.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the delegation.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the delegation.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Vehicle entity toast configuration\r\n */\r\nconst vehicleToastConfig: EntityToastConfig<{ make: string; model: string }> = {\r\n  entityName: 'Vehicle',\r\n  getDisplayName: vehicle => `${vehicle.make} ${vehicle.model}`,\r\n  messages: {\r\n    created: {\r\n      title: 'Vehicle Added',\r\n      description: name =>\r\n        `The vehicle \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Vehicle Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Vehicle Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the vehicle.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the vehicle.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the vehicle.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Task entity toast configuration\r\n */\r\nconst taskToastConfig: EntityToastConfig<{ title?: string; name?: string }> = {\r\n  entityName: 'Task',\r\n  getDisplayName: task => task.title || task.name || 'Task',\r\n  messages: {\r\n    created: {\r\n      title: 'Task Created',\r\n      description: name => `The task \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Task Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Task Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the task.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the task.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the task.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Service Record-specific toast messages following DRY principles\r\n */\r\nexport class ServiceRecordToastService extends ToastService {\r\n  /**\r\n   * Show service record created success toast\r\n   */\r\n  serviceRecordCreated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Added',\r\n      `${serviceType} service for \"${vehicleName}\" has been successfully logged.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record updated success toast\r\n   */\r\n  serviceRecordUpdated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Updated',\r\n      `${serviceType} service for \"${vehicleName}\" has been updated.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deleted success toast\r\n   */\r\n  serviceRecordDeleted(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Deleted',\r\n      `${serviceType} service record for \"${vehicleName}\" has been permanently removed.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record creation error toast\r\n   */\r\n  serviceRecordCreationError(error: string) {\r\n    return this.error(\r\n      'Failed to Log Service Record',\r\n      error || 'An unexpected error occurred while logging the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record update error toast\r\n   */\r\n  serviceRecordUpdateError(error: string) {\r\n    return this.error(\r\n      'Update Failed',\r\n      error || 'An unexpected error occurred while updating the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deletion error toast\r\n   */\r\n  serviceRecordDeletionError(error: string) {\r\n    return this.error(\r\n      'Failed to Delete Service Record',\r\n      error || 'An unexpected error occurred while deleting the service record.'\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// UTILITY FACTORY FUNCTIONS\r\n// =============================================================================\r\n\r\n/**\r\n * Factory function to create a generic entity toast service for any entity type\r\n * Useful for creating toast services for new entities without pre-configuration\r\n */\r\nexport function createEntityToastService<T>(config: EntityToastConfig<T>) {\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n/**\r\n * Factory function to create a simple entity toast service with minimal configuration\r\n * For entities that only need basic CRUD messages\r\n */\r\nexport function createSimpleEntityToastService<T>(\r\n  entityName: string,\r\n  getDisplayName: (entity: T) => string\r\n): GenericEntityToastService<T> {\r\n  const config: EntityToastConfig<T> = {\r\n    entityName,\r\n    getDisplayName,\r\n    messages: {\r\n      created: {\r\n        title: `${entityName} Created`,\r\n        description: (displayName: string) =>\r\n          `The ${entityName.toLowerCase()} \"${displayName}\" has been successfully created.`,\r\n      },\r\n      updated: {\r\n        title: `${entityName} Updated Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been updated.`,\r\n      },\r\n      deleted: {\r\n        title: `${entityName} Deleted Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been permanently removed.`,\r\n      },\r\n      creationError: {\r\n        title: `Failed to Create ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while creating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      updateError: {\r\n        title: 'Update Failed',\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while updating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      deletionError: {\r\n        title: `Failed to Delete ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while deleting the ${entityName.toLowerCase()}.`,\r\n      },\r\n    },\r\n  };\r\n\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n// =============================================================================\r\n// SINGLETON INSTANCES - Pre-configured toast services for each domain\r\n// =============================================================================\r\n\r\n// Base toast service for generic use\r\nexport const toastService = new ToastService();\r\n\r\n// Entity-specific toast services using the generic service\r\nexport const employeeToast = new GenericEntityToastService(employeeToastConfig);\r\nexport const delegationToast = new GenericEntityToastService(\r\n  delegationToastConfig\r\n);\r\nexport const vehicleToast = new GenericEntityToastService(vehicleToastConfig);\r\nexport const taskToast = new GenericEntityToastService(taskToastConfig);\r\nexport const serviceRecordToast = new ServiceRecordToastService();\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;AAED;;AA6CA;;CAEC,GACD,MAAM;IACJ;;GAEC,GACD,KAAK,OAAqB,EAAE;QAC1B,OAAO,CAAA,GAAA,wIAAA,CAAA,QAAK,AAAD,EAAE;YACX,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO,IAAI;YAC5B,GAAI,QAAQ,QAAQ,IAAI;gBAAE,UAAU,QAAQ,QAAQ;YAAC,CAAC;QACxD;IACF;IAEA;;GAEC,GACD,QAAQ,KAAa,EAAE,WAAoB,EAAE;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,MAAM,KAAa,EAAE,WAAoB,EAAE;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,KAAK,KAAa,EAAE,WAAoB,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;AACF;AAKO,MAAM,kCAA2C;IAC9C,OAA6B;IAErC,YAAY,MAA4B,CAAE;QACxC,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;IAEA;;GAEC,GACD,kBAAkB,KAAa,EAAE;QAC/B,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;IAEjD;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;AACF;AAEA,gFAAgF;AAChF,gEAAgE;AAChE,gFAAgF;AAEhF;;CAEC,GACD,MAAM,sBAA2D;IAC/D,YAAY;IACZ,gBAAgB,CAAA,WAAY,SAAS,IAAI;IACzC,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,cAAc,EAAE,KAAK,gCAAgC,CAAC;QAC3D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,GAAG,KAAK,8CAA8C,CAAC;QAC3D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,wBAGD;IACH,YAAY;IACZ,gBAAgB,CAAA,aACd,WAAW,KAAK,IAAI,WAAW,QAAQ,IAAI;IAC7C,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,gBAAgB,EAAE,KAAK,gCAAgC,CAAC;QAC7D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,qBAAyE;IAC7E,YAAY;IACZ,gBAAgB,CAAA,UAAW,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;IAC7D,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,aAAa,EAAE,KAAK,gCAAgC,CAAC;QAC1D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,kBAAwE;IAC5E,YAAY;IACZ,gBAAgB,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI;IACnD,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,CAAC,UAAU,EAAE,KAAK,gCAAgC,CAAC;QAC1E;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAKO,MAAM,kCAAkC;IAC7C;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,wBACA,GAAG,YAAY,cAAc,EAAE,YAAY,+BAA+B,CAAC;IAE/E;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,cAAc,EAAE,YAAY,mBAAmB,CAAC;IAEnE;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,qBAAqB,EAAE,YAAY,+BAA+B,CAAC;IAEtF;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,gCACA,SAAS;IAEb;IAEA;;GAEC,GACD,yBAAyB,KAAa,EAAE;QACtC,OAAO,IAAI,CAAC,KAAK,CACf,iBACA,SAAS;IAEb;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,mCACA,SAAS;IAEb;AACF;AAUO,SAAS,yBAA4B,MAA4B;IACtE,OAAO,IAAI,0BAA6B;AAC1C;AAMO,SAAS,+BACd,UAAkB,EAClB,cAAqC;IAErC,MAAM,SAA+B;QACnC;QACA;QACA,UAAU;YACR,SAAS;gBACP,OAAO,GAAG,WAAW,QAAQ,CAAC;gBAC9B,aAAa,CAAC,cACZ,CAAC,IAAI,EAAE,WAAW,WAAW,GAAG,EAAE,EAAE,YAAY,gCAAgC,CAAC;YACrF;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,kBAAkB,CAAC;YACtC;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,8BAA8B,CAAC;YAClD;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,aAAa;gBACX,OAAO;gBACP,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;QACF;IACF;IAEA,OAAO,IAAI,0BAA6B;AAC1C;AAOO,MAAM,eAAe,IAAI;AAGzB,MAAM,gBAAgB,IAAI,0BAA0B;AACpD,MAAM,kBAAkB,IAAI,0BACjC;AAEK,MAAM,eAAe,IAAI,0BAA0B;AACnD,MAAM,YAAY,IAAI,0BAA0B;AAChD,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 5769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/forms/useFormToast.ts"], "sourcesContent": ["/**\r\n * Form Toast Hook - Integrates generic toast service with form operations\r\n *\r\n * This hook provides a consistent interface for showing toast notifications\r\n * in form components, supporting both generic and entity-specific messaging.\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport {\r\n  type EntityToastConfig,\r\n  type GenericEntityToastService,\r\n  toastService,\r\n  createEntityToastService,\r\n} from '@/lib/services/toastService';\r\n\r\nexport interface FormToastOptions {\r\n  successTitle?: string;\r\n  successDescription?: string;\r\n  errorTitle?: string;\r\n  errorDescription?: string;\r\n}\r\n\r\nexport interface EntityFormToastOptions<T> {\r\n  entityConfig?: EntityToastConfig<T>;\r\n  entityService?: GenericEntityToastService<T>;\r\n}\r\n\r\n/**\r\n * Hook for generic form toast notifications\r\n */\r\nexport function useFormToast() {\r\n  const showSuccess = useCallback((title: string, description?: string) => {\r\n    return toastService.success(title, description);\r\n  }, []);\r\n\r\n  const showError = useCallback((title: string, description?: string) => {\r\n    return toastService.error(title, description);\r\n  }, []);\r\n\r\n  const showInfo = useCallback((title: string, description?: string) => {\r\n    return toastService.info(title, description);\r\n  }, []);\r\n\r\n  const showFormSuccess = useCallback(\r\n    (options?: FormToastOptions) => {\r\n      return showSuccess(\r\n        options?.successTitle || 'Success',\r\n        options?.successDescription || 'Operation completed successfully'\r\n      );\r\n    },\r\n    [showSuccess]\r\n  );\r\n\r\n  const showFormError = useCallback(\r\n    (error: Error | string, options?: FormToastOptions) => {\r\n      const errorMessage = error instanceof Error ? error.message : error;\r\n      return showError(\r\n        options?.errorTitle || 'Error',\r\n        options?.errorDescription ||\r\n          errorMessage ||\r\n          'An unexpected error occurred'\r\n      );\r\n    },\r\n    [showError]\r\n  );\r\n\r\n  return {\r\n    showSuccess,\r\n    showError,\r\n    showInfo,\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for entity-specific form toast notifications\r\n */\r\nexport function useEntityFormToast<T>(\r\n  entityConfig?: EntityToastConfig<T>,\r\n  entityService?: GenericEntityToastService<T>\r\n) {\r\n  const { showFormSuccess, showFormError } = useFormToast();\r\n\r\n  // Create or use provided entity service\r\n  const entityToastService =\r\n    entityService ||\r\n    (entityConfig ? createEntityToastService(entityConfig) : null);\r\n\r\n  const showEntityCreated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityCreated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Created',\r\n        successDescription: 'Item has been created successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityUpdated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityUpdated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Updated',\r\n        successDescription: 'Item has been updated successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityDeleted = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityDeleted(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Deleted',\r\n        successDescription: 'Item has been deleted successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityCreationError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityCreationError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Creation Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityUpdateError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityUpdateError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Update Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityDeletionError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityDeletionError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Deletion Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  return {\r\n    showEntityCreated,\r\n    showEntityUpdated,\r\n    showEntityDeleted,\r\n    showEntityCreationError,\r\n    showEntityUpdateError,\r\n    showEntityDeletionError,\r\n    // Also expose generic methods\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for predefined entity toast services\r\n */\r\nexport function usePredefinedEntityToast(\r\n  entityType: 'employee' | 'vehicle' | 'task' | 'delegation'\r\n) {\r\n  let entityService: GenericEntityToastService<any>;\r\n\r\n  // Lazy import to avoid circular dependencies\r\n  switch (entityType) {\r\n    case 'employee':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').employeeToast;\r\n      break;\r\n    case 'vehicle':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').vehicleToast;\r\n      break;\r\n    case 'task':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').taskToast;\r\n      break;\r\n    case 'delegation':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').delegationToast;\r\n      break;\r\n    default:\r\n      throw new Error(`Unknown entity type: ${entityType}`);\r\n  }\r\n\r\n  return useEntityFormToast(undefined, entityService);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AAEA;;;;AAsBO,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,OAAe;YAC9C,OAAO,yIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,OAAO;QACrC;gDAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC,OAAe;YAC5C,OAAO,yIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;QACnC;8CAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,OAAe;YAC3C,OAAO,yIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO;QAClC;6CAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAChC,CAAC;YACC,OAAO,YACL,SAAS,gBAAgB,WACzB,SAAS,sBAAsB;QAEnC;oDACA;QAAC;KAAY;IAGf,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAC9B,CAAC,OAAuB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,UACL,SAAS,cAAc,SACvB,SAAS,oBACP,gBACA;QAEN;kDACA;QAAC;KAAU;IAGb,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GA3CgB;AAgDT,SAAS,mBACd,YAAmC,EACnC,aAA4C;;IAE5C,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG;IAE3C,wCAAwC;IACxC,MAAM,qBACJ,iBACA,CAAC,eAAe,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE,gBAAgB,IAAI;IAE/D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAClC,CAAC;YACC,IAAI,oBAAoB;gBACtB,OAAO,mBAAmB,aAAa,CAAC;YAC1C;YACA,OAAO,gBAAgB;gBACrB,cAAc;gBACd,oBAAoB;YACtB;QACF;4DACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAClC,CAAC;YACC,IAAI,oBAAoB;gBACtB,OAAO,mBAAmB,aAAa,CAAC;YAC1C;YACA,OAAO,gBAAgB;gBACrB,cAAc;gBACd,oBAAoB;YACtB;QACF;4DACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAClC,CAAC;YACC,IAAI,oBAAoB;gBACtB,OAAO,mBAAmB,aAAa,CAAC;YAC1C;YACA,OAAO,gBAAgB;gBACrB,cAAc;gBACd,oBAAoB;YACtB;QACF;4DACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEACxC,CAAC;YACC,IAAI,oBAAoB;gBACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,OAAO,mBAAmB,mBAAmB,CAAC;YAChD;YACA,OAAO,cAAc,OAAO;gBAAE,YAAY;YAAkB;QAC9D;kEACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEACtC,CAAC;YACC,IAAI,oBAAoB;gBACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,OAAO,mBAAmB,iBAAiB,CAAC;YAC9C;YACA,OAAO,cAAc,OAAO;gBAAE,YAAY;YAAgB;QAC5D;gEACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEACxC,CAAC;YACC,IAAI,oBAAoB;gBACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,OAAO,mBAAmB,mBAAmB,CAAC;YAChD;YACA,OAAO,cAAc,OAAO;gBAAE,YAAY;YAAkB;QAC9D;kEACA;QAAC;QAAoB;KAAc;IAGrC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,8BAA8B;QAC9B;QACA;IACF;AACF;IA9FgB;;QAI6B;;;AA+FtC,SAAS,yBACd,UAA0D;;IAE1D,IAAI;IAEJ,6CAA6C;IAC7C,OAAQ;QACN,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,gGAAuC,aAAa;YACpE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,gGAAuC,YAAY;YACnE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,gGAAuC,SAAS;YAChE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,gGAAuC,eAAe;YACtE;QACF;YACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,OAAO,mBAAmB,WAAW;AACvC;IA5BgB;;QA2BP", "debugId": null}}, {"offset": {"line": 5971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/services/WebSocketManager.ts"], "sourcesContent": ["/**\r\n * @file Unified WebSocket Manager for WorkHub Application\r\n * Provides centralized WebSocket connection management with domain-specific channels\r\n * Follows SRP and DRY principles with smart fallback strategies\r\n * @module services/WebSocketManager\r\n */\r\n\r\nimport type { Socket } from 'socket.io-client';\r\n\r\nimport { io } from 'socket.io-client';\r\n\r\nimport { getEnvironmentConfig } from '../config/environment';\r\nimport { supabase } from '../supabase';\r\nimport { getTokenRefreshService } from './TokenRefreshService';\r\n/*import logger from '../utils/logger';\r\n\r\n/**\r\n * WebSocket connection states\r\n */\r\nexport type ConnectionState =\r\n  | 'connected'\r\n  | 'connecting'\r\n  | 'disconnected'\r\n  | 'error'\r\n  | 'reconnecting';\r\n\r\n/**\r\n * Domain-specific channels for organized event management\r\n */\r\nexport type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';\r\n\r\n/**\r\n * Event subscription callback type\r\n */\r\nexport type EventCallback<T = any> = (data: T) => void;\r\n\r\n/**\r\n * WebSocket configuration options\r\n */\r\nexport interface WebSocketConfig {\r\n  autoConnect?: boolean;\r\n  reconnectAttempts?: number;\r\n  reconnectDelay?: number;\r\n  timeout?: number;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Unified WebSocket Manager\r\n * Implements Singleton pattern for single connection per application\r\n * Provides domain-specific channels and centralized subscription management\r\n */\r\nexport class WebSocketManager {\r\n  private static instance: null | WebSocketManager = null;\r\n  private readonly config: Required<WebSocketConfig>;\r\n  private connectionState: ConnectionState = 'disconnected';\r\n  private reconnectAttempts = 0;\r\n  private socket: null | Socket = null;\r\n  private readonly stateListeners = new Set<(state: ConnectionState) => void>();\r\n  private readonly subscriptions = new Map<string, Set<EventCallback>>();\r\n\r\n  private constructor(config: WebSocketConfig = {}) {\r\n    this.config = {\r\n      autoConnect: config.autoConnect ?? true,\r\n      reconnectAttempts: config.reconnectAttempts ?? 5,\r\n      reconnectDelay: config.reconnectDelay ?? 1000,\r\n      timeout: config.timeout ?? 10_000,\r\n      url:\r\n        config.url ??\r\n        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??\r\n        getEnvironmentConfig()\r\n          .wsUrl.replace('ws://', 'http://')\r\n          .replace('wss://', 'https://'),\r\n    };\r\n\r\n    if (this.config.autoConnect) {\r\n      this.connect();\r\n    }\r\n\r\n    // Subscribe to token refresh events\r\n    this.setupTokenRefreshHandling();\r\n  }\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  public static getInstance(config?: WebSocketConfig): WebSocketManager {\r\n    WebSocketManager.instance ??= new WebSocketManager(config);\r\n    return WebSocketManager.instance;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  public async connect(): Promise<void> {\r\n    if (this.socket?.connected) {\r\n      console.debug('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('connecting');\r\n\r\n    try {\r\n      // Get current session and token for authentication\r\n      const {\r\n        data: { session },\r\n        error,\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (error) {\r\n        console.warn('Failed to get session for WebSocket connection:', error);\r\n      }\r\n\r\n      const connectionOptions: any = {\r\n        forceNew: true,\r\n        timeout: this.config.timeout,\r\n        transports: ['websocket', 'polling'],\r\n        withCredentials: true, // Ensure cookies are sent with WebSocket handshake\r\n      };\r\n\r\n      // Add authentication token if available\r\n      if (session?.access_token) {\r\n        connectionOptions.auth = {\r\n          token: session.access_token,\r\n        };\r\n        console.debug('🔐 WebSocket connecting with authentication token');\r\n\r\n        // Validate token expiration\r\n        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;\r\n        const now = Date.now();\r\n        const timeUntilExpiry = tokenExpiry - now;\r\n\r\n        if (timeUntilExpiry <= 60_000) {\r\n          // Less than 1 minute\r\n          console.warn('⚠️ WebSocket token expires soon, may need refresh');\r\n        }\r\n      } else {\r\n        console.warn(\r\n          '⚠️ WebSocket connecting without authentication token - connection may fail'\r\n        );\r\n      }\r\n\r\n      this.socket = io(this.config.url, connectionOptions);\r\n\r\n      this.setupEventHandlers();\r\n    } catch (error) {\r\n      console.error('Failed to connect WebSocket:', error);\r\n      this.setConnectionState('error');\r\n      this.scheduleReconnect();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  public destroy(): void {\r\n    this.disconnect();\r\n    this.subscriptions.clear();\r\n    this.stateListeners.clear();\r\n    WebSocketManager.instance = null;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n    this.setConnectionState('disconnected');\r\n    this.reconnectAttempts = 0;\r\n  }\r\n\r\n  /**\r\n   * Emit event to specific domain channel\r\n   */\r\n  public emit(channel: DomainChannel, event: string, data?: any): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit(event, data);\r\n  }\r\n\r\n  /**\r\n   * Get current connection state\r\n   */\r\n  public getConnectionState(): ConnectionState {\r\n    return this.connectionState;\r\n  }\r\n\r\n  /**\r\n   * Check if WebSocket is connected\r\n   */\r\n  public isConnected(): boolean {\r\n    return (\r\n      this.connectionState === 'connected' && this.socket?.connected === true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Join domain-specific room\r\n   */\r\n  public joinRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot join room ${room} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('join-room', room);\r\n  }\r\n\r\n  /**\r\n   * Leave domain-specific room\r\n   */\r\n  public leaveRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('leave-room', room);\r\n  }\r\n\r\n  /**\r\n   * Subscribe to connection state changes\r\n   */\r\n  public onStateChange(callback: (state: ConnectionState) => void): () => void {\r\n    this.stateListeners.add(callback);\r\n\r\n    return () => {\r\n      this.stateListeners.delete(callback);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to domain-specific events\r\n   */\r\n  public subscribe<T = any>(\r\n    channel: DomainChannel,\r\n    event: string,\r\n    callback: EventCallback<T>\r\n  ): () => void {\r\n    const eventKey = `${channel}:${event}`;\r\n\r\n    if (!this.subscriptions.has(eventKey)) {\r\n      this.subscriptions.set(eventKey, new Set());\r\n    }\r\n\r\n    this.subscriptions.get(eventKey)!.add(callback);\r\n\r\n    // Set up socket listener if connected\r\n    if (this.socket?.connected && event) {\r\n      this.socket.on(event, callback);\r\n    }\r\n\r\n    // Return unsubscribe function\r\n    return () => {\r\n      const callbacks = this.subscriptions.get(eventKey);\r\n      if (callbacks) {\r\n        callbacks.delete(callback);\r\n        if (callbacks.size === 0) {\r\n          this.subscriptions.delete(eventKey);\r\n        }\r\n      }\r\n\r\n      if (this.socket && event) {\r\n        this.socket.off(event, callback);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Handle authentication errors by triggering token refresh\r\n   */\r\n  private handleAuthenticationError(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    console.log('🔐 Handling WebSocket authentication error...');\r\n\r\n    // Disconnect current socket to prevent further auth errors\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n\r\n    // Attempt to refresh token\r\n    tokenRefreshService\r\n      .refreshNow()\r\n      .then(success => {\r\n        if (success) {\r\n          console.log(\r\n            '🔄 Token refresh successful, retrying WebSocket connection'\r\n          );\r\n          // The reconnection will be handled by setupTokenRefreshHandling\r\n        } else {\r\n          console.error('🔄 Token refresh failed, scheduling normal reconnect');\r\n          this.scheduleReconnect();\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('🔄 Token refresh error:', error);\r\n        this.scheduleReconnect();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resubscribe to all events after reconnection\r\n   */\r\n  private resubscribeToEvents(): void {\r\n    if (!this.socket) return;\r\n\r\n    for (const [eventKey, callbacks] of this.subscriptions) {\r\n      const [, event] = eventKey.split(':');\r\n      for (const callback of callbacks) {\r\n        if (event) {\r\n          this.socket!.on(event, callback);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection with exponential backoff\r\n   */\r\n  private scheduleReconnect(): void {\r\n    if (this.reconnectAttempts >= this.config.reconnectAttempts) {\r\n      console.error('Max reconnection attempts reached');\r\n      this.setConnectionState('error');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('reconnecting');\r\n    this.reconnectAttempts++;\r\n\r\n    const delay =\r\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\r\n\r\n    setTimeout(() => {\r\n      console.info(\r\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`\r\n      );\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * Set connection state and notify listeners\r\n   */\r\n  private setConnectionState(state: ConnectionState): void {\r\n    if (this.connectionState !== state) {\r\n      this.connectionState = state;\r\n      for (const listener of this.stateListeners) listener(state);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup socket event handlers\r\n   */\r\n  private setupEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      console.info('WebSocket connected');\r\n      this.setConnectionState('connected');\r\n      this.reconnectAttempts = 0;\r\n      this.resubscribeToEvents();\r\n    });\r\n\r\n    this.socket.on('disconnect', reason => {\r\n      console.warn('WebSocket disconnected:', reason);\r\n      this.setConnectionState('disconnected');\r\n\r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, don't reconnect automatically\r\n        return;\r\n      }\r\n\r\n      this.scheduleReconnect();\r\n    });\r\n\r\n    this.socket.on('connect_error', error => {\r\n      console.error('WebSocket connection error:', error);\r\n      this.setConnectionState('error');\r\n\r\n      // Check if error is authentication-related\r\n      if (\r\n        error.message?.includes('Authentication') ||\r\n        error.message?.includes('token') ||\r\n        error.message?.includes('No token provided') ||\r\n        error.message?.includes('Unauthorized')\r\n      ) {\r\n        console.warn(\r\n          '🔐 Authentication error detected, attempting token refresh'\r\n        );\r\n        this.handleAuthenticationError();\r\n      } else {\r\n        this.scheduleReconnect();\r\n      }\r\n    });\r\n\r\n    // Listen for authentication errors from the server\r\n    this.socket.on('auth_error', errorData => {\r\n      console.error('🔐 Server authentication error:', errorData);\r\n      this.handleAuthenticationError();\r\n    });\r\n\r\n    // Listen for token refresh requests from server\r\n    this.socket.on('token_refresh_required', () => {\r\n      console.warn('🔄 Server requested token refresh');\r\n      this.handleAuthenticationError();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup token refresh event handling\r\n   */\r\n  private setupTokenRefreshHandling(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    tokenRefreshService.subscribe((event, _data) => {\r\n      switch (event) {\r\n        case 'critical_refresh_failed': {\r\n          console.error(\r\n            '🔄 Critical token refresh failure, disconnecting WebSocket'\r\n          );\r\n          this.disconnect();\r\n          this.setConnectionState('error');\r\n          break;\r\n        }\r\n\r\n        case 'refresh_failed': {\r\n          console.error(\r\n            '🔄 Token refresh failed, WebSocket may lose connection'\r\n          );\r\n          break;\r\n        }\r\n\r\n        case 'refresh_success': {\r\n          console.log(\r\n            '🔄 Token refreshed, reconnecting WebSocket with new token'\r\n          );\r\n          // Disconnect current connection and reconnect with new token\r\n          if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n          }\r\n          // Reconnect with fresh token\r\n          setTimeout(() => this.connect(), 500);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the singleton WebSocket manager instance\r\n */\r\nexport const getWebSocketManager = (\r\n  config?: WebSocketConfig\r\n): WebSocketManager => {\r\n  return WebSocketManager.getInstance(config);\r\n};\r\n\r\n/**\r\n * Hook for WebSocket connection state\r\n */\r\nexport const useWebSocketState = () => {\r\n  const manager = getWebSocketManager();\r\n  return {\r\n    connectionState: manager.getConnectionState(),\r\n    isConnected: manager.isConnected(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAgEO;AA5DR;AAAA;AAEA;AACA;AACA;;;;;AAuCO,MAAM;IACX,OAAe,WAAoC,KAAK;IACvC,OAAkC;IAC3C,kBAAmC,eAAe;IAClD,oBAAoB,EAAE;IACtB,SAAwB,KAAK;IACpB,iBAAiB,IAAI,MAAwC;IAC7D,gBAAgB,IAAI,MAAkC;IAEvE,YAAoB,SAA0B,CAAC,CAAC,CAAE;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,OAAO,WAAW,IAAI;YACnC,mBAAmB,OAAO,iBAAiB,IAAI;YAC/C,gBAAgB,OAAO,cAAc,IAAI;YACzC,SAAS,OAAO,OAAO,IAAI;YAC3B,KACE,OAAO,GAAG,IACV,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,IACrC,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,IAChB,KAAK,CAAC,OAAO,CAAC,SAAS,WACvB,OAAO,CAAC,UAAU;QACzB;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,OAAO;QACd;QAEA,oCAAoC;QACpC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,OAAc,YAAY,MAAwB,EAAoB;QACpE,iBAAiB,QAAQ,KAAK,IAAI,iBAAiB;QACnD,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QAExB,IAAI;YACF,mDAAmD;YACnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EACjB,KAAK,EACN,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,mDAAmD;YAClE;YAEA,MAAM,oBAAyB;gBAC7B,UAAU;gBACV,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,iBAAiB;YACnB;YAEA,wCAAwC;YACxC,IAAI,SAAS,cAAc;gBACzB,kBAAkB,IAAI,GAAG;oBACvB,OAAO,QAAQ,YAAY;gBAC7B;gBACA,QAAQ,KAAK,CAAC;gBAEd,4BAA4B;gBAC5B,MAAM,cAAc,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO;gBACrE,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,kBAAkB,cAAc;gBAEtC,IAAI,mBAAmB,QAAQ;oBAC7B,qBAAqB;oBACrB,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CACV;YAEJ;YAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAElC,IAAI,CAAC,kBAAkB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,iBAAiB,QAAQ,GAAG;IAC9B;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAO,KAAK,OAAsB,EAAE,KAAa,EAAE,IAAU,EAAQ;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,0BAA0B,CAAC;YACxE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAsC;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OACE,IAAI,CAAC,eAAe,KAAK,eAAe,IAAI,CAAC,MAAM,EAAE,cAAc;IAEvE;IAEA;;GAEC,GACD,AAAO,SAAS,IAAY,EAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,0BAA0B,CAAC;YACjE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA;;GAEC,GACD,AAAO,UAAU,IAAY,EAAQ;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IACjC;IAEA;;GAEC,GACD,AAAO,cAAc,QAA0C,EAAc;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAO,UACL,OAAsB,EACtB,KAAa,EACb,QAA0B,EACd;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,OAAO;QAEtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI;QACvC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAW,GAAG,CAAC;QAEtC,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,OAAO;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;QAEA,8BAA8B;QAC9B,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACzC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5B;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YACzB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;QAEjD,QAAQ,GAAG,CAAC;QAEZ,2DAA2D;QAC3D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,2BAA2B;QAC3B,oBACG,UAAU,GACV,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS;gBACX,QAAQ,GAAG,CACT;YAEF,gEAAgE;YAClE,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,iBAAiB;YACxB;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,iBAAiB;QACxB;IACJ;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,KAAK,MAAM,CAAC,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,CAAE;YACtD,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,OAAO;oBACT,IAAI,CAAC,MAAM,CAAE,EAAE,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAC3D,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QACJ,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEpE,WAAW;YACT,QAAQ,IAAI,CACV,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAsB,EAAQ;QACvD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAE,SAAS;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,IAAI,CAAC,2BAA2B;YACxC,IAAI,CAAC,kBAAkB,CAAC;YAExB,IAAI,WAAW,wBAAwB;gBACrC,6DAA6D;gBAC7D;YACF;YAEA,IAAI,CAAC,iBAAiB;QACxB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAA;YAC9B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,kBAAkB,CAAC;YAExB,2CAA2C;YAC3C,IACE,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,wBACxB,MAAM,OAAO,EAAE,SAAS,iBACxB;gBACA,QAAQ,IAAI,CACV;gBAEF,IAAI,CAAC,yBAAyB;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB;YACxB;QACF;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,yBAAyB;QAChC;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0BAA0B;YACvC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,yBAAyB;QAChC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;QAEjD,oBAAoB,SAAS,CAAC,CAAC,OAAO;YACpC,OAAQ;gBACN,KAAK;oBAA2B;wBAC9B,QAAQ,KAAK,CACX;wBAEF,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,kBAAkB,CAAC;wBACxB;oBACF;gBAEA,KAAK;oBAAkB;wBACrB,QAAQ,KAAK,CACX;wBAEF;oBACF;gBAEA,KAAK;oBAAmB;wBACtB,QAAQ,GAAG,CACT;wBAEF,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,IAAI,CAAC,MAAM,CAAC,UAAU;4BACtB,IAAI,CAAC,MAAM,GAAG;wBAChB;wBACA,6BAA6B;wBAC7B,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI;wBACjC;oBACF;YACF;QACF;IACF;AACF;AAKO,MAAM,sBAAsB,CACjC;IAEA,OAAO,iBAAiB,WAAW,CAAC;AACtC;AAKO,MAAM,oBAAoB;IAC/B,MAAM,UAAU;IAChB,OAAO;QACL,iBAAiB,QAAQ,kBAAkB;QAC3C,aAAa,QAAQ,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 6310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/api/useSmartQuery.ts"], "sourcesContent": ["/**\r\n * @file Smart Query Hook with WebSocket Integration\r\n * Automatically disables polling when WebSocket is connected\r\n * Follows modern best practices for real-time data management\r\n * @module hooks/useSmartQuery\r\n */\r\n\r\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\r\n\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useEffect, useState } from 'react';\r\n\r\nimport type { DomainChannel } from '../../lib/services/WebSocketManager';\r\n\r\nimport { getWebSocketManager } from '../../lib/services/WebSocketManager';\r\n\r\n/**\r\n * Mapping of domain channels to Socket.IO room names\r\n * This ensures the frontend joins the correct rooms that the backend emits events to\r\n */\r\nconst CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {\r\n  crud: 'entity-updates',\r\n  notifications: 'notifications-monitoring',\r\n  reliability: 'reliability-monitoring',\r\n  system: 'system-monitoring',\r\n} as const;\r\n\r\n/**\r\n * Smart query configuration for WebSocket integration\r\n * @template T - The data type returned by the query function\r\n */\r\nexport interface SmartQueryConfig<T = unknown> {\r\n  /**\r\n   * Domain channel for WebSocket events\r\n   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING\r\n   */\r\n  channel: DomainChannel;\r\n  /** Whether to enable fallback polling when WebSocket is disconnected */\r\n  enableFallback?: boolean;\r\n  /** Whether to enable WebSocket integration and room joining */\r\n  enableWebSocket?: boolean;\r\n  /** Events that should trigger data refetch when received via WebSocket */\r\n  events: string[];\r\n  /** Fallback polling interval when WebSocket is disconnected (ms) */\r\n  fallbackInterval?: number;\r\n}\r\n\r\n/**\r\n * Hook for CRUD operations with smart real-time updates\r\n */\r\nexport function useCrudQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  entityType: string,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'crud',\r\n      events: [\r\n        `${entityType}:created`,\r\n        `${entityType}:updated`,\r\n        `${entityType}:deleted`,\r\n        `refresh:${entityType}`,\r\n      ],\r\n      fallbackInterval: 30_000,\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for system notifications with smart real-time updates\r\n */\r\nexport function useNotificationQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'notifications',\r\n      events: ['notification-created', 'notification-updated'],\r\n      fallbackInterval: 60_000, // 1 minute for notifications\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for reliability monitoring with smart real-time updates\r\n */\r\nexport function useReliabilityQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  // Increased intervals to reduce aggressive polling and cancellations\r\n  const intervalMap = {\r\n    alerts: 30_000, // 30 seconds for alerts (was 10s)\r\n    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)\r\n    health: 45_000, // 45 seconds for health (was 15s)\r\n    metrics: 60_000, // 60 seconds for metrics (was 30s)\r\n  };\r\n\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Join reliability monitoring room when WebSocket is connected\r\n  useEffect(() => {\r\n    if (webSocketManager.isConnected()) {\r\n      console.debug(\r\n        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`\r\n      );\r\n      webSocketManager.joinRoom('reliability-monitoring');\r\n    }\r\n\r\n    // Subscribe to connection state changes to join room when connected\r\n    const unsubscribe = webSocketManager.onStateChange(state => {\r\n      if (state === 'connected') {\r\n        console.debug(\r\n          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`\r\n        );\r\n        webSocketManager.joinRoom('reliability-monitoring');\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      // Leave room when component unmounts\r\n      if (webSocketManager.isConnected()) {\r\n        webSocketManager.leaveRoom('reliability-monitoring');\r\n      }\r\n    };\r\n  }, [webSocketManager, monitoringType]);\r\n\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'reliability',\r\n      events: [\r\n        `${monitoringType}-update`,\r\n        `${monitoringType}-created`,\r\n        `${monitoringType}-resolved`,\r\n      ],\r\n      fallbackInterval: intervalMap[monitoringType],\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Smart Query Hook with Socket.IO Room Management\r\n *\r\n * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.\r\n * This hook automatically:\r\n * - Joins the appropriate Socket.IO room based on the domain channel\r\n * - Subscribes to WebSocket events for real-time data updates\r\n * - Switches between WebSocket and polling based on connection state\r\n * - Handles room cleanup when component unmounts\r\n *\r\n * **Room Mapping:**\r\n * - `crud` channel → `entity-updates` room\r\n * - `reliability` channel → `reliability-monitoring` room\r\n * - `notifications` channel → `notifications-monitoring` room\r\n * - `system` channel → `system-monitoring` room\r\n *\r\n * @template T - The data type returned by the query function\r\n * @template E - The error type for failed queries\r\n * @param queryKey - React Query key for caching and invalidation\r\n * @param queryFn - Data fetching function that returns a Promise<T>\r\n * @param config - Smart query configuration including channel and events\r\n * @param options - Additional React Query options (merged with smart defaults)\r\n * @returns Enhanced query result with WebSocket integration and connection state\r\n */\r\nexport function useSmartQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  config: SmartQueryConfig<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n): UseQueryResult<T, E> & {\r\n  isUsingFallback: boolean;\r\n  isWebSocketConnected: boolean;\r\n} {\r\n  const {\r\n    channel,\r\n    enableFallback = true,\r\n    enableWebSocket = true,\r\n    events,\r\n    fallbackInterval = 30_000,\r\n  } = config;\r\n\r\n  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Track WebSocket connection state\r\n  useEffect(() => {\r\n    const updateConnectionState = () => {\r\n      setIsWebSocketConnected(webSocketManager.isConnected());\r\n    };\r\n\r\n    // Initial state\r\n    updateConnectionState();\r\n\r\n    // Subscribe to state changes\r\n    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);\r\n\r\n    return unsubscribe;\r\n  }, [webSocketManager]);\r\n\r\n  // Determine if we should use fallback polling\r\n  const isUsingFallback =\r\n    enableFallback && (!enableWebSocket || !isWebSocketConnected);\r\n\r\n  // Configure React Query options based on WebSocket state\r\n  const queryOptions: UseQueryOptions<T, E> = {\r\n    // Longer cache time for better performance\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    queryFn,\r\n    queryKey,\r\n    // Disable polling when WebSocket is connected\r\n    refetchInterval: isUsingFallback ? fallbackInterval : false,\r\n    refetchOnReconnect: true, // Always refetch on network reconnect\r\n    // Enable background refetch only when using fallback\r\n    refetchOnWindowFocus: isUsingFallback,\r\n    // Shorter stale time when using WebSocket (real-time updates)\r\n    staleTime: isWebSocketConnected ? 0 : 30_000,\r\n    ...options,\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryResult = useQuery<T, E>(queryOptions);\r\n\r\n  // Manage Socket.IO room joining/leaving based on channel\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected) {\r\n      return;\r\n    }\r\n\r\n    const roomName = CHANNEL_ROOM_MAPPING[channel];\r\n    if (!roomName) {\r\n      console.warn(\r\n        `[SmartQuery] No room mapping found for channel: ${channel}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Join the appropriate room for this channel\r\n    try {\r\n      webSocketManager.joinRoom(roomName);\r\n      console.log(\r\n        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`\r\n      );\r\n    } catch (error) {\r\n      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);\r\n    }\r\n\r\n    // Cleanup: leave room when component unmounts or dependencies change\r\n    return () => {\r\n      try {\r\n        webSocketManager.leaveRoom(roomName);\r\n        console.log(\r\n          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`\r\n        );\r\n      } catch (error) {\r\n        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);\r\n      }\r\n    };\r\n  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);\r\n\r\n  // Subscribe to WebSocket events for real-time updates\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const unsubscribers: (() => void)[] = [];\r\n\r\n    // Subscribe to each event\r\n    for (const event of events) {\r\n      const unsubscribe = webSocketManager.subscribe(\r\n        channel,\r\n        event,\r\n        (data: unknown) => {\r\n          console.log(\r\n            `[SmartQuery] WebSocket event received: ${channel}:${event}`,\r\n            data\r\n          );\r\n\r\n          // Invalidate the specific query to trigger refetch\r\n          queryClient.invalidateQueries({ queryKey });\r\n        }\r\n      );\r\n\r\n      unsubscribers.push(unsubscribe);\r\n    }\r\n\r\n    return () => {\r\n      for (const unsubscribe of unsubscribers) unsubscribe();\r\n    };\r\n  }, [\r\n    enableWebSocket,\r\n    isWebSocketConnected,\r\n    events,\r\n    channel,\r\n    webSocketManager,\r\n    queryClient,\r\n    queryKey,\r\n  ]);\r\n\r\n  return {\r\n    ...queryResult,\r\n    isUsingFallback,\r\n    isWebSocketConnected,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for system-wide events with smart real-time updates\r\n */\r\nexport function useSystemQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'system',\r\n      events: ['system-update', 'config-changed'],\r\n      fallbackInterval: 120_000, // 2 minutes for system events\r\n    },\r\n    options\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAID;AAAA;AACA;AAIA;;;;;AAEA;;;CAGC,GACD,MAAM,uBAAsD;IAC1D,MAAM;IACN,eAAe;IACf,aAAa;IACb,QAAQ;AACV;AAyBO,SAAS,aACd,QAAmB,EACnB,OAAyB,EACzB,UAAkB,EAClB,OAA6D;;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,CAAC,QAAQ,EAAE,YAAY;SACxB;QACD,kBAAkB;IACpB,GACA;AAEJ;GArBgB;;QAMP;;;AAoBF,SAAS,qBACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAwB;SAAuB;QACxD,kBAAkB;IACpB,GACA;AAEJ;IAfgB;;QAKP;;;AAeF,SAAS,oBACd,QAAmB,EACnB,OAAyB,EACzB,cAAoE,EACpE,OAA6D;;IAE7D,qEAAqE;IACrE,MAAM,cAAc;QAClB,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;IAE3C,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,iBAAiB,WAAW,IAAI;gBAClC,QAAQ,KAAK,CACX,CAAC,2DAA2D,EAAE,gBAAgB;gBAEhF,iBAAiB,QAAQ,CAAC;YAC5B;YAEA,oEAAoE;YACpE,MAAM,cAAc,iBAAiB,aAAa;6DAAC,CAAA;oBACjD,IAAI,UAAU,aAAa;wBACzB,QAAQ,KAAK,CACX,CAAC,gFAAgF,EAAE,gBAAgB;wBAErG,iBAAiB,QAAQ,CAAC;oBAC5B;gBACF;;YAEA;iDAAO;oBACL;oBACA,qCAAqC;oBACrC,IAAI,iBAAiB,WAAW,IAAI;wBAClC,iBAAiB,SAAS,CAAC;oBAC7B;gBACF;;QACF;wCAAG;QAAC;QAAkB;KAAe;IAErC,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,eAAe,OAAO,CAAC;YAC1B,GAAG,eAAe,QAAQ,CAAC;YAC3B,GAAG,eAAe,SAAS,CAAC;SAC7B;QACD,kBAAkB,WAAW,CAAC,eAAe;IAC/C,GACA;AAEJ;IA1DgB;;QA4CP;;;AAwCF,SAAS,cACd,QAAmB,EACnB,OAAyB,EACzB,MAA2B,EAC3B,OAA6D;;IAK7D,MAAM,EACJ,OAAO,EACP,iBAAiB,IAAI,EACrB,kBAAkB,IAAI,EACtB,MAAM,EACN,mBAAmB,MAAM,EAC1B,GAAG;IAEJ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,mBAAmB,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;IAE3C,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;iEAAwB;oBAC5B,wBAAwB,iBAAiB,WAAW;gBACtD;;YAEA,gBAAgB;YAChB;YAEA,6BAA6B;YAC7B,MAAM,cAAc,iBAAiB,aAAa,CAAC;YAEnD,OAAO;QACT;kCAAG;QAAC;KAAiB;IAErB,8CAA8C;IAC9C,MAAM,kBACJ,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,oBAAoB;IAE9D,yDAAyD;IACzD,MAAM,eAAsC;QAC1C,2CAA2C;QAC3C,QAAQ,KAAK,KAAK;QAClB;QACA;QACA,8CAA8C;QAC9C,iBAAiB,kBAAkB,mBAAmB;QACtD,oBAAoB;QACpB,qDAAqD;QACrD,sBAAsB;QACtB,8DAA8D;QAC9D,WAAW,uBAAuB,IAAI;QACtC,GAAG,OAAO;IACZ;IAEA,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAQ;IAEnC,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;gBAC7C;YACF;YAEA,MAAM,WAAW,oBAAoB,CAAC,QAAQ;YAC9C,IAAI,CAAC,UAAU;gBACb,QAAQ,IAAI,CACV,CAAC,gDAAgD,EAAE,SAAS;gBAE9D;YACF;YAEA,6CAA6C;YAC7C,IAAI;gBACF,iBAAiB,QAAQ,CAAC;gBAC1B,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,cAAc,EAAE,SAAS;YAEnE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE;YAEA,qEAAqE;YACrE;2CAAO;oBACL,IAAI;wBACF,iBAAiB,SAAS,CAAC;wBAC3B,QAAQ,GAAG,CACT,CAAC,wBAAwB,EAAE,SAAS,cAAc,EAAE,SAAS;oBAEjE,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;oBAClE;gBACF;;QACF;kCAAG;QAAC;QAAiB;QAAsB;QAAS;KAAiB;IAErE,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,OAAO,MAAM,KAAK,GAAG;gBACpE;YACF;YAEA,MAAM,gBAAgC,EAAE;YAExC,0BAA0B;YAC1B,KAAK,MAAM,SAAS,OAAQ;gBAC1B,MAAM,cAAc,iBAAiB,SAAS,CAC5C,SACA;2DACA,CAAC;wBACC,QAAQ,GAAG,CACT,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,OAAO,EAC5D;wBAGF,mDAAmD;wBACnD,YAAY,iBAAiB,CAAC;4BAAE;wBAAS;oBAC3C;;gBAGF,cAAc,IAAI,CAAC;YACrB;YAEA;2CAAO;oBACL,KAAK,MAAM,eAAe,cAAe;gBAC3C;;QACF;kCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,GAAG,WAAW;QACd;QACA;IACF;AACF;IA5IgB;;QAuDM,yLAAA,CAAA,iBAAc;QACd,8KAAA,CAAA,WAAQ;;;AAyFvB,SAAS,eACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAiB;SAAiB;QAC3C,kBAAkB;IACpB,GACA;AAEJ;IAfgB;;QAKP", "debugId": null}}, {"offset": {"line": 6577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/transformers/delegationEnrichment.ts"], "sourcesContent": ["/**\r\n * @file Delegation enrichment transformer following established patterns\r\n * @description Handles the enrichment of delegation data with employee and vehicle details\r\n * @module transformers/delegationEnrichment\r\n */\r\n\r\nimport type {\r\n  Delegation,\r\n  DelegationDriver,\r\n  DelegationEscort,\r\n  DelegationVehicleAssignment,\r\n  Employee,\r\n  Vehicle,\r\n} from '../types/domain';\r\n\r\n/**\r\n * Transformer class for enriching delegation data with related entities\r\n * Follows the same pattern as other transformers in the codebase\r\n */\r\nexport class DelegationEnrichmentTransformer {\r\n  /**\r\n   * Main enrichment method that combines delegation data with employee and vehicle details\r\n   * @param delegation - Base delegation data\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Fully enriched delegation\r\n   */\r\n  static enrich(\r\n    delegation: Delegation,\r\n    employees: Employee[],\r\n    vehicles: Vehicle[]\r\n  ): Delegation {\r\n    const { employeeMap, vehicleMap } = this.createLookupMaps(\r\n      employees,\r\n      vehicles\r\n    );\r\n\r\n    return {\r\n      ...delegation,\r\n      drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],\r\n      escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],\r\n      vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates optimized lookup maps for O(1) performance\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Object containing employee and vehicle maps\r\n   */\r\n  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {\r\n    return {\r\n      employeeMap: new Map(employees.map(emp => [emp.id, emp])),\r\n      vehicleMap: new Map(vehicles.map(veh => [veh.id, veh])),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches driver assignments with employee details\r\n   * @param drivers - Array of driver assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched driver assignments\r\n   */\r\n  private static enrichDrivers(\r\n    drivers: DelegationDriver[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationDriver[] | undefined {\r\n    return drivers?.map(driver => {\r\n      const employee =\r\n        driver.employee || employeeMap.get(Number(driver.employeeId));\r\n      return {\r\n        ...driver,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches escort assignments with employee details\r\n   * @param escorts - Array of escort assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched escort assignments\r\n   */\r\n  private static enrichEscorts(\r\n    escorts: DelegationEscort[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationEscort[] | undefined {\r\n    return escorts?.map(escort => {\r\n      const employee =\r\n        escort.employee || employeeMap.get(Number(escort.employeeId));\r\n      return {\r\n        ...escort,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches vehicle assignments with vehicle details\r\n   * @param vehicles - Array of vehicle assignments\r\n   * @param vehicleMap - Map of vehicles for O(1) lookup\r\n   * @returns Enriched vehicle assignments\r\n   */\r\n  private static enrichVehicles(\r\n    vehicles: DelegationVehicleAssignment[] | undefined,\r\n    vehicleMap: Map<number, Vehicle>\r\n  ): DelegationVehicleAssignment[] | undefined {\r\n    return vehicles?.map(vehicleAssignment => {\r\n      const vehicle =\r\n        vehicleAssignment.vehicle ||\r\n        vehicleMap.get(vehicleAssignment.vehicleId);\r\n      return {\r\n        ...vehicleAssignment,\r\n        ...(vehicle && { vehicle }),\r\n      };\r\n    });\r\n  }\r\n}\r\n\r\n// Export the main enrichment function for backward compatibility\r\nexport const enrichDelegation = (\r\n  delegation: Delegation,\r\n  employees: Employee[],\r\n  vehicles: Vehicle[]\r\n): Delegation => {\r\n  return DelegationEnrichmentTransformer.enrich(\r\n    delegation,\r\n    employees,\r\n    vehicles\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAeM,MAAM;IACX;;;;;;GAMC,GACD,OAAO,OACL,UAAsB,EACtB,SAAqB,EACrB,QAAmB,EACP;QACZ,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CACvD,WACA;QAGF,OAAO;YACL,GAAG,UAAU;YACb,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,EAAE,eAAe,EAAE;QACtE;IACF;IAEA;;;;;GAKC,GACD,OAAe,iBAAiB,SAAqB,EAAE,QAAmB,EAAE;QAC1E,OAAO;YACL,aAAa,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;YACvD,YAAY,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;QACvD;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,eACb,QAAmD,EACnD,UAAgC,EACW;QAC3C,OAAO,UAAU,IAAI,CAAA;YACnB,MAAM,UACJ,kBAAkB,OAAO,IACzB,WAAW,GAAG,CAAC,kBAAkB,SAAS;YAC5C,OAAO;gBACL,GAAG,iBAAiB;gBACpB,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;IACF;AACF;AAGO,MAAM,mBAAmB,CAC9B,YACA,WACA;IAEA,OAAO,gCAAgC,MAAM,CAC3C,YACA,WACA;AAEJ", "debugId": null}}, {"offset": {"line": 6679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/stores/queries/delegationQueries.ts"], "sourcesContent": ["/**\r\n * @file Delegation query configurations following Single Responsibility Principle\r\n * @description Centralized query configurations for delegation-related data fetching\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport type { Delegation } from '../../types/domain';\r\n\r\nimport {\r\n  delegationApiService,\r\n  employeeApiService,\r\n  vehicleApiService,\r\n} from '../../api/services/apiServiceFactory'; // Use centralized services\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\n\r\n/**\r\n * Query keys for delegation-related queries\r\n */\r\nexport const delegationQueryKeys = {\r\n  all: ['delegations'] as const,\r\n  detail: (id: string) => ['delegations', id] as const,\r\n  withAssignments: (id: string) =>\r\n    ['delegations', id, 'with-assignments'] as const,\r\n};\r\n\r\n/**\r\n * Creates query configuration for fetching a single delegation\r\n */\r\nexport const createDelegationQuery = (id: string) => ({\r\n  enabled: !!id,\r\n  queryFn: () => delegationApiService.getById(id),\r\n  queryKey: delegationQueryKeys.detail(id),\r\n  staleTime: 5 * 60 * 1000, // 5 minutes\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all employees\r\n */\r\nexport const createEmployeesQuery = () => ({\r\n  queryFn: () => employeeApiService.getAll(),\r\n  queryKey: ['employees'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all vehicles\r\n */\r\nexport const createVehiclesQuery = () => ({\r\n  queryFn: () => vehicleApiService.getAll(),\r\n  queryKey: ['vehicles'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently\r\n});\r\n\r\n/**\r\n * Creates parallel query configurations for delegation with assignments\r\n */\r\nexport const createDelegationWithAssignmentsQueries = (id: string) => [\r\n  createDelegationQuery(id),\r\n  createEmployeesQuery(),\r\n  createVehiclesQuery(),\r\n];\r\n\r\n/**\r\n * Standard query options for delegation queries\r\n */\r\nexport const delegationQueryOptions: Partial<\r\n  UseQueryOptions<Delegation, Error>\r\n> = {\r\n  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time\r\n  retry: 3,\r\n  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n  staleTime: 5 * 60 * 1000,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD,sTAI+C,2BAA2B;AAJ1E;;AAUO,MAAM,sBAAsB;IACjC,KAAK;QAAC;KAAc;IACpB,QAAQ,CAAC,KAAe;YAAC;YAAe;SAAG;IAC3C,iBAAiB,CAAC,KAChB;YAAC;YAAe;YAAI;SAAmB;AAC3C;AAKO,MAAM,wBAAwB,CAAC,KAAe,CAAC;QACpD,SAAS,CAAC,CAAC;QACX,SAAS,IAAM,2IAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C,UAAU,oBAAoB,MAAM,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB,CAAC;AAKM,MAAM,uBAAuB,IAAM,CAAC;QACzC,SAAS,IAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;QACxC,UAAU;YAAC;SAAY;QACvB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,sBAAsB,IAAM,CAAC;QACxC,SAAS,IAAM,2IAAA,CAAA,oBAAiB,CAAC,MAAM;QACvC,UAAU;YAAC;SAAW;QACtB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,yCAAyC,CAAC,KAAe;QACpE,sBAAsB;QACtB;QACA;KACD;AAKM,MAAM,yBAET;IACF,QAAQ,KAAK,KAAK;IAClB,OAAO;IACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IAC/D,WAAW,IAAI,KAAK;AACtB", "debugId": null}}, {"offset": {"line": 6747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/stores/queries/useDelegations.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Delegation-related data.\r\n * These hooks manage fetching, caching, and mutating delegation data,\r\n * integrating with the DelegationApiService and DelegationTransformer.\r\n * @module stores/queries/useDelegations\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\n\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\n// import { DelegationFormData } from '../../schemas/delegationSchemas'; // Not directly used by hooks' public API\r\nimport type { UpdateDelegationRequest } from '../../types/api'; // For useUpdateDelegation\r\nimport type {\r\n  CreateDelegationData,\r\n  Delegation,\r\n  DelegationStatusPrisma,\r\n  FlightDetails,\r\n  // DelegationEscort, // Not directly used in optimistic updates in a way that needs separate import here\r\n  // DelegationDriver,\r\n  // DelegationVehicleAssignment,\r\n} from '../../types/domain';\r\n\r\nimport { useCrudQuery } from '../../../hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { delegationApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\nimport { enrichDelegation } from '../../transformers/delegationEnrichment';\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\nimport {\r\n  createDelegationWithAssignmentsQueries,\r\n  delegationQueryKeys,\r\n} from './delegationQueries';\r\n\r\nexport const useDelegations = (\r\n  options?: Omit<\r\n    UseQueryOptions<Delegation[], Error>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Delegation[], Error>(\r\n    [...delegationQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const result = await delegationApiService.getAll();\r\n      return result.data;\r\n    },\r\n    'delegation', // entityType\r\n    {\r\n      staleTime: 0, // Existing staleTime\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\nexport const useDelegation = (id: string) => {\r\n  return useCrudQuery<Delegation, Error>(\r\n    [...delegationQueryKeys.detail(id)],\r\n    async () => {\r\n      return await delegationApiService.getById(id);\r\n    },\r\n    'delegation', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id,\r\n      staleTime: 5 * 60 * 1000,\r\n    }\r\n  );\r\n};\r\n\r\n// ✅ OPTIMIZED: Fast parallel data fetching for delegation with assignments\r\nexport const useDelegationWithAssignments = (id: string) => {\r\n  // Execute all queries in parallel using useQueries for maximum performance\r\n  const results = useQueries({\r\n    queries: createDelegationWithAssignmentsQueries(id),\r\n  });\r\n\r\n  const [delegationQuery, employeesQuery, vehiclesQuery] = results;\r\n\r\n  // Compute enriched delegation when all data is available\r\n  const enrichedDelegation = useMemo(() => {\r\n    if (\r\n      !delegationQuery?.data ||\r\n      !employeesQuery?.data ||\r\n      !vehiclesQuery?.data\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer\r\n      // No need to apply DelegationTransformer.fromApi() again\r\n      const delegation = delegationQuery.data as Delegation;\r\n      return enrichDelegation(\r\n        delegation,\r\n        employeesQuery.data as any,\r\n        vehiclesQuery.data as any\r\n      );\r\n    } catch (error) {\r\n      console.error('Error enriching delegation data:', error);\r\n      throw error;\r\n    }\r\n  }, [delegationQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);\r\n\r\n  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders\r\n  const refetch = useCallback(() => {\r\n    delegationQuery?.refetch();\r\n    employeesQuery?.refetch();\r\n    vehiclesQuery?.refetch();\r\n  }, [\r\n    delegationQuery?.refetch,\r\n    employeesQuery?.refetch,\r\n    vehiclesQuery?.refetch,\r\n  ]);\r\n\r\n  // Return combined state with optimized loading states\r\n  return {\r\n    data: enrichedDelegation,\r\n    error:\r\n      delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,\r\n    isError:\r\n      delegationQuery?.isError ||\r\n      employeesQuery?.isError ||\r\n      vehiclesQuery?.isError,\r\n    isLoading:\r\n      delegationQuery?.isLoading ||\r\n      employeesQuery?.isLoading ||\r\n      vehiclesQuery?.isLoading,\r\n    isPending:\r\n      delegationQuery?.isPending ||\r\n      employeesQuery?.isPending ||\r\n      vehiclesQuery?.isPending,\r\n    refetch,\r\n  };\r\n};\r\n\r\n// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook\r\nexport const useDelegationEnriched = useDelegationWithAssignments;\r\n\r\nexport const useCreateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface CreateContext {\r\n    previousDelegations: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<Delegation, Error, CreateDelegationData, CreateContext>({\r\n    mutationFn: async (delegationData: CreateDelegationData) => {\r\n      const apiPayload = DelegationTransformer.toCreateRequest(delegationData);\r\n      // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation\r\n      return await delegationApiService.create(apiPayload);\r\n    },\r\n    onError: (err, _delegationData, context) => {\r\n      if (context?.previousDelegations) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegations\r\n        );\r\n      }\r\n      console.error('Failed to create delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async (delegationData: CreateDelegationData) => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      const previousDelegations = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = `optimistic-${Date.now()}`;\r\n          const now = new Date().toISOString();\r\n\r\n          const optimisticArrivalFlight: FlightDetails | null =\r\n            delegationData.flightArrivalDetails\r\n              ? {\r\n                  id: `optimistic-flight-arr-${Date.now()}`,\r\n                  ...delegationData.flightArrivalDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDepartureFlight: FlightDetails | null =\r\n            delegationData.flightDepartureDetails\r\n              ? {\r\n                  id: `optimistic-flight-dep-${Date.now() + 1}`,\r\n                  ...delegationData.flightDepartureDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDelegates: Delegation['delegates'] =\r\n            delegationData.delegates?.map((d, index) => ({\r\n              id: `optimistic-delegate-${tempId}-${index}`,\r\n              name: d.name, // Use d.name directly\r\n              notes: d.notes ?? null,\r\n              title: d.title, // Use d.title directly\r\n            })) || [];\r\n\r\n          const optimisticDelegation: Delegation = {\r\n            arrivalFlight: optimisticArrivalFlight ?? null,\r\n            createdAt: now,\r\n            delegates: optimisticDelegates,\r\n            departureFlight: optimisticDepartureFlight ?? null,\r\n            drivers:\r\n              delegationData.drivers?.map(d => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: d.employeeId, // Keep as number\r\n                id: `optimistic-driver-${tempId}-${d.employeeId}`, // Placeholder ID\r\n                notes: d.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            durationFrom: delegationData.durationFrom,\r\n            durationTo: delegationData.durationTo,\r\n            escorts:\r\n              delegationData.escorts?.map(e => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: e.employeeId, // Keep as number\r\n                id: `optimistic-escort-${tempId}-${e.employeeId}`, // Placeholder ID\r\n                notes: e.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            eventName: delegationData.eventName,\r\n            id: tempId,\r\n            imageUrl: delegationData.imageUrl ?? null,\r\n            invitationFrom: delegationData.invitationFrom ?? null,\r\n            invitationTo: delegationData.invitationTo ?? null,\r\n            location: delegationData.location,\r\n            notes: delegationData.notes ?? null,\r\n            status: delegationData.status || 'Planned',\r\n            statusHistory: [],\r\n            updatedAt: now,\r\n            vehicles:\r\n              delegationData.vehicles?.map(v => ({\r\n                assignedDate: v.assignedDate,\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                id: `optimistic-vehicle-${tempId}-${v.vehicleId}`, // Placeholder ID\r\n                notes: v.notes ?? null,\r\n                returnDate: v.returnDate ?? null,\r\n                updatedAt: now, // Placeholder timestamp\r\n                vehicleId: v.vehicleId,\r\n              })) || [],\r\n          };\r\n          return [...old, optimisticDelegation];\r\n        }\r\n      );\r\n      return { previousDelegations };\r\n    },\r\n    onSettled: () => {\r\n      // Invalidate to ensure consistency after success or failure\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface UpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { data: UpdateDelegationRequest; id: string }, // Corrected: data is UpdateDelegationRequest\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation\r\n      return await delegationApiService.update(id, data);\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to update delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      // data is UpdateDelegationRequest\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          const now = new Date().toISOString();\r\n\r\n          // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest\r\n          const updatedOptimistic: Delegation = {\r\n            ...old,\r\n            // Handle flight details updates\r\n            arrivalFlight: undefinedToNull(\r\n              data.flightArrivalDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightArrivalDetails === undefined\r\n                  ? old.arrivalFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightArrivalDetails.airport ||\r\n                        old.arrivalFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightArrivalDetails.dateTime ||\r\n                        old.arrivalFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightArrivalDetails.flightNumber ||\r\n                        old.arrivalFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightArrivalDetails.notes ??\r\n                        old.arrivalFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightArrivalDetails.terminal ??\r\n                        old.arrivalFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            departureFlight: undefinedToNull(\r\n              data.flightDepartureDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightDepartureDetails === undefined\r\n                  ? old.departureFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightDepartureDetails.airport ||\r\n                        old.departureFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightDepartureDetails.dateTime ||\r\n                        old.departureFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightDepartureDetails.flightNumber ||\r\n                        old.departureFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.departureFlight?.id ||\r\n                        `optimistic-dep-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightDepartureDetails.notes ??\r\n                        old.departureFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightDepartureDetails.terminal ??\r\n                        old.departureFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            durationFrom: data.durationFrom ?? old.durationFrom, // ✅ Direct mapping\r\n            durationTo: data.durationTo ?? old.durationTo, // ✅ Direct mapping\r\n            // Direct field mappings (no transformation needed)\r\n            eventName: data.eventName ?? old.eventName, // ✅ Direct mapping\r\n            imageUrl: undefinedToNull(data.imageUrl ?? old.imageUrl),\r\n            invitationFrom: undefinedToNull(\r\n              data.invitationFrom ?? old.invitationFrom\r\n            ),\r\n            invitationTo: undefinedToNull(\r\n              data.invitationTo ?? old.invitationTo\r\n            ),\r\n            location: data.location ?? old.location,\r\n            notes: undefinedToNull(data.notes ?? old.notes),\r\n            status: (data.status as DelegationStatusPrisma) ?? old.status, // Cast status\r\n            updatedAt: now,\r\n            // Note: Nested assignments (escorts, drivers, vehicles) are typically managed via separate mutations,\r\n            // so they are not included in the main delegation update optimistic logic here.\r\n          };\r\n          return updatedOptimistic;\r\n        }\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (oldList = []) =>\r\n          oldList.map(delegation =>\r\n            delegation.id === id\r\n              ? queryClient.getQueryData<Delegation>(\r\n                  delegationQueryKeys.detail(id)\r\n                ) || delegation\r\n              : delegation\r\n          )\r\n      );\r\n\r\n      return { previousDelegation, previousDelegationsList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      // Always refetch after error or success\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegationStatus = () => {\r\n  const queryClient = useQueryClient();\r\n  interface StatusUpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { id: string; status: DelegationStatusPrisma; statusChangeReason?: string },\r\n    StatusUpdateContext\r\n  >({\r\n    mutationFn: async ({ id, status, statusChangeReason }) => {\r\n      const response = await delegationApiService.updateStatus(\r\n        id,\r\n        status,\r\n        statusChangeReason\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to update delegation status:', err);\r\n    },\r\n    onMutate: async ({ id, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => (old ? { ...old, status: status } : undefined)\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useManageDelegationFlightDetails = () => {\r\n  const queryClient = useQueryClient();\r\n  interface FlightDetailsContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { flightDetails: FlightDetails; id: string },\r\n    FlightDetailsContext\r\n  >({\r\n    mutationFn: async ({ flightDetails, id }) => {\r\n      // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>\r\n      // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)\r\n      const response = await delegationApiService.manageFlightDetails(\r\n        id,\r\n        flightDetails\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to manage delegation flight details:', err);\r\n    },\r\n    onMutate: async ({ flightDetails, id }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          // This optimistic update assumes flightDetails is for arrival.\r\n          // A more robust solution would need to know if it's arrival or departure.\r\n          return { ...old, arrivalFlight: flightDetails };\r\n        }\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface DeleteContext {\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await delegationApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to delete delegation:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => old.filter(delegation => delegation.id !== id)\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: delegationQueryKeys.detail(id) });\r\n\r\n      return { previousDelegationsList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;AAID;AAAA;AAAA;AACA;AAEA;AAcA,6OAAiE,uBAAuB;AACxF,sTAA6E,0BAA0B;AAAvG;AACA;AACA;AACA;;;;;;;;;;AAKO,MAAM,iBAAiB,CAC5B;;IAKA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,uJAAA,CAAA,sBAAmB,CAAC,GAAG;KAAC;uCAC5B;YACE,MAAM,SAAS,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM;YAChD,OAAO,OAAO,IAAI;QACpB;sCACA,cACA;QACE,WAAW;QACX,GAAG,OAAO;IACZ;AAEJ;GAlBa;;QAMJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;KAAI;sCACnC;YACE,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C;qCACA,cACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAZa;;QACJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,+BAA+B,CAAC;;IAC3C,2EAA2E;IAC3E,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,uJAAA,CAAA,yCAAsC,AAAD,EAAE;IAClD;IAEA,MAAM,CAAC,iBAAiB,gBAAgB,cAAc,GAAG;IAEzD,yDAAyD;IACzD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oEAAE;YACjC,IACE,CAAC,iBAAiB,QAClB,CAAC,gBAAgB,QACjB,CAAC,eAAe,MAChB;gBACA;YACF;YAEA,IAAI;gBACF,qFAAqF;gBACrF,yDAAyD;gBACzD,MAAM,aAAa,gBAAgB,IAAI;gBACvC,OAAO,CAAA,GAAA,qJAAA,CAAA,mBAAgB,AAAD,EACpB,YACA,eAAe,IAAI,EACnB,cAAc,IAAI;YAEtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;mEAAG;QAAC,iBAAiB;QAAM,gBAAgB;QAAM,eAAe;KAAK;IAErE,4EAA4E;IAC5E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAC1B,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;QACjB;4DAAG;QACD,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;KAChB;IAED,sDAAsD;IACtD,OAAO;QACL,MAAM;QACN,OACE,iBAAiB,SAAS,gBAAgB,SAAS,eAAe;QACpE,SACE,iBAAiB,WACjB,gBAAgB,WAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB;IACF;AACF;IA/Da;;QAEK,gLAAA,CAAA,aAAU;;;AAgErB,MAAM,wBAAwB;AAE9B,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA0D;QACzE,UAAU;+CAAE,OAAO;gBACjB,MAAM,aAAa,sJAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;gBACzD,iFAAiF;gBACjF,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;YAC3C;;QACA,OAAO;+CAAE,CAAC,KAAK,iBAAiB;gBAC9B,IAAI,SAAS,qBAAqB;oBAChC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,mBAAmB;gBAE/B;gBACA,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;QACA,QAAQ;+CAAE,OAAO;gBACf,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,sBAAsB,YAAY,YAAY,CAClD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,MAAM,EAAE;wBACP,MAAM,SAAS,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;wBACzC,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,MAAM,0BACJ,eAAe,oBAAoB,GAC/B;4BACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,IAAI;4BACzC,GAAG,eAAe,oBAAoB;wBACxC,IACA;wBAEN,MAAM,4BACJ,eAAe,sBAAsB,GACjC;4BACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,KAAK,GAAG;4BAC7C,GAAG,eAAe,sBAAsB;wBAC1C,IACA;wBAEN,MAAM,sBACJ,eAAe,SAAS,EAAE;+DAAI,CAAC,GAAG,QAAU,CAAC;oCAC3C,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,OAAO;oCAC5C,MAAM,EAAE,IAAI;oCACZ,OAAO,EAAE,KAAK,IAAI;oCAClB,OAAO,EAAE,KAAK;gCAChB,CAAC;iEAAM,EAAE;wBAEX,MAAM,uBAAmC;4BACvC,eAAe,2BAA2B;4BAC1C,WAAW;4BACX,WAAW;4BACX,iBAAiB,6BAA6B;4BAC9C,SACE,eAAe,OAAO,EAAE;mEAAI,CAAA,IAAK,CAAC;wCAChC,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,YAAY,EAAE,UAAU;wCACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,WAAW;oCACb,CAAC;qEAAM,EAAE;4BACX,cAAc,eAAe,YAAY;4BACzC,YAAY,eAAe,UAAU;4BACrC,SACE,eAAe,OAAO,EAAE;mEAAI,CAAA,IAAK,CAAC;wCAChC,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,YAAY,EAAE,UAAU;wCACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,WAAW;oCACb,CAAC;qEAAM,EAAE;4BACX,WAAW,eAAe,SAAS;4BACnC,IAAI;4BACJ,UAAU,eAAe,QAAQ,IAAI;4BACrC,gBAAgB,eAAe,cAAc,IAAI;4BACjD,cAAc,eAAe,YAAY,IAAI;4BAC7C,UAAU,eAAe,QAAQ;4BACjC,OAAO,eAAe,KAAK,IAAI;4BAC/B,QAAQ,eAAe,MAAM,IAAI;4BACjC,eAAe,EAAE;4BACjB,WAAW;4BACX,UACE,eAAe,QAAQ,EAAE;mEAAI,CAAA,IAAK,CAAC;wCACjC,cAAc,EAAE,YAAY;wCAC5B,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,YAAY,EAAE,UAAU,IAAI;wCAC5B,WAAW;wCACX,WAAW,EAAE,SAAS;oCACxB,CAAC;qEAAM,EAAE;wBACb;wBACA,OAAO;+BAAI;4BAAK;yBAAqB;oBACvC;;gBAEF,OAAO;oBAAE;gBAAoB;YAC/B;;QACA,SAAS;+CAAE;gBACT,4DAA4D;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAvHa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AAmHb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;+CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,iFAAiF;gBACjF,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,IAAI;YAC/C;;QACA,OAAO;+CAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,IAAI,SAAS,yBAAyB;oBACpC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;gBAEnC;gBACA,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;QACA,QAAQ;+CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC3B,kCAAkC;gBAClC,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBAEA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,MAAM,0BAA0B,YAAY,YAAY,CACtD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;uDAC3B,CAAA;wBACE,IAAI,CAAC,KAAK;wBACV,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,2EAA2E;wBAC3E,MAAM,oBAAgC;4BACpC,GAAG,GAAG;4BACN,gCAAgC;4BAChC,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,oBAAoB,KAAK,OAC1B,KAAK,sCAAsC;+BAC3C,KAAK,oBAAoB,KAAK,YAC5B,IAAI,aAAa,GACjB;gCACE,SACE,KAAK,oBAAoB,CAAC,OAAO,IACjC,IAAI,aAAa,EAAE,WACnB;gCACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;gCACF,cACE,KAAK,oBAAoB,CAAC,YAAY,IACtC,IAAI,aAAa,EAAE,gBACnB;gCACF,IACE,IAAI,aAAa,EAAE,MAAM,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;gCACzD,OACE,KAAK,oBAAoB,CAAC,KAAK,IAC/B,IAAI,aAAa,EAAE,SACnB;gCACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;4BACJ,EAAE,mCAAmC;;4BAE7C,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,sBAAsB,KAAK,OAC5B,KAAK,sCAAsC;+BAC3C,KAAK,sBAAsB,KAAK,YAC9B,IAAI,eAAe,GACnB;gCACE,SACE,KAAK,sBAAsB,CAAC,OAAO,IACnC,IAAI,eAAe,EAAE,WACrB;gCACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;gCACF,cACE,KAAK,sBAAsB,CAAC,YAAY,IACxC,IAAI,eAAe,EAAE,gBACrB;gCACF,IACE,IAAI,eAAe,EAAE,MACrB,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;gCAChC,OACE,KAAK,sBAAsB,CAAC,KAAK,IACjC,IAAI,eAAe,EAAE,SACrB;gCACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;4BACJ,EAAE,mCAAmC;;4BAE7C,cAAc,KAAK,YAAY,IAAI,IAAI,YAAY;4BACnD,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;4BAC7C,mDAAmD;4BACnD,WAAW,KAAK,SAAS,IAAI,IAAI,SAAS;4BAC1C,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvD,gBAAgB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC5B,KAAK,cAAc,IAAI,IAAI,cAAc;4BAE3C,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,IAAI,YAAY;4BAEvC,UAAU,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvC,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK;4BAC9C,QAAQ,AAAC,KAAK,MAAM,IAA+B,IAAI,MAAM;4BAC7D,WAAW;wBAGb;wBACA,OAAO;oBACT;;gBAGF,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,UAAU,EAAE,GACX,QAAQ,GAAG;+DAAC,CAAA,aACV,WAAW,EAAE,KAAK,KACd,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,QACxB,aACL;;;gBAIV,OAAO;oBAAE;oBAAoB;gBAAwB;YACvD;;QACA,SAAS;+CAAE,CAAC,OAAO,QAAQ;gBACzB,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAtKa;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW;;;AAiKb,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;qDAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBACnD,MAAM,WAAW,MAAM,2IAAA,CAAA,uBAAoB,CAAC,YAAY,CACtD,IACA,QACA;gBAEF,OAAO;YACT;;QACA,OAAO;qDAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,QAAQ,KAAK,CAAC,uCAAuC;YACvD;;QACA,QAAQ;qDAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE;gBAC7B,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;6DAC3B,CAAA,MAAQ,MAAM;4BAAE,GAAG,GAAG;4BAAE,QAAQ;wBAAO,IAAI;;gBAE7C,OAAO;oBAAE;gBAAmB;YAC9B;;QACA,SAAS;qDAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAjDa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AA6Cb,MAAM,mCAAmC;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;4DAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;gBACtC,oGAAoG;gBACpG,wGAAwG;gBACxG,MAAM,WAAW,MAAM,2IAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAC7D,IACA;gBAEF,OAAO;YACT;;QACA,OAAO;4DAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;;QACA,QAAQ;4DAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;gBACpC,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;oEAC3B,CAAA;wBACE,IAAI,CAAC,KAAK;wBACV,+DAA+D;wBAC/D,0EAA0E;wBAC1E,OAAO;4BAAE,GAAG,GAAG;4BAAE,eAAe;wBAAc;oBAChD;;gBAEF,OAAO;oBAAE;gBAAmB;YAC9B;;QACA,SAAS;4DAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAvDa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AAmDb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;+CAAE,OAAO;gBACjB,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;gBAClC,OAAO;YACT;;QACA,OAAO;+CAAE,CAAC,KAAK,KAAK;gBAClB,IAAI,SAAS,yBAAyB;oBACpC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;gBAEnC;gBACA,QAAQ,KAAK,CAAC,gCAAgC;YAChD;;QACA,QAAQ;+CAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBAEA,MAAM,0BAA0B,YAAY,YAAY,CACtD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM;+DAAC,CAAA,aAAc,WAAW,EAAE,KAAK;;;gBAG3D,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAAI;gBAErE,OAAO;oBAAE;gBAAwB;YACnC;;QACA,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IA3Ca;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 7294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/app/delegations/%5Bid%5D/page.tsx"], "sourcesContent": ["// @ts-nocheck - Keeping this for now due to potential complexities with Delegate type\r\n'use client';\r\n\r\nimport { format, parseISO } from 'date-fns';\r\nimport {\r\n  AlertTriangle,\r\n  ArrowLeft,\r\n  Briefcase,\r\n  CalendarDays,\r\n  Car,\r\n  Clock,\r\n  Edit,\r\n  Info,\r\n  MapPin,\r\n  Plane,\r\n  PlaneLanding,\r\n  PlaneTakeoff,\r\n  Printer,\r\n  RefreshCw,\r\n  Shield,\r\n  Trash2,\r\n  User,\r\n  Users,\r\n} from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { useParams, useRouter } from 'next/navigation';\r\nimport { useCallback, useEffect, useState } from 'react'; // useState will be removed for hook state\r\n\r\nimport type {\r\n  DelegationStatusPrisma,\r\n  Delegation as DomainDelegation,\r\n  FlightDetails as DomainFlightDetails, // Renamed for clarity\r\n  DelegationStatusUpdate as DomainStatusHistoryEntry, // Renamed for clarity\r\n} from '@/lib/types/domain'; // Updated types\r\n\r\nimport {\r\n  DelegationDetailHeader,\r\n  DelegationSidebar,\r\n  DelegationTabs,\r\n} from '@/components/features/delegations/detail'; // Import the new components\r\nimport { ViewReportButton } from '@/components/reports/ViewReportButton';\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n  AlertDialogTrigger,\r\n} from '@/components/ui/alert-dialog';\r\nimport { AppBreadcrumb } from '@/components/ui/app-breadcrumb';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { DataLoader, SkeletonLoader } from '@/components/ui/loading'; // ErrorDisplay removed as DataLoader handles it\r\nimport { PageHeader } from '@/components/ui/PageHeader';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { usePredefinedEntityToast } from '@/hooks/forms/useFormToast';\r\nimport {\r\n  useDelegation,\r\n  useDeleteDelegation,\r\n  useUpdateDelegationStatus, // Import the new hook\r\n} from '@/lib/stores/queries/useDelegations'; // Updated hooks\r\nimport { cn } from '@/lib/utils';\r\nimport {\r\n  formatDelegationStatusForDisplay,\r\n  formatEmployeeName,\r\n  formatEmployeeRole,\r\n} from '@/lib/utils/formattingUtils';\r\nimport { getSafeDelegationImageUrl } from '@/lib/utils/imageUtils';\r\n\r\n// getStatusColor now uses DelegationStatusPrisma\r\nconst getStatusColor = (status: DelegationStatusPrisma | undefined) => {\r\n  switch (status) {\r\n    case 'Cancelled': {\r\n      return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';\r\n    }\r\n    case 'Completed': {\r\n      return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';\r\n    }\r\n    case 'Confirmed': {\r\n      return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';\r\n    }\r\n    case 'In_Progress': {\r\n      return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';\r\n    }\r\n    case 'Planned': {\r\n      return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';\r\n    }\r\n    case 'No_details':\r\n    default: {\r\n      return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';\r\n    }\r\n  }\r\n};\r\n\r\nconst formatDate = (dateString: string | undefined, includeTime = false) => {\r\n  if (!dateString) return 'N/A'; // Added check for undefined dateString\r\n  try {\r\n    return format(\r\n      parseISO(dateString),\r\n      includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy'\r\n    );\r\n  } catch {\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n\r\ninterface DetailItemProps {\r\n  children?: React.ReactNode;\r\n  icon: React.ElementType;\r\n  label: string;\r\n  value?: null | number | string;\r\n  valueClassName?: string;\r\n}\r\n\r\nexport default function DelegationDetailPage() {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const {\r\n    showEntityDeleted,\r\n    showEntityDeletionError,\r\n    showEntityUpdated,\r\n    showEntityUpdateError,\r\n  } = usePredefinedEntityToast('delegation');\r\n\r\n  const delegationId = params.id as string;\r\n\r\n  const {\r\n    data: delegation, // This is DomainDelegation | undefined\r\n    error,\r\n    isLoading,\r\n    refetch,\r\n  } = useDelegation(delegationId);\r\n\r\n  const deleteDelegationMutation = useDeleteDelegation();\r\n  const updateDelegationStatusMutation = useUpdateDelegationStatus(); // Initialize the new mutation hook\r\n\r\n  const handleDeleteDelegation = async () => {\r\n    if (delegation) {\r\n      try {\r\n        await deleteDelegationMutation.mutateAsync(delegation.id);\r\n        const delegationForToast = {\r\n          event: delegation.eventName,\r\n          location: delegation.location,\r\n        };\r\n        showEntityDeleted(delegationForToast);\r\n        router.push('/delegations');\r\n      } catch (deleteError: any) {\r\n        console.error('Error deleting delegation:', deleteError);\r\n        showEntityDeletionError(\r\n          deleteError.message ||\r\n            'Failed to delete delegation. Please try again.'\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleStatusUpdateConfirm = async (\r\n    status: DelegationStatusPrisma,\r\n    reason: string\r\n  ) => {\r\n    if (delegation) {\r\n      try {\r\n        await updateDelegationStatusMutation.mutateAsync({\r\n          id: delegation.id,\r\n          status: status,\r\n          statusChangeReason: reason,\r\n        });\r\n        const delegationForToast = {\r\n          event: delegation.eventName,\r\n          location: delegation.location,\r\n        };\r\n        showEntityUpdated(delegationForToast);\r\n        // Modal state is managed by StatusHistoryCard component\r\n        // No need to refetch explicitly, react-query's onSettled in useUpdateDelegationStatus handles invalidation\r\n      } catch (updateError: any) {\r\n        console.error('Error updating delegation status:', updateError);\r\n        showEntityUpdateError(\r\n          updateError.message ||\r\n            'Failed to update delegation status. Please try again.'\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <DataLoader\r\n      data={delegation}\r\n      emptyComponent={\r\n        <div className=\"py-10 text-center\">\r\n          <PageHeader icon={AlertTriangle} title=\"Delegation Not Found\" />\r\n          <p className=\"mb-4\">The requested delegation could not be found.</p>\r\n        </div>\r\n      }\r\n      error={error ? (error as Error).message : null}\r\n      isLoading={isLoading}\r\n      loadingComponent={\r\n        <div className=\"space-y-6\">\r\n          <PageHeader icon={Briefcase} title=\"Loading Delegation...\" />\r\n          <SkeletonLoader count={1} variant=\"card\" />\r\n          <div className=\"grid items-start gap-6 md:grid-cols-3\">\r\n            <SkeletonLoader\r\n              className=\"md:col-span-2\"\r\n              count={1}\r\n              variant=\"card\"\r\n            />\r\n            <SkeletonLoader count={1} variant=\"card\" />\r\n          </div>\r\n        </div>\r\n      }\r\n      onRetry={refetch}\r\n    >\r\n      {(loadedDelegation: DomainDelegation) => (\r\n        <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n          {/* Breadcrumb Container */}\r\n          <div className=\"container mx-auto px-4 pt-6\">\r\n            <AppBreadcrumb />\r\n          </div>\r\n\r\n          {/* Modern Header */}\r\n          <DelegationDetailHeader\r\n            delegation={loadedDelegation}\r\n            onDelete={handleDeleteDelegation}\r\n          />\r\n\r\n          {/* Main Content */}\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"grid gap-8 lg:grid-cols-4\">\r\n              {/* Main Content Area */}\r\n              <div className=\"lg:col-span-3\">\r\n                <DelegationTabs\r\n                  delegation={loadedDelegation}\r\n                  onStatusUpdate={handleStatusUpdateConfirm}\r\n                />\r\n              </div>\r\n\r\n              {/* Sidebar */}\r\n              <div className=\"lg:col-span-1\">\r\n                <DelegationSidebar delegation={loadedDelegation} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </DataLoader>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,sFAAsF;;;;;AAGtF;AAAA;AACA;AAAA;AAsBA;AAUA,6UAImD,4BAA4B;AAJ/E;AAAA;AAAA;AAkBA;AAUA,2OAAsE,gDAAgD;AACtH;AAEA;AACA,oQAI8C,gBAAgB;;;AAvE9D;;;;;;;;;;AAgFA,iDAAiD;AACjD,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAa;gBAChB,OAAO;YACT;QACA,KAAK;YAAe;gBAClB,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;QACL;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAEA,MAAM,aAAa,CAAC,YAAgC,cAAc,KAAK;IACrE,IAAI,CAAC,YAAY,OAAO,OAAO,uCAAuC;IACtE,IAAI;QACF,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EACV,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,aACT,cAAc,uBAAuB;IAEzC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,iBAAiB,EACjB,uBAAuB,EACvB,iBAAiB,EACjB,qBAAqB,EACtB,GAAG,CAAA,GAAA,wIAAA,CAAA,2BAAwB,AAAD,EAAE;IAE7B,MAAM,eAAe,OAAO,EAAE;IAE9B,MAAM,EACJ,MAAM,UAAU,EAChB,KAAK,EACL,SAAS,EACT,OAAO,EACR,GAAG,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE;IAElB,MAAM,2BAA2B,CAAA,GAAA,oJAAA,CAAA,sBAAmB,AAAD;IACnD,MAAM,iCAAiC,CAAA,GAAA,oJAAA,CAAA,4BAAyB,AAAD,KAAK,mCAAmC;IAEvG,MAAM,yBAAyB;QAC7B,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,yBAAyB,WAAW,CAAC,WAAW,EAAE;gBACxD,MAAM,qBAAqB;oBACzB,OAAO,WAAW,SAAS;oBAC3B,UAAU,WAAW,QAAQ;gBAC/B;gBACA,kBAAkB;gBAClB,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,aAAkB;gBACzB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,wBACE,YAAY,OAAO,IACjB;YAEN;QACF;IACF;IAEA,MAAM,4BAA4B,OAChC,QACA;QAEA,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,+BAA+B,WAAW,CAAC;oBAC/C,IAAI,WAAW,EAAE;oBACjB,QAAQ;oBACR,oBAAoB;gBACtB;gBACA,MAAM,qBAAqB;oBACzB,OAAO,WAAW,SAAS;oBAC3B,UAAU,WAAW,QAAQ;gBAC/B;gBACA,kBAAkB;YAClB,wDAAwD;YACxD,2GAA2G;YAC7G,EAAE,OAAO,aAAkB;gBACzB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,sBACE,YAAY,OAAO,IACjB;YAEN;QACF;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,aAAU;QACT,MAAM;QACN,8BACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,aAAU;oBAAC,MAAM,2NAAA,CAAA,gBAAa;oBAAE,OAAM;;;;;;8BACvC,6LAAC;oBAAE,WAAU;8BAAO;;;;;;;;;;;;QAGxB,OAAO,QAAQ,AAAC,MAAgB,OAAO,GAAG;QAC1C,WAAW;QACX,gCACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,aAAU;oBAAC,MAAM,+MAAA,CAAA,YAAS;oBAAE,OAAM;;;;;;8BACnC,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,OAAO;oBAAG,SAAQ;;;;;;8BAClC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,sIAAA,CAAA,iBAAc;4BACb,WAAU;4BACV,OAAO;4BACP,SAAQ;;;;;;sCAEV,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;4BAAG,SAAQ;;;;;;;;;;;;;;;;;;QAIxC,SAAS;kBAER,CAAC,iCACA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gJAAA,CAAA,gBAAa;;;;;;;;;;kCAIhB,6LAAC,oLAAA,CAAA,yBAAsB;wBACrB,YAAY;wBACZ,UAAU;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,4KAAA,CAAA,iBAAc;wCACb,YAAY;wCACZ,gBAAgB;;;;;;;;;;;8CAKpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+KAAA,CAAA,oBAAiB;wCAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GAnIwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAMpB,wIAAA,CAAA,2BAAwB;QASxB,oJAAA,CAAA,gBAAa;QAEgB,oJAAA,CAAA,sBAAmB;QACb,oJAAA,CAAA,4BAAyB;;;KApB1C", "debugId": null}}]}