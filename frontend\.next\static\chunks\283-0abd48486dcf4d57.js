"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{1955:(e,t,i)=>{var r=i(25982);r.v},3619:(e,t,i)=>{var r=i(25982);r.v},8264:(e,t,i)=>{i.d(t,{b:()=>n});var r=i(12115),s=i(34389);function n(){let[e,t]=(0,r.useState)({csrfToken:null,isTokenValid:!1,tokenExpiresAt:null,isInitialized:!1}),i=(0,r.useCallback)(i=>{if(!s.k.isProtectionRequired(i))return i;let r=s.k.getCurrentToken();return r?(r.token!==e.csrfToken&&t(e=>({...e,csrfToken:r.token,isTokenValid:r.isValid,tokenExpiresAt:r.expiresAt})),s.k.attachToRequest(i)):(console.warn("No valid CSRF token available for request"),i)},[e.csrfToken]),n=(0,r.useCallback)(e=>s.k.validateToken(e),[]),o=(0,r.useCallback)(()=>{let e=s.k.refreshToken();return t(t=>({...t,csrfToken:e.token,isTokenValid:e.isValid,tokenExpiresAt:e.expiresAt})),e},[]),a=(0,r.useCallback)(()=>{s.k.clearToken(),t(e=>({...e,csrfToken:null,isTokenValid:!1,tokenExpiresAt:null}))},[]),l=(0,r.useCallback)(e=>s.k.isProtectionRequired(e),[]);return(0,r.useEffect)(()=>{try{let e=s.k.initialize();t({csrfToken:e.token,isTokenValid:e.isValid,tokenExpiresAt:e.expiresAt,isInitialized:!0})}catch(e){console.error("Failed to initialize CSRF protection:",e),t(e=>({...e,isInitialized:!0}))}},[]),(0,r.useEffect)(()=>{if(!e.isInitialized||!e.tokenExpiresAt)return;let t=()=>{let t=new Date;e.tokenExpiresAt.getTime()-t.getTime()<=3e5&&o()},i=setInterval(t,6e4);return t(),()=>clearInterval(i)},[e.isInitialized,e.tokenExpiresAt,o]),(0,r.useEffect)(()=>{if(!e.csrfToken)return;let t=setInterval(()=>{!s.k.validateToken(e.csrfToken).isValid&&e.isTokenValid&&o()},3e5);return()=>clearInterval(t)},[e.csrfToken,e.isTokenValid,o]),{csrfToken:e.csrfToken,isTokenValid:e.isTokenValid,tokenExpiresAt:e.tokenExpiresAt,isInitialized:e.isInitialized,attachCSRF:i,validateCSRF:n,refreshCSRFToken:o,clearCSRFToken:a,isProtectionRequired:l}}},10431:(e,t,i)=>{i.d(t,{$f:()=>S,lo:()=>p,E9:()=>h,Cv:()=>a.SessionManager,tC:()=>u});var r=i(34389),s=i(52474),n=i(38750);class o{static setClientSideCookie(e,t,i){try{let r={...this.DEFAULT_COOKIE_OPTIONS,...i},s="".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(t));if(r.maxAge&&(s+="; Max-Age=".concat(r.maxAge)),r.path&&(s+="; Path=".concat(r.path)),r.domain&&(s+="; Domain=".concat(r.domain)),r.secure&&(s+="; Secure"),r.sameSite&&(s+="; SameSite=".concat(r.sameSite)),r.httpOnly)throw Error("Cannot set httpOnly cookies from client-side. Use server-side cookie setting.");return document.cookie=s,{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to set client-side cookie"}}}static getSecureItem(e){try{return document.cookie.split(";").reduce((e,t)=>{let[i,r]=t.trim().split("=");return i&&r&&(e[decodeURIComponent(i)]=decodeURIComponent(r)),e},{})[e]||null}catch(e){return console.error("Failed to get secure item:",e),null}}static removeSecureItem(e,t){try{let i={...this.DEFAULT_COOKIE_OPTIONS,...t},r="".concat(encodeURIComponent(e),"=; expires=Thu, 01 Jan 1970 00:00:00 GMT");return i.path&&(r+="; Path=".concat(i.path)),i.domain&&(r+="; Domain=".concat(i.domain)),document.cookie=r,{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to remove secure item"}}}static isAvailable(){try{let e="__secure_storage_test__",t="test";this.setClientSideCookie(e,t,{httpOnly:!1});let i=this.getSecureItem(e);return this.removeSecureItem(e),i===t}catch(e){return!1}}static clearAllCookies(){try{for(let e of document.cookie.split(";")){let t=e.indexOf("="),i=t>-1?e.substring(0,t).trim():e.trim();i&&(document.cookie="".concat(i,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/"),document.cookie="".concat(i,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=").concat(window.location.hostname))}return{success:!0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Failed to clear cookies"}}}static getAllCookies(){try{return document.cookie.split(";").reduce((e,t)=>{let[i,r]=t.trim().split("=");return i&&r&&(e[decodeURIComponent(i)]=decodeURIComponent(r)),e},{})}catch(e){return{}}}static hasCookie(e){return null!==this.getSecureItem(e)}static getCookieExpiration(e){return null}}o.DEFAULT_COOKIE_OPTIONS={httpOnly:!0,secure:!0,sameSite:"lax",path:"/"};var a=i(89699),l=i(29159),c=i(54120);class u{static extractTokenFromHeader(e){if(!e)return null;let t=e.split(" ");return 2!==t.length||"Bearer"!==t[0]?null:(0,c.d$)(t[1])}static extractTokenFromCookie(e){var t;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"sb-access-token";if(!e)return null;let r={};return e.split(";").forEach(e=>{let[t,i]=e.trim().split("=");t&&i&&(r[t]=decodeURIComponent(i))}),null!=(t=r[i])?t:null}static validateToken(e){try{if(!e||"string"!=typeof e)return{isValid:!1,isExpired:!0,payload:null,error:"Invalid token format"};let t=(0,l.s)(e),i=Math.floor(Date.now()/1e3),r=t.exp<i,s={isValid:!r,isExpired:r,payload:t};return r&&(s.error="Token expired"),s}catch(e){return{isValid:!1,isExpired:!0,payload:null,error:e instanceof Error?e.message:"Token validation failed"}}}static extractUserRole(e){var t,i;let r=this.validateToken(e);if(!r.isValid||!r.payload)return null;let s=null==(t=r.payload.custom_claims)?void 0:t.user_role;return s||(null!=(i=r.payload.user_role)?i:"USER")}static extractEmployeeId(e){var t,i,r;let s=this.validateToken(e);return s.isValid&&s.payload&&null!=(r=null!=(i=null==(t=s.payload.custom_claims)?void 0:t.employee_id)?i:s.payload.employee_id)?r:null}static isUserActive(e){var t,i,r;let s=this.validateToken(e);return!!s.isValid&&!!s.payload&&(null==(r=null!=(i=null==(t=s.payload.custom_claims)?void 0:t.is_active)?i:s.payload.is_active)||r)}static getTokenExpiration(e){let t=this.validateToken(e);return t.isValid&&t.payload?new Date(1e3*t.payload.exp):null}static willExpireSoon(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,i=this.getTokenExpiration(e);return!i||i<=new Date(Date.now()+60*t*1e3)}static hashTokenForLogging(e){return e?e.length<16?"short-token":"".concat(e.substring(0,8),"...").concat(e.substring(e.length-8)):"no-token"}static isValidTokenFormat(e){if(!e||"string"!=typeof e)return!1;let t=e.split(".");return 3===t.length&&t.every(e=>e.length>0)}}let d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0},h={attachCSRFToRequest:r.k.attachToRequest,clearAllCookies:o.clearAllCookies,detectTimeout:a.SessionManager.detectTimeout,escapeForDisplay:s.B.escapeForDisplay,extractEmployeeId:u.extractEmployeeId,extractUserRole:u.extractUserRole,generateCSRFToken:r.k.generateToken,getCurrentSessionId:a.SessionManager.getCurrentSessionId,getPermissionsForRole:n.B.getPermissionsForRole,getSecureItem:o.getSecureItem,hasMinimumRole:n.B.hasMinimumRole,hasPermission:n.B.hasPermission,isCSRFRequired:r.k.isProtectionRequired,isStorageAvailable:o.isAvailable,isUserActive:u.isUserActive,removeSecureItem:o.removeSecureItem,sanitizeForXSS:s.B.sanitizeForXSS,setClientSideCookie:o.setClientSideCookie,updateActivity:a.SessionManager.updateActivity,validateCSRFToken:r.k.validateToken,validateEmail:s.B.validateEmail,validateObject:s.B.validateObject,validatePhone:s.B.validatePhone,validateToken:u.validateToken,validateURL:s.B.validateURL,validateUUID:s.B.validateUUID,validateValue:s.B.validateValue,willExpireSoon:u.willExpireSoon,canPerformSecurityCheck(){let e=Date.now();return(d.isOpen&&e-d.lastAttemptTime>S.CIRCUIT_BREAKER_RESET_TIMEOUT&&(d.isOpen=!1,d.attempts=0,console.log("\uD83D\uDD04 Circuit breaker reset - security operations enabled")),d.isOpen)?(console.warn("\uD83D\uDD12 Circuit breaker OPEN - preventing security verification loop"),!1):!(e-d.lastOperationTime<S.SECURITY_OPERATION_COOLDOWN)||(console.debug("⏳ Security operation cooldown active"),!1)},recordSecurityAttempt(){let e=Date.now();d.attempts++,d.lastAttemptTime=e;try{localStorage.setItem(S.VERIFICATION_LOOP_STORAGE_KEY,JSON.stringify({attempts:d.attempts,lastAttemptTime:e}))}catch(e){console.warn("Failed to store verification attempts:",e)}d.attempts>=S.CIRCUIT_BREAKER_MAX_ATTEMPTS&&(d.isOpen=!0,console.error("\uD83D\uDEA8 Security verification loop detected - circuit breaker activated"),this.forceSecurityReset())},recordSecuritySuccess(){d.attempts=0,d.isOpen=!1,d.lastOperationTime=Date.now();try{localStorage.removeItem(S.VERIFICATION_LOOP_STORAGE_KEY)}catch(e){console.warn("Failed to clear verification attempts:",e)}},startSecurityOperation(e){if(!this.canPerformSecurityCheck())return!1;if(d.activeOperations.has(e))return console.debug("\uD83D\uDD04 Security operation ".concat(e," already in progress")),!1;d.activeOperations.add(e),d.lastOperationTime=Date.now();try{localStorage.setItem(S.SECURITY_OPERATIONS_STORAGE_KEY,JSON.stringify(Array.from(d.activeOperations)))}catch(e){console.warn("Failed to store active operations:",e)}return console.debug("\uD83D\uDD10 Started security operation: ".concat(e)),!0},endSecurityOperation(e){d.activeOperations.delete(e);try{0===d.activeOperations.size?localStorage.removeItem(S.SECURITY_OPERATIONS_STORAGE_KEY):localStorage.setItem(S.SECURITY_OPERATIONS_STORAGE_KEY,JSON.stringify(Array.from(d.activeOperations)))}catch(e){console.warn("Failed to update active operations:",e)}console.debug("✅ Ended security operation: ".concat(e))},isCircuitOpen:()=>d.isOpen,getCircuitBreakerState:()=>({attemptCount:d.attempts,lastAttempt:d.lastAttemptTime,isOpen:d.isOpen,activeOperations:Array.from(d.activeOperations),lastOperationTime:d.lastOperationTime}),forceSecurityReset(){console.warn("\uD83D\uDD27 Forcing security state reset to break verification loop");try{a.SessionManager.clearSessionState(),localStorage.removeItem(S.VERIFICATION_LOOP_STORAGE_KEY),localStorage.removeItem(S.SECURITY_OPERATIONS_STORAGE_KEY),localStorage.removeItem(S.LOGOUT_EVENT_KEY),d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0},console.log("✅ Security state reset complete"),setTimeout(()=>{window.location.href="/auth-test"},1e3)}catch(e){console.error("❌ Failed to reset security state:",e),window.location.reload()}},resetCircuitBreakerForTesting(){d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0};try{localStorage.removeItem(S.VERIFICATION_LOOP_STORAGE_KEY),localStorage.removeItem(S.SECURITY_OPERATIONS_STORAGE_KEY)}catch(e){}},initializeCircuitBreaker(){try{let e=localStorage.getItem(S.VERIFICATION_LOOP_STORAGE_KEY);if(e){let{attempts:t,lastAttemptTime:i}=JSON.parse(e);Date.now()-i<S.CIRCUIT_BREAKER_RESET_TIMEOUT?(d.attempts=t,d.lastAttemptTime=i,t>=S.CIRCUIT_BREAKER_MAX_ATTEMPTS&&(d.isOpen=!0,console.warn("\uD83D\uDD12 Circuit breaker restored in OPEN state"))):localStorage.removeItem(S.VERIFICATION_LOOP_STORAGE_KEY)}localStorage.getItem(S.SECURITY_OPERATIONS_STORAGE_KEY)&&(localStorage.removeItem(S.SECURITY_OPERATIONS_STORAGE_KEY),d.activeOperations.clear()),console.log("\uD83D\uDD10 Circuit breaker initialized")}catch(e){console.warn("Failed to initialize circuit breaker:",e),d={attempts:0,lastAttemptTime:0,isOpen:!1,activeOperations:new Set,lastOperationTime:0}}}},S={DEFAULT_COOKIE_NAME:"sb-access-token",LOGOUT_EVENT_KEY:"workhub-logout-event",MAX_CONCURRENT_SESSIONS:5,REFRESH_COOKIE_NAME:"sb-refresh-token",SESSION_TIMEOUT_MINUTES:30,TOKEN_EXPIRY_THRESHOLD_MINUTES:5,CIRCUIT_BREAKER_MAX_ATTEMPTS:3,CIRCUIT_BREAKER_RESET_TIMEOUT:3e4,SECURITY_OPERATION_COOLDOWN:5e3,VERIFICATION_LOOP_STORAGE_KEY:"workhub_verification_attempts",SECURITY_OPERATIONS_STORAGE_KEY:"workhub_active_operations"},p={CROSS_TAB_LOGOUT:"cross_tab_logout",SECURITY_VIOLATION:"security_violation",SESSION_TIMEOUT:"session_timeout",TOKEN_EXPIRED:"token_expired",TOKEN_REFRESH_FAILED:"token_refresh_failed",UNAUTHORIZED_ACCESS:"unauthorized_access"}},20249:(e,t,i)=>{i.d(t,{C:()=>o});var r=i(12115),s=i(10431),n=i(28113);function o(e){let[t,i]=(0,r.useState)({isTokenExpired:!1,isTokenValid:!1,lastValidation:null,tokenError:null,willExpireSoon:!1}),o=(0,r.useMemo)(()=>(0,n.Q)(),[]),a=(0,r.useCallback)(()=>{if(!e)return null;if(!s.E9.canPerformSecurityCheck())return console.debug("\uD83D\uDD12 Token validation blocked by circuit breaker"),null;let t="token-validation";if(!s.E9.startSecurityOperation(t))return console.debug("\uD83D\uDD04 Token validation already in progress"),null;try{let t=s.tC.validateToken(e);return t.isValid?(s.E9.recordSecuritySuccess(),s.Cv.handleSessionValidation(!0)):(console.warn("❌ Token validation failed:",t.error),s.E9.recordSecurityAttempt(),s.Cv.handleSessionValidation(!1,{error:t.error})),i(i=>({...i,isTokenExpired:t.isExpired,isTokenValid:t.isValid,lastValidation:new Date,tokenError:t.error||null,willExpireSoon:!!t.isValid&&s.tC.willExpireSoon(e)})),t}catch(t){let e=t instanceof Error?t.message:"Token validation failed";return console.error("❌ Token validation error:",e),s.E9.recordSecurityAttempt(),i(t=>({...t,isTokenValid:!1,tokenError:e,lastValidation:new Date})),null}finally{s.E9.endSecurityOperation(t)}},[e]),l=(0,r.useCallback)(async()=>{if(!s.E9.canPerformSecurityCheck())return console.debug("\uD83D\uDD12 Token refresh blocked by circuit breaker"),!1;let e="token-refresh";if(!s.E9.startSecurityOperation(e))return console.debug("\uD83D\uDD04 Token refresh already in progress"),!1;try{i(e=>({...e,tokenError:null})),console.log("\uD83D\uDD04 Starting token refresh...");let e=await o.refreshNow();return e?(console.log("✅ Token refresh successful"),s.E9.recordSecuritySuccess(),s.Cv.handleTokenRefresh(!0),i(e=>({...e,isTokenValid:!0,isTokenExpired:!1,tokenError:null,lastValidation:new Date}))):(console.warn("❌ Token refresh failed"),s.E9.recordSecurityAttempt(),s.Cv.handleTokenRefresh(!1),i(e=>({...e,isTokenValid:!1,tokenError:"Token refresh failed"}))),e}catch(t){let e=t instanceof Error?t.message:"Token refresh failed";return console.error("❌ Token refresh error:",e),s.E9.recordSecurityAttempt(),s.Cv.handleTokenRefresh(!1,{error:e}),i(t=>({...t,isTokenValid:!1,tokenError:e})),!1}finally{s.E9.endSecurityOperation(e)}},[o]),c=(0,r.useCallback)(()=>{i({isTokenExpired:!1,isTokenValid:!1,lastValidation:null,tokenError:null,willExpireSoon:!1})},[]),u=(0,r.useCallback)(()=>!e||s.tC.willExpireSoon(e,s.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES),[e]),d=(0,r.useCallback)(()=>e?s.tC.getTokenExpiration(e):null,[e]);return(0,r.useEffect)(()=>{e?a():c()},[e,a,c]),(0,r.useEffect)(()=>{if(!e||!t.isTokenValid)return;let i=()=>{if(!s.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Auto token refresh blocked by circuit breaker");let t="auto-token-refresh-check";if(!s.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Auto token refresh check already in progress");try{s.tC.willExpireSoon(e,2)&&(console.log("⏰ Token will expire soon, triggering auto-refresh"),l())}catch(e){console.error("❌ Auto token refresh check failed:",e),s.E9.recordSecurityAttempt()}finally{s.E9.endSecurityOperation(t)}},r=setInterval(i,12e4);return setTimeout(i,5e3),()=>clearInterval(r)},[e,t.isTokenValid,l]),{checkTokenExpiry:u,clearToken:c,getTokenExpiration:d,isTokenExpired:t.isTokenExpired,isTokenValid:t.isTokenValid,lastValidation:t.lastValidation,refreshToken:l,tokenError:t.tokenError,validateCurrentToken:a,willExpireSoon:t.willExpireSoon}}},21991:(e,t,i)=>{i.d(t,{n:()=>l});var r=i(12115),s=i(54983);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}markComponentMigrated(e){this.migratedComponents.add(e),this.pendingComponents.delete(e),console.log("✅ Migration: ".concat(e," successfully migrated to new architecture"))}markComponentPending(e){this.pendingComponents.add(e),console.log("\uD83D\uDD04 Migration: ".concat(e," marked for migration"))}getMigrationStatus(){let e=Array.from(this.migratedComponents),t=Array.from(this.pendingComponents),i=e.length+t.length,r=i>0?e.length/i*100:0;return{migrated:e,pending:t,total:i,progress:r}}logMigrationStatus(){let e=this.getMigrationStatus();console.log("\uD83D\uDCCA Migration Status:",{progress:"".concat(e.progress.toFixed(1),"%"),migrated:e.migrated.length,pending:e.pending.length,total:e.total})}constructor(){this.migratedComponents=new Set,this.pendingComponents=new Set}}let o={createLegacyApiAdapter:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{enableLogging:t=!0,enableEnhancedFeatures:i=!1,onMigrationWarning:r,onMigrationError:n}=e,o=(0,s.Z8)({enableLogging:t,...n&&{onSecurityError:n}});return t&&console.warn("\uD83D\uDD04 Migration: Using legacy API adapter. Consider migrating to useSecureApiClient for enhanced features."),{hasValidToken:o.hasValidToken,isAuthenticated:o.isAuthenticated,refreshToken:o.refreshToken,sanitizeInput:o.sanitizeInput,secureRequest:o.secureRequest,__migrationInfo:{isUsingNewArchitecture:!0,migrationVersion:"4.0.0",deprecationWarnings:[]}}},createEnhancedApiAdapter:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,s.a8)({enableLogging:!0,...e});return{secureApiClient:t.client,hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,refreshToken:t.refreshToken,sanitizeInput:t.sanitizeInput,secureRequest:t.secureRequest,securityStatus:t.securityStatus,refreshSecurityFeatures:t.refreshSecurityFeatures,updateSecurityConfig:t.updateSecurityConfig,isInitialized:t.isInitialized,isLoading:t.isLoading,error:t.error}},validateMigrationReadiness:function(e){let t=[];return t.push("Consider migrating ".concat(e," to use useSecureApiClient for enhanced features")),t.push("Update imports to use @/lib/api/security instead of @/hooks/security"),t.push("Wrap component tree with SecurityConfigProvider for centralized configuration"),{isReady:!0,warnings:[],recommendations:t}},scanForMigrationCandidates:function(){return["useSecureReportTypes","useSecureApiClient (hooks/api)","components using useSecureApi directly"]},MigrationTracker:n.getInstance(),IMPORT_MAP:{"@/hooks/security/useSecureApi":"@/lib/api/security","@/hooks/security/useCSRFProtection":"@/lib/api/security/hooks","@/hooks/security/useTokenManagement":"@/lib/api/security/hooks","@/hooks/security/useInputValidation":"@/lib/api/security/hooks","@/hooks/security/useSessionSecurity":"@/lib/api/security/hooks"}};class a extends Error{constructor({code:e,details:t,message:i,status:r}){super(i),this.name="ApiError",this.status=r||0,this.code=e||"",this.details=t}}function l(){o.MigrationTracker.markComponentMigrated("useSecureApi");let e=(0,s.Z8)({enableLogging:!0}),t=(0,r.useCallback)(async t=>{try{return await e.secureRequest({url:t.url,method:t.method||"GET",data:t.data,headers:t.headers||{},timeout:t.timeout||1e4})}catch(e){if(e instanceof Error)throw new a({code:"REQUEST_FAILED",message:e.message,details:e});throw e}},[e]);return{hasValidToken:e.hasValidToken,isAuthenticated:e.isAuthenticated,refreshToken:e.refreshToken,sanitizeInput:e.sanitizeInput,secureRequest:t}}},23505:(e,t,i)=>{i.d(t,{z:()=>l});var r=i(10431),s=i(55411),n=i(3695),o=i(46265);class a extends s.O{getSecurityStatus(){let e={lastSecurityCheck:this.lastSecurityCheck,securityConstants:r.$f,securityFeaturesEnabled:this.securityConfig,securityFeaturesInitialized:this.securityInitialized,sessionInfo:this.getSessionInfo(),userInfo:this.extractUserFromToken()};if(this.securityComposer){let t=this.securityComposer.getSecurityStatus();return{...e,hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,threatLevel:this.assessThreatLevel(t)}}return{...e,hasValidToken:!1,isAuthenticated:!1,threatLevel:"critical"}}initializeSecurity(e){try{this.securityConfig.validateSecurityFeatures&&this.validateSecurityFeatures(e),this.securityFeatures=e,this.securityComposer?(this.securityComposer.updateSecurityFeatures(e),this.securityComposer.updateConfig(this.enhancedSecurityConfig)):this.securityComposer=new o.B(e,this.enhancedSecurityConfig),this.securityInitialized=!0,this.lastSecurityCheck=new Date,console.log("\uD83D\uDD10 SecureApiClient: Security features initialized with SecurityUtils integration",{featuresInitialized:Object.keys(e).filter(t=>e[t]),securityConfig:this.enhancedSecurityConfig,timestamp:this.lastSecurityCheck.toISOString()})}catch(e){throw console.error("SecureApiClient: Failed to initialize security features:",e),Error("Security initialization failed: ".concat(e instanceof Error?e.message:"Unknown error"))}}refreshSecurityFeatures(){this.securityFeatures&&this.securityComposer?(this.securityComposer.updateSecurityFeatures(this.securityFeatures),this.lastSecurityCheck=new Date,console.log("\uD83D\uDD04 SecureApiClient: Security features refreshed")):console.warn("SecureApiClient: Cannot refresh - security features not initialized")}async secureRequest(e,t,i,r){var s,o,a,l,c,u;if(!this.securityInitialized||!this.securityComposer)throw new n.v3("Security features not initialized. Call initializeSecurity() first.","SECURITY_NOT_INITIALIZED");this.lastSecurityCheck=new Date;let d={hasValidToken:null!=(c=null==(o=this.securityFeatures)||null==(s=o.tokenManagement)?void 0:s.isTokenValid)&&c,isAuthenticated:null!=(u=null==(l=this.securityFeatures)||null==(a=l.sessionSecurity)?void 0:a.isSessionActive)&&u,session:this.getSessionInfo(),timestamp:this.lastSecurityCheck,user:this.extractUserFromToken()},h={...r,body:i,method:e,url:t};try{await this.performPreRequestSecurityChecks(d);let{body:i,...r}=await this.securityComposer.processRequest(h,d),s=await super[e.toLowerCase()](t,i,r);return await this.performPostRequestSecurityChecks(d),s}catch(e){throw await this.handleSecurityError(e,d),e}}updateSecurityConfig(e){this.securityComposer?(this.securityComposer.updateConfig(e),console.log("\uD83D\uDD10 SecureApiClient: Security configuration updated",e)):console.warn("SecureApiClient: Cannot update config - SecurityComposer not initialized")}assessThreatLevel(e){try{if(!this.securityInitialized||!e.securityFeaturesInitialized)return"critical";if(!e.isAuthenticated||!e.hasValidToken)return"high";let t=r.E9.getSecureItem("auth_token");if(r.E9.detectTimeout()||t&&r.E9.willExpireSoon(t))return"medium";return"low"}catch(e){return console.warn("SecureApiClient: Failed to assess threat level:",e),"medium"}}extractUserFromToken(){try{var e,t;if(!(null==(t=this.securityFeatures)||null==(e=t.tokenManagement)?void 0:e.isTokenValid))return;let i=r.E9.getSecureItem("auth_token");if(!i)return;let s=r.E9.extractEmployeeId(i),n=r.E9.extractUserRole(i);return s||n?{employeeId:s,userRole:n}:void 0}catch(e){console.warn("SecureApiClient: Failed to extract user from token:",e);return}}getSessionInfo(){try{let e=r.E9.getCurrentSessionId(),t=r.E9.detectTimeout();return e?{isTimeout:t,sessionId:e}:void 0}catch(e){console.warn("SecureApiClient: Failed to get session info:",e);return}}async handleSecurityError(e,t){if(!r.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Security error handling blocked by circuit breaker");let i="api-error-handling";if(!r.E9.startSecurityOperation(i))return void console.debug("\uD83D\uDD04 Security error handling already in progress");try{this.securityComposer&&await this.securityComposer.handleError(e,t),e instanceof Error&&((e.message.includes("401")||e.message.includes("Unauthorized")||e.message.includes("Authentication"))&&(console.warn("\uD83D\uDD10 SecureApiClient: Authentication error detected"),r.E9.recordSecurityAttempt(),await this.attemptSessionRecovery()||(r.E9.clearAllCookies(),console.log("\uD83E\uDDF9 Cleared secure storage due to auth failure"))),(e.message.includes("CSRF")||e.message.includes("403"))&&(console.warn("\uD83D\uDEE1️ SecureApiClient: CSRF error detected"),r.E9.recordSecurityAttempt()),(e.message.includes("Network")||e.message.includes("fetch"))&&console.warn("\uD83C\uDF10 SecureApiClient: Network error detected"),(e.message.includes("timeout")||e.message.includes("Timeout"))&&(console.warn("⏰ SecureApiClient: Timeout error detected"),r.E9.recordSecurityAttempt()))}catch(e){console.error("SecureApiClient: Error in security error handling:",e),r.E9.recordSecurityAttempt()}finally{r.E9.endSecurityOperation(i)}}async performPostRequestSecurityChecks(e){try{var t;e.isAuthenticated&&(null==(t=this.securityFeatures)?void 0:t.sessionSecurity)&&this.securityFeatures.sessionSecurity.updateActivity()}catch(e){console.warn("SecureApiClient: Post-request security check failed:",e)}}async performPreRequestSecurityChecks(e){if(!r.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Pre-request security checks blocked by circuit breaker");let t="pre-request-security-check";if(!r.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Pre-request security check already in progress");try{var i;if(r.E9.detectTimeout()&&(console.warn("⏰ Session timeout detected in pre-request check"),r.E9.recordSecurityAttempt(),!await this.attemptSessionRecovery()))throw new n.v3("Session has timed out","SESSION_TIMEOUT");if(e.hasValidToken&&(null==(i=this.securityFeatures)?void 0:i.tokenManagement)){let{isTokenExpired:e}=this.securityFeatures.tokenManagement,t=r.E9.getSecureItem("auth_token");if(e||t&&r.E9.willExpireSoon(t)){console.warn("\uD83D\uDD04 SecureApiClient: Token will expire soon, attempting refresh");try{await this.securityFeatures.tokenManagement.refreshToken()?(console.log("✅ Token refreshed successfully"),r.E9.recordSecuritySuccess()):(console.warn("❌ Token refresh failed"),r.E9.recordSecurityAttempt())}catch(e){console.error("❌ Token refresh error:",e),r.E9.recordSecurityAttempt()}}}r.E9.recordSecuritySuccess()}catch(e){throw console.error("SecureApiClient: Pre-request security check failed:",e),r.E9.recordSecurityAttempt(),e}finally{r.E9.endSecurityOperation(t)}}async attemptSessionRecovery(){try{console.log("\uD83D\uDD27 Attempting session recovery...");let{SessionManager:e}=await Promise.resolve().then(i.bind(i,89699));if(await e.performIntegrityCheck())return console.log("✅ Session integrity check passed"),!0;if(e.recoverFromCorruptedState())return console.log("✅ Session state recovered successfully"),!0;return console.warn("❌ Session recovery failed"),!1}catch(e){return console.error("❌ Session recovery error:",e),!1}}validateSecurityFeatures(e){let t=[];if(this.securityConfig.enableTokenValidation&&e.tokenManagement){let{isTokenExpired:i,isTokenValid:r}=e.tokenManagement;("boolean"!=typeof r||"boolean"!=typeof i)&&t.push("Token management features must provide boolean status indicators")}if(this.securityConfig.enableCSRF&&e.csrfProtection&&"function"!=typeof e.csrfProtection.attachCSRF&&t.push("CSRF protection must provide attachCSRF function"),this.securityConfig.enableInputSanitization&&e.inputValidation&&"function"!=typeof e.inputValidation.sanitizeInput&&t.push("Input validation must provide sanitizeInput function"),this.securityConfig.enableAutoLogout&&e.sessionSecurity){let{clearSession:i,isSessionActive:r}=e.sessionSecurity;("boolean"!=typeof r||"function"!=typeof i)&&t.push("Session security must provide boolean status and clearSession function")}if(t.length>0)throw Error("Security feature validation failed: ".concat(t.join(", ")))}constructor(e,t){var i,s,n,o,a,l;super(e),this.lastSecurityCheck=new Date,this.securityInitialized=!1,this.securityConfig={enableAutoLogout:null==(i=e.enableAutoLogout)||i,enableCSRF:null==(s=e.enableCSRF)||s,enableInputSanitization:null==(n=e.enableInputSanitization)||n,enableTokenValidation:null==(o=e.enableTokenValidation)||o,securityConfig:null!=(a=e.securityConfig)?a:{},validateSecurityFeatures:null==(l=e.validateSecurityFeatures)||l},this.enhancedSecurityConfig={authentication:{autoLogout:!0,enabled:this.securityConfig.enableAutoLogout,redirectOnFailure:!0},csrf:{enabled:this.securityConfig.enableCSRF,excludePaths:[],tokenHeader:"X-CSRF-Token"},http:{baseURL:e.baseURL||"/api",retryAttempts:e.retryAttempts||3,timeout:e.timeout||1e4},inputSanitization:{enabled:this.securityConfig.enableInputSanitization,sanitizers:["xss","sql"]},tokenValidation:{autoRefresh:!0,enabled:this.securityConfig.enableTokenValidation,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES},...e.securityConfig},this.securityComposer=t,console.log("\uD83D\uDD10 SecureApiClient: Initialized with SecurityUtils integration",{constants:{sessionTimeout:r.$f.SESSION_TIMEOUT_MINUTES,tokenThreshold:r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES},securityFeatures:Object.keys(this.securityConfig).filter(e=>this.securityConfig[e])})}}function l(e,t){let i,s={authentication:{autoLogout:!0,enabled:!0,redirectOnFailure:!0},csrf:{enabled:!0,excludePaths:[],tokenHeader:"X-CSRF-Token"},http:{baseURL:"/api",retryAttempts:3,timeout:1e4},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},tokenValidation:{autoRefresh:!0,enabled:!0,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES}},n={enableAutoLogout:!0,enableCSRF:!0,enableInputSanitization:!0,enableTokenValidation:!0,validateSecurityFeatures:!0,...e,securityConfig:{...s,...e.securityConfig}};t&&(i=new o.B(t,n.securityConfig));let l=new a(n,i);return console.log("\uD83C\uDFED SecureApiClient Factory: Created enhanced secure API client",{config:{autoLogout:n.enableAutoLogout,csrf:n.enableCSRF,inputSanitization:n.enableInputSanitization,tokenValidation:n.enableTokenValidation},securityConstants:r.$f,securityFeatures:t?Object.keys(t):[]}),l}},34389:(e,t,i)=>{i.d(t,{k:()=>r});class r{static generateToken(){let e=new Uint8Array(32);if(window.crypto)window.crypto.getRandomValues(e);else for(let t=0;t<e.length;t++)e[t]=Math.floor(256*Math.random());return btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}static validateToken(e){if(!e||"string"!=typeof e||!/^[A-Za-z0-9_-]+$/.test(e))return{isValid:!1,error:"Invalid token format"};if(e.length<32)return{isValid:!1,error:"Token too short"};let t=this.getStoredToken();return t?!t.isValid||t.expiresAt<new Date?{isValid:!1,error:"Stored token expired"}:e!==t.token?{isValid:!1,error:"Token mismatch"}:{isValid:!0,token:e}:{isValid:!1,error:"No stored token found"}}static attachToRequest(e){if(!["POST","PUT","PATCH","DELETE"].includes((e.method||"GET").toUpperCase()))return e;let t=this.getCurrentToken();if(!t)return console.warn("No CSRF token available for request"),e;let i={...e.headers,[this.CSRF_HEADER_NAME]:t.token};return{...e,headers:i}}static getCurrentToken(){let e=this.getStoredToken();return!e||!e.isValid||e.expiresAt<new Date?this.refreshToken():e}static refreshToken(){let e={token:this.generateToken(),expiresAt:new Date(Date.now()+60*this.TOKEN_LIFETIME_MINUTES*1e3),isValid:!0};return this.storeToken(e),e}static clearToken(){try{localStorage.removeItem(this.CSRF_STORAGE_KEY),document.cookie="".concat(this.CSRF_COOKIE_NAME,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}catch(e){console.error("Failed to clear CSRF token:",e)}}static initialize(){return this.refreshToken()}static isProtectionRequired(e){return["POST","PUT","PATCH","DELETE"].includes((e.method||"GET").toUpperCase())}static extractTokenFromResponse(e){return e[this.CSRF_HEADER_NAME.toLowerCase()]||e[this.CSRF_HEADER_NAME]||null}static getStoredToken(){try{let e=localStorage.getItem(this.CSRF_STORAGE_KEY);if(!e)return null;let t=JSON.parse(e);return{...t,expiresAt:new Date(t.expiresAt)}}catch(e){return console.error("Failed to get stored CSRF token:",e),null}}static storeToken(e){try{localStorage.setItem(this.CSRF_STORAGE_KEY,JSON.stringify(e))}catch(e){console.error("Failed to store CSRF token:",e)}}}r.CSRF_HEADER_NAME="X-CSRF-Token",r.CSRF_COOKIE_NAME="csrf-token",r.CSRF_STORAGE_KEY="workhub_csrf_token",r.TOKEN_LIFETIME_MINUTES=60},38750:(e,t,i)=>{i.d(t,{B:()=>r});class r{static hasPermission(e,t){let i=this.normalizeRole(e);return i?this.ROLE_PERMISSIONS[i].includes(t)||this.checkHierarchicalPermission(i,t)?{hasPermission:!0}:{hasPermission:!1,reason:"Access denied. Required role: ".concat(i||"Unknown"),requiredRole:i||"USER"}:{hasPermission:!1,reason:"Invalid user role"}}static hasMinimumRole(e,t){let i=this.normalizeRole(e);return!!i&&this.ROLE_HIERARCHY[i]>=this.ROLE_HIERARCHY[t]}static getPermissionsForRole(e){let t=this.normalizeRole(e);return t?[...this.ROLE_PERMISSIONS[t]]:[]}static hasAllPermissions(e,t){for(let i of t){let t=this.hasPermission(e,i);if(!t.hasPermission)return{hasPermission:!1,reason:"Missing permission: ".concat(i),requiredRole:t.requiredRole||"USER"}}return{hasPermission:!0}}static hasAnyPermission(e,t){for(let i of t)if(this.hasPermission(e,i).hasPermission)return{hasPermission:!0};return{hasPermission:!1,reason:"None of the required permissions found: ".concat(t.join(", "))}}static normalizeRole(e){if(!e||"string"!=typeof e)return null;let t=e.toUpperCase();return Object.keys(this.ROLE_HIERARCHY).includes(t)?t:null}static checkHierarchicalPermission(e,t){let i=this.ROLE_HIERARCHY[e];for(let[e,r]of Object.entries(this.ROLE_HIERARCHY))if(r>i&&this.ROLE_PERMISSIONS[e].includes(t))break;return!1}static getMinimumRoleForPermission(e){for(let[t,i]of Object.entries(this.ROLE_PERMISSIONS))if(i.includes(e))return t}}r.ROLE_HIERARCHY={READONLY:0,USER:1,MANAGER:2,ADMIN:3,SUPER_ADMIN:4},r.ROLE_PERMISSIONS={READONLY:["read","employees:read","vehicles:read","delegations:read","tasks:read","reports:read"],USER:["read","employees:read","vehicles:read","delegations:read","tasks:read","reports:read","settings:read"],MANAGER:["read","write","employees:read","employees:write","vehicles:read","vehicles:write","delegations:read","delegations:write","tasks:read","tasks:write","reports:read","reports:write","settings:read"],ADMIN:["read","write","delete","employees:read","employees:write","employees:delete","vehicles:read","vehicles:write","vehicles:delete","delegations:read","delegations:write","delegations:delete","tasks:read","tasks:write","tasks:delete","reports:read","reports:write","settings:read","settings:write"],SUPER_ADMIN:["read","write","delete","admin","employees:read","employees:write","employees:delete","employees:admin","vehicles:read","vehicles:write","vehicles:delete","vehicles:admin","delegations:read","delegations:write","delegations:delete","delegations:admin","tasks:read","tasks:write","tasks:delete","tasks:admin","reports:read","reports:write","reports:admin","settings:read","settings:write","settings:admin","system:admin","system:audit","system:backup"]}},39807:(e,t,i)=>{i.d(t,{G:()=>d});var r=i(12115),s=i(23505),n=i(40283),o=i(87085),a=i(8264),l=i(20249),c=i(67612),u=i(53953);function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoInitialize:t=!0,enableLogging:i=!0,onSecurityError:d,onSecurityStatusChange:h,...S}=e,{session:p,user:g,loading:m,signOut:y}=(0,n.useAuthContext)(),{config:f,isConfigValid:E}=(0,o.KW)(),k=(0,a.b)(),T=(0,l.C)(null==p?void 0:p.access_token),C=(0,c.j8)(),v=(0,u.o)(),A=(0,r.useRef)(null),R=(0,r.useRef)(null),I=(0,r.useMemo)(()=>!!g&&!!(null==p?void 0:p.access_token)&&!m,[g,null==p?void 0:p.access_token,m]),b=(0,r.useMemo)(()=>I&&T.isTokenValid&&!T.isTokenExpired,[I,T.isTokenValid,T.isTokenExpired]),O=(0,r.useMemo)(()=>({csrfProtection:k,tokenManagement:T,inputValidation:C,sessionSecurity:v}),[k,T,C,v]),_=(0,r.useMemo)(()=>({baseURL:f.http.baseURL,timeout:f.http.timeout,retryAttempts:f.http.retryAttempts,getAuthToken:()=>(null==p?void 0:p.access_token)||null,enableCSRF:f.csrf.enabled,enableInputSanitization:f.inputSanitization.enabled,enableTokenValidation:f.tokenValidation.enabled,enableAutoLogout:f.authentication.autoLogout,securityConfig:f,validateSecurityFeatures:!0,...S}),[f,null==p?void 0:p.access_token,S]),w=(0,r.useMemo)(()=>{try{if(!E)throw Error("Invalid security configuration");let e=(0,s.z)(_,O);return t&&O&&e.initializeSecurity(O),A.current=e,R.current=null,i&&console.log("\uD83D\uDD10 useSecureHttpClient: Client initialized successfully",{isAuthenticated:I,hasValidToken:b,securityFeatures:Object.keys(O).filter(e=>O[e])}),e}catch(t){let e=t instanceof Error?t:Error("Failed to initialize secure client");throw R.current=e,null==d||d(e),console.error("useSecureHttpClient: Failed to initialize client:",e),e}},[_,O,t,E,I,b,i,d]),F=(0,r.useMemo)(()=>{try{return(null==w?void 0:w.getSecurityStatus())||null}catch(e){return console.warn("useSecureHttpClient: Failed to get security status:",e),null}},[w]);(0,r.useEffect)(()=>{F&&h&&h(F)},[F,h]);let P=(0,r.useCallback)(()=>{try{w&&O&&(w.refreshSecurityFeatures(),i&&console.log("\uD83D\uDD04 useSecureHttpClient: Security features refreshed"))}catch(e){console.error("useSecureHttpClient: Failed to refresh security features:",e),null==d||d(e instanceof Error?e:Error("Refresh failed"))}},[w,O,i,d]),V=(0,r.useCallback)(e=>{try{w&&(w.updateSecurityConfig(e),i&&console.log("\uD83D\uDD27 useSecureHttpClient: Security configuration updated",e))}catch(e){console.error("useSecureHttpClient: Failed to update security config:",e),null==d||d(e instanceof Error?e:Error("Config update failed"))}},[w,i,d]),N=(0,r.useCallback)(e=>C.sanitizeInput(e),[C]);return{client:w,isAuthenticated:I,hasValidToken:b,securityStatus:F,refreshToken:(0,r.useCallback)(async()=>{try{return await T.refreshToken()}catch(e){return console.error("useSecureHttpClient: Token refresh failed:",e),null==d||d(e instanceof Error?e:Error("Token refresh failed")),!1}},[T,d]),refreshSecurityFeatures:P,updateSecurityConfig:V,sanitizeInput:N,isInitialized:!!w&&!R.current,isLoading:m,error:R.current}}},40283:(e,t,i)=>{i.d(t,{AuthProvider:()=>h,useAuthContext:()=>S});var r=i(95155),s=i(12115),n=i(72248),o=i(75908),a=i(10431),l=i(28113),c=i(14163);let u="workhub-logout-event",d=(0,s.createContext)(void 0);function h(e){var t,i,h,S;let{children:p}=e,[g,m]=(0,s.useState)(null),[y,f]=(0,s.useState)(null),[E,k]=(0,s.useState)(!0),[T,C]=(0,s.useState)(null),[v,A]=(0,s.useState)(!1),R=e=>{var t;return{app_metadata:e.app_metadata,created_at:e.created_at,email:e.email,email_confirmed_at:e.email_confirmed_at,id:e.id,is_anonymous:e.is_anonymous,is_sso_user:(null==(t=e.app_metadata)?void 0:t.provider)!=="email",last_sign_in_at:e.last_sign_in_at,updated_at:e.updated_at,user_metadata:e.user_metadata}};(0,s.useEffect)(()=>{let e=!0;(async()=>{if(a.E9.initializeCircuitBreaker(),!a.E9.canPerformSecurityCheck()){console.debug("\uD83D\uDD12 Auth initialization blocked by circuit breaker"),e&&(k(!1),A(!0),C("Authentication system temporarily unavailable"));return}let t="auth-initialization";if(!a.E9.startSecurityOperation(t))return console.debug("\uD83D\uDD04 Auth initialization already in progress");try{let{data:{session:t},error:i}=await c.N.auth.getSession();i?(console.error("Error getting initial session:",i),a.E9.recordSecurityAttempt(),C(i.message)):e&&(console.log("✅ Auth initialization successful"),a.E9.recordSecuritySuccess(),m(t),f((null==t?void 0:t.user)?R(t.user):null),t&&(a.Cv.updateActivity(),setTimeout(async()=>{try{await a.Cv.performIntegrityCheck()?console.log("✅ Session integrity check passed after auth initialization"):(console.log("\uD83D\uDCCA Session integrity check failed - automatic recovery will handle this"),a.Cv.recoverFromCorruptedState()||console.warn("⚠️ Session recovery completed with warnings after auth initialization"))}catch(e){console.warn("Session integrity check error:",e)}},1e3)))}catch(t){console.error("Error initializing auth:",t),a.E9.recordSecurityAttempt(),e&&C("Failed to initialize authentication")}finally{a.E9.endSecurityOperation(t),e&&(k(!1),A(!0))}})();let{data:{subscription:t}}=c.N.auth.onAuthStateChange(async(t,i)=>{var r;e&&(console.log("Auth state changed:",t,null==i||null==(r=i.user)?void 0:r.email),m(i),f((null==i?void 0:i.user)?R(i.user):null),k(!1),A(!0),"SIGNED_OUT"===t&&C(null))});return()=>{e=!1,t.unsubscribe()}},[]);let I=async(e,t)=>{try{k(!0),C(null);let{data:i,error:r}=await c.N.auth.signInWithPassword({email:e,password:t});if(r)return C(r.message),{error:r.message};return{}}catch(t){let e=t instanceof Error?t.message:"An unexpected error occurred";return C(e),{error:e}}finally{k(!1)}},b=async()=>{if(!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Sign out blocked by circuit breaker");let e="auth-signout";if(!a.E9.startSecurityOperation(e))return void console.debug("\uD83D\uDD04 Sign out already in progress");try{k(!0),console.log("\uD83D\uDD10 Starting sign out process..."),a.Cv.clearSessionState(),a.E9.clearAllCookies();let{error:e}=await c.N.auth.signOut();e?(console.error("❌ Supabase sign out error:",e),a.E9.recordSecurityAttempt(),C(e.message)):(console.log("✅ Sign out successful"),a.E9.recordSecuritySuccess(),m(null),f(null),C(null))}catch(e){console.error("Sign out error:",e),a.E9.recordSecurityAttempt(),C("Sign out failed")}finally{a.E9.endSecurityOperation(e),k(!1)}},O={clearError:()=>{C(null)},error:T,isInitialized:v,loading:E,session:g?{access_token:g.access_token,user:null!=y?y:null}:null,signIn:I,signOut:b,user:y,userRole:y?(null==(h=y.user_metadata)?void 0:h.role)||(null==(S=y.app_metadata)?void 0:S.role)||"USER":null},_=(0,s.useRef)(!1),w=(0,l.Q)();(0,s.useEffect)(()=>{let e=()=>{var e;return(null==(e=O.session)?void 0:e.access_token)||null};(0,n.cw)(e),(0,o.Hy)(e)},[null==(t=O.session)?void 0:t.access_token]);let F=(0,s.useCallback)(e=>{if(e.key===u&&"true"===e.newValue){if(console.log("\uD83D\uDD10 Cross-tab logout detected. Signing out..."),!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Cross-tab logout blocked by circuit breaker");if(!_.current){_.current=!0;let e="cross-tab-logout";a.E9.startSecurityOperation(e)?O.signOut().finally(()=>{a.E9.endSecurityOperation(e),_.current=!1,localStorage.removeItem(u)}):(console.debug("\uD83D\uDD04 Cross-tab logout already in progress"),_.current=!1)}}},[O.signOut]);return(0,s.useEffect)(()=>{let e=()=>{if(console.warn("\uD83D\uDD10 Critical token refresh failure detected, signing out user"),!a.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Critical refresh failure handling blocked by circuit breaker");if(!_.current){_.current=!0;let e="critical-refresh-failure";a.E9.startSecurityOperation(e)?(a.E9.recordSecurityAttempt(),O.signOut().finally(()=>{a.E9.endSecurityOperation(e),_.current=!1})):(console.debug("\uD83D\uDD04 Critical refresh failure handling already in progress"),_.current=!1)}};return w.subscribe(t=>{"critical_refresh_failed"===t&&e()}),()=>{}},[O.signOut,w]),(0,s.useEffect)(()=>{var e;null==(e=O.session)||e.access_token,globalThis.addEventListener("storage",F),O.session||O.loading||_.current?O.session&&"true"===localStorage.getItem(u)&&localStorage.removeItem(u):localStorage.setItem(u,"true")},[null==(i=O.session)?void 0:i.access_token,O.user,O.loading,F]),(0,s.useEffect)(()=>()=>{globalThis.removeEventListener("storage",F)},[F]),(0,r.jsx)(d.Provider,{value:O,children:p})}function S(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e}},46265:(e,t,i)=>{i.d(t,{B:()=>s});var r=i(10431);class s{async processRequest(e,t){let i={...e};try{var r,s,n,o,a;if((null==(r=this.config.authentication)?void 0:r.enabled)&&this.securityFeatures.sessionSecurity&&!this.securityFeatures.sessionSecurity.isSessionActive)throw Error("Authentication required for secure API calls");if((null==(s=this.config.tokenValidation)?void 0:s.enabled)&&this.securityFeatures.tokenManagement){let{isTokenValid:e,isTokenExpired:t,refreshToken:i}=this.securityFeatures.tokenManagement;if(!e||t){if(console.log("\uD83D\uDD04 SecurityComposer: Token invalid/expired, attempting refresh..."),!await i())throw Error("Token refresh failed - authentication required");console.log("✅ SecurityComposer: Token refreshed successfully")}}if((null==(n=this.config.inputSanitization)?void 0:n.enabled)&&this.securityFeatures.inputValidation&&i.body){let{sanitizeInput:e}=this.securityFeatures.inputValidation;i.body=e(i.body),console.debug("\uD83E\uDDF9 SecurityComposer: Input sanitized using moved hooks")}if((null==(o=this.config.csrf)?void 0:o.enabled)&&this.securityFeatures.csrfProtection){let e=null==(a=i.method)?void 0:a.toUpperCase();if(["POST","PUT","PATCH","DELETE"].includes(e||"")){let{attachCSRF:e}=this.securityFeatures.csrfProtection,t={url:i.url||"",method:i.method||"GET",headers:i.headers||{},body:i.body},r=e(t);i={...i,...r},console.debug("\uD83D\uDEE1️ SecurityComposer: CSRF protection applied using moved hooks")}}return i}catch(e){throw console.error("SecurityComposer: Error processing request:",e),e}}async handleError(e,t){try{var i;(null==(i=this.config.authentication)?void 0:i.autoLogout)&&this.securityFeatures.sessionSecurity&&e instanceof Error&&(e.message.includes("401")||e.message.includes("Authentication")||e.message.includes("Unauthorized"))&&(console.warn("\uD83D\uDD10 SecurityComposer: Authentication error detected, clearing session..."),this.securityFeatures.sessionSecurity.clearSession()),this.securityFeatures.sessionSecurity&&this.securityFeatures.sessionSecurity.updateActivity()}catch(e){console.error("SecurityComposer: Error in error handling:",e)}}getSecurityStatus(){var e,t,i,r,s,n;return{isAuthenticated:null!=(r=null==(e=this.securityFeatures.sessionSecurity)?void 0:e.isSessionActive)&&r,hasValidToken:null!=(s=null==(t=this.securityFeatures.tokenManagement)?void 0:t.isTokenValid)&&s,sessionActive:null!=(n=null==(i=this.securityFeatures.sessionSecurity)?void 0:i.isSessionActive)&&n,securityFeaturesEnabled:this.config,securityFeaturesInitialized:!!(this.securityFeatures.csrfProtection||this.securityFeatures.tokenManagement||this.securityFeatures.inputValidation||this.securityFeatures.sessionSecurity)}}updateConfig(e){this.config={...this.config,...e}}updateSecurityFeatures(e){this.securityFeatures={...this.securityFeatures,...e}}constructor(e,t={}){this.securityFeatures=e,this.config={csrf:{enabled:!0,tokenHeader:"X-CSRF-Token",excludePaths:[]},tokenValidation:{enabled:!0,refreshThreshold:60*r.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES,autoRefresh:!0},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},authentication:{enabled:!0,autoLogout:!0,redirectOnFailure:!0},...t}}}},52474:(e,t,i)=>{i.d(t,{B:()=>r});class r{static validateValue(e,t){let i=[],r=e;return(t.sanitizer&&(r=t.sanitizer(e)),t.required&&(null==e||""===e))?(i.push("This field is required"),{isValid:!1,errors:i}):t.required||null!=e&&""!==e?("string"==typeof e&&(t.minLength&&e.length<t.minLength&&i.push("Minimum length is ".concat(t.minLength," characters")),t.maxLength&&e.length>t.maxLength&&i.push("Maximum length is ".concat(t.maxLength," characters")),t.pattern&&!t.pattern.test(e)&&i.push("Invalid format"),this.containsDangerousContent(e)&&i.push("Contains potentially dangerous content")),t.customValidator&&!t.customValidator(r)&&i.push("Custom validation failed"),{isValid:0===i.length,errors:i,sanitizedValue:r}):{isValid:!0,errors:[],sanitizedValue:r}}static validateObject(e,t){let i=[],r={};for(let[s,n]of Object.entries(t)){let t=e[s],o=this.validateValue(t,n);o.isValid?r[s]=o.sanitizedValue:i.push(...o.errors.map(e=>"".concat(s,": ").concat(e)))}return{isValid:0===i.length,errors:i,sanitizedValue:r}}static sanitizeForXSS(e){return"string"!=typeof e?e:e.replace(this.PATTERNS.XSS,"").replace(/javascript:/gi,"").replace(/vbscript:/gi,"").replace(/data:text\/html/gi,"data:text/plain").replace(/on\w+\s*=/gi,"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}static escapeForDisplay(e){return"string"!=typeof e?e:e.replace(/'/g,"''").replace(/;/g,"").replace(/--/g,"").replace(/\/\*/g,"").replace(/\*\//g,"").replace(/\b(UNION|SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b/gi,"")}static validateEmail(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.EMAIL,maxLength:254,sanitizer:e=>e.toLowerCase().trim()})}static validatePhone(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.PHONE,sanitizer:e=>e.replace(/\s/g,"")})}static validateURL(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.URL,customValidator:e=>{try{return new URL(e),!0}catch(e){return!1}}})}static validateUUID(e){return this.validateValue(e,{required:!0,pattern:this.PATTERNS.UUID})}static containsDangerousContent(e){let t=e.toLowerCase();for(let e of this.DANGEROUS_STRINGS)if(t.includes(e.toLowerCase()))return!0;return!!(this.PATTERNS.SQL_INJECTION.test(e)||this.PATTERNS.XSS.test(e))}static createEmployeeValidationSchema(){return{firstName:{required:!0,minLength:2,maxLength:50,pattern:this.PATTERNS.ALPHA,sanitizer:this.sanitizeForXSS},lastName:{required:!0,minLength:2,maxLength:50,pattern:this.PATTERNS.ALPHA,sanitizer:this.sanitizeForXSS},email:{required:!0,pattern:this.PATTERNS.EMAIL,maxLength:254,sanitizer:e=>this.sanitizeForXSS(e.toLowerCase().trim())},phone:{required:!1,pattern:this.PATTERNS.PHONE,sanitizer:e=>(null==e?void 0:e.replace(/\s/g,""))||""},position:{required:!0,minLength:2,maxLength:100,sanitizer:this.sanitizeForXSS}}}static createVehicleValidationSchema(){return{make:{required:!0,minLength:2,maxLength:50,sanitizer:this.sanitizeForXSS},model:{required:!0,minLength:1,maxLength:50,sanitizer:this.sanitizeForXSS},year:{required:!0,pattern:/^\d{4}$/,customValidator:e=>{let t=parseInt(e.toString());return t>=1900&&t<=new Date().getFullYear()+1}},licensePlate:{required:!0,minLength:2,maxLength:20,sanitizer:e=>this.sanitizeForXSS(e.toUpperCase().trim())},vin:{required:!1,minLength:17,maxLength:17,pattern:/^[A-HJ-NPR-Z0-9]{17}$/i,sanitizer:e=>(null==e?void 0:e.toUpperCase().trim())||""}}}}r.PATTERNS={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,PHONE:/^\+?[\d\s\-\(\)]{10,}$/,URL:/^https?:\/\/[^\s/$.?#].[^\s]*$/,ALPHANUMERIC:/^[a-zA-Z0-9]+$/,ALPHA:/^[a-zA-Z]+$/,NUMERIC:/^\d+$/,UUID:/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,SQL_INJECTION:/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/i,XSS:/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,HTML_TAGS:/<[^>]*>/g},r.DANGEROUS_STRINGS=["javascript:","vbscript:","onload=","onerror=","onclick=","onmouseover=","onfocus=","onblur=","onchange=","onsubmit=","data:text/html","eval(","expression(","setTimeout(","setInterval("]},52747:(e,t,i)=>{i.d(t,{sf:()=>l.sf,Sk:()=>o,a8:()=>a.a8}),i(8264),i(67612);var r=i(12115),s=i(40283),n=i(38750);function o(){let{user:e,userRole:t,loading:i}=(0,s.useAuthContext)(),o=(0,r.useMemo)(()=>!!e&&!i,[e,i]),a=(0,r.useMemo)(()=>{if(!o||!t)return null;let e=t.toUpperCase();return["USER","ADMIN","SUPER_ADMIN"].includes(e)?e:"USER"},[o,t]),l=(0,r.useCallback)(e=>!!a&&n.B.hasPermission(a,e).hasPermission,[a]),c=(0,r.useCallback)(e=>!!a&&n.B.hasAllPermissions(a,e).hasPermission,[a]),u=(0,r.useCallback)(e=>!!a&&n.B.hasAnyPermission(a,e).hasPermission,[a]),d=(0,r.useCallback)(e=>!!a&&n.B.hasMinimumRole(a,e),[a]),h=(0,r.useCallback)(e=>a?n.B.hasPermission(a,e):{hasPermission:!1,reason:"User not authenticated"},[a]),S=(0,r.useCallback)(()=>a?n.B.getPermissionsForRole(a):[],[a]),p=(0,r.useMemo)(()=>"USER"===a,[a]),g=(0,r.useMemo)(()=>"ADMIN"===a,[a]),m=(0,r.useMemo)(()=>"SUPER_ADMIN"===a,[a]);return{userRole:a,isAuthenticated:o,hasPermission:l,hasAllPermissions:c,hasAnyPermission:u,hasMinimumRole:d,getPermissionCheck:h,getAllPermissions:S,isUser:p,isAdmin:g,isSuperAdmin:m}}i(21991);var a=i(54983);i(39807),i(10431);i(53953),i(20249),i(28113);var l=i(87085);i(46265),i(23505)},53953:(e,t,i)=>{i.d(t,{o:()=>o});var r=i(12115),s=i(40283),n=i(10431);function o(){let{signOut:e}=(0,s.useAuthContext)(),[t,i]=(0,r.useState)({concurrentSessions:[],isSessionActive:!0,isSessionExpired:!1,lastActivity:null,sessionId:"",sessionWarning:!1}),o=(0,r.useCallback)(()=>{n.Cv.updateActivity(),i(e=>({...e,isSessionActive:!0,lastActivity:new Date,sessionWarning:!1}))},[]),a=(0,r.useCallback)(()=>{n.Cv.handleCrossTabLogout(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0}))},[]),l=(0,r.useCallback)(()=>{n.Cv.clearSessionState(),i({concurrentSessions:[],isSessionActive:!1,isSessionExpired:!0,lastActivity:null,sessionId:"",sessionWarning:!1})},[]),c=(0,r.useCallback)(()=>{let e=n.Cv.getSessionState(),t=n.Cv.getCurrentSessionId();e&&i(i=>({...i,isSessionActive:e.isActive,isSessionExpired:!1,lastActivity:e.lastActivity,sessionId:t,sessionWarning:!1})),n.Cv.manageConcurrentSessions()},[]);return(0,r.useEffect)(()=>(n.E9.initializeCircuitBreaker(),n.Cv.initialize(),(async()=>{try{if(!await n.Cv.performIntegrityCheck()&&(console.warn("\uD83D\uDCCA Initial session integrity check failed, attempting recovery..."),!n.Cv.recoverFromCorruptedState())){console.error("❌ Initial session recovery failed"),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0}));return}let e=n.Cv.getSessionState(),t=n.Cv.getCurrentSessionId();i(i=>{var r,s;return{...i,isSessionActive:null==(r=null==e?void 0:e.isActive)||r,lastActivity:null!=(s=null==e?void 0:e.lastActivity)?s:new Date,sessionId:t}}),console.log("✅ Session security initialized successfully")}catch(e){console.error("❌ Session security initialization failed:",e),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0}))}})(),()=>{n.Cv.cleanup()}),[]),(0,r.useEffect)(()=>n.Cv.addSessionEventListener(t=>{if(!n.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Session event handling blocked by circuit breaker");let r="session-event-".concat(t.type);if(!n.E9.startSecurityOperation(r))return void console.debug("\uD83D\uDD04 Session event ".concat(t.type," already being handled"));try{switch(t.type){case n.lo.CROSS_TAB_LOGOUT:console.log("\uD83D\uDD04 Cross-tab logout event received"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),setTimeout(()=>e(),100);break;case n.lo.SESSION_TIMEOUT:console.log("⏰ Session timeout event received"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),setTimeout(()=>e(),100);break;case"session_validated":console.log("✅ Session validated event received"),n.E9.recordSecuritySuccess(),i(e=>({...e,isSessionActive:!0,isSessionExpired:!1,sessionWarning:!1}));break;case"token_refresh_success":console.log("\uD83D\uDD04 Token refresh success event received"),n.E9.recordSecuritySuccess(),c();break;case n.lo.TOKEN_REFRESH_FAILED:console.warn("❌ Token refresh failed event received"),n.E9.recordSecurityAttempt(),i(e=>({...e,sessionWarning:!0}));break;default:console.debug("\uD83D\uDD0D Unknown session event: ".concat(t.type))}}catch(e){console.error("❌ Error handling session event ".concat(t.type,":"),e),n.E9.recordSecurityAttempt()}finally{n.E9.endSecurityOperation(r)}}),[e,c]),(0,r.useEffect)(()=>{let t=()=>{if(!n.E9.canPerformSecurityCheck())return void console.debug("\uD83D\uDD12 Session timeout check blocked by circuit breaker");let t="session-timeout-check";if(!n.E9.startSecurityOperation(t))return void console.debug("\uD83D\uDD04 Session timeout check already in progress");try{if(!n.Cv.validateSessionConsistency()&&(console.warn("\uD83D\uDCCA Session state inconsistent, attempting recovery..."),!n.Cv.recoverFromCorruptedState())){console.error("❌ Session recovery failed, forcing logout"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),e();return}if(n.Cv.detectTimeout()){console.log("⏰ Session timeout detected"),n.E9.recordSecurityAttempt(),i(e=>({...e,isSessionActive:!1,isSessionExpired:!0})),setTimeout(()=>e(),100);return}let t=n.Cv.getSessionState();(null==t?void 0:t.lastActivity)&&Date.now()-t.lastActivity.getTime()>15e5&&i(e=>({...e,sessionWarning:!0})),n.E9.recordSecuritySuccess()}catch(e){console.error("❌ Session timeout check failed:",e),n.E9.recordSecurityAttempt()}finally{n.E9.endSecurityOperation(t)}},r=setInterval(t,12e4);return setTimeout(t,1e3),()=>clearInterval(r)},[e]),(0,r.useEffect)(()=>{let e=setInterval(()=>{n.Cv.manageConcurrentSessions()},3e5);return()=>clearInterval(e)},[]),{clearSession:l,concurrentSessions:t.concurrentSessions,handleCrossTabLogout:a,isSessionActive:t.isSessionActive,isSessionExpired:t.isSessionExpired,lastActivity:t.lastActivity,refreshSession:c,sessionId:t.sessionId,sessionWarning:t.sessionWarning,updateActivity:o}}},54983:(e,t,i)=>{i.d(t,{Z8:()=>o,a8:()=>n});var r=i(12115),s=i(39807);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{client:t,isAuthenticated:i,hasValidToken:n,securityStatus:o,refreshToken:a,refreshSecurityFeatures:l,updateSecurityConfig:c,sanitizeInput:u,isInitialized:d,isLoading:h,error:S}=(0,s.G)(e);return{hasValidToken:n,isAuthenticated:i,refreshToken:a,sanitizeInput:u,secureRequest:(0,r.useCallback)(async e=>{let{data:i,headers:r={},method:s="GET",timeout:n,url:o}=e;try{let e,a={headers:r,timeout:n||1e4};switch(s.toUpperCase()){case"GET":e=await t.get(o,a);break;case"POST":e=await t.post(o,i,a);break;case"PUT":e=await t.put(o,i,a);break;case"PATCH":e=await t.patch(o,i,a);break;case"DELETE":e=await t.delete(o,a);break;default:throw Error("Unsupported HTTP method: ".concat(s))}return{data:e,headers:{},status:200,statusText:"OK"}}catch(e){if(e instanceof Error)throw e;throw Error("Request failed")}},[t]),client:t,securityStatus:o,refreshSecurityFeatures:l,updateSecurityConfig:c,isInitialized:d,isLoading:h,error:S}}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=n(e);return{hasValidToken:t.hasValidToken,isAuthenticated:t.isAuthenticated,refreshToken:t.refreshToken,sanitizeInput:t.sanitizeInput,secureRequest:t.secureRequest}}},67612:(e,t,i)=>{i.d(t,{j8:()=>n});var r=i(12115),s=i(52474);function n(e){let[t,i]=(0,r.useState)({isValid:!0,errors:{},touched:{},isValidating:!1}),n=(0,r.useCallback)((e,t,r)=>{i(e=>({...e,isValidating:!0}));let n=s.B.validateValue(t,r);return i(t=>({...t,isValidating:!1,errors:{...t.errors,[e]:n.isValid?[]:n.errors},isValid:n.isValid&&Object.values({...t.errors,[e]:n.isValid?[]:n.errors}).every(e=>0===e.length)})),n},[]),o=(0,r.useCallback)((e,t)=>{i(e=>({...e,isValidating:!0}));let r=s.B.validateObject(e,t),n={};return r.errors.forEach(e=>{let[t,...i]=e.split(": "),r=i.join(": ");t&&(n[t]||(n[t]=[]),n[t].push(r))}),i(e=>({...e,isValidating:!1,errors:n,isValid:r.isValid})),r},[]),a=(0,r.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];i(i=>({...i,touched:{...i.touched,[e]:t}}))},[]),l=(0,r.useCallback)(e=>{i(t=>({...t,errors:{...t.errors,[e]:[]},isValid:Object.values({...t.errors,[e]:[]}).every(e=>0===e.length)}))},[]),c=(0,r.useCallback)(()=>{i(e=>({...e,errors:{},isValid:!0}))},[]),u=(0,r.useCallback)(()=>{i({isValid:!0,errors:{},touched:{},isValidating:!1})},[]),d=(0,r.useCallback)(e=>{if("string"==typeof e)return s.B.sanitizeForXSS(e);if(Array.isArray(e))return e.map(e=>d(e));if(e&&"object"==typeof e){let t={};for(let[i,r]of Object.entries(e))t[i]=d(r);return t}return e},[]),h=(0,r.useCallback)(e=>{let i=t.errors[e];return i&&i.length>0&&i[0]||null},[t.errors]),S=(0,r.useCallback)(e=>{let i=t.errors[e];return!!i&&i.length>0},[t.errors]),p=(0,r.useCallback)(e=>t.touched[e]||!1,[t.touched]);return{isValid:t.isValid,errors:t.errors,touched:t.touched,isValidating:t.isValidating,validateField:n,validateForm:o,setFieldTouched:a,clearFieldErrors:l,clearAllErrors:c,resetValidation:u,sanitizeInput:d,getFieldError:h,hasFieldError:S,isFieldTouched:p}}},72248:(e,t,i)=>{i.d(t,{cw:()=>o,getGlobalAuthTokenProvider:()=>a,uE:()=>c});var r=i(55411);i(25982),i(3695);var s=i(38549);i(52747),i(976),i(12430),i(90137),i(97966),i(3619),i(1955);let n=null;function o(e){n=e}function a(){return n}let l=(0,s.Qq)(),c=new r.O({baseURL:l.apiBaseUrl,getAuthToken:function(){return n?n():null},headers:{"Content-Type":"application/json"},retryAttempts:3,timeout:1e4})},87085:(e,t,i)=>{i.d(t,{KW:()=>u,sf:()=>c});var r=i(95155),s=i(12115),n=i(38549),o=i(10431);let a=(()=>{let e=(0,n.Qq)();return{csrf:{enabled:!0,tokenHeader:"X-CSRF-Token",excludePaths:["/api/health","/api/status"]},tokenValidation:{enabled:!0,refreshThreshold:60*o.$f.TOKEN_EXPIRY_THRESHOLD_MINUTES,autoRefresh:!0},inputSanitization:{enabled:!0,sanitizers:["xss","sql"]},authentication:{enabled:!0,autoLogout:!0,redirectOnFailure:!0},http:{baseURL:e.apiBaseUrl,timeout:1e4,retryAttempts:3}}})(),l=(0,s.createContext)(null);function c(e){let{children:t,initialConfig:i={},configVersion:n="1.0.0",onConfigChange:c,validateConfig:u=!0}=e,d=(0,s.useMemo)(()=>({...a,...i,csrf:{...a.csrf,...i.csrf},tokenValidation:{...a.tokenValidation,...i.tokenValidation},inputSanitization:{...a.inputSanitization,...i.inputSanitization},authentication:{...a.authentication,...i.authentication},http:{...a.http,...i.http}}),[i]),h=(0,s.useMemo)(()=>{if(!u)return!0;try{if(!d.http.baseURL||d.http.timeout<=0||d.http.retryAttempts<0||d.tokenValidation.refreshThreshold<=0||d.csrf.enabled&&!d.csrf.tokenHeader||d.inputSanitization.enabled&&0===d.inputSanitization.sanitizers.length)return!1;return!0}catch(e){return console.error("SecurityConfigProvider: Configuration validation failed:",e),!1}},[d,u]),S=(0,s.useMemo)(()=>e=>{let t={...d,...e,csrf:{...d.csrf,...e.csrf},tokenValidation:{...d.tokenValidation,...e.tokenValidation},inputSanitization:{...d.inputSanitization,...e.inputSanitization},authentication:{...d.authentication,...e.authentication},http:{...d.http,...e.http}};null==c||c(t)},[d,c]),p=(0,s.useMemo)(()=>()=>{null==c||c(a)},[c]),g=(0,s.useMemo)(()=>({config:d,updateConfig:S,resetConfig:p,isConfigValid:h,configVersion:n}),[d,S,p,h,n]);return s.useEffect(()=>{console.log("\uD83D\uDD27 SecurityConfigProvider: Configuration initialized",{isValid:h,version:n,constants:o.$f,config:{csrf:d.csrf.enabled,tokenValidation:d.tokenValidation.enabled,inputSanitization:d.inputSanitization.enabled,authentication:d.authentication.enabled}})},[d,h,n]),(0,r.jsx)(l.Provider,{value:g,children:t})}function u(){let e=(0,s.useContext)(l);if(!e)throw Error("useSecurityConfig must be used within a SecurityConfigProvider");return e}}}]);