(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7721],{19018:(e,s,t)=>{"use strict";t.d(s,{Breadcrumb:()=>c,BreadcrumbItem:()=>o,BreadcrumbLink:()=>m,BreadcrumbList:()=>d,BreadcrumbPage:()=>x,BreadcrumbSeparator:()=>u});var a=t(95155),r=t(99708),l=t(73158),i=(t(3561),t(12115)),n=t(54036);let c=i.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb",className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",t),ref:s,...r})});c.displayName="Breadcrumb";let d=i.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("ol",{className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",t),ref:s,...r})});d.displayName="BreadcrumbList";let o=i.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("li",{className:(0,n.cn)("inline-flex items-center gap-1.5",t),ref:s,...r})});o.displayName="BreadcrumbItem";let m=i.forwardRef((e,s)=>{let{asChild:t,className:l,...i}=e,c=t?r.DX:"a";return(0,a.jsx)(c,{className:(0,n.cn)("transition-colors hover:text-foreground",l),ref:s,...i})});m.displayName="BreadcrumbLink";let x=i.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,n.cn)("font-normal text-foreground",t),ref:s,role:"link",...r})});x.displayName="BreadcrumbPage";let u=e=>{let{children:s,className:t,...r}=e;return(0,a.jsx)("span",{"aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),role:"presentation",...r,children:null!=s?s:(0,a.jsx)(l.A,{className:"size-4"})})};u.displayName="BreadcrumbSeparator"},24840:(e,s,t)=>{Promise.resolve().then(t.bind(t,92999)),Promise.resolve().then(t.bind(t,45876)),Promise.resolve().then(t.bind(t,44005)),Promise.resolve().then(t.bind(t,59482)),Promise.resolve().then(t.bind(t,88331)),Promise.resolve().then(t.bind(t,43503)),Promise.resolve().then(t.bind(t,76422)),Promise.resolve().then(t.bind(t,81665)),Promise.resolve().then(t.bind(t,89440)),Promise.resolve().then(t.bind(t,40283))},30008:(e,s,t)=>{"use strict";t.d(s,{BB:()=>o,Gz:()=>m,Hs:()=>d,Rt:()=>n,TR:()=>h,Zz:()=>u,_r:()=>x,n8:()=>i,u_:()=>j,vW:()=>c}),t(42366);var a=t(90111),r=t(75908);let l={all:["reliability"],health:()=>[...l.all,"health"],healthDetailed:()=>[...l.health(),"detailed"],healthDependencies:()=>[...l.health(),"dependencies"],healthTrends:()=>[...l.health(),"trends"],circuitBreakers:()=>[...l.all,"circuit-breakers"],circuitBreakerHistory:()=>[...l.circuitBreakers(),"history"],httpRequestMetrics:()=>[...l.metrics(),"http-requests"],metrics:()=>[...l.all,"metrics"],deduplication:()=>[...l.metrics(),"deduplication"],alerts:()=>[...l.all,"alerts"],alertsHistory:(e,s)=>[...l.alerts(),"history",e,s],alertsStatistics:()=>[...l.alerts(),"statistics"]},i=e=>(0,a.ol)([...l.health()],()=>r.reliabilityApiService.getSystemHealth(),"health",e),n=e=>(0,a.ol)([...l.healthDetailed()],()=>r.reliabilityApiService.getDetailedHealth(),"health",e),c=e=>(0,a.ol)([...l.healthDependencies()],()=>r.reliabilityApiService.getDependencyHealth(),"health",e),d=e=>(0,a.ol)([...l.circuitBreakers()],()=>r.reliabilityApiService.getCircuitBreakerStatus(),"circuit-breakers",e),o=e=>(0,a.ol)([...l.metrics()],()=>r.reliabilityApiService.getMetrics(),"metrics",e),m=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"24h",s=arguments.length>1?arguments[1]:void 0;return(0,a.ol)([...l.healthTrends(),e],()=>r.reliabilityApiService.getHealthTrends(e),"health",{...s,staleTime:3e5,gcTime:6e5})},x=e=>(0,a.ol)([...l.httpRequestMetrics()],()=>r.reliabilityApiService.getHttpRequestMetrics(),"metrics",{...e,staleTime:6e4,gcTime:18e4}),u=e=>(0,a.ol)([...l.alerts()],()=>r.reliabilityApiService.getActiveAlerts(),"alerts",e),h=e=>(0,a.ol)([...l.alertsStatistics()],()=>r.reliabilityApiService.getAlertStatistics(),"alerts",e),j=e=>(0,a.ol)([...l.all,"dashboard"],()=>r.reliabilityApiService.getReliabilityDashboardData(),"metrics",{staleTime:3e4,...e})},30356:(e,s,t)=>{"use strict";t.d(s,{C:()=>d,z:()=>c});var a=t(95155),r=t(54059),l=t(70154),i=t(12115),n=t(54036);let c=i.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,n.cn)("grid gap-2",t),...l,ref:s})});c.displayName=r.bL.displayName;let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(r.q7,{className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i,children:(0,a.jsx)(r.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"size-2.5 fill-current text-current"})})})});d.displayName=r.q7.displayName},43503:(e,s,t)=>{"use strict";t.d(s,{DashboardSettings:()=>v});var a=t(95155),r=t(18271),l=t(29471),i=t(34214),n=t(35200);t(12115);var c=t(6560),d=t(26126),o=t(66695),m=t(85057),x=t(30356),u=t(59409),h=t(22346),j=t(76202),g=t(80333),p=t(17313),f=t(14056);let v=e=>{let{className:s=""}=e,t=(0,f.XD)(e=>e.preferences),v=(0,f.XD)(e=>e.setDashboardLayout),N=(0,f.XD)(e=>e.setGridColumns),b=(0,f.XD)(e=>e.setRefreshInterval),y=(0,f.XD)(e=>e.setNotificationPreferences),w=(0,f.XD)(e=>e.toggleWidget),k=(0,f.XD)(e=>e.resetPreferencesToDefaults),A=(0,f.XD)(e=>e.monitoring.isEnabled),C=(0,f.XD)(e=>e.setMonitoringEnabled),S=[{value:5e3,label:"5 seconds"},{value:1e4,label:"10 seconds"},{value:15e3,label:"15 seconds"},{value:3e4,label:"30 seconds"},{value:6e4,label:"1 minute"},{value:3e5,label:"5 minutes"}],R=(e,s)=>{b(e,parseInt(s))},M=(e,s)=>{y({[e]:s})};return(0,a.jsxs)(o.Zp,{className:s,children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5"}),"Dashboard Settings"]}),(0,a.jsx)(o.BT,{children:"Customize your reliability monitoring dashboard"})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)(p.tU,{defaultValue:"layout",className:"w-full",children:[(0,a.jsxs)(p.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(p.Xi,{value:"layout",children:"Layout"}),(0,a.jsx)(p.Xi,{value:"widgets",children:"Widgets"}),(0,a.jsx)(p.Xi,{value:"refresh",children:"Refresh"}),(0,a.jsx)(p.Xi,{value:"notifications",children:"Alerts"})]}),(0,a.jsx)(p.av,{value:"layout",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"text-base font-medium",children:"Dashboard Layout"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose how widgets are arranged on the dashboard"})]}),(0,a.jsxs)(x.z,{value:t.dashboardLayout.layout,onValueChange:e=>{v(e)},className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 rounded-lg border p-4",children:[(0,a.jsx)(x.C,{value:"grid",id:"grid"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)(m.J,{htmlFor:"grid",children:"Grid"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 rounded-lg border p-4",children:[(0,a.jsx)(x.C,{value:"list",id:"list"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)(m.J,{htmlFor:"list",children:"List"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 rounded-lg border p-4",children:[(0,a.jsx)(x.C,{value:"compact",id:"compact"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)(m.J,{htmlFor:"compact",children:"Compact"})]})]})]}),"grid"===t.dashboardLayout.layout&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(m.J,{className:"text-sm font-medium",children:["Grid Columns: ",t.dashboardLayout.gridColumns]}),(0,a.jsx)(j.A,{value:[t.dashboardLayout.gridColumns],onValueChange:e=>{let s=e[0];void 0!==s&&N(s)},max:6,min:1,step:1,className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,a.jsx)("span",{children:"1 column"}),(0,a.jsx)("span",{children:"6 columns"})]})]})]})}),(0,a.jsx)(p.av,{value:"widgets",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"text-base font-medium",children:"Widget Visibility"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose which widgets to display on the dashboard"})]}),(0,a.jsx)("div",{className:"space-y-3",children:[{id:"system-health",name:"System Health",description:"Overall system status"},{id:"health-status-indicators",name:"Health Status Indicators",description:"Real-time component health status"},{id:"circuit-breakers",name:"Circuit Breakers Overview",description:"Circuit breaker status and failure protection"},{id:"circuit-breaker-metrics",name:"Circuit Breaker Metrics",description:"Performance metrics and failure rate charts"},{id:"performance-overview",name:"Performance Overview",description:"Comprehensive performance metrics and scoring"},{id:"system-metrics",name:"System Performance",description:"Performance-focused system metrics monitoring"},{id:"http-metrics",name:"HTTP Request Metrics",description:"Request performance and throughput analysis"},{id:"performance-metrics",name:"Performance Metrics",description:"System performance monitoring and analysis"},{id:"deduplication-metrics",name:"Deduplication Metrics",description:"Cache efficiency and request deduplication analysis"},{id:"active-alerts",name:"Active Alerts",description:"Current system alerts and notifications"},{id:"alert-statistics",name:"Alert Statistics",description:"Alert trends and statistical analysis"},{id:"dependency-status",name:"Dependency Status",description:"External dependency health monitoring"},{id:"dependency-health",name:"Dependency Health",description:"External service and dependency status"},{id:"health-trends",name:"Health Trends",description:"Historical health trend visualization"}].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.J,{className:"font-medium",children:e.name}),t.dashboardLayout.visibleWidgets.has(e.id)&&(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:"Visible"})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]}),(0,a.jsx)(g.d,{checked:t.dashboardLayout.visibleWidgets.has(e.id),onCheckedChange:()=>w(e.id)})]},e.id))})]})}),(0,a.jsx)(p.av,{value:"refresh",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"text-base font-medium",children:"Refresh Intervals"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure how often data is refreshed for different components"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Health Monitoring"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"System health checks"})]}),(0,a.jsxs)(u.l6,{value:t.refreshIntervals.health.toString(),onValueChange:e=>R("health",e),children:[(0,a.jsx)(u.bq,{className:"w-32",children:(0,a.jsx)(u.yv,{})}),(0,a.jsx)(u.gC,{children:S.map(e=>(0,a.jsx)(u.eb,{value:e.value.toString(),children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Circuit Breakers"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Circuit breaker status"})]}),(0,a.jsxs)(u.l6,{value:t.refreshIntervals["circuit-breakers"].toString(),onValueChange:e=>R("circuit-breakers",e),children:[(0,a.jsx)(u.bq,{className:"w-32",children:(0,a.jsx)(u.yv,{})}),(0,a.jsx)(u.gC,{children:S.map(e=>(0,a.jsx)(u.eb,{value:e.value.toString(),children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Performance Metrics"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"System performance data"})]}),(0,a.jsxs)(u.l6,{value:t.refreshIntervals.metrics.toString(),onValueChange:e=>R("metrics",e),children:[(0,a.jsx)(u.bq,{className:"w-32",children:(0,a.jsx)(u.yv,{})}),(0,a.jsx)(u.gC,{children:S.map(e=>(0,a.jsx)(u.eb,{value:e.value.toString(),children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Alerts"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Alert notifications"})]}),(0,a.jsxs)(u.l6,{value:t.refreshIntervals.alerts.toString(),onValueChange:e=>R("alerts",e),children:[(0,a.jsx)(u.bq,{className:"w-32",children:(0,a.jsx)(u.yv,{})}),(0,a.jsx)(u.gC,{children:S.map(e=>(0,a.jsx)(u.eb,{value:e.value.toString(),children:e.label},e.value))})]})]})]})]})}),(0,a.jsx)(p.av,{value:"notifications",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"text-base font-medium",children:"Alert Notifications"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Configure how you receive alert notifications"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Sound Notifications"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Play sound for new alerts"})]}),(0,a.jsx)(g.d,{checked:t.notifications.soundEnabled,onCheckedChange:e=>M("soundEnabled",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Desktop Notifications"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Show browser notifications"})]}),(0,a.jsx)(g.d,{checked:t.notifications.desktopEnabled,onCheckedChange:e=>M("desktopEnabled",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Minimum Severity"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Only notify for alerts above this level"})]}),(0,a.jsxs)(u.l6,{value:t.notifications.minimumSeverity,onValueChange:e=>M("minimumSeverity",e),children:[(0,a.jsx)(u.bq,{className:"w-32",children:(0,a.jsx)(u.yv,{})}),(0,a.jsxs)(u.gC,{children:[(0,a.jsx)(u.eb,{value:"low",children:"Low"}),(0,a.jsx)(u.eb,{value:"medium",children:"Medium"}),(0,a.jsx)(u.eb,{value:"high",children:"High"}),(0,a.jsx)(u.eb,{value:"critical",children:"Critical"})]})]})]})]})]})})]}),(0,a.jsx)(h.w,{className:"my-6"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(m.J,{className:"font-medium",children:"Monitoring Status"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:A?"Real-time monitoring active":"Monitoring paused"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.d,{checked:A,onCheckedChange:C}),(0,a.jsx)(c.r,{actionType:"secondary",size:"sm",onClick:k,children:"Reset to Defaults"})]})]})]})]})}},44005:(e,s,t)=>{"use strict";t.d(s,{ConnectionStatusIndicator:()=>m});var a=t(95155),r=t(8376),l=t(50172),i=t(32087);t(12115);var n=t(26126),c=t(46102),d=t(55012),o=t(54036);let m=e=>{let{className:s="",showText:t=!0,size:m="md"}=e,{connectionState:x,isConnected:u}=(0,d.gv)(),h=(()=>{switch(x){case"connected":return{bgColor:"bg-green-100 dark:bg-green-900/20",borderColor:"border-green-200 dark:border-green-800",color:"text-green-600 dark:text-green-400",description:"Real-time connection active",icon:r.A,text:"Connected",variant:"default"};case"connecting":case"reconnecting":return{animate:!0,bgColor:"bg-yellow-100 dark:bg-yellow-900/20",borderColor:"border-yellow-200 dark:border-yellow-800",color:"text-yellow-600 dark:text-yellow-400",description:"connecting"===x?"Establishing connection...":"Attempting to reconnect...",icon:l.A,text:"connecting"===x?"Connecting":"Reconnecting",variant:"secondary"};default:return{bgColor:"bg-red-100 dark:bg-red-900/20",borderColor:"border-red-200 dark:border-red-800",color:"text-red-600 dark:text-red-400",description:"error"===x?"Connection error occurred":"Real-time connection lost",icon:i.A,text:"error"===x?"Error":"Disconnected",variant:"destructive"}}})(),j=h.icon,g=(()=>{switch(m){case"lg":return{badge:"text-sm px-3 py-1.5",icon:"h-5 w-5",text:"text-sm"};case"sm":return{badge:"text-xs px-2 py-1",icon:"h-3 w-3",text:"text-xs"};default:return{badge:"text-xs px-2.5 py-1",icon:"h-4 w-4",text:"text-sm"}}})();return(0,a.jsx)(c.Bc,{children:(0,a.jsxs)(c.m_,{children:[(0,a.jsx)(c.k$,{asChild:!0,children:(0,a.jsxs)("div",{"aria-label":"Connection status: ".concat(h.text),className:(0,o.cn)("flex items-center gap-2",s),role:"status",children:[(0,a.jsx)("div",{className:(0,o.cn)("flex items-center justify-center rounded-full p-1",h.bgColor,h.borderColor,"border"),children:(0,a.jsx)(j,{"aria-hidden":"true",className:(0,o.cn)(g.icon,h.color,h.animate&&"animate-spin")})}),t&&(0,a.jsx)(n.E,{className:(0,o.cn)(g.badge,"font-medium"),variant:h.variant,children:h.text})]})}),(0,a.jsxs)(c.ZI,{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:h.text}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:h.description})]})]})})}},45876:(e,s,t)=>{"use strict";t.d(s,{ProtectedRoute:()=>m});var a=t(95155),r=t(50172),l=t(31949),i=t(45731);t(12115);var n=t(40283),c=t(55365),d=t(66695),o=t(92999);function m(e){let{allowedRoles:s=[],children:t,fallback:m,requireEmailVerification:x=!0}=e,{error:u,loading:h,session:j,user:g,userRole:p}=(0,n.useAuthContext)();if(h)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:(0,a.jsx)(d.Zp,{className:"mx-auto w-full max-w-md",children:(0,a.jsxs)(d.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(r.A,{className:"mb-4 size-8 animate-spin text-blue-600"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Verifying security credentials..."})]})})});if(u&&!g)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center text-red-600",children:[(0,a.jsx)(l.A,{className:"mr-2 size-5"}),"Authentication Error"]}),(0,a.jsx)(d.BT,{children:"There was a problem with the security system"})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsx)(c.Fc,{variant:"destructive",children:(0,a.jsx)(c.TN,{children:u})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(o.LoginForm,{})})]})]})});if(!g||!j)return m?(0,a.jsx)(a.Fragment,{children:m}):(0,a.jsx)(o.LoginForm,{onSuccess:()=>{globalThis.location.href="/"}});if(x&&!g.email_confirmed_at)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center text-yellow-600",children:[(0,a.jsx)(i.A,{className:"mr-2 size-5"}),"Email Verification Required"]}),(0,a.jsx)(d.BT,{children:"Please verify your email address to continue"})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsxs)(c.Fc,{children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsxs)(c.TN,{children:["We've sent a verification email to ",(0,a.jsx)("strong",{children:g.email}),". Please check your inbox and click the verification link to access the system."]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Didn't receive the email? Check your spam folder or contact your administrator."})})]})]})});if(s.length>0){let e=p||"USER";if(!s.includes(e))return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center text-red-600",children:[(0,a.jsx)(i.A,{className:"mr-2 size-5"}),"Access Denied"]}),(0,a.jsx)(d.BT,{children:"Insufficient permissions to access this resource"})]}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsx)(c.Fc,{variant:"destructive",children:(0,a.jsxs)(c.TN,{children:["Your account (",e,") does not have permission to access this area. Required roles: ",s.join(", ")]})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Contact your administrator if you believe this is an error."})})]})]})})}return(0,a.jsx)(a.Fragment,{children:t})}},46102:(e,s,t)=>{"use strict";t.d(s,{Bc:()=>n,ZI:()=>o,k$:()=>d,m_:()=>c});var a=t(95155),r=t(89613),l=t(12115),i=t(54036);let n=r.Kq,c=r.bL,d=r.l9,o=l.forwardRef((e,s)=>{let{className:t,sideOffset:l=4,...n}=e;return(0,a.jsx)(r.UC,{className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),ref:s,sideOffset:l,...n})});o.displayName=r.UC.displayName},59482:(e,s,t)=>{"use strict";t.d(s,{DashboardGrid:()=>se});var a=t(95155),r=t(12115),l=t(81665),i=t(55365),n=t(30285),c=t(76570),d=t(45731),o=t(28044),m=t(31949),x=t(37648),u=t(8376),h=t(77070),j=t(26126),g=t(66695),p=t(24944),f=t(30008),v=t(54036);let N=e=>{let{className:s="",showDetails:t=!0}=e,{data:r,isLoading:l,error:i}=(0,f.Hs)(),n=e=>{switch(e){case"CLOSED":return{icon:c.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",description:"Operating normally"};case"HALF_OPEN":return{icon:d.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Testing",description:"Testing recovery"};case"OPEN":return{icon:o.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Failed",description:"Blocking requests"}}},N=()=>{if(!(null==r?void 0:r.summary))return 0;let{total:e,closed:s}=r.summary;return e>0?Math.round(s/e*100):100};if(l)return(0,a.jsx)("div",{className:(0,v.cn)("space-y-4",s),children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:Array.from({length:4}).map((e,s)=>(0,a.jsxs)(g.Zp,{className:"animate-pulse",children:[(0,a.jsx)(g.aR,{className:"pb-2",children:(0,a.jsx)("div",{className:"h-4 w-20 bg-muted rounded"})}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"h-8 w-12 bg-muted rounded mb-2"}),(0,a.jsx)("div",{className:"h-3 w-16 bg-muted rounded"})]})]},s))})});if(i||!r)return(0,a.jsx)("div",{className:(0,v.cn)("flex items-center justify-center py-8",s),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker data"})]})});let{summary:b,circuitBreakers:y}=r,w=N(),k=(()=>{let e=N();return e>=90?"healthy":e>=70?"degraded":"unhealthy"})();return(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Total"}),(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:b.total}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Circuit breakers"})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Healthy"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:b.closed}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Operating normally"})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Testing"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:b.halfOpen}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Testing recovery"})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Failed"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:b.open}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Blocking requests"})]})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:(0,v.cn)("h-5 w-5","healthy"===k?"text-green-600":"degraded"===k?"text-yellow-600":"text-red-600")}),"Overall Health",(0,a.jsxs)(j.E,{variant:"healthy"===k?"default":"degraded"===k?"secondary":"destructive",children:[w,"%"]})]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(p.k,{value:w,className:"h-2","aria-label":"Circuit breaker health: ".concat(w,"%")}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-muted-foreground",children:[b.closed," of ",b.total," circuit breakers healthy"]}),b.open>0&&(0,a.jsxs)("span",{className:"flex items-center gap-1 text-red-600",children:[(0,a.jsx)(h.A,{className:"h-3 w-3"}),b.open," failing"]})]})]})})]}),t&&y.length>0&&(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breaker Status"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[y.slice(0,5).map(e=>{let s=n(e.state),t=s.icon;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,v.cn)("p-2 rounded-full",s.bgColor),children:(0,a.jsx)(t,{className:(0,v.cn)("h-4 w-4",s.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.description})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)(j.E,{variant:"CLOSED"===e.state?"default":"HALF_OPEN"===e.state?"secondary":"destructive",children:s.label}),e.failureCount>0&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[e.failureCount," failures"]})]})]},e.name)}),y.length>5&&(0,a.jsx)("div",{className:"text-center pt-2",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["+",y.length-5," more circuit breakers"]})})]})})]})]})};var b=t(67554),y=t(59099),w=t(62523),k=t(59409),A=t(85127),C=t(46102);let S=e=>{let{className:s="",maxItems:t=10}=e,{data:l,isLoading:i,error:u,refetch:h}=(0,f.Hs)(),[p,N]=(0,r.useState)(""),[S,R]=(0,r.useState)("all"),M=e=>{switch(e){case"CLOSED":return{icon:c.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",variant:"default"};case"HALF_OPEN":return{icon:d.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Testing",variant:"secondary"};case"OPEN":return{icon:o.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Failed",variant:"destructive"}}},T=e=>{if(!e)return"Never";let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/6e4),r=Math.floor(a/60),l=Math.floor(r/24);return l>0?"".concat(l,"d ago"):r>0?"".concat(r,"h ago"):a>0?"".concat(a,"m ago"):"Just now"},B=e=>{if(!e)return null;let s=new Date,t=new Date(e).getTime()-s.getTime();if(t<=0)return"Ready to test";let a=Math.floor(t/6e4),r=Math.floor(t%6e4/1e3);return a>0?"".concat(a,"m ").concat(r,"s"):"".concat(r,"s")},D=r.useMemo(()=>(null==l?void 0:l.circuitBreakers)?l.circuitBreakers.filter(e=>{let s=e.name.toLowerCase().includes(p.toLowerCase()),t="all"===S||e.state===S;return s&&t}).slice(0,t):[],[null==l?void 0:l.circuitBreakers,p,S,t]);return i?(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breakers"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 animate-pulse",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-muted rounded-full"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-1/3"}),(0,a.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,a.jsx)("div",{className:"h-6 w-16 bg-muted rounded"})]},s))})})]}):u||!l?(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breakers"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:"Failed to load circuit breaker data"}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>h(),children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Retry"]})]})})})]}):(0,a.jsx)(C.Bc,{children:(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsxs)(g.aR,{children:[(0,a.jsx)(g.ZB,{children:"Circuit Breakers"}),(0,a.jsxs)("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(w.p,{placeholder:"Search circuit breakers...",value:p,onChange:e=>N(e.target.value),className:"w-64"}),(0,a.jsxs)(k.l6,{value:S,onValueChange:e=>R(e),children:[(0,a.jsx)(k.bq,{className:"w-32",children:(0,a.jsx)(k.yv,{})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"all",children:"All States"}),(0,a.jsx)(k.eb,{value:"CLOSED",children:"Healthy"}),(0,a.jsx)(k.eb,{value:"HALF_OPEN",children:"Testing"}),(0,a.jsx)(k.eb,{value:"OPEN",children:"Failed"})]})]})]}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>h(),children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)(g.Wu,{children:[0===D.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(d.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:p||"all"!==S?"No circuit breakers match your filters":"No circuit breakers found"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)(A.XI,{children:[(0,a.jsx)(A.A0,{children:(0,a.jsxs)(A.Hj,{children:[(0,a.jsx)(A.nd,{children:"Name"}),(0,a.jsx)(A.nd,{children:"Status"}),(0,a.jsx)(A.nd,{children:"Failures"}),(0,a.jsx)(A.nd,{children:"Last Failure"}),(0,a.jsx)(A.nd,{children:"Next Attempt"})]})}),(0,a.jsx)(A.BF,{children:D.map(e=>{let s=M(e.state),t=s.icon,r=B(e.nextAttempt);return(0,a.jsxs)(A.Hj,{children:[(0,a.jsx)(A.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,v.cn)("p-2 rounded-full",s.bgColor),children:(0,a.jsx)(t,{className:(0,v.cn)("h-4 w-4",s.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),e.timeout&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Timeout: ",e.timeout,"ms"]})]})]})}),(0,a.jsx)(A.nA,{children:(0,a.jsx)(j.E,{variant:s.variant,children:s.label})}),(0,a.jsx)(A.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:(0,v.cn)("font-medium",e.failureCount>0?"text-red-600":"text-muted-foreground"),children:e.failureCount}),void 0!==e.successCount&&(0,a.jsxs)(C.m_,{children:[(0,a.jsx)(C.k$,{children:(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["+",e.successCount]})}),(0,a.jsxs)(C.ZI,{children:[e.successCount," successful requests"]})]})]})}),(0,a.jsx)(A.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"}),T(e.lastFailureTime)]})}),(0,a.jsx)(A.nA,{children:r?(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 text-yellow-600"}),(0,a.jsx)("span",{className:"text-yellow-600",children:r})]}):(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"—"})})]},e.name)})})]})}),l.circuitBreakers.length>t&&(0,a.jsx)("div",{className:"text-center pt-4 border-t",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",Math.min(D.length,t)," of ",l.circuitBreakers.length," circuit breakers"]})})]})]})})};var R=t(80659),M=t(5263),T=t(9041),B=t(83540),D=t(8782),E=t(34e3),L=t(54811),Z=t(94517),P=t(24026);let F={dark:".dark",light:""},H=r.createContext(null);function z(){let e=r.useContext(H);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let W=r.forwardRef((e,s)=>{let{children:t,className:l,config:i,id:n,...c}=e,d=r.useId(),o="chart-".concat(n||d.replaceAll(":",""));return(0,a.jsx)(H.Provider,{value:{config:i},children:(0,a.jsxs)("div",{className:(0,v.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",l),"data-chart":o,ref:s,...c,children:[(0,a.jsx)(q,{config:i,id:o}),(0,a.jsx)(B.u,{children:t})]})})});W.displayName="Chart";let q=e=>{let{config:s,id:t}=e,r=Object.entries(s).filter(e=>{let[,s]=e;return s.theme||s.color});return 0===r.length?null:(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(F).map(e=>{let[s,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(r.map(e=>{var t;let[a,r]=e,l=(null==(t=r.theme)?void 0:t[s])||r.color;return l?"  --color-".concat(a,": ").concat(l,";"):null}).join("\n"),"\n}\n")}).join("\n")}})},O=Z.m,I=r.forwardRef((e,s)=>{let{active:t,className:l,color:i,formatter:n,hideIndicator:c=!1,hideLabel:d=!1,indicator:o="dot",label:m,labelClassName:x,labelFormatter:u,labelKey:h,nameKey:j,payload:g}=e,{config:p}=z(),f=r.useMemo(()=>{var e;if(d||!(null==g?void 0:g.length))return null;let[s]=g,t="".concat(h||(null==s?void 0:s.dataKey)||(null==s?void 0:s.name)||"value"),r=U(p,s,t),l=h||"string"!=typeof m?null==r?void 0:r.label:(null==(e=p[m])?void 0:e.label)||m;return u?(0,a.jsx)("div",{className:(0,v.cn)("font-medium",x),children:u(l,g)}):l?(0,a.jsx)("div",{className:(0,v.cn)("font-medium",x),children:l}):null},[m,u,g,d,x,p,h]);if(!t||!(null==g?void 0:g.length))return null;let N=1===g.length&&"dot"!==o;return(0,a.jsxs)("div",{className:(0,v.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",l),ref:s,children:[N?null:f,(0,a.jsx)("div",{className:"grid gap-1.5",children:g.map((e,s)=>{let t="".concat(j||e.name||e.dataKey||"value"),r=U(p,e,t),l=i||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,v.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===o&&"items-center"),children:n&&(null==e?void 0:e.value)!==void 0&&e.name?n(e.value,e.name,e,s,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==r?void 0:r.icon)?(0,a.jsx)(r.icon,{}):!c&&(0,a.jsx)("div",{className:(0,v.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===o,"my-0.5":N&&"dashed"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"w-1":"line"===o}),style:{"--color-bg":l,"--color-border":l}}),(0,a.jsxs)("div",{className:(0,v.cn)("flex flex-1 justify-between leading-none",N?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[N?f:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==r?void 0:r.label)||e.name})]}),e.value&&(0,a.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});function U(e,s,t){if("object"!=typeof s||null===s)return;let a="payload"in s&&"object"==typeof s.payload&&null!==s.payload?s.payload:void 0,r=t;return t in s&&"string"==typeof s[t]?r=s[t]:a&&t in a&&"string"==typeof a[t]&&(r=a[t]),r in e?e[r]:e[t]}I.displayName="ChartTooltip",P.s,r.forwardRef((e,s)=>{let{className:t,hideIcon:r=!1,nameKey:l,payload:i,verticalAlign:n="bottom"}=e,{config:c}=z();return(null==i?void 0:i.length)?(0,a.jsx)("div",{className:(0,v.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",t),ref:s,children:i.map(e=>{let s="".concat(l||e.dataKey||"value"),t=U(c,e,s);return(0,a.jsxs)("div",{className:(0,v.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==t?void 0:t.icon)&&!r?(0,a.jsx)(t.icon,{}):(0,a.jsx)("div",{className:"size-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==t?void 0:t.label]},e.value)})}):null}).displayName="ChartLegend";let _=e=>{let{className:s="",showCharts:t=!0}=e,{data:r,isLoading:l,error:i}=(0,f.Hs)();if(l)return(0,a.jsx)("div",{className:(0,v.cn)("space-y-4",s),children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,a.jsxs)(g.Zp,{className:"animate-pulse",children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)("div",{className:"h-4 w-24 bg-muted rounded"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"h-20 bg-muted rounded"})})]},s))})});if(i||!r)return(0,a.jsx)("div",{className:(0,v.cn)("flex items-center justify-center py-8",s),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker metrics"})]})});let n=(()=>{if(!(null==r?void 0:r.summary))return[];let{closed:e,halfOpen:s,open:t}=r.summary;return[{name:"Healthy",value:e,color:"#10b981",percentage:r.summary.total>0?Math.round(e/r.summary.total*100):0},{name:"Testing",value:s,color:"#f59e0b",percentage:r.summary.total>0?Math.round(s/r.summary.total*100):0},{name:"Failed",value:t,color:"#ef4444",percentage:r.summary.total>0?Math.round(t/r.summary.total*100):0}].filter(e=>e.value>0)})(),c=(()=>{if(!(null==r?void 0:r.circuitBreakers))return{totalFailures:0,totalSuccesses:0,averageFailureRate:0,healthScore:100};let e=r.circuitBreakers.reduce((e,s)=>e+s.failureCount,0),s=r.circuitBreakers.reduce((e,s)=>e+(s.successCount||0),0),t=e+s;return{totalFailures:e,totalSuccesses:s,averageFailureRate:t>0?e/t*100:0,healthScore:r.summary.total>0?Math.round(r.summary.closed/r.summary.total*100):100}})();return(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Health Score"}),(0,a.jsx)(R.A,{className:(0,v.cn)("h-4 w-4",c.healthScore>=90?"text-green-600":c.healthScore>=70?"text-yellow-600":"text-red-600")})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsxs)("div",{className:(0,v.cn)("text-2xl font-bold",c.healthScore>=90?"text-green-600":c.healthScore>=70?"text-yellow-600":"text-red-600"),children:[c.healthScore,"%"]}),(0,a.jsx)(p.k,{value:c.healthScore,className:"mt-2 h-2","aria-label":"Health score: ".concat(c.healthScore,"%")})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Failure Rate"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[c.averageFailureRate.toFixed(1),"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[c.totalFailures," failures"]})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Successes"}),(0,a.jsx)(R.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:c.totalSuccesses.toLocaleString()}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Successful requests"})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(g.ZB,{className:"text-sm font-medium",children:"Active"}),(0,a.jsx)(M.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:r.summary.total}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Circuit breakers"})]})]})]}),t&&n.length>0&&(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-2",children:[(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"h-5 w-5"}),"State Distribution"]})}),(0,a.jsxs)(g.Wu,{children:[(0,a.jsx)(W,{config:{healthy:{label:"Healthy",color:"#10b981"},testing:{label:"Testing",color:"#f59e0b"},failed:{label:"Failed",color:"#ef4444"}},className:"h-[200px]",children:(0,a.jsx)(B.u,{width:"100%",height:"100%",children:(0,a.jsxs)(D.r,{children:[(0,a.jsx)(E.F,{data:n,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:2,dataKey:"value",children:n.map((e,s)=>(0,a.jsx)(L.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{formatter:(e,s)=>{var t;return["".concat(e," (").concat(null==(t=n.find(e=>e.name===s))?void 0:t.percentage,"%)"),s]}})})]})})}),(0,a.jsx)("div",{className:"flex justify-center gap-4 mt-4",children:n.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.name," (",e.percentage,"%)"]})]},e.name))})]})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),"Performance Summary"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Success Rate"}),(0,a.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:[(100-c.averageFailureRate).toFixed(1),"%"]})]}),(0,a.jsx)(p.k,{value:100-c.averageFailureRate,className:"h-2","aria-label":"Success rate: ".concat((100-c.averageFailureRate).toFixed(1),"%")})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Failure Rate"}),(0,a.jsxs)("span",{className:"text-sm text-red-600 font-medium",children:[c.averageFailureRate.toFixed(1),"%"]})]}),(0,a.jsx)(p.k,{value:c.averageFailureRate,className:"h-2","aria-label":"Failure rate: ".concat(c.averageFailureRate.toFixed(1),"%")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-2 border-t",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-600",children:c.totalSuccesses.toLocaleString()}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Successes"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-red-600",children:c.totalFailures.toLocaleString()}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Failures"})]})]})]})})]})]})]})};var X=t(3638),K=t(51920);let G=e=>{let s=new Date,t=[],a="1h"===e?1:"6h"===e?6:"24h"===e?24:"7d"===e?168:720,r=Math.min(a/2,20);for(let e=0;e<r;e++){let r=new Date(s.getTime()-Math.random()*a*36e5),l=["CLOSED","OPEN","HALF_OPEN"],i=l[Math.floor(Math.random()*l.length)],n=l[Math.floor(Math.random()*l.length)];i!==n&&t.push({id:"event-".concat(e),circuitBreakerName:"service-".concat(Math.floor(5*Math.random())+1),fromState:i,toState:n,timestamp:r.toISOString(),reason:"CLOSED"===i&&"OPEN"===n?"Failure threshold exceeded":"OPEN"===i&&"HALF_OPEN"===n?"Timeout period elapsed":"HALF_OPEN"===i&&"CLOSED"===n?"Test request succeeded":"HALF_OPEN"===i&&"OPEN"===n?"Test request failed":"State transition",failureCount:Math.floor(10*Math.random())})}return t.sort((e,s)=>new Date(s.timestamp).getTime()-new Date(e.timestamp).getTime())},$=e=>{let{className:s="",timeRange:t="24h"}=e,{data:l,isLoading:i,error:n}=(0,f.Hs)(),[u,h]=(0,r.useState)(t),p=e=>{switch(e){case"CLOSED":return{icon:c.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",variant:"default"};case"HALF_OPEN":return{icon:d.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Testing",variant:"secondary"};case"OPEN":return{icon:o.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Failed",variant:"destructive"}}},N=e=>{let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/6e4),r=Math.floor(a/60),l=Math.floor(r/24);return l>0?"".concat(l,"d ago"):r>0?"".concat(r,"h ago"):a>0?"".concat(a,"m ago"):"Just now"},b=(e,s)=>"CLOSED"===e&&"OPEN"===s?"failure":"OPEN"===e&&"HALF_OPEN"===s?"recovery-attempt":"HALF_OPEN"===e&&"CLOSED"===s?"recovery-success":"HALF_OPEN"===e&&"OPEN"===s?"recovery-failure":"neutral",y=G(u);return i?(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breaker History"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 animate-pulse",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-muted rounded-full"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-2/3"}),(0,a.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,a.jsx)("div",{className:"h-6 w-16 bg-muted rounded"})]},s))})})]}):n||!l?(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breaker History"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker history"})]})})})]}):(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(X.A,{className:"h-5 w-5"}),"Circuit Breaker History"]}),(0,a.jsxs)(k.l6,{value:u,onValueChange:e=>h(e),children:[(0,a.jsx)(k.bq,{className:"w-32",children:(0,a.jsx)(k.yv,{})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"1h",children:"Last Hour"}),(0,a.jsx)(k.eb,{value:"6h",children:"Last 6 Hours"}),(0,a.jsx)(k.eb,{value:"24h",children:"Last 24 Hours"}),(0,a.jsx)(k.eb,{value:"7d",children:"Last 7 Days"}),(0,a.jsx)(k.eb,{value:"30d",children:"Last 30 Days"})]})]})]})}),(0,a.jsx)(g.Wu,{children:0===y.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(K.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No state changes in the selected time range"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"This indicates stable circuit breaker operation"})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6",children:[(0,a.jsxs)("div",{className:"text-center p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)("div",{className:"text-lg font-bold",children:y.length}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"State Changes"})]}),(0,a.jsxs)("div",{className:"text-center p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-red-600",children:y.filter(e=>e.fromState&&e.toState&&"failure"===b(e.fromState,e.toState)).length}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Failures"})]}),(0,a.jsxs)("div",{className:"text-center p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-600",children:y.filter(e=>e.fromState&&e.toState&&"recovery-success"===b(e.fromState,e.toState)).length}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Recoveries"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:y.map((e,s)=>{if(!e.fromState||!e.toState)return null;let t=p(e.fromState),r=p(e.toState),l=b(e.fromState,e.toState),i=t.icon,n=r.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-4 p-3 rounded-lg border bg-card",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:(0,v.cn)("w-3 h-3 rounded-full","failure"===l?"bg-red-500":"recovery-success"===l?"bg-green-500":"recovery-attempt"===l?"bg-yellow-500":"bg-muted-foreground")}),s<y.length-1&&(0,a.jsx)("div",{className:"w-px h-8 bg-border mt-2"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.circuitBreakerName}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,v.cn)("p-1 rounded",t.bgColor),children:(0,a.jsx)(i,{className:(0,v.cn)("h-3 w-3",t.color)})}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"→"}),(0,a.jsx)("div",{className:(0,v.cn)("p-1 rounded",r.bgColor),children:(0,a.jsx)(n,{className:(0,v.cn)("h-3 w-3",r.color)})})]}),(0,a.jsx)(j.E,{variant:r.variant,className:"text-xs",children:r.label})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.reason,e.failureCount>0&&(0,a.jsxs)("span",{className:"ml-2 text-red-600",children:["(",e.failureCount," failures)"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"}),N(e.timestamp)]})]})]})]},e.id)})})]})})]})};var J=t(36931),V=t(3819);let Q=e=>{let{className:s="",maxAlerts:t=10}=e,{data:l,isLoading:i,error:c}=(0,f.Zz)(),[h,p]=(0,r.useState)("all"),N=e=>{switch(e){case"critical":return{icon:o.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",variant:"destructive",label:"Critical"};case"high":return{icon:m.A,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-100 dark:bg-orange-900/20",variant:"destructive",label:"High"};case"medium":return{icon:d.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",variant:"secondary",label:"Medium"};case"low":return{icon:J.A,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",variant:"outline",label:"Low"}}},b=e=>{let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/6e4),r=Math.floor(a/60),l=Math.floor(r/24);return l>0?"".concat(l,"d ago"):r>0?"".concat(r,"h ago"):a>0?"".concat(a,"m ago"):"Just now"},y=r.useMemo(()=>l?l.filter(e=>{var s;let t="circuit-breaker"===e.type||(null==(s=e.source)?void 0:s.includes("circuit"))||e.message.toLowerCase().includes("circuit"),a="all"===h||e.severity===h,r="active"===e.status;return t&&a&&r}).slice(0,t):[],[l,h,t]),w=async e=>{console.log("Acknowledging alert:",e)},A=async e=>{console.log("Resolving alert:",e)};return i?(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breaker Alerts"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 animate-pulse",children:[(0,a.jsx)("div",{className:"h-10 w-10 bg-muted rounded-full"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-muted rounded w-2/3"}),(0,a.jsx)("div",{className:"h-3 bg-muted rounded w-1/2"})]}),(0,a.jsx)("div",{className:"h-6 w-16 bg-muted rounded"})]},s))})})]}):c||!l?(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Circuit Breaker Alerts"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load circuit breaker alerts"})]})})})]}):(0,a.jsx)(C.Bc,{children:(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(J.A,{className:"h-5 w-5"}),"Circuit Breaker Alerts",y.length>0&&(0,a.jsx)(j.E,{variant:"destructive",className:"ml-2",children:y.length})]}),(0,a.jsxs)(k.l6,{value:h,onValueChange:e=>p(e),children:[(0,a.jsx)(k.bq,{className:"w-32",children:(0,a.jsx)(k.yv,{})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"all",children:"All Levels"}),(0,a.jsx)(k.eb,{value:"critical",children:"Critical"}),(0,a.jsx)(k.eb,{value:"high",children:"High"}),(0,a.jsx)(k.eb,{value:"medium",children:"Medium"}),(0,a.jsx)(k.eb,{value:"low",children:"Low"})]})]})]})}),(0,a.jsx)(g.Wu,{children:0===y.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(u.A,{className:"mx-auto h-8 w-8 text-green-600 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No active circuit breaker alerts"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"All circuit breakers are operating normally"})]}):(0,a.jsxs)("div",{className:"space-y-3",children:[y.map(e=>{let s=N(e.severity),t=s.icon;return(0,a.jsxs)("div",{className:(0,v.cn)("flex items-start gap-3 p-3 rounded-lg border",s.bgColor),children:[(0,a.jsx)("div",{className:(0,v.cn)("p-2 rounded-full bg-background/50"),children:(0,a.jsx)(t,{className:(0,v.cn)("h-4 w-4",s.color)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.message}),e.source&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),(0,a.jsx)(j.E,{variant:s.variant,className:"text-xs",children:s.label})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[(0,a.jsx)(x.A,{className:"h-3 w-3"}),b(e.timestamp)]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)(C.m_,{children:[(0,a.jsx)(C.k$,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>w(e.id),className:"h-7 w-7 p-0",children:(0,a.jsx)(V.A,{className:"h-3 w-3"})})}),(0,a.jsx)(C.ZI,{children:"Acknowledge alert"})]}),(0,a.jsxs)(C.m_,{children:[(0,a.jsx)(C.k$,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>A(e.id),className:"h-7 w-7 p-0",children:(0,a.jsx)(u.A,{className:"h-3 w-3"})})}),(0,a.jsx)(C.ZI,{children:"Resolve alert"})]})]})]})]})]},e.id)}),l.filter(e=>{var s;return("circuit-breaker"===e.type||(null==(s=e.source)?void 0:s.includes("circuit")))&&"active"===e.status}).length>t&&(0,a.jsxs)("div",{className:"text-center pt-3 border-t",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["+",l.filter(e=>{var s;return("circuit-breaker"===e.type||(null==(s=e.source)?void 0:s.includes("circuit")))&&"active"===e.status}).length-t," ","more alerts"]}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",className:"mt-2",children:"View All Alerts"})]})]})})]})})};var Y=t(24371),ee=t(68098),es=t(87057),et=t(57679),ea=t(40207),er=t(45727);let el=e=>{let{className:s="",showDetails:t=!0,showDependencies:r=!0}=e,{data:l,isLoading:i,error:n}=(0,f.n8)(),{data:c}=(0,f.Rt)(),d=e=>{switch(e){case"healthy":return{icon:u.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",label:"Healthy",variant:"default"};case"degraded":return{icon:m.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",label:"Degraded",variant:"secondary"};case"unhealthy":return{icon:Y.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",label:"Unhealthy",variant:"destructive"};default:return{icon:x.A,color:"text-gray-600 dark:text-gray-400",bgColor:"bg-gray-100 dark:bg-gray-900/20",label:"Unknown",variant:"outline"}}};if(i)return(0,a.jsx)("div",{className:(0,v.cn)("space-y-4",s),children:(0,a.jsxs)(g.Zp,{className:"animate-pulse",children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)("div",{className:"h-6 w-32 bg-muted rounded"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-20 bg-muted rounded"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)("div",{className:"h-16 bg-muted rounded"}),(0,a.jsx)("div",{className:"h-16 bg-muted rounded"})]})]})})]})});if(n||!l)return(0,a.jsx)("div",{className:(0,v.cn)("flex items-center justify-center py-8",s),children:(0,a.jsx)(g.Zp,{className:"w-full",children:(0,a.jsx)(g.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load system health data"})]})})})});let o=d(l.status),h=o.icon,N=(()=>{if(!(null==c?void 0:c.checks))return 0;let e=c.checks,s=Object.values(e).filter(e=>"healthy"===e.status).length;return Object.keys(e).length>0?Math.round(s/Object.keys(e).length*100):100})();return(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"h-5 w-5 text-red-500"}),"System Health",(0,a.jsx)(j.E,{variant:o.variant,children:o.label})]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:(0,v.cn)("flex items-center gap-4 p-4 rounded-lg",o.bgColor),children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(h,{className:(0,v.cn)("h-8 w-8",o.color)})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("h3",{className:"font-semibold text-lg",children:["System is ",o.label]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"All systems operational"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:(0,v.cn)("text-2xl font-bold",o.color),children:[N,"%"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Health Score"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Overall Health"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[N,"%"]})]}),(0,a.jsx)(p.k,{value:N,className:"h-2","aria-label":"System health score: ".concat(N,"%")})]}),t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Uptime"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(e=>{if(!e)return"Unknown";let s=Math.floor(e/864e5),t=Math.floor(e%864e5/36e5),a=Math.floor(e%36e5/6e4);return s>0?"".concat(s,"d ").concat(t,"h"):t>0?"".concat(t,"h ").concat(a,"m"):"".concat(a,"m")})(l.uptime)})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)(es.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Version"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:l.version||"Unknown"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)(et.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Environment"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:l.environment||"Unknown"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Last Check"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:l.timestamp?new Date(l.timestamp).toLocaleTimeString():"Unknown"})]})]})]})]})})]}),r&&(null==c?void 0:c.checks)&&(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(er.A,{className:"h-5 w-5"}),"System Components"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:Object.entries(c.checks).map(e=>{let[s,t]=e,r=d(t.status),l=r.icon;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,v.cn)("p-2 rounded-full",r.bgColor),children:(0,a.jsx)(l,{className:(0,v.cn)("h-4 w-4",r.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium capitalize",children:s.replace(/([A-Z])/g," $1").trim()}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:r.label})]})]}),(0,a.jsx)(j.E,{variant:r.variant,children:r.label})]},s)})})})]})]})};var ei=t(68856);let en=e=>{switch(e){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"degraded":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"unhealthy":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},ec=e=>s=>{let{className:t}=s;switch(e){case"healthy":return(0,a.jsx)(u.A,{className:t});case"degraded":return(0,a.jsx)(m.A,{className:t});case"unhealthy":return(0,a.jsx)(Y.A,{className:t});default:return(0,a.jsx)(x.A,{className:t})}},ed=e=>e?e<1e3?"".concat(e,"ms"):"".concat((e/1e3).toFixed(2),"s"):"N/A",eo=e=>{let{className:s="",showDetails:t=!0,showResponseTimes:l=!0,compact:i=!1}=e,{data:n,isLoading:c,error:o}=(0,f.n8)(),{data:m,isLoading:x,error:u}=(0,f.Rt)(),h=o||u,p=r.useMemo(()=>{var e,s,t,r,l;return(null==m?void 0:m.checks)?[{id:"database",label:"Database",status:m.checks.database.status,icon:e=>{let{className:s}=e;return(0,a.jsx)(er.A,{className:s})},responseTime:m.checks.database.responseTime||0,details:"PostgreSQL connection and query performance"},{id:"supabase",label:"Supabase",status:m.checks.supabase.status,icon:e=>{let{className:s}=e;return(0,a.jsx)(et.A,{className:s})},responseTime:m.checks.supabase.responseTime||0,details:"Supabase API and real-time connections"},{id:"cache",label:"Cache",status:m.checks.cache.status,icon:e=>{let{className:s}=e;return(0,a.jsx)(es.A,{className:s})},responseTime:m.checks.cache.responseTime||0,details:(null==(s=m.checks.cache.details)||null==(e=s.redis)?void 0:e.status)||"Cache system status"},{id:"circuitBreakers",label:"Circuit Breakers",status:m.checks.circuitBreakers.status,icon:e=>{let{className:s}=e;return(0,a.jsx)(d.A,{className:s})},responseTime:m.checks.circuitBreakers.responseTime||0,details:"".concat((null==(t=m.checks.circuitBreakers.details)?void 0:t.openBreakers)||0," open breakers")},{id:"systemResources",label:"System Resources",status:m.checks.systemResources.status,icon:e=>{let{className:s}=e;return(0,a.jsx)(ea.A,{className:s})},responseTime:m.checks.systemResources.responseTime||0,details:"".concat((null==(l=m.checks.systemResources.details)||null==(r=l.memory)?void 0:r.usagePercent)||0,"% memory usage")},{id:"businessLogic",label:"Business Logic",status:m.checks.businessLogic.status,icon:e=>{let{className:s}=e;return(0,a.jsx)(ee.A,{className:s})},responseTime:m.checks.businessLogic.responseTime||0,details:"Core application services and workflows"}]:[]},[m]);return c||x?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-32"}),(0,a.jsx)(ei.E,{className:"h-5 w-20"})]}),(0,a.jsx)("div",{className:"grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",children:Array.from({length:6}).map((e,s)=>(0,a.jsx)(ei.E,{className:"h-16 w-full"},s))})]}):h?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",s),children:[(0,a.jsx)(Y.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load health status"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:h.message||"Unable to retrieve system health information"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"System Health Status"})]}),n&&(0,a.jsx)(j.E,{variant:"outline",className:(0,v.cn)("font-medium",en(n.status)),children:n.status.toUpperCase()})]}),(0,a.jsx)("div",{className:(0,v.cn)("grid gap-3",i?"grid-cols-2 sm:grid-cols-3":"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"),children:p.map(e=>{let s=ec(e.status),r=e.icon;return(0,a.jsx)(g.Zp,{className:(0,v.cn)("transition-all duration-200 hover:shadow-md",en(e.status),i?"p-3":""),children:(0,a.jsxs)(g.Wu,{className:(0,v.cn)("p-4",i&&"p-3"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r,{className:(0,v.cn)("h-4 w-4",i?"h-3 w-3":"h-4 w-4")}),(0,a.jsx)("span",{className:(0,v.cn)("font-medium",i?"text-xs":"text-sm"),children:e.label})]}),(0,a.jsx)(s,{className:(0,v.cn)(i?"h-3 w-3":"h-4 w-4")})]}),l&&e.responseTime&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("p",{className:(0,v.cn)("text-muted-foreground","text-xs"),children:["Response: ",ed(e.responseTime)]})}),t&&e.details&&!i&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.details})})]})},e.id)})}),(null==m?void 0:m.summary)&&!i&&(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 pt-2",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-green-600",children:m.summary.healthyChecks}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Healthy"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-yellow-600",children:m.summary.degradedChecks}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Degraded"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-red-600",children:m.summary.unhealthyChecks}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unhealthy"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:m.summary.totalChecks}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total"})]})]})]})};var em=t(48639),ex=t(89829);let eu=e=>{switch(e){case"healthy":return"text-green-600 bg-green-50 border-green-200";case"degraded":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"unhealthy":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},eh=e=>s=>{let{className:t}=s;switch(e){case"healthy":return(0,a.jsx)(u.A,{className:t});case"degraded":return(0,a.jsx)(m.A,{className:t});case"unhealthy":return(0,a.jsx)(Y.A,{className:t});default:return(0,a.jsx)(x.A,{className:t})}},ej=e=>e?e<1e3?"".concat(e,"ms"):"".concat((e/1e3).toFixed(2),"s"):"N/A",eg=e=>void 0===e?"N/A":"".concat(e.toFixed(2),"%"),ep=e=>e?e>=99.9?"text-green-600":e>=99?"text-yellow-600":"text-red-600":"text-gray-500",ef=e=>{let{Icon:s,className:t}=e;return(0,a.jsx)(s,{className:t})},ev=e=>{let{className:s="",showResponseTimes:t=!0,showAvailability:l=!0,compact:i=!1}=e,{data:n,isLoading:c,error:d}=(0,f.vW)(),o=r.useCallback(e=>{let s=e.toLowerCase();return s.includes("database")||s.includes("db")?e=>(0,a.jsx)(ef,{Icon:er.A,...e}):s.includes("api")||s.includes("service")?e=>(0,a.jsx)(ef,{Icon:es.A,...e}):s.includes("cache")||s.includes("redis")?e=>(0,a.jsx)(ef,{Icon:em.A}):s.includes("auth")||s.includes("jwt")?e=>(0,a.jsx)(ef,{Icon:ex.A,...e}):e=>(0,a.jsx)(ef,{Icon:es.A,...e})},[]),m=r.useCallback(e=>{let s=e.toLowerCase();return s.includes("database")?"Primary database connection":s.includes("supabase")?"Supabase backend services":s.includes("redis")?"Cache and session storage":s.includes("websocket")?"Real-time communication":s.includes("auth")?"Authentication services":"External service dependency"},[]),x=r.useMemo(()=>(null==n?void 0:n.dependencies)?(Array.isArray(n.dependencies)?n.dependencies:Object.entries(n.dependencies).map(e=>{let[s,t]=e;return{name:s,status:t.status,responseTime:t.responseTime,lastChecked:t.lastChecked||new Date().toISOString(),error:t.error}})).map(e=>({id:e.name.toLowerCase().replace(/\s+/g,"-"),name:e.name,status:e.status,responseTime:e.responseTime||0,availability:10*Math.random()+90,lastChecked:e.lastChecked,icon:o(e.name),description:m(e.name)})):[],[n,o,m]);return c?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-40"}),(0,a.jsx)(ei.E,{className:"h-5 w-24"})]}),(0,a.jsx)("div",{className:"grid gap-3 grid-cols-1 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)(ei.E,{className:"h-24 w-full"},s))})]}):d?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",s),children:[(0,a.jsx)(Y.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load dependency status"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:d.message||"Unable to retrieve dependency health information"})]}):x.length?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"External Dependencies"})]}),n&&(0,a.jsx)(j.E,{variant:"outline",className:(0,v.cn)("font-medium",eu(n.summary.healthy>n.summary.unhealthy?"healthy":"unhealthy")),children:n.summary.healthy>n.summary.unhealthy?"HEALTHY":"UNHEALTHY"})]}),(0,a.jsx)("div",{className:(0,v.cn)("grid gap-3",i?"grid-cols-1":"grid-cols-1 sm:grid-cols-2"),children:x.map(e=>{let s=eh(e.status),r=e.icon;return(0,a.jsx)(g.Zp,{className:(0,v.cn)("transition-all duration-200 hover:shadow-md",eu(e.status)),children:(0,a.jsxs)(g.Wu,{className:(0,v.cn)("p-4",i&&"p-3"),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:(0,v.cn)("font-medium",i?"text-xs":"text-sm"),children:e.name}),!i&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]}),(0,a.jsx)(s,{className:"h-4 w-4 flex-shrink-0"})]}),(0,a.jsxs)("div",{className:"mt-3 space-y-2",children:[t&&e.responseTime&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Response Time:"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:ej(e.responseTime)})]}),l&&void 0!==e.availability&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Availability:"}),(0,a.jsx)("span",{className:(0,v.cn)("text-xs font-medium",ep(e.availability)),children:eg(e.availability)})]}),e.lastChecked&&!i&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Last Checked:"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:new Date(e.lastChecked).toLocaleTimeString()})]})]})]})},e.id)})}),(null==n?void 0:n.summary)&&!i&&(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 pt-2",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-green-600",children:n.summary.healthy}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Healthy"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-yellow-600",children:n.summary.degraded}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Degraded"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-red-600",children:n.summary.unhealthy}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unhealthy"})]})]})]}):(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",s),children:[(0,a.jsx)(es.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No dependencies configured"})]})};var eN=t(93504),eb=t(96025),ey=t(16238),ew=t(21374),ek=t(3401),eA=t(83394),eC=t(99445),eS=t(62341);let eR=[{value:"1h",label:"1 Hour"},{value:"6h",label:"6 Hours"},{value:"24h",label:"24 Hours"},{value:"7d",label:"7 Days"}],eM=(e,s)=>{switch(s){case"1h":case"6h":case"24h":return e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"});case"7d":return e.toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit"});default:return e.toLocaleTimeString()}},eT=e=>{let{className:s="",chartType:t="area",showTimeRangeSelector:l=!0,height:i=300,defaultTimeframe:c="24h"}=e,[d,o]=r.useState(c),{data:m,isLoading:x,error:u}=(0,f.Gz)(d),j=r.useMemo(()=>(null==m?void 0:m.dataPoints)?m.dataPoints.map(e=>({timestamp:e.timestamp,time:eM(new Date(e.timestamp),d),healthy:e.summary.healthyChecks,degraded:e.summary.degradedChecks,unhealthy:e.summary.unhealthyChecks,overallScore:Math.round(e.summary.healthyChecks/e.summary.totalChecks*100)})):[],[m,d]),g=r.useMemo(()=>{if(j.length<2)return"stable";let e=j.slice(-3),s=j.slice(-6,-3),t=e.reduce((e,s)=>e+s.overallScore,0)/e.length-s.reduce((e,s)=>e+s.overallScore,0)/s.length;return t>2?"improving":t<-2?"declining":"stable"},[j]),p={healthy:{label:"Healthy",color:"#10b981"},degraded:{label:"Degraded",color:"#f59e0b"},unhealthy:{label:"Unhealthy",color:"#ef4444"},overallScore:{label:"Overall Score",color:"#3b82f6"}};return x?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-32"}),(0,a.jsx)(ei.E,{className:"h-8 w-24"})]}),(0,a.jsx)(ei.E,{className:"w-full",style:{height:"".concat(i,"px")}})]}):u?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",s),children:[(0,a.jsx)(M.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load health trends"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:u.message||"Unable to retrieve health trend data"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"Health Trends"}),"stable"!==g&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:["improving"===g?(0,a.jsx)(R.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:(0,v.cn)("text-xs font-medium","improving"===g?"text-green-600":"text-red-600"),children:g})]})]}),l&&(0,a.jsx)("div",{className:"flex items-center gap-1",children:eR.map(e=>(0,a.jsx)(n.$,{variant:d===e.value?"default":"ghost",size:"sm",className:"h-7 px-2 text-xs",onClick:()=>o(e.value),children:e.label},e.value))})]}),(0,a.jsx)(W,{config:p,className:"w-full",style:{height:"".concat(i,"px")},children:(0,a.jsx)(B.u,{width:"100%",height:"100%",children:(()=>{let e={data:j,margin:{top:5,right:30,left:20,bottom:5}};switch(t){case"line":return(0,a.jsxs)(eN.b,{...e,children:[(0,a.jsx)(eb.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(ey.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(ew.N,{type:"monotone",dataKey:"overallScore",stroke:p.overallScore.color,strokeWidth:2,dot:!1})]});case"bar":return(0,a.jsxs)(ek.E,{...e,children:[(0,a.jsx)(eb.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(ey.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(eA.y,{dataKey:"healthy",stackId:"a",fill:p.healthy.color}),(0,a.jsx)(eA.y,{dataKey:"degraded",stackId:"a",fill:p.degraded.color}),(0,a.jsx)(eA.y,{dataKey:"unhealthy",stackId:"a",fill:p.unhealthy.color})]});default:return(0,a.jsxs)(eC.Q,{...e,children:[(0,a.jsx)(eb.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(ey.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(eS.G,{type:"monotone",dataKey:"healthy",stackId:"1",stroke:p.healthy.color,fill:p.healthy.color,fillOpacity:.6}),(0,a.jsx)(eS.G,{type:"monotone",dataKey:"degraded",stackId:"1",stroke:p.degraded.color,fill:p.degraded.color,fillOpacity:.6}),(0,a.jsx)(eS.G,{type:"monotone",dataKey:"unhealthy",stackId:"1",stroke:p.unhealthy.color,fill:p.unhealthy.color,fillOpacity:.6})]})}})()})})]})};var eB=t(12236),eD=t(27140),eE=t(95120);let eL=(e,s,t)=>e>=t?"text-red-600":e>=s?"text-yellow-600":"text-green-600",eZ=e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat(parseFloat((e/Math.pow(1024,s)).toFixed(1))," ").concat(["B","KB","MB","GB","TB"][s])},eP=e=>{var s,t,l,i,n,c,d;let{className:o="",showDetails:m=!0,compact:x=!1,showConnections:u=!0}=e,{data:h,isLoading:g,error:N}=(0,f.BB)(),b=r.useMemo(()=>{var e,s,t,r,l,i,n;if(!h)return[];let c=[];return(null==(t=h.systemMetrics)||null==(s=t.cpu)||null==(e=s.loadAverage)?void 0:e[0])!==void 0&&c.push({id:"cpu-load",label:"CPU Load",value:100*h.systemMetrics.cpu.loadAverage[0],unit:"%",threshold:{warning:70,critical:90},icon:e=>{let{className:s}=e;return(0,a.jsx)(eB.A,{className:s})},description:"1-minute load average"}),(null==(l=h.systemMetrics)||null==(r=l.cpu)?void 0:r.usage)!==void 0&&c.push({id:"cpu-usage",label:"CPU Usage",value:h.systemMetrics.cpu.usage,unit:"%",threshold:{warning:70,critical:90},icon:e=>{let{className:s}=e;return(0,a.jsx)(eB.A,{className:s})},description:"Current CPU utilization"}),(null==(n=h.systemMetrics)||null==(i=n.memory)?void 0:i.usagePercent)!==void 0&&c.push({id:"memory-usage",label:"Memory Usage",value:h.systemMetrics.memory.usagePercent,unit:"%",threshold:{warning:80,critical:95},icon:e=>{let{className:s}=e;return(0,a.jsx)(eD.A,{className:s})},description:"".concat(eZ(h.systemMetrics.memory.used)," / ").concat(eZ(h.systemMetrics.memory.total))}),c},[h]),y=r.useMemo(()=>{var e;return(null==h||null==(e=h.systemMetrics)?void 0:e.connections)?{activeConnections:h.systemMetrics.connections.active||0,requestsPerSecond:0,averageResponseTime:0}:null},[h]);return g?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",o),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-32"}),(0,a.jsx)(ei.E,{className:"h-5 w-20"})]}),(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(ei.E,{className:"h-4 w-20"}),(0,a.jsx)(ei.E,{className:"h-4 w-12"})]}),(0,a.jsx)(ei.E,{className:"h-2 w-full"})]},s))})]}):N?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",o),children:[(0,a.jsx)(eE.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load system metrics"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:N.message||"Unable to retrieve system resource information"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",o),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"System Resources"})]}),(0,a.jsx)(j.E,{variant:"outline",className:"font-medium",children:"System Monitor"})]}),(0,a.jsx)("div",{className:"space-y-3",children:b.map(e=>{let s=e.icon,t=eL(e.value,e.threshold.warning,e.threshold.critical);return(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:(0,v.cn)("font-medium",x?"text-xs":"text-sm"),children:e.label})]}),(0,a.jsxs)("span",{className:(0,v.cn)("font-semibold",x?"text-xs":"text-sm",t),children:[e.value.toFixed(1),e.unit]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(p.k,{value:Math.min(e.value,100),className:"h-2"}),m&&!x&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.id)})}),u&&y&&(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(em.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Connections"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:(0,v.cn)("font-semibold text-blue-600",x?"text-sm":"text-lg"),children:y.activeConnections}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:(0,v.cn)("font-semibold text-green-600",x?"text-sm":"text-lg"),children:y.requestsPerSecond.toFixed(1)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Req/sec"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:(0,v.cn)("font-semibold text-purple-600",x?"text-sm":"text-lg"),children:[y.averageResponseTime.toFixed(0),"ms"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Avg Response"})]})]})]}),h&&m&&!x&&(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(es.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("h4",{className:"font-medium text-sm",children:"System Info"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"CPU Cores:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(l=h.systemMetrics)||null==(t=l.cpu)||null==(s=t.loadAverage)?void 0:s.length)||"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Memory Total:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(n=h.systemMetrics)||null==(i=n.memory)?void 0:i.total)?eZ(h.systemMetrics.memory.total):"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Connections:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(d=h.systemMetrics)||null==(c=d.connections)?void 0:c.total)||"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)("p",{className:"font-medium",children:"Monitoring"})]})]})]})]})},eF=e=>{var s,t,a,r,l,i,n;let c=100,d=(null==(t=e.systemMetrics)||null==(s=t.cpu)?void 0:s.usage)||0;d>90?c-=30:d>70?c-=20:d>50&&(c-=10);let o=(null==(r=e.systemMetrics)||null==(a=r.memory)?void 0:a.usagePercent)||0;o>95?c-=25:o>80?c-=15:o>60&&(c-=5);let m=(null==(l=e.deduplicationMetrics)?void 0:l.hitRate)||0;return m<40?c-=25:m<60?c-=15:m<80&&(c-=5),0===((null==(n=e.httpRequestMetrics)||null==(i=n.values)?void 0:i.length)||0)&&(c-=10),Math.max(0,Math.min(100,c))},eH=e=>e>=90?"excellent":e>=75?"good":e>=50?"warning":"poor",ez=e=>{switch(e){case"poor":return"text-red-600 bg-red-50 border-red-200";case"excellent":return"text-green-600 bg-green-50 border-green-200";case"good":return"text-blue-600 bg-blue-50 border-blue-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},eW=e=>{switch(e){case"down":return e=>{let{className:s}=e;return(0,a.jsx)(h.A,{className:s})};case"up":return e=>{let{className:s}=e;return(0,a.jsx)(R.A,{className:s})};default:return e=>{let{className:s}=e;return(0,a.jsx)(ea.A,{className:s})}}},eq=e=>{let{className:s="",compact:t=!1,showDetails:l=!0,showTrends:i=!0}=e,{data:n,error:c,isLoading:d}=(0,f.BB)(),o=r.useMemo(()=>{var e,s,t;if(!n)return[];let r=[],l=(()=>{var e,s;return(null==(s=n.httpRequestMetrics)||null==(e=s.values)?void 0:e.length)?Math.round(1e3*(n.httpRequestMetrics.values.reduce((e,s)=>e+s.value,0)/n.httpRequestMetrics.values.length)):200})();r.push({description:"Average API response time",icon:e=>{let{className:s}=e;return(0,a.jsx)(x.A,{className:s})},id:"response-time",label:"Avg Response Time",recommendation:l<300?"Excellent":l<500?"Good":l<1e3?"Warning":"Critical",status:l<200?"excellent":l<500?"good":l<1e3?"warning":"poor",trend:l<300?"stable":"up",unit:"ms",value:l});let i=(null==(s=n.systemMetrics)||null==(e=s.connections)?void 0:e.active)||0;r.push({description:"Request processing throughput",icon:e=>{let{className:s}=e;return(0,a.jsx)(ex.A,{className:s})},id:"throughput",label:"Throughput",recommendation:i>50?"Excellent":i>20?"Good":i>5?"Warning":"Critical",status:i>50?"excellent":i>20?"good":i>5?"warning":"poor",trend:"stable",unit:"req/min",value:10*i});let c=(null==(t=n.deduplicationMetrics)?void 0:t.hitRate)||0;r.push({description:"Request deduplication efficiency",icon:e=>{let{className:s}=e;return(0,a.jsx)(M.A,{className:s})},id:"cache-hit-rate",label:"Cache Hit Rate",recommendation:c>80?"Excellent":c>60?"Good":c>40?"Warning":"Critical",status:c>80?"excellent":c>60?"good":c>40?"warning":"poor",trend:c>70?"up":"down",unit:"%",value:c});let d=(()=>{var e,s;if(!(null==(s=n.httpRequestMetrics)||null==(e=s.values)?void 0:e.length))return 0;let t=n.httpRequestMetrics.values.reduce((e,s)=>e+s.value,0),a=n.httpRequestMetrics.values.filter(e=>parseInt(e.labels.statusCode)>=400).reduce((e,s)=>e+s.value,0);return t>0?a/t*100:0})();return r.push({description:"Request error percentage",icon:e=>{let{className:s}=e;return(0,a.jsx)(m.A,{className:s})},id:"error-rate",label:"Error Rate",recommendation:d<1?"Excellent":d<3?"Good":d<5?"Warning":"Critical",status:d<1?"excellent":d<3?"good":d<5?"warning":"poor",trend:d<2?"down":"up",unit:"%",value:d}),r},[n]),u=r.useMemo(()=>n?eF(n):0,[n]),h=eH(u);return d?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-40"}),(0,a.jsx)(ei.E,{className:"h-8 w-24"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)(ei.E,{className:"h-20 w-full"},s))}),(0,a.jsx)(ei.E,{className:"h-16 w-full"})]}):c?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",s),children:[(0,a.jsx)(m.A,{className:"mx-auto mb-4 size-12 text-red-500"}),(0,a.jsx)("p",{className:"text-sm font-medium text-red-600",children:"Failed to load performance metrics"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:c.message||"Unable to retrieve performance data"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ea.A,{className:"size-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"text-sm font-semibold",children:"Performance Overview"})]}),(0,a.jsxs)(j.E,{className:(0,v.cn)("font-medium",ez(h)),variant:"outline",children:["Score: ",u]})]}),!t&&(0,a.jsxs)(g.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Overall Performance"}),(0,a.jsx)("span",{className:(0,v.cn)("text-sm font-semibold","excellent"===h?"text-green-600":"good"===h?"text-blue-600":"warning"===h?"text-yellow-600":"text-red-600"),children:h.toUpperCase()})]}),(0,a.jsx)(p.k,{className:"h-3",value:u}),(0,a.jsx)("p",{className:"mt-2 text-xs text-muted-foreground",children:"Performance score based on CPU, memory, cache efficiency, and response times"})]}),(0,a.jsx)("div",{className:(0,v.cn)("grid gap-3",t?"grid-cols-2":"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"),children:o.map(e=>{let s=eW(e.trend);return(0,a.jsx)(g.Zp,{className:(0,v.cn)("transition-all duration-200 hover:shadow-md",ez(e.status)),children:(0,a.jsxs)(g.Wu,{className:(0,v.cn)("p-4",t&&"p-3"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,a.jsx)(e.icon,{className:"size-4"}),(0,a.jsx)("span",{className:(0,v.cn)("font-medium",t?"text-xs":"text-sm"),children:e.label})]}),i&&e.trend&&(0,a.jsx)(s,{className:(0,v.cn)(t?"h-3 w-3":"h-4 w-4","up"===e.trend?"text-green-600":"down"===e.trend?"text-red-600":"text-gray-600")})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("p",{className:(0,v.cn)("font-semibold",t?"text-sm":"text-lg"),children:[e.value.toFixed(+("%"===e.unit)),e.unit]}),l&&!t&&(0,a.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:e.description})]})]})},e.id)})})]})},eO=(e,s)=>{let t=[];for(let a=11;a>=0;a--){let r=Math.max(0,Math.min(100,e+5*Math.sin(a/12*Math.PI*2)));t.push({time:"".concat(5*a,"m ago"),[s]:r,timestamp:Date.now()-5*a*6e4})}return t},eI=e=>{switch(e){case"low":return"text-green-600";case"medium":return"text-yellow-600";case"high":return"text-red-600";default:return"text-gray-600"}},eU=(e,s,t)=>e>=t?"text-red-600 bg-red-50 border-red-200":e>=s?"text-yellow-600 bg-yellow-50 border-yellow-200":"text-green-600 bg-green-50 border-green-200",e_=e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat(parseFloat((e/Math.pow(1024,s)).toFixed(1))," ").concat(["B","KB","MB","GB","TB"][s])},eX=e=>{var s,t,l,i,n,c,d,o,m,x;let{className:u="",showCharts:h=!0,showRecommendations:N=!0,compact:b=!1}=e,{data:y,isLoading:w,error:k}=(0,f.BB)(),A=r.useMemo(()=>{var e,s,t,r,l,i,n,c;if(!y)return[];let d=[];if((null==(s=y.systemMetrics)||null==(e=s.cpu)?void 0:e.usage)!==void 0){let e=y.systemMetrics.cpu.usage,s={id:"cpu-performance",label:"CPU Performance",value:e,unit:"%",threshold:{warning:70,critical:90},performanceImpact:e>80?"high":e>60?"medium":"low",icon:e=>{let{className:s}=e;return(0,a.jsx)(eB.A,{className:s})},description:"CPU utilization impact on response times"};e>80&&(s.recommendation="Consider scaling or optimizing CPU-intensive operations"),d.push(s)}if((null==(r=y.systemMetrics)||null==(t=r.memory)?void 0:t.usagePercent)!==void 0){let e=y.systemMetrics.memory.usagePercent,s={id:"memory-performance",label:"Memory Efficiency",value:e,unit:"%",threshold:{warning:80,critical:95},performanceImpact:e>85?"high":e>70?"medium":"low",icon:e=>{let{className:s}=e;return(0,a.jsx)(eD.A,{className:s})},description:"Memory usage impact on application performance"};e>85&&(s.recommendation="Memory optimization needed - check for memory leaks"),d.push(s)}if(null==(l=y.systemMetrics)?void 0:l.connections){let{active:e,total:s}=y.systemMetrics.connections,t=s>0?e/s*100:0;d.push({id:"connection-performance",label:"Connection Pool",value:t,unit:"%",threshold:{warning:80,critical:95},performanceImpact:t>90?"high":t>70?"medium":"low",icon:e=>{let{className:s}=e;return(0,a.jsx)(em.A,{className:s})},description:"Connection pool utilization and efficiency",recommendation:t>90?"Excellent connection performance":t>70?"Good connection performance":t>40?"Consider optimizing connection pooling":"Critical: Review connection management"})}if((null==(c=y.systemMetrics)||null==(n=c.cpu)||null==(i=n.loadAverage)?void 0:i[0])!==void 0){let e=Math.min(100,100*y.systemMetrics.cpu.loadAverage[0]);d.push({id:"load-performance",label:"System Load",value:e,unit:"%",threshold:{warning:70,critical:90},performanceImpact:e>80?"high":e>60?"medium":"low",icon:e=>{let{className:s}=e;return(0,a.jsx)(ea.A,{className:s})},description:"1-minute load average performance impact",recommendation:e>80?"Excellent load handling":e>60?"Good load performance":e>40?"Consider load balancing optimization":"Critical: Review load distribution"})}return d},[y]),C=r.useMemo(()=>{if(!A.length)return[];let e=A.find(e=>"cpu-performance"===e.id);return e?eO(e.value,"CPU"):[]},[A]),S={CPU:{label:"CPU Usage",color:"#3b82f6"}};return w?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",u),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-40"}),(0,a.jsx)(ei.E,{className:"h-5 w-24"})]}),(0,a.jsx)("div",{className:"grid gap-3 grid-cols-1 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)(ei.E,{className:"h-24 w-full"},s))}),h&&(0,a.jsx)(ei.E,{className:"h-64 w-full"})]}):k?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",u),children:[(0,a.jsx)(eE.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load system metrics"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:k.message||"Unable to retrieve system performance data"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",u),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"System Performance"})]}),(0,a.jsx)(j.E,{variant:"outline",className:"font-medium",children:"Real-time"})]}),(0,a.jsx)("div",{className:(0,v.cn)("grid gap-3",b?"grid-cols-1":"grid-cols-1 sm:grid-cols-2"),children:A.map(e=>{let s=e.icon,t=eU(e.value,e.threshold.warning,e.threshold.critical),r=eI(e.performanceImpact);return(0,a.jsx)(g.Zp,{className:(0,v.cn)("transition-all duration-200 hover:shadow-md",t),children:(0,a.jsxs)(g.Wu,{className:(0,v.cn)("p-4",b&&"p-3"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:(0,v.cn)("font-medium",b?"text-xs":"text-sm"),children:e.label})]}),(0,a.jsxs)(j.E,{variant:"outline",className:(0,v.cn)("text-xs",r),children:[e.performanceImpact," impact"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:(0,v.cn)("font-semibold",b?"text-sm":"text-lg"),children:[e.value.toFixed(1),e.unit]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Warning: ",e.threshold.warning,e.unit]})]}),(0,a.jsx)(p.k,{value:Math.min(e.value,100),className:"h-2"}),!b&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),N&&e.recommendation&&!b&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded",children:(0,a.jsxs)("p",{className:"text-xs text-yellow-800",children:["\uD83D\uDCA1 ",e.recommendation]})})]})]})},e.id)})}),h&&C.length>0&&!b&&(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-5 w-5"}),"Performance Trends"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)(W,{config:S,className:"w-full",style:{height:"256px"},children:(0,a.jsx)(B.u,{width:"100%",height:"100%",children:(0,a.jsxs)(eC.Q,{data:C,children:[(0,a.jsx)(eb.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(ey.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1,domain:[0,100]}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(eS.G,{type:"monotone",dataKey:"CPU",stroke:S.CPU.color,fill:S.CPU.color,fillOpacity:.3,strokeWidth:2})]})})})})]}),y&&!b&&(0,a.jsx)(g.Zp,{children:(0,a.jsx)(g.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 text-xs",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Total Memory:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(t=y.systemMetrics)||null==(s=t.memory)?void 0:s.total)?e_(y.systemMetrics.memory.total):"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Used Memory:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(i=y.systemMetrics)||null==(l=i.memory)?void 0:l.used)?e_(y.systemMetrics.memory.used):"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Active Connections:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(c=y.systemMetrics)||null==(n=c.connections)?void 0:n.active)||0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Load Average:"}),(0,a.jsx)("p",{className:"font-medium",children:(null==(x=y.systemMetrics)||null==(m=x.cpu)||null==(o=m.loadAverage)||null==(d=o[0])?void 0:d.toFixed(2))||"Unknown"})]})]})})})]})},eK=()=>[{range:"0-100ms",count:45,percentage:45},{range:"100-200ms",count:30,percentage:30},{range:"200-500ms",count:15,percentage:15},{range:"500ms-1s",count:7,percentage:7},{range:"1s+",count:3,percentage:3}],eG=()=>{let e=[],s=new Date;for(let t=11;t>=0;t--){let a=new Date(s.getTime()-5*t*6e4),r=100+50*Math.random();e.push({time:a.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),requests:Math.round(r),responseTime:Math.round(150+100*Math.random()),timestamp:a.getTime()})}return e},e$=e=>{switch(e){case"excellent":return"text-green-600 bg-green-50 border-green-200";case"good":return"text-blue-600 bg-blue-50 border-blue-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"critical":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},eJ=e=>{let{className:s="",showCharts:t=!0,showEndpoints:l=!0,compact:i=!1}=e,{data:n,isLoading:c,error:d}=(0,f._r)(),o=r.useMemo(()=>{var e;if(!(null==n||null==(e=n.values)?void 0:e.length))return{totalRequests:0,averageResponseTime:0,errorRate:0,throughput:0,slowestEndpoint:"N/A",fastestEndpoint:"N/A"};let s=n.values,t=s.reduce((e,s)=>e+s.value,0),a=s.reduce((e,s)=>e+1e3*s.value,0),r=s.filter(e=>parseInt(e.labels.statusCode)>=400).reduce((e,s)=>e+s.value,0),l=new Map;s.forEach(e=>{let s=e.labels.route||"unknown",t=1e3*e.value;l.has(s)||l.set(s,[]),l.get(s).push(t)});let i="N/A",c="N/A",d=0,o=1/0;return l.forEach((e,s)=>{let t=e.reduce((e,s)=>e+s,0)/e.length;t>d&&(d=t,i=s),t<o&&(o=t,c=s)}),{totalRequests:t,averageResponseTime:Math.round(t>0?a/t:0),errorRate:Math.round(10*(t>0?r/t*100:0))/10,throughput:Math.round(12*t),slowestEndpoint:i,fastestEndpoint:c}},[n]),m=r.useMemo(()=>{var e;if(!(null==n||null==(e=n.values)?void 0:e.length))return[];let s=new Map;return n.values.forEach(e=>{let t=e.labels.route||"unknown",a=e.labels.method||"GET",r=parseInt(e.labels.statusCode)||200,l=1e3*e.value;s.has(t)||s.set(t,{requests:0,methods:new Set,statusCodes:[],responseTimes:[]});let i=s.get(t);i.requests+=e.value,i.methods.add(a),i.statusCodes.push(r),i.responseTimes.push(l)}),Array.from(s.entries()).map(e=>{let[s,t]=e,a=t.statusCodes.filter(e=>e>=400).length,r=t.statusCodes.length>0?a/t.statusCodes.length*100:0,l=t.responseTimes.length>0?t.responseTimes.reduce((e,s)=>e+s,0)/t.responseTimes.length:0,i="excellent";return l>500||r>5?i="critical":l>300||r>2?i="warning":(l>200||r>1)&&(i="good"),{endpoint:s,method:Array.from(t.methods).join(", "),requests:t.requests,avgResponseTime:Math.round(l),errorRate:Math.round(10*r)/10,status:i}}).sort((e,s)=>s.requests-e.requests).slice(0,5)},[n]),u=r.useMemo(()=>{var e;if(!(null==n||null==(e=n.values)?void 0:e.length))return eK();let s=n.values.map(e=>1e3*e.value);return[{range:"0-100ms",min:0,max:100},{range:"100-200ms",min:100,max:200},{range:"200-500ms",min:200,max:500},{range:"500ms-1s",min:500,max:1e3},{range:"1s+",min:1e3,max:1/0}].map(e=>{let{range:t,min:a,max:r}=e,l=s.filter(e=>e>=a&&e<r).length,i=s.length>0?l/s.length*100:0;return{range:t,count:l,percentage:Math.round(i)}})},[n]),p=r.useMemo(()=>{var e;if(!(null==n||null==(e=n.values)?void 0:e.length))return eG();let s=new Date,t=[],a=Math.max(1,Math.floor(n.values.reduce((e,s)=>e+s.value,0)/12));for(let e=11;e>=0;e--){let r=new Date(s.getTime()-5*e*6e4),l=Math.max(1,Math.floor(a*(1+.3*Math.sin(e/12*Math.PI*2)))),i=n.values.length>0?Math.round(n.values.reduce((e,s)=>e+s.value,0)/n.values.length*1e3):150;t.push({time:r.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),requests:l,responseTime:i,timestamp:r.getTime()})}return t},[n]),N={requests:{label:"Requests",color:"#3b82f6"},responseTime:{label:"Response Time",color:"#10b981"},count:{label:"Count",color:"#8b5cf6"}};return c?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-40"}),(0,a.jsx)(ei.E,{className:"h-5 w-24"})]}),(0,a.jsx)("div",{className:"grid gap-4 grid-cols-2 sm:grid-cols-4",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)(ei.E,{className:"h-16 w-full"},s))}),t&&(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 lg:grid-cols-2",children:[(0,a.jsx)(ei.E,{className:"h-64 w-full"}),(0,a.jsx)(ei.E,{className:"h-64 w-full"})]})]}):d?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",s),children:[(0,a.jsx)(et.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 font-medium",children:"Failed to load HTTP metrics"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:d.message||"Unable to retrieve HTTP request data"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-5 w-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"HTTP Request Metrics"})]}),(0,a.jsx)(j.E,{variant:"outline",className:"font-medium",children:"Live Data"})]}),(0,a.jsxs)("div",{className:"grid gap-3 grid-cols-2 sm:grid-cols-4",children:[(0,a.jsxs)(g.Zp,{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Total Requests"})]}),(0,a.jsx)("p",{className:"text-lg font-semibold mt-1",children:o.totalRequests.toLocaleString()})]}),(0,a.jsxs)(g.Zp,{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Avg Response"})]}),(0,a.jsxs)("p",{className:"text-lg font-semibold mt-1",children:[o.averageResponseTime,"ms"]})]}),(0,a.jsxs)(g.Zp,{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ex.A,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Throughput"})]}),(0,a.jsxs)("p",{className:"text-lg font-semibold mt-1",children:[o.throughput,"/min"]})]}),(0,a.jsxs)(g.Zp,{className:(0,v.cn)("p-3",o.errorRate>5?"bg-red-50 border-red-200":o.errorRate>2?"bg-yellow-50 border-yellow-200":""),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-xs font-medium",children:"Error Rate"})]}),(0,a.jsxs)("p",{className:"text-lg font-semibold mt-1",children:[o.errorRate,"%"]})]})]}),t&&!i&&(0,a.jsxs)("div",{className:"grid gap-4 grid-cols-1 lg:grid-cols-2",children:[(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"h-5 w-5"}),"Request Volume"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)(W,{config:N,className:"w-full",style:{height:"192px"},children:(0,a.jsx)(B.u,{width:"100%",height:"100%",children:(0,a.jsxs)(eN.b,{data:p,children:[(0,a.jsx)(eb.W,{dataKey:"time",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(ey.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(ew.N,{type:"monotone",dataKey:"requests",stroke:N.requests.color,strokeWidth:2,dot:!1})]})})})})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),"Response Time Distribution"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)(W,{config:N,className:"w-full",style:{height:"192px"},children:(0,a.jsx)(B.u,{width:"100%",height:"100%",children:(0,a.jsxs)(ek.E,{data:u,children:[(0,a.jsx)(eb.W,{dataKey:"range",tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(ey.h,{tick:{fontSize:12},tickLine:!1,axisLine:!1}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(eA.y,{dataKey:"percentage",fill:N.count.color,radius:[4,4,0,0]})]})})})})]})]}),l&&m.length>0&&!i&&(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsx)(g.ZB,{children:"Top Endpoints"})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:m.map((e,s)=>(0,a.jsxs)("div",{className:(0,v.cn)("flex items-center justify-between p-3 rounded-lg border",e$(e.status)),children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-mono text-sm font-medium",children:e.endpoint}),(0,a.jsx)(j.E,{variant:"outline",className:"text-xs",children:e.method})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-1 text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{children:[e.requests," requests"]}),(0,a.jsxs)("span",{children:[e.avgResponseTime,"ms avg"]}),(0,a.jsxs)("span",{children:[e.errorRate,"% errors"]})]})]}),(0,a.jsx)(j.E,{variant:"outline",className:(0,v.cn)("ml-2","excellent"===e.status?"text-green-600":"good"===e.status?"text-blue-600":"warning"===e.status?"text-yellow-600":"text-red-600"),children:e.status})]},e.endpoint))})})]})]})};var eV=t(65064),eQ=t(25844);let eY=e=>{let s=[];for(let t=11;t>=0;t--){let a=new Date(Date.now()-5*t*6e4),r=Math.max(0,Math.min(100,e+3*Math.sin(t/12*Math.PI*2))),l=100-r;s.push({hits:r,misses:l,time:a.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),timestamp:a.getTime()})}return s},e0=e=>[{color:"#10b981",name:"Cache Hits",value:e},{color:"#ef4444",name:"Cache Misses",value:100-e}],e2=e=>e>=90?"excellent":e>=75?"good":e>=50?"warning":"poor",e1=e=>{switch(e){case"excellent":return"text-green-600 bg-green-50 border-green-200";case"good":return"text-blue-600 bg-blue-50 border-blue-200";case"poor":return"text-red-600 bg-red-50 border-red-200";case"warning":return"text-yellow-600 bg-yellow-50 border-yellow-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},e4=e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat(Number.parseFloat((e/Math.pow(1024,s)).toFixed(1))," ").concat(["B","KB","MB","GB","TB"][s])},e5=e=>{var s,t,l,i,n;let{className:c="",compact:d=!1,showCharts:o=!0,showOptimizations:m=!0}=e,{data:x,error:u,isLoading:h}=(0,f.BB)(),N=r.useMemo(()=>{if(!(null==x?void 0:x.deduplicationMetrics))return[];let e=x.deduplicationMetrics,s=[],t=e.hitRate||0,r=e2(t);s.push({description:"Percentage of requests served from cache",icon:e=>{let{className:s}=e;return(0,a.jsx)(er.A,{className:s})},id:"hit-rate",label:"Cache Hit Rate",optimization:t<75?"Consider increasing cache TTL or improving cache keys":void 0,status:r,unit:"%",value:t});let l=Math.min(100,1.2*t);s.push({description:"Overall cache performance and optimization",icon:e=>{let{className:s}=e;return(0,a.jsx)(ex.A,{className:s})},id:"efficiency",label:"Cache Efficiency",optimization:l<80?"Optimize cache strategy and key patterns":void 0,status:e2(l),unit:"%",value:l});let i=e.cacheHits&&e.totalRequests?e.cacheHits/e.totalRequests*100:0;s.push({description:"Memory saved through request deduplication",icon:e=>{let{className:s}=e;return(0,a.jsx)(eV.A,{className:s})},id:"memory-savings",label:"Memory Savings",optimization:i<50?"Review cache size and eviction policies":void 0,status:i>60?"excellent":i>40?"good":i>20?"warning":"poor",unit:"%",value:i});let n=e.totalRequests>0?(e.totalRequests-e.cacheMisses)/e.totalRequests*100:0;return s.push({description:"Percentage of duplicate requests eliminated",icon:e=>{let{className:s}=e;return(0,a.jsx)(eQ.A,{className:s})},id:"dedup-rate",label:"Deduplication Rate",optimization:n<70?"Improve request fingerprinting algorithm":void 0,status:e2(n),unit:"%",value:n}),s},[x]),b=(null==x||null==(s=x.deduplicationMetrics)?void 0:s.hitRate)||0,y=eY(b),w=e0(b),k={hits:{color:"#10b981",label:"Cache Hits"},misses:{color:"#ef4444",label:"Cache Misses"}};return h?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",c),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(ei.E,{className:"h-6 w-40"}),(0,a.jsx)(ei.E,{className:"h-5 w-24"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)(ei.E,{className:"h-24 w-full"},s))}),o&&(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-2",children:[(0,a.jsx)(ei.E,{className:"h-64 w-full"}),(0,a.jsx)(ei.E,{className:"h-64 w-full"})]})]}):u?(0,a.jsxs)("div",{className:(0,v.cn)("text-center py-8",c),children:[(0,a.jsx)(er.A,{className:"mx-auto mb-4 size-12 text-red-500"}),(0,a.jsx)("p",{className:"text-sm font-medium text-red-600",children:"Failed to load deduplication metrics"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:u.message||"Unable to retrieve cache performance data"})]}):(0,a.jsxs)("div",{className:(0,v.cn)("space-y-4",c),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(er.A,{className:"size-5 text-muted-foreground"}),(0,a.jsx)("h3",{className:"text-sm font-semibold",children:"Cache & Deduplication"})]}),(0,a.jsxs)(j.E,{className:(0,v.cn)("font-medium",e1(e2(b))),variant:"outline",children:[b.toFixed(1),"% Hit Rate"]})]}),(0,a.jsx)("div",{className:(0,v.cn)("grid gap-3",d?"grid-cols-1":"grid-cols-1 sm:grid-cols-2"),children:N.map(e=>{let s=e.icon,t=e1(e.status);return(0,a.jsx)(g.Zp,{className:(0,v.cn)("transition-all duration-200 hover:shadow-md",t),children:(0,a.jsxs)(g.Wu,{className:(0,v.cn)("p-4",d&&"p-3"),children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"size-4"}),(0,a.jsx)("span",{className:(0,v.cn)("font-medium",d?"text-xs":"text-sm"),children:e.label})]}),(0,a.jsx)(j.E,{className:(0,v.cn)("text-xs","excellent"===e.status?"text-green-600":"good"===e.status?"text-blue-600":"warning"===e.status?"text-yellow-600":"text-red-600"),variant:"outline",children:e.status})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("span",{className:(0,v.cn)("font-semibold",d?"text-sm":"text-lg"),children:[e.value.toFixed(1),e.unit]})}),(0,a.jsx)(p.k,{className:"h-2",value:Math.min(e.value,100)}),!d&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description}),m&&e.optimization&&!d&&(0,a.jsx)("div",{className:"mt-2 rounded border border-blue-200 bg-blue-50 p-2",children:(0,a.jsxs)("p",{className:"text-xs text-blue-800",children:["\uD83D\uDCA1 ",e.optimization]})})]})]})},e.id)})}),o&&!d&&(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-2",children:[(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(R.A,{className:"size-5"}),"Cache Performance Trends"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)(W,{className:"h-48",config:k,children:(0,a.jsx)(B.u,{height:"100%",width:"100%",children:(0,a.jsxs)(eC.Q,{data:y,children:[(0,a.jsx)(eb.W,{axisLine:!1,dataKey:"time",tick:{fontSize:12},tickLine:!1}),(0,a.jsx)(ey.h,{axisLine:!1,domain:[0,100],tick:{fontSize:12},tickLine:!1}),(0,a.jsx)(O,{content:(0,a.jsx)(I,{})}),(0,a.jsx)(eS.G,{dataKey:"hits",fill:k.hits.color,fillOpacity:.6,stackId:"1",stroke:k.hits.color,type:"monotone"}),(0,a.jsx)(eS.G,{dataKey:"misses",fill:k.misses.color,fillOpacity:.6,stackId:"1",stroke:k.misses.color,type:"monotone"})]})})})})]}),(0,a.jsxs)(g.Zp,{children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"size-5"}),"Cache Distribution"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)(W,{className:"h-48",config:k,children:(0,a.jsx)(B.u,{height:"100%",width:"100%",children:(0,a.jsxs)(D.r,{children:[(0,a.jsx)(E.F,{cx:"50%",cy:"50%",data:w,dataKey:"value",innerRadius:40,outerRadius:80,paddingAngle:5,children:w.map((e,s)=>(0,a.jsx)(L.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(O,{content:e=>{var s;let{active:t,payload:r}=e;if(t&&(null==r?void 0:r.length)&&(null==(s=r[0])?void 0:s.payload)){let e=r[0].payload;return(0,a.jsxs)("div",{className:"rounded border bg-white p-2 shadow",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.value.toFixed(1),"%"]})]})}return null}})]})})})})]})]}),x&&!d&&(0,a.jsx)(g.Zp,{children:(0,a.jsx)(g.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs sm:grid-cols-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Total Requests:"}),(0,a.jsx)("p",{className:"font-medium",children:((null==(t=x.deduplicationMetrics)?void 0:t.totalRequests)||0).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Cache Hits:"}),(0,a.jsx)("p",{className:"font-medium text-green-600",children:Math.round(((null==(l=x.deduplicationMetrics)?void 0:l.totalRequests)||0)*b/100).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Cache Misses:"}),(0,a.jsx)("p",{className:"font-medium text-red-600",children:Math.round(((null==(i=x.deduplicationMetrics)?void 0:i.totalRequests)||0)*(100-b)/100).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Memory Saved:"}),(0,a.jsx)("p",{className:"font-medium text-blue-600",children:e4(((null==(n=x.deduplicationMetrics)?void 0:n.totalRequests)||0)*b*1048.576)})]})]})})})]})},e6=e=>{let{className:s="",maxAlerts:t=10}=e,{data:r,isLoading:l,error:i}=(0,f.Zz)(),n=e=>{switch(e){case"critical":return{icon:Y.A,color:"text-red-600 dark:text-red-400",bgColor:"bg-red-100 dark:bg-red-900/20",variant:"destructive"};case"high":return{icon:m.A,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-100 dark:bg-orange-900/20",variant:"destructive"};case"medium":return{icon:m.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",variant:"secondary"};case"low":return{icon:x.A,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",variant:"outline"}}},c=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/6e4);return t<1?"Just now":t<60?"".concat(t,"m ago"):t<1440?"".concat(Math.floor(t/60),"h ago"):s.toLocaleDateString()};if(l)return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Active Alerts"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-16 bg-muted rounded"})},s))})})]});if(i)return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Active Alerts"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(m.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load alerts"})]})})]});let d=(null==r?void 0:r.slice(0,t))||[],o=(null==r?void 0:r.filter(e=>"critical"===e.severity).length)||0;return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Active Alerts"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(j.E,{variant:o>0?"destructive":"default",children:[(null==r?void 0:r.length)||0," total"]}),o>0&&(0,a.jsxs)(j.E,{variant:"destructive",children:[o," critical"]})]})]})}),(0,a.jsx)(g.Wu,{children:0===d.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(u.A,{className:"mx-auto h-8 w-8 text-green-600 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No active alerts"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"All systems operating normally"})]}):(0,a.jsxs)("div",{className:"space-y-3",children:[d.map(e=>{let s=n(e.severity),t=s.icon;return(0,a.jsxs)("div",{className:(0,v.cn)("flex items-start gap-3 p-3 rounded-lg border",s.bgColor),children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsx)(t,{className:(0,v.cn)("h-4 w-4",s.color)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:e.message}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[e.type," • ",e.source]})]}),(0,a.jsx)(j.E,{variant:s.variant,className:"flex-shrink-0",children:e.severity})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:c(e.timestamp)}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Status: ",e.status]})]})]})]},e.id)}),r&&r.length>t&&(0,a.jsx)("div",{className:"text-center pt-2",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["+",r.length-t," more alerts"]})})]})})]})},e3=e=>{let{className:s=""}=e,{data:t,isLoading:r,error:l}=(0,f.TR)(),i=e=>{switch(e){case"critical":return"bg-red-500";case"high":return"bg-orange-500";case"medium":return"bg-yellow-500";case"low":return"bg-blue-500";default:return"bg-gray-500"}};if(r)return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),"Alert Statistics"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:4}).map((e,s)=>(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-muted rounded mb-2"}),(0,a.jsx)("div",{className:"h-2 bg-muted rounded"})]},s))})})]});if(l||!t)return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),"Alert Statistics"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)(M.A,{className:"mx-auto h-8 w-8 text-muted-foreground mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load statistics"})]})})]});let{total:n,active:c,resolved:d,bySeverity:o,averageResolutionTime:m,recentTrends:u}=t,j=n>0?Math.round(d/n*100):0;return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),"Alert Statistics"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:n}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total Alerts"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:d}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Resolved"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:c}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[j,"%"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Resolution Rate"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Resolution Rate"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[j,"%"]})]}),(0,a.jsx)(p.k,{value:j,className:"h-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"By Severity"}),(0,a.jsx)("div",{className:"space-y-2",children:Object.entries(o).map(e=>{let[s,t]=e,r=n>0?Math.round(t/n*100):0;return(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:(0,v.cn)("w-3 h-3 rounded-full",i(s))}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm capitalize",children:s}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[t," (",r,"%)"]})]}),(0,a.jsx)(p.k,{value:r,className:"h-1 mt-1"})]})]},s)})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-muted/50",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Avg. Resolution Time"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(e=>e<60?"".concat(Math.round(e),"m"):e<1440?"".concat(Math.round(e/60),"h"):"".concat(Math.round(e/1440),"d"))(m)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Recent Trends"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Last 24 Hours"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:u.last24Hours}),u.last24Hours>u.last7Days/7?(0,a.jsx)(R.A,{className:"h-3 w-3 text-red-500"}):(0,a.jsx)(h.A,{className:"h-3 w-3 text-green-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Last 7 Days"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:u.last7Days}),u.last7Days>u.last30Days/4?(0,a.jsx)(R.A,{className:"h-3 w-3 text-red-500"}):(0,a.jsx)(h.A,{className:"h-3 w-3 text-green-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Last 30 Days"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:u.last30Days})]})]})]})]})})]})},e8=e=>{let{className:s=""}=e,{data:t,error:r,isLoading:l}=(0,f.vW)(),i=e=>{switch(e){case"degraded":return{bgColor:"bg-yellow-100 dark:bg-yellow-900/20",color:"text-yellow-600 dark:text-yellow-400",icon:m.A,variant:"secondary"};case"healthy":return{bgColor:"bg-green-100 dark:bg-green-900/20",color:"text-green-600 dark:text-green-400",icon:u.A,variant:"default"};case"unhealthy":return{bgColor:"bg-red-100 dark:bg-red-900/20",color:"text-red-600 dark:text-red-400",icon:Y.A,variant:"destructive"};default:return{bgColor:"bg-gray-100 dark:bg-gray-900/20",color:"text-gray-600 dark:text-gray-400",icon:x.A,variant:"outline"}}},n=e=>{let s=e.toLowerCase();return s.includes("database")||s.includes("db")?er.A:s.includes("api")||s.includes("service")?es.A:s.includes("network")||s.includes("connection")?em.A:et.A};if(l)return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"size-5"}),"Dependency Health"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsx)("div",{className:"h-16 rounded bg-muted"})},s))})})]});if(r||!t)return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"size-5"}),"Dependency Health"]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"py-4 text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto mb-2 size-8 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Failed to load dependency health"})]})})]});let c=(()=>{if(!(null==t?void 0:t.summary))return 0;let{healthy:e,total:s}=t.summary;return s>0?Math.round(e/s*100):100})(),d=(()=>{if(!(null==t?void 0:t.summary))return"unhealthy";let{degraded:e,healthy:s,unhealthy:a}=t.summary;return a>0?"unhealthy":e>0?"degraded":"healthy"})(),o=i(d);return(0,a.jsxs)(g.Zp,{className:s,children:[(0,a.jsx)(g.aR,{children:(0,a.jsxs)(g.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"size-5"}),"Dependency Health"]}),(0,a.jsxs)(j.E,{variant:o.variant,children:[c,"%"]})]})}),(0,a.jsx)(g.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Overall Health"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[c,"%"]})]}),(0,a.jsx)(p.k,{className:"h-2",value:c})]}),(0,a.jsx)("div",{className:"space-y-3",children:(t.dependencies||[]).map(e=>{let s=i(e.status),t=s.icon,r=n(e.name);return(0,a.jsxs)("div",{className:(0,v.cn)("flex items-center justify-between p-3 rounded-lg border",s.bgColor),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r,{className:"size-4 text-muted-foreground"}),(0,a.jsx)(t,{className:(0,v.cn)("h-4 w-4",s.color)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium capitalize",children:e.name.replaceAll(/([A-Z])/g," $1").trim()}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"healthy"===e.status?"Operating normally":"degraded"===e.status?"Performance issues":"Service unavailable"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)(j.E,{variant:s.variant,children:e.status}),e.responseTime&&(0,a.jsxs)("p",{className:"mt-1 text-xs text-muted-foreground",children:[e.responseTime,"ms"]})]})]},e.name)})}),(0,a.jsxs)("div",{className:(0,v.cn)("p-3 rounded-lg",o.bgColor),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.icon,{className:(0,v.cn)("h-4 w-4",o.color)}),(0,a.jsx)("span",{className:"font-medium",children:"healthy"===d?"All dependencies healthy":"degraded"===d?"Some dependencies degraded":"Critical dependencies failing"})]}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-muted-foreground",children:["Last checked: ",new Date().toLocaleTimeString()]})]})]})})]})};var e9=t(14056);let e7=e=>{let{widgetId:s,title:t,description:r}=e;return(0,a.jsx)("div",{className:"flex h-64 items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/10",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"font-medium text-muted-foreground",children:t}),(0,a.jsx)("p",{className:"mt-1 text-sm text-muted-foreground/75",children:r}),(0,a.jsxs)("p",{className:"mt-2 text-xs text-muted-foreground/50",children:["Widget ID: ",s]})]})})},se=e=>{let{className:s=""}=e,t=(0,e9.XD)(e=>e.preferences.dashboardLayout.layout),r=(0,e9.XD)(e=>e.preferences.dashboardLayout.gridColumns),c=(0,e9.XD)(e=>e.getVisibleWidgets),d=(0,e9.XD)(e=>e.ui.activeTab),o=(0,e9.XD)(e=>e.toggleWidget),m=c(),x={"system-health":{title:"System Health",description:"Overall system status and health indicators",component:el},"health-status-indicators":{title:"Health Status Indicators",description:"Real-time component health status indicators",component:eo},"dependency-status":{title:"Dependency Status",description:"External dependency health monitoring",component:ev},"health-trends":{title:"Health Trends",description:"Historical health trend visualization",component:eT},"system-resources":{title:"System Resources",description:"CPU, memory, and connection monitoring",component:eP},"performance-overview":{title:"Performance Overview",description:"Comprehensive performance metrics and scoring",component:eq},"system-metrics":{title:"System Performance",description:"Performance-focused system metrics monitoring",component:eX},"http-metrics":{title:"HTTP Request Metrics",description:"Request performance and throughput analysis",component:eJ},"deduplication-metrics":{title:"Deduplication Metrics",description:"Cache efficiency and request deduplication analysis",component:e5},"performance-metrics":{title:"Performance Metrics",description:"System performance monitoring and analysis",component:eX},"circuit-breakers":{title:"Circuit Breakers Overview",description:"Circuit breaker status and failure protection",component:N},"circuit-breaker-list":{title:"Circuit Breaker Details",description:"Detailed circuit breaker list with status",component:S},"circuit-breaker-metrics":{title:"Circuit Breaker Metrics",description:"Performance metrics and failure rate charts",component:_},"circuit-breaker-history":{title:"Circuit Breaker History",description:"Historical state changes and recovery patterns",component:$},"circuit-breaker-alerts":{title:"Circuit Breaker Alerts",description:"Active alerts related to circuit breaker failures",component:Q},"active-alerts":{title:"Active Alerts",description:"Current system alerts and notifications",component:e6},"alert-statistics":{title:"Alert Statistics",description:"Alert trends and statistical analysis",component:e3},"dependency-health":{title:"Dependency Health",description:"External service and dependency status",component:e8}},u=(()=>{switch(d){case"health":return m.filter(e=>["system-health","dependency-health","circuit-breakers","circuit-breaker-list"].includes(e));case"metrics":return m.filter(e=>["performance-overview","system-metrics","http-metrics","deduplication-metrics","performance-metrics","circuit-breaker-metrics"].includes(e));case"alerts":return m.filter(e=>["active-alerts","alert-statistics","circuit-breaker-alerts"].includes(e));case"history":return m.filter(e=>["alert-statistics","performance-metrics","circuit-breaker-history"].includes(e));default:return m}})(),h=e=>{let s=x[e];if(!s)return(0,a.jsx)(e7,{widgetId:e,title:"Unknown Widget",description:"Widget configuration not found"});if(s.component){let e=s.component;return(0,a.jsx)(e,{})}return(0,a.jsx)(e7,{widgetId:e,title:s.title,description:s.description})};return 0===u.length?(0,a.jsxs)("div",{className:(0,v.cn)("space-y-6",s),children:[(0,a.jsx)(i.Fc,{children:(0,a.jsxs)(i.TN,{children:["No widgets are currently visible for the ",d," tab. You can enable widgets in the dashboard settings."]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(n.$,{variant:"outline",onClick:()=>{(({overview:["system-health","health-status-indicators","circuit-breakers","active-alerts"],health:["health-status-indicators","dependency-status","system-resources","health-trends"],metrics:["performance-overview","system-metrics","http-metrics","deduplication-metrics","performance-metrics","circuit-breaker-metrics"],alerts:["active-alerts","circuit-breaker-alerts","alert-statistics"],history:["health-trends","circuit-breaker-history","alert-statistics"]})[d]||["system-health"]).forEach(e=>o(e))},children:"Enable Default Widgets"})})]}):(0,a.jsx)("div",{className:(0,v.cn)((()=>{if("list"===t)return"grid grid-cols-1 gap-6";if("compact"===t)return"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4";let e={1:"grid-cols-1",2:"grid-cols-1 lg:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",5:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",6:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"};return"grid gap-6 ".concat(e[r]||e[3])})(),s),role:"grid","aria-label":"Dashboard widgets in ".concat(t," layout"),children:u.map(e=>{var s;return(0,a.jsx)(l.WidgetContainer,{widgetId:e,title:(null==(s=x[e])?void 0:s.title)||e,className:"min-h-[200px]",children:h(e)},e)})})}},76202:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(95155),r=t(54073),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsxs)(r.bL,{className:(0,i.cn)("relative flex w-full touch-none select-none items-center",t),ref:s,...l,children:[(0,a.jsx)(r.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,a.jsx)(r.Q6,{className:"absolute h-full bg-primary"})}),(0,a.jsx)(r.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});n.displayName=r.bL.displayName},76422:(e,s,t)=>{"use strict";t.d(s,{ReliabilityDashboard:()=>j});var a=t(95155),r=t(12115),l=t(59482),i=t(88331),n=t(43503),c=t(88240),d=t(55365),o=t(50172),m=t(54036);let x=e=>{let{className:s,fullPage:t=!1,size:r="md",text:l}=e;return(0,a.jsxs)("div",{className:(0,m.cn)("flex flex-col items-center justify-center",t?"fixed inset-0 bg-background/80 backdrop-blur-sm z-50":"",s),children:[(0,a.jsx)(o.A,{className:(0,m.cn)("animate-spin text-primary",{lg:"h-12 w-12",md:"h-8 w-8",sm:"h-4 w-4",xl:"h-16 w-16"}[r])}),l&&(0,a.jsx)("p",{className:(0,m.cn)("mt-2 text-muted-foreground",{lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"}[r]),children:l})]})};var u=t(14056),h=t(30008);let j=e=>{let{className:s="",showSettings:t=!1}=e,o=(0,u.XD)(e=>e.monitoring.isEnabled),m=(0,u.XD)(e=>e.monitoring.connectionStatus),j=(0,u.XD)(e=>e.ui.activeTab),g=(0,u.XD)(e=>e.setActiveTab),{data:p,isLoading:f,error:v}=(0,h.u_)(),N={data:null==p?void 0:p.systemHealth},b={data:null==p?void 0:p.circuitBreakers};return((0,r.useEffect)(()=>{j||g("overview")},[j,g]),o)?!f||N.data||b.data?!v||N.data||b.data?(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"space-y-6 ".concat(s),role:"main","aria-label":"Reliability monitoring dashboard",children:[(0,a.jsx)(i.DashboardHeader,{}),"disconnected"===m&&(0,a.jsxs)(d.Fc,{variant:"destructive",className:"mx-auto max-w-4xl",children:[(0,a.jsx)(d.XL,{children:"Real-time Connection Lost"}),(0,a.jsx)(d.TN,{children:"The real-time connection to the monitoring system has been lost. Data may not be current. The system will automatically attempt to reconnect."})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(l.DashboardGrid,{}),t&&(0,a.jsx)(n.DashboardSettings,{})]})]})}):(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"space-y-6 ".concat(s),children:[(0,a.jsx)(i.DashboardHeader,{}),(0,a.jsxs)(d.Fc,{variant:"destructive",className:"mx-auto max-w-2xl",children:[(0,a.jsx)(d.XL,{children:"Dashboard Error"}),(0,a.jsxs)(d.TN,{children:["Failed to load reliability dashboard data. Please check your connection and try again.",v&&(0,a.jsxs)("details",{className:"mt-2",children:[(0,a.jsx)("summary",{className:"cursor-pointer text-sm",children:"Error details"}),(0,a.jsx)("pre",{className:"mt-1 text-xs",children:v.message})]})]})]})]})}):(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"space-y-6 ".concat(s),children:[(0,a.jsx)(i.DashboardHeader,{}),(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(x,{size:"lg"}),(0,a.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"Loading reliability dashboard..."})]})})]})}):(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"space-y-6 ".concat(s),children:[(0,a.jsx)(i.DashboardHeader,{}),(0,a.jsxs)(d.Fc,{className:"mx-auto max-w-2xl",children:[(0,a.jsx)(d.XL,{children:"Monitoring Disabled"}),(0,a.jsx)(d.TN,{children:"Reliability monitoring is currently disabled. Enable monitoring in the dashboard settings to view real-time system health data."})]}),(0,a.jsx)(n.DashboardSettings,{})]})})}},80333:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var a=t(95155),r=t(4884),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...l,ref:s,children:(0,a.jsx)(r.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=r.bL.displayName},81665:(e,s,t)=>{"use strict";t.d(s,{WidgetContainer:()=>p});var a=t(95155),r=t(67554),l=t(38350),i=t(77381),n=t(79556),c=t(4607),d=t(12115),o=t(6560),m=t(30285),x=t(66695),u=t(44838),h=t(46102),j=t(14056),g=t(54036);let p=e=>{let{widgetId:s,title:t,subtitle:p,children:f,className:v="",refreshable:N=!1,onRefresh:b,isLoading:y=!1,error:w=null,actions:k}=e,A=(0,j.XD)(e=>e.preferences.dashboardLayout.expandedWidgets),C=(0,j.XD)(e=>e.setWidgetExpanded),S=(0,j.XD)(e=>e.toggleWidget),[R,M]=(0,d.useState)(!1),T=A.has(s),B=async()=>{if(b&&!R){M(!0);try{await b()}catch(e){console.error("Failed to refresh widget ".concat(s,":"),e)}finally{M(!1)}}};return(0,a.jsx)(h.Bc,{children:(0,a.jsxs)(x.Zp,{className:(0,g.cn)("shadow-md transition-all duration-200",T?"row-span-2":"",w?"border-destructive/50":"",v),role:"region","aria-labelledby":"widget-title-".concat(s),"aria-expanded":T,children:[(0,a.jsxs)(x.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(x.ZB,{id:"widget-title-".concat(s),className:"text-base font-semibold",children:t}),p&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:p})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[k,N&&(0,a.jsxs)(h.m_,{children:[(0,a.jsx)(h.k$,{asChild:!0,children:(0,a.jsx)(m.$,{variant:"ghost",size:"sm",onClick:B,disabled:R||y,"aria-label":"Refresh ".concat(t),children:(0,a.jsx)(r.A,{className:(0,g.cn)("h-4 w-4",(R||y)&&"animate-spin")})})}),(0,a.jsx)(h.ZI,{children:"Refresh widget data"})]}),(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:(0,a.jsx)(m.$,{variant:"ghost",size:"sm","aria-label":"".concat(t," widget options"),children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(u.SQ,{align:"end",className:"w-48",children:[(0,a.jsx)(u._2,{onClick:()=>{C(s,!T)},children:T?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Collapse Widget"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Expand Widget"]})}),N&&(0,a.jsxs)(u._2,{onClick:B,disabled:R||y,children:[(0,a.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Refresh Data"]}),(0,a.jsx)(u.mB,{}),(0,a.jsxs)(u._2,{onClick:()=>{S(s)},className:"text-destructive focus:text-destructive",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Hide Widget"]})]})]})]})]}),(0,a.jsx)(x.Wu,{className:"pt-0",children:w?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-destructive font-medium",children:"Failed to load widget data"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:w}),N&&(0,a.jsx)(o.r,{actionType:"secondary",size:"sm",className:"mt-3",onClick:B,isLoading:R,children:"Retry"})]})}):(0,a.jsx)("div",{className:(0,g.cn)("transition-all duration-200",T?"min-h-[400px]":"min-h-[200px]"),children:f})})]})})}},88331:(e,s,t)=>{"use strict";t.d(s,{DashboardHeader:()=>A});var a=t(95155),r=t(53764),l=t(13300),i=t(18271),n=t(45731),c=t(5263),d=t(18186),o=t(40207),m=t(31949),x=t(3638),u=t(12115),h=t(6560),j=t(26126),g=t(30285),p=t(54165),f=t(44838),v=t(17313),N=t(46102),b=t(14056),y=t(30008),w=t(44005),k=t(43503);let A=e=>{let{className:s=""}=e,[t,A]=(0,u.useState)(!1),C=(0,b.XD)(e=>e.ui.activeTab),S=(0,b.XD)(e=>e.setActiveTab),R=(0,b.XD)(e=>e.monitoring.isEnabled),M=(0,b.XD)(e=>e.setMonitoringEnabled),T=(0,b.XD)(e=>e.toggleFilterPanel),B=(0,b.XD)(e=>e.ui.isFilterPanelOpen),{data:D}=(0,y.Zz)(),{data:E}=(0,y.TR)(),L=(null==D?void 0:D.filter(e=>"active"===e.status).length)||0,Z=(null==D?void 0:D.filter(e=>"active"===e.status&&"critical"===e.severity).length)||0;return(0,a.jsx)(N.Bc,{children:(0,a.jsxs)("header",{"aria-label":"Reliability dashboard header",className:"space-y-2 ".concat(s),role:"banner",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg font-semibold text-primary",children:"Reliability Dashboard"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"System monitoring"})]}),(0,a.jsx)(w.ConnectionStatusIndicator,{})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(N.m_,{children:[(0,a.jsx)(N.k$,{asChild:!0,children:(0,a.jsx)(h.r,{actionType:R?"secondary":"primary","aria-label":R?"Pause monitoring":"Resume monitoring",icon:R?(0,a.jsx)(r.A,{className:"size-4"}):(0,a.jsx)(l.A,{className:"size-4"}),onClick:()=>{M(!R)},size:"sm",children:R?"Pause":"Resume"})}),(0,a.jsx)(N.ZI,{children:R?"Pause real-time monitoring":"Resume real-time monitoring"})]}),(0,a.jsxs)(f.rI,{children:[(0,a.jsx)(f.ty,{asChild:!0,children:(0,a.jsxs)(g.$,{size:"sm",variant:"outline",children:[(0,a.jsx)(i.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Dashboard settings"})]})}),(0,a.jsxs)(f.SQ,{align:"end",className:"w-48",children:[(0,a.jsxs)(f._2,{onClick:()=>{A(!0)},children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),"Dashboard Settings"]}),(0,a.jsxs)(f._2,{onClick:T,children:[(0,a.jsx)(n.A,{className:"mr-2 size-4"}),B?"Hide Filters":"Show Filters"]}),(0,a.jsx)(f.mB,{}),(0,a.jsxs)(f._2,{children:[(0,a.jsx)(c.A,{className:"mr-2 size-4"}),"Export Data"]})]})]})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto border-b",children:(0,a.jsx)(v.tU,{className:"w-full",onValueChange:e=>S(e),value:C,children:(0,a.jsxs)(v.j7,{className:"inline-flex w-full min-w-fit lg:grid lg:w-full lg:grid-cols-5",children:[(0,a.jsxs)(v.Xi,{"aria-label":"Overview dashboard",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"overview",children:[(0,a.jsx)(d.A,{className:"size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Overview"})]}),(0,a.jsxs)(v.Xi,{"aria-label":"System health monitoring",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"health",children:[(0,a.jsx)(o.A,{className:"size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Health"})]}),(0,a.jsxs)(v.Xi,{"aria-label":"Performance metrics",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"metrics",children:[(0,a.jsx)(c.A,{className:"size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Metrics"})]}),(0,a.jsxs)(v.Xi,{"aria-label":"Active alerts and notifications",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"alerts",children:[(0,a.jsx)(m.A,{className:"size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Alerts"}),L>0&&(0,a.jsx)(j.E,{className:"ml-1 h-5 min-w-[20px] shrink-0 text-xs",variant:Z>0?"destructive":"secondary",children:L})]}),(0,a.jsxs)(v.Xi,{"aria-label":"Historical data and trends",className:"flex shrink-0 items-center gap-2 px-3 py-2 lg:justify-center",value:"history",children:[(0,a.jsx)(x.A,{className:"size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"History"})]})]})})}),L>0&&(0,a.jsx)("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-950",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"size-4 text-orange-600 dark:text-orange-400"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-orange-800 dark:text-orange-200",children:[L," active alert",1===L?"":"s",Z>0&&(0,a.jsxs)("span",{className:"ml-1 text-red-600 dark:text-red-400",children:["(",Z," critical)"]})]})]}),(0,a.jsx)(g.$,{className:"text-orange-700 hover:text-orange-800 dark:text-orange-300 dark:hover:text-orange-200",onClick:()=>S("alerts"),size:"sm",variant:"outline",children:"View Alerts"})]})}),(0,a.jsx)(p.lG,{onOpenChange:A,open:t,children:(0,a.jsxs)(p.Cf,{className:"max-h-[90vh] max-w-4xl overflow-y-auto",children:[(0,a.jsx)(p.c7,{children:(0,a.jsx)(p.L3,{children:"Dashboard Settings"})}),(0,a.jsx)(k.DashboardSettings,{})]})})]})})}},89440:(e,s,t)=>{"use strict";t.d(s,{AppBreadcrumb:()=>o});var a=t(95155),r=t(6874),l=t.n(r),i=t(35695),n=t(12115),c=t(19018),d=t(54036);function o(e){let{className:s,homeHref:t="/",homeLabel:r="Dashboard",showContainer:o=!0}=e,m=(0,i.usePathname)(),x=m?m.split("/").filter(Boolean):[],u=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let s={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return s[e]?s[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},h=x.map((e,s)=>{let t="/"+x.slice(0,s+1).join("/"),r=s===x.length-1,i=u(e);return(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(c.BreadcrumbItem,{children:r?(0,a.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:i}):(0,a.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:i})})}),!r&&(0,a.jsx)(c.BreadcrumbSeparator,{})]},t)}),j=(0,a.jsx)(c.Breadcrumb,{className:(0,d.cn)("text-sm",s),children:(0,a.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,a.jsx)(c.BreadcrumbItem,{children:(0,a.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:r})})}),x.length>0&&(0,a.jsx)(c.BreadcrumbSeparator,{}),h]})});return o?(0,a.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"flex items-center",children:j})}):j}}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,1137,3860,9664,1263,5495,1859,6874,5247,5669,9028,2913,5487,3030,9212,9318,4036,8658,111,3712,283,3615,2999,7684,8441,1684,7358],()=>s(24840)),_N_E=e.O()}]);