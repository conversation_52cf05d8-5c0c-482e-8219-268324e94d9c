(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8708],{3235:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},3561:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5041:(e,t,s)=>{"use strict";s.d(t,{n:()=>c});var r=s(12115),a=s(34560),l=s(7165),i=s(25910),n=s(52020),o=class extends i.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,n.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,n.EN)(t.mutationKey)!==(0,n.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#l(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#a(),this.#l()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#a(){let e=this.#s?.state??(0,a.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#l(e){l.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},d=s(26715);function c(e,t){let s=(0,d.jE)(t),[a]=r.useState(()=>new o(s,e));r.useEffect(()=>{a.setOptions(e)},[a,e]);let i=r.useSyncExternalStore(r.useCallback(e=>a.subscribe(l.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),c=r.useCallback((e,t)=>{a.mutate(e,t).catch(n.lQ)},[a]);if(i.error&&(0,n.GU)(a.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:c,mutateAsync:i.mutate}}},11133:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},19637:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},19968:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},20636:(e,t,s)=>{"use strict";s.d(t,{O2:()=>l,Q:()=>i,gT:()=>n});var r=s(71153),a=s(33450);let l=r.k5(["Active","On_Leave","Terminated","Inactive"]),i=r.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),n=r.Ik({availability:a.X.optional().nullable(),contactEmail:r.Yj().email("Invalid email address").nullable().optional(),contactInfo:r.Yj().min(1,"Contact Info is required"),contactMobile:r.Yj().nullable().optional(),contactPhone:r.Yj().nullable().optional(),currentLocation:r.Yj().nullable().optional(),department:r.Yj().nullable().optional(),employeeId:r.Yj().min(1,"Employee ID (unique business ID) is required"),fullName:r.Yj().nullable().optional(),generalAssignments:r.YO(r.Yj()),hireDate:r.Yj().nullable().optional().refine(e=>null===e||""===e||null==e||""===e||!isNaN(Date.parse(e)),{message:"Invalid hire date"}),name:r.Yj().min(1,"Name is required"),notes:r.Yj().nullable().optional(),position:r.Yj().nullable().optional(),profileImageUrl:r.Yj().optional().nullable().refine(e=>{if(!e||""===e.trim())return!0;try{return new URL(e),!0}catch(e){return!1}},{message:"Invalid URL for profile image"}),role:i,shiftSchedule:r.Yj().nullable().optional(),skills:r.YO(r.Yj()),status:l,workingHours:r.Yj().nullable().optional()})},31554:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},31949:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},33450:(e,t,s)=>{"use strict";s.d(t,{X:()=>r});let r=s(71153).k5(["On_Shift","Off_Shift","On_Break","Busy"])},34301:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},50172:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>f,gC:()=>x,l6:()=>c,yv:()=>u});var r=s(95155),a=s(31992),l=s(79556),i=s(77381),n=s(10518),o=s(12115),d=s(54036);let c=a.bL;a.YJ;let u=a.WT,m=o.forwardRef((e,t)=>{let{children:s,className:i,...n}=e;return(0,r.jsxs)(a.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",i),ref:t,...n,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})});m.displayName=a.l9.displayName;let h=o.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...l,children:(0,r.jsx)(i.A,{className:"size-4"})})});h.displayName=a.PP.displayName;let p=o.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,r.jsx)(a.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),ref:t,...i,children:(0,r.jsx)(l.A,{className:"size-4"})})});p.displayName=a.wn.displayName;let x=o.forwardRef((e,t)=>{let{children:s,className:l,position:i="popper",...n}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",l),position:i,ref:t,...n,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(p,{})]})})});x.displayName=a.UC.displayName,o.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),ref:t,...l})}).displayName=a.JU.displayName;let f=o.memo(o.forwardRef((e,t)=>{let{children:s,className:l,...i}=e,c=o.useCallback(e=>{"function"==typeof t?t(e):t&&(t.current=e)},[t]);return(0,r.jsxs)(a.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l),ref:c,...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:s})]})}));f.displayName=a.q7.displayName,o.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,r.jsx)(a.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),ref:t,...l})}).displayName=a.wv.displayName},60335:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("CircleUserRound",[["path",{d:"M18 20a6 6 0 0 0-12 0",key:"1qehca"}],["circle",{cx:"12",cy:"10",r:"4",key:"1h16sb"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(95155),a=s(12115),l=s(54036);let i=a.forwardRef((e,t)=>{let{className:s,type:a,...i}=e;return(0,r.jsx)("input",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,type:a,...i})});i.displayName="Input"},65319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>J});var r=s(95155),a=s(79239),l=s(67554),i=s(34301),n=s(75074),o=s(6874),d=s.n(o),c=s(12115),u=s(41784),m=s(83343),h=s(60335),p=s(86950),x=s(19637),f=s(31554),y=s(76570),g=s(83662),b=s(3235),j=s(19968),v=s(66766),N=s(6560),k=s(26126),w=s(66695),A=s(22346),C=s(54036);let M=e=>{switch(e){case"Active":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"On_Leave":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Terminated":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},R=e=>{if(!e)return"";switch(e){case"Busy":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Off_Shift":default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20";case"On_Break":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"On_Shift":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20"}};function E(e){var t;let{employee:s}=e;return(0,r.jsxs)(w.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,r.jsx)(w.aR,{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"relative flex size-12 items-center justify-center overflow-hidden rounded-full bg-muted ring-2 ring-primary/30",children:s.profileImageUrl?(0,r.jsx)(v.default,{alt:s.fullName||s.name,"data-ai-hint":"employee photo",layout:"fill",objectFit:"cover",src:s.profileImageUrl}):(0,r.jsx)(h.A,{className:"size-8 text-muted-foreground"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.ZB,{className:"text-xl font-semibold text-primary",children:s.fullName||s.name}),(0,r.jsxs)(w.BT,{className:"text-sm text-muted-foreground",children:[s.position," (",s.role?s.role.charAt(0).toUpperCase()+s.role.slice(1).replace("_"," "):"N/A",")"]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end gap-1",children:[(0,r.jsx)(k.E,{className:(0,C.cn)("text-xs py-1 px-2 font-semibold",M(s.status)),children:s.status}),"driver"===s.role&&s.availability&&(0,r.jsx)(k.E,{className:(0,C.cn)("text-xs py-1 px-2 font-semibold",R(s.availability)),children:null==(t=s.availability)?void 0:t.replace("_"," ")})]})]})}),(0,r.jsxs)(w.Wu,{className:"flex grow flex-col p-5",children:[(0,r.jsx)(A.w,{className:"my-3 bg-border/50"}),(0,r.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Department: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.department})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Email: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.contactEmail})]})]}),s.contactMobile&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Mobile: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.contactMobile})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Hire Date: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.hireDate?(0,u.GP)((0,m.H)(s.hireDate),"MMM d, yyyy"):"N/A"})]})]}),"driver"===s.role&&(0,r.jsx)(r.Fragment,{children:s.currentLocation&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Location: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.currentLocation})]})]})}),s.skills&&s.skills.length>0&&(0,r.jsxs)("div",{className:"flex items-start pt-1",children:[(0,r.jsx)(b.A,{className:"mr-2.5 mt-0.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Skills: "}),(0,r.jsx)("p",{className:"text-xs font-semibold leading-tight",children:s.skills.join(", ")})]})]})]})]}),(0,r.jsx)(w.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,r.jsx)(N.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,r.jsx)(j.A,{className:"size-4"}),children:(0,r.jsx)(d(),{href:"/employees/".concat(s.id),children:"View Details"})})})]})}var z=s(88240),L=s(89440),O=s(62523),_=s(85057),S=s(77023),q=s(95647),Y=s(59409),I=s(68856),U=s(53712),D=s(20636),F=s(83761),T=s(99673);function P(){return(0,r.jsxs)("div",{className:"flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-lg",children:[(0,r.jsxs)("div",{className:"flex grow flex-col p-5",children:[(0,r.jsxs)("div",{className:"mb-2 flex items-start justify-between",children:[(0,r.jsx)(I.E,{className:"h-8 w-3/5 bg-muted/50"}),(0,r.jsx)(I.E,{className:"h-5 w-1/4 rounded-full bg-muted/50"})]}),(0,r.jsx)(I.E,{className:"mb-1 h-4 w-1/2 bg-muted/50"}),(0,r.jsx)(I.E,{className:"mb-3 h-4 w-1/3 bg-muted/50"}),(0,r.jsx)(I.E,{className:"my-3 h-px w-full bg-border/50"}),(0,r.jsx)("div",{className:"grow space-y-2.5",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(I.E,{className:"mr-2.5 size-5 rounded-full bg-muted/50"}),(0,r.jsx)(I.E,{className:"h-5 w-2/3 bg-muted/50"})]},t))})]}),(0,r.jsx)("div",{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,r.jsx)(I.E,{className:"h-10 w-full bg-muted/50"})})]})}let B=()=>{let{showFormSuccess:e,showFormError:t}=(0,U.t6)(),{data:s=[],error:o,isLoading:u,refetch:m}=(0,F.nR)(),[h,p]=(0,c.useState)(""),[x,f]=(0,c.useState)("all"),[y,g]=(0,c.useState)("all"),[b,j]=(0,c.useState)("all"),v=(0,c.useMemo)(()=>[...new Set(s.map(e=>e.department).filter(Boolean))].sort(),[s]),k=(0,c.useMemo)(()=>{let e=[...s],t=h.toLowerCase();return"all"!==x&&(e=e.filter(e=>e.status===x)),"all"!==y&&(e=e.filter(e=>e.department===y)),"all"!==b&&(e=e.filter(e=>e.role===b)),t&&(e=e.filter(e=>(e.name||"").toLowerCase().includes(t)||(e.contactInfo||"").toLowerCase().includes(t)||(e.position||"").toLowerCase().includes(t)||(e.department||"").toLowerCase().includes(t)||(e.role||"").toLowerCase().includes(t)||(e.contactEmail||"").toLowerCase().includes(t)||(e.contactPhone||"").toLowerCase().includes(t)||(e.contactMobile||"").toLowerCase().includes(t)||e.skills&&e.skills.some(e=>(e||"").toLowerCase().includes(t))||"driver"===e.role&&e.availability&&(e.availability||"").toLowerCase().includes(t)||"driver"===e.role&&e.currentLocation&&(e.currentLocation||"").toLowerCase().includes(t))),e},[h,s,x,y,b]),A=(0,c.useCallback)(async()=>{try{await m(),e({successTitle:"Refresh Complete",successDescription:"Employee list has been updated."})}catch(e){console.error("Error refreshing employees:",e),t(e,{errorTitle:"Refresh Failed",errorDescription:"Could not update employee list. Please try again."})}},[m,e,t]),C=h||"all"!==x||"all"!==y||"all"!==b;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(L.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,r.jsx)(q.z,{description:"Oversee employee profiles, roles, status, and assignments.",icon:a.A,title:"Manage Employees",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(N.r,{actionType:"tertiary",icon:(0,r.jsx)(l.A,{className:"size-4 ".concat(u?"animate-spin":"")}),isLoading:u,loadingText:"Refreshing...",onClick:A,children:"Refresh"}),(0,r.jsx)(N.r,{actionType:"primary",asChild:!0,icon:(0,r.jsx)(i.A,{className:"size-4"}),children:(0,r.jsx)(d(),{href:"/employees/new",children:"Add New Employee"})})]})}),(0,r.jsxs)(w.Zp,{className:"relative mb-6 p-4 shadow",children:[u&&(0,r.jsxs)("div",{className:"absolute right-4 top-4 flex items-center text-xs text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"mr-1 size-3 animate-spin"}),"Refreshing..."]}),(0,r.jsx)(w.Wu,{className:"pt-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 items-end gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"relative lg:col-span-1",children:[(0,r.jsx)(_.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"search-employees",children:"Search Employees"}),(0,r.jsx)(n.A,{className:"absolute left-3 top-[calc(50%_-_0.5rem_+_12px)] size-5 text-muted-foreground"}),(0,r.jsx)(O.p,{className:"w-full pl-10",id:"search-employees",onChange:e=>p(e.target.value),placeholder:"Name, Role, Dept, Skill...",type:"text",value:h})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(_.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"status-filter",children:"Filter by Status"}),(0,r.jsxs)(Y.l6,{onValueChange:f,value:x,children:[(0,r.jsx)(Y.bq,{id:"status-filter",children:(0,r.jsx)(Y.yv,{placeholder:"All Statuses"})}),(0,r.jsxs)(Y.gC,{children:[(0,r.jsx)(Y.eb,{value:"all",children:"All Statuses"}),D.O2.options.map(e=>(0,r.jsx)(Y.eb,{value:e,children:(0,T.vq)(e)},e))]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(_.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"department-filter",children:"Filter by Department"}),(0,r.jsxs)(Y.l6,{onValueChange:g,value:y,children:[(0,r.jsx)(Y.bq,{id:"department-filter",children:(0,r.jsx)(Y.yv,{placeholder:"All Departments"})}),(0,r.jsxs)(Y.gC,{children:[(0,r.jsx)(Y.eb,{value:"all",children:"All Departments"}),v.map(e=>(0,r.jsx)(Y.eb,{value:e||"",children:e},e||""))]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(_.J,{className:"mb-1 block text-sm font-medium text-muted-foreground",htmlFor:"role-filter",children:"Filter by Role"}),(0,r.jsxs)(Y.l6,{onValueChange:j,value:b,children:[(0,r.jsx)(Y.bq,{id:"role-filter",children:(0,r.jsx)(Y.yv,{placeholder:"All Roles"})}),(0,r.jsxs)(Y.gC,{children:[(0,r.jsx)(Y.eb,{value:"all",children:"All Roles"}),D.Q.options.map(e=>(0,r.jsx)(Y.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))]})]})]})]})})]}),(0,r.jsx)(S.gO,{data:k,emptyComponent:(0,r.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,r.jsx)(a.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,r.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:C?"No Employees Match Your Filters":"No Employees Registered Yet"}),(0,r.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:C?"Try adjusting your search or filter criteria.":"Get started by adding an employee."}),!C&&(0,r.jsx)(N.r,{actionType:"primary",asChild:!0,icon:(0,r.jsx)(i.A,{className:"size-4"}),size:"lg",children:(0,r.jsx)(d(),{href:"/employees/add",children:"Add Your First Employee"})})]}),error:o?o.message:null,isLoading:u,loadingComponent:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:3}).map((e,t)=>(0,r.jsx)(P,{},t))}),onRetry:A,children:e=>(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:e.map(e=>(0,r.jsx)(E,{employee:e},e.id))})})]})};function J(){return(0,r.jsx)(z.A,{children:(0,r.jsx)(B,{})})}},67554:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},73158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},75074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},76570:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},79239:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},83662:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(95155),a=s(12115),l=s(40968),i=s(74466),n=s(54036);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)(o(),s),...a})});d.displayName=l.b.displayName},86038:(e,t,s)=>{Promise.resolve().then(s.bind(s,65319))},86950:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},87489:(e,t,s)=>{"use strict";s.d(t,{b:()=>d});var r=s(12115),a=s(63655),l=s(95155),i="horizontal",n=["horizontal","vertical"],o=r.forwardRef((e,t)=>{var s;let{decorative:r,orientation:o=i,...d}=e,c=(s=o,n.includes(s))?o:i;return(0,l.jsx)(a.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...d,ref:t})});o.displayName="Separator";var d=o},88240:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(95155),a=s(31949),l=s(67554),i=s(12115),n=s(55365),o=s(30285);class d extends i.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,t){this.setState({errorInfo:t}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.props.onError&&this.props.onError(e,t)}render(){let{description:e="An unexpected error occurred.",resetLabel:t="Try Again",title:s="Something went wrong"}=this.props;if(this.state.hasError){var i;return this.props.fallback?this.props.fallback:(0,r.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,r.jsx)(a.A,{className:"mr-2 size-4"}),(0,r.jsx)(n.XL,{className:"text-lg font-semibold",children:s}),(0,r.jsxs)(n.TN,{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-2",children:(null==(i=this.state.error)?void 0:i.message)||e}),!1,(0,r.jsxs)(o.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,r.jsx)(l.A,{className:"mr-2 size-4"}),t]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let c=d},99673:(e,t,s)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function a(e){var t,s;if(null==(t=e.fullName)?void 0:t.trim())return e.fullName.trim();if(null==(s=e.name)?void 0:s.trim()){let t=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(t.toLowerCase())||t.includes("_")){let e=t.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return t}if(e.role){let t=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(t," (Role)")}return"Unknown Employee"}function l(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function i(e){return e.replaceAll("_"," ")}s.d(t,{DV:()=>a,fZ:()=>r,s:()=>l,vq:()=>i})}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,1137,3860,9664,1263,5495,1859,6874,5669,6766,4036,8658,111,3712,7515,6762,8441,1684,7358],()=>t(86038)),_N_E=e.O()}]);