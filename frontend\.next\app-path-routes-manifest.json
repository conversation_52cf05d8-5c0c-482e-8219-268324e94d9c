{"/_not-found/page": "/_not-found", "/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/auth-test/page": "/auth-test", "/delegations/page": "/delegations", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/employees/[id]/edit/page": "/employees/[id]/edit", "/delegations/[id]/page": "/delegations/[id]", "/employees/[id]/page": "/employees/[id]", "/employees/add/page": "/employees/add", "/employees/page": "/employees", "/font-size-demo/page": "/font-size-demo", "/employees/new/page": "/employees/new", "/page": "/", "/login/page": "/login", "/delegations/add/page": "/delegations/add", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/profile/page": "/profile", "/settings/page": "/settings", "/service-records/[id]/page": "/service-records/[id]", "/tasks/add/page": "/tasks/add", "/service-history/page": "/service-history", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/tasks/page": "/tasks", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/[id]/page": "/vehicles/[id]", "/tasks/[id]/page": "/tasks/[id]", "/vehicles/page": "/vehicles", "/vehicles/new/page": "/vehicles/new", "/zustand-test/page": "/zustand-test", "/delegations/report/list/page": "/delegations/report/list", "/admin/page": "/admin", "/delegations/[id]/report/page": "/delegations/[id]/report", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/reliability/page": "/reliability", "/tasks/report/page": "/tasks/report", "/reports/page": "/reports", "/reports/analytics/page": "/reports/analytics", "/reports/data/page": "/reports/data"}