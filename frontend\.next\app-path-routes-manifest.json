{"/_not-found/page": "/_not-found", "/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/auth-test/page": "/auth-test", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/delegations/add/page": "/delegations/add", "/delegations/[id]/page": "/delegations/[id]", "/delegations/page": "/delegations", "/employees/[id]/edit/page": "/employees/[id]/edit", "/employees/[id]/page": "/employees/[id]", "/employees/add/page": "/employees/add", "/employees/new/page": "/employees/new", "/employees/page": "/employees", "/font-size-demo/page": "/font-size-demo", "/login/page": "/login", "/page": "/", "/profile/page": "/profile", "/service-history/page": "/service-history", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/service-records/[id]/page": "/service-records/[id]", "/tasks/[id]/page": "/tasks/[id]", "/tasks/add/page": "/tasks/add", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/settings/page": "/settings", "/vehicles/[id]/page": "/vehicles/[id]", "/tasks/page": "/tasks", "/vehicles/new/page": "/vehicles/new", "/vehicles/page": "/vehicles", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/zustand-test/page": "/zustand-test", "/delegations/report/list/page": "/delegations/report/list", "/delegations/[id]/report/page": "/delegations/[id]/report", "/admin/page": "/admin", "/tasks/report/page": "/tasks/report", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/reports/data/page": "/reports/data", "/reports/page": "/reports", "/reports/analytics/page": "/reports/analytics", "/reliability/page": "/reliability"}