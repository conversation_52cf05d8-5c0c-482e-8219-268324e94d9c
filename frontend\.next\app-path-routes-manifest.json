{"/_not-found/page": "/_not-found", "/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/delegations/page": "/delegations", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/auth-test/page": "/auth-test", "/delegations/[id]/page": "/delegations/[id]", "/employees/[id]/edit/page": "/employees/[id]/edit", "/employees/[id]/page": "/employees/[id]", "/employees/new/page": "/employees/new", "/delegations/add/page": "/delegations/add", "/employees/page": "/employees", "/employees/add/page": "/employees/add", "/font-size-demo/page": "/font-size-demo", "/login/page": "/login", "/page": "/", "/profile/page": "/profile", "/settings/page": "/settings", "/service-history/page": "/service-history", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/service-records/[id]/page": "/service-records/[id]", "/tasks/add/page": "/tasks/add", "/tasks/[id]/page": "/tasks/[id]", "/tasks/page": "/tasks", "/vehicles/[id]/page": "/vehicles/[id]", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/new/page": "/vehicles/new", "/vehicles/page": "/vehicles", "/zustand-test/page": "/zustand-test", "/delegations/[id]/report/page": "/delegations/[id]/report", "/delegations/report/list/page": "/delegations/report/list", "/admin/page": "/admin", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/tasks/report/page": "/tasks/report", "/reports/page": "/reports", "/reports/data/page": "/reports/data", "/reports/analytics/page": "/reports/analytics", "/reliability/page": "/reliability"}