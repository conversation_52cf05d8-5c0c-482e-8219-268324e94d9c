{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { Session, User as SupabaseUser } from '@supabase/supabase-js';\r\nimport type { ReactNode } from 'react';\r\n\r\nimport React, {\r\n  createContext,\r\n  useCallback,\r\n  useContext,\r\n  useEffect,\r\n  useRef,\r\n  useState,\r\n} from 'react'; // Import useRef, useCallback, useState\r\n\r\n// Remove circular dependency - auth logic will be moved here\r\nimport { setSecureAuthTokenProvider } from '../lib/api';\r\nimport { SecurityUtils, SessionManager } from '../lib/security';\r\nimport { getTokenRefreshService } from '../lib/services/TokenRefreshService';\r\nimport { supabase } from '../lib/supabase';\r\n\r\n// Define a key for cross-tab logout events\r\nconst LOGOUT_EVENT_KEY = 'workhub-logout-event';\r\n\r\n// Define AuthContextType interface directly\r\ninterface AuthContextType {\r\n  clearError: () => void;\r\n  // Auth state\r\n  error: null | string;\r\n  isInitialized: boolean;\r\n  loading: boolean;\r\n  session: null | { access_token?: string; user?: null | User };\r\n  // Auth actions\r\n  signIn: (email: string, password: string) => Promise<{ error?: string }>;\r\n\r\n  signOut: () => Promise<void>;\r\n  user: null | User;\r\n  userRole: null | string;\r\n}\r\n\r\n// Define comprehensive user interface that matches Supabase User exactly\r\ninterface User {\r\n  app_metadata?: any;\r\n  created_at?: string | undefined;\r\n  email?: string | undefined;\r\n  email_confirmed_at?: string | undefined;\r\n  id: string;\r\n  is_anonymous?: boolean | undefined;\r\n  is_sso_user?: boolean;\r\n  last_sign_in_at?: string | undefined;\r\n  updated_at?: string | undefined;\r\n  user_metadata?: any;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\n/**\r\n * Authentication Context Provider\r\n *\r\n * This provider wraps the application and provides authentication state\r\n * and methods to all child components with integrated auth logic.\r\n */\r\nexport function AuthProvider({ children }: AuthProviderProps) {\r\n  // State management for authentication\r\n  const [session, setSession] = useState<null | Session>(null);\r\n  const [user, setUser] = useState<null | User>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<null | string>(null);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Helper function to transform Supabase user to our User interface\r\n  const transformUser = (supabaseUser: SupabaseUser): User => ({\r\n    app_metadata: supabaseUser.app_metadata,\r\n    created_at: supabaseUser.created_at,\r\n    email: supabaseUser.email,\r\n    email_confirmed_at: supabaseUser.email_confirmed_at,\r\n    id: supabaseUser.id,\r\n    is_anonymous: supabaseUser.is_anonymous,\r\n    is_sso_user: supabaseUser.app_metadata?.provider !== 'email',\r\n    last_sign_in_at: supabaseUser.last_sign_in_at,\r\n    updated_at: supabaseUser.updated_at,\r\n    user_metadata: supabaseUser.user_metadata,\r\n  });\r\n\r\n  // Initialize Supabase auth state\r\n  useEffect(() => {\r\n    let mounted = true;\r\n\r\n    // Get initial session with circuit breaker integration\r\n    const initializeAuth = async () => {\r\n      // Initialize circuit breaker first\r\n      SecurityUtils.initializeCircuitBreaker();\r\n\r\n      // Circuit breaker check for auth initialization\r\n      if (!SecurityUtils.canPerformSecurityCheck()) {\r\n        console.debug('🔒 Auth initialization blocked by circuit breaker');\r\n        if (mounted) {\r\n          setLoading(false);\r\n          setIsInitialized(true);\r\n          setError('Authentication system temporarily unavailable');\r\n        }\r\n        return;\r\n      }\r\n\r\n      const operationId = 'auth-initialization';\r\n      if (!SecurityUtils.startSecurityOperation(operationId)) {\r\n        console.debug('🔄 Auth initialization already in progress');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // First, get the initial session without integrity check to avoid race condition\r\n        const {\r\n          data: { session: initialSession },\r\n          error,\r\n        } = await supabase.auth.getSession();\r\n\r\n        if (error) {\r\n          console.error('Error getting initial session:', error);\r\n          SecurityUtils.recordSecurityAttempt();\r\n          setError(error.message);\r\n        } else if (mounted) {\r\n          console.log('✅ Auth initialization successful');\r\n          SecurityUtils.recordSecuritySuccess();\r\n\r\n          setSession(initialSession);\r\n          setUser(\r\n            initialSession?.user ? transformUser(initialSession.user) : null\r\n          );\r\n\r\n          // Update session manager with successful authentication\r\n          if (initialSession) {\r\n            SessionManager.updateActivity();\r\n\r\n            // Perform session integrity check AFTER authentication is established\r\n            setTimeout(async () => {\r\n              try {\r\n                const integrityCheck =\r\n                  await SessionManager.performIntegrityCheck();\r\n                if (integrityCheck) {\r\n                  console.log(\r\n                    '✅ Session integrity check passed after auth initialization'\r\n                  );\r\n                } else {\r\n                  console.log(\r\n                    '📊 Session integrity check failed - automatic recovery will handle this'\r\n                  );\r\n                  const recovered = SessionManager.recoverFromCorruptedState();\r\n                  if (!recovered) {\r\n                    console.warn(\r\n                      '⚠️ Session recovery completed with warnings after auth initialization'\r\n                    );\r\n                  }\r\n                }\r\n              } catch (error) {\r\n                console.warn('Session integrity check error:', error);\r\n                // Don't treat this as a critical error during initialization\r\n              }\r\n            }, 1000); // Increased delay to allow cookies to be properly set\r\n          }\r\n        }\r\n      } catch (error_) {\r\n        console.error('Error initializing auth:', error_);\r\n        SecurityUtils.recordSecurityAttempt();\r\n        if (mounted) {\r\n          setError('Failed to initialize authentication');\r\n        }\r\n      } finally {\r\n        SecurityUtils.endSecurityOperation(operationId);\r\n        if (mounted) {\r\n          setLoading(false);\r\n          setIsInitialized(true);\r\n        }\r\n      }\r\n    };\r\n\r\n    initializeAuth();\r\n\r\n    // Listen for auth changes\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\r\n      if (!mounted) return;\r\n\r\n      console.log('Auth state changed:', event, session?.user?.email);\r\n\r\n      setSession(session);\r\n      setUser(session?.user ? transformUser(session.user) : null);\r\n      setLoading(false);\r\n      setIsInitialized(true);\r\n\r\n      if (event === 'SIGNED_OUT') {\r\n        setError(null);\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      mounted = false;\r\n      subscription.unsubscribe();\r\n    };\r\n  }, []);\r\n\r\n  // Get user role from metadata\r\n  const getUserRole = (user: null | User): null | string => {\r\n    if (!user) return null;\r\n    return user.user_metadata?.role || user.app_metadata?.role || 'USER';\r\n  };\r\n\r\n  // Auth actions\r\n  const signIn = async (email: string, password: string) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const { data, error: signInError } =\r\n        await supabase.auth.signInWithPassword({\r\n          email,\r\n          password,\r\n        });\r\n\r\n      if (signInError) {\r\n        setError(signInError.message);\r\n        return { error: signInError.message };\r\n      }\r\n\r\n      return {};\r\n    } catch (error_) {\r\n      const errorMessage =\r\n        error_ instanceof Error\r\n          ? error_.message\r\n          : 'An unexpected error occurred';\r\n      setError(errorMessage);\r\n      return { error: errorMessage };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const signOut = async () => {\r\n    // Circuit breaker check for sign out\r\n    if (!SecurityUtils.canPerformSecurityCheck()) {\r\n      console.debug('🔒 Sign out blocked by circuit breaker');\r\n      return;\r\n    }\r\n\r\n    const operationId = 'auth-signout';\r\n    if (!SecurityUtils.startSecurityOperation(operationId)) {\r\n      console.debug('🔄 Sign out already in progress');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      console.log('🔐 Starting sign out process...');\r\n\r\n      // Clear session state first\r\n      SessionManager.clearSessionState();\r\n\r\n      // Clear all secure storage\r\n      SecurityUtils.clearAllCookies();\r\n\r\n      const { error: signOutError } = await supabase.auth.signOut();\r\n      if (signOutError) {\r\n        console.error('❌ Supabase sign out error:', signOutError);\r\n        SecurityUtils.recordSecurityAttempt();\r\n        setError(signOutError.message);\r\n      } else {\r\n        console.log('✅ Sign out successful');\r\n        SecurityUtils.recordSecuritySuccess();\r\n\r\n        // Clear any remaining authentication state\r\n        setSession(null);\r\n        setUser(null);\r\n        setError(null);\r\n      }\r\n    } catch (error_) {\r\n      console.error('Sign out error:', error_);\r\n      SecurityUtils.recordSecurityAttempt();\r\n      setError('Sign out failed');\r\n    } finally {\r\n      SecurityUtils.endSecurityOperation(operationId);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const clearError = () => {\r\n    setError(null);\r\n  };\r\n\r\n  // Create auth object\r\n  // SECURITY NOTE: HttpOnly Cookie Compliance\r\n  // The access_token exposed here is for client-side validation and header construction only.\r\n  // Actual authentication relies on HttpOnly cookies set by the backend.\r\n  // This token should NOT be stored in localStorage or used for direct API authentication.\r\n  const auth: AuthContextType = {\r\n    clearError,\r\n    error,\r\n    isInitialized,\r\n    loading,\r\n    session: session\r\n      ? {\r\n          // SECURITY: Token exposed for compatibility but HttpOnly cookies are primary auth method\r\n          access_token: session.access_token,\r\n          user: user ?? null,\r\n        }\r\n      : null,\r\n    signIn,\r\n    signOut,\r\n    user,\r\n    userRole: getUserRole(user),\r\n  };\r\n  const isLoggingOutRef = useRef(false);\r\n  const tokenRefreshService = getTokenRefreshService();\r\n\r\n  // Set up secure auth token provider for ALL API clients\r\n  useEffect(() => {\r\n    const getSecureToken = () => auth.session?.access_token || null;\r\n\r\n    // Set the SINGLE secure token provider for the entire application\r\n    // This replaces legacy setGlobalAuthTokenProvider and setFactoryAuthTokenProvider\r\n    setSecureAuthTokenProvider(getSecureToken);\r\n\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('🔐 AuthContext: Secure token provider updated', {\r\n        hasToken: !!auth.session?.access_token,\r\n        tokenLength: auth.session?.access_token?.length || 0,\r\n      });\r\n    }\r\n  }, [auth.session?.access_token]);\r\n\r\n  // Handle cross-tab logout with circuit breaker protection\r\n  const handleStorageChange = useCallback(\r\n    (event: StorageEvent) => {\r\n      if (event.key === LOGOUT_EVENT_KEY && event.newValue === 'true') {\r\n        console.log('🔐 Cross-tab logout detected. Signing out...');\r\n\r\n        // Circuit breaker check for cross-tab logout\r\n        if (!SecurityUtils.canPerformSecurityCheck()) {\r\n          console.debug('🔒 Cross-tab logout blocked by circuit breaker');\r\n          return;\r\n        }\r\n\r\n        if (!isLoggingOutRef.current) {\r\n          isLoggingOutRef.current = true;\r\n\r\n          const operationId = 'cross-tab-logout';\r\n          if (SecurityUtils.startSecurityOperation(operationId)) {\r\n            auth.signOut().finally(() => {\r\n              SecurityUtils.endSecurityOperation(operationId);\r\n              isLoggingOutRef.current = false;\r\n              // Clear the event key to allow future logout events\r\n              localStorage.removeItem(LOGOUT_EVENT_KEY);\r\n            });\r\n          } else {\r\n            console.debug('🔄 Cross-tab logout already in progress');\r\n            isLoggingOutRef.current = false;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    [auth.signOut]\r\n  );\r\n\r\n  // Handle critical token refresh failures with circuit breaker protection\r\n  useEffect(() => {\r\n    const handleCriticalRefreshFailed = () => {\r\n      console.warn(\r\n        '🔐 Critical token refresh failure detected, signing out user'\r\n      );\r\n\r\n      // Circuit breaker check for critical refresh failure\r\n      if (!SecurityUtils.canPerformSecurityCheck()) {\r\n        console.debug(\r\n          '🔒 Critical refresh failure handling blocked by circuit breaker'\r\n        );\r\n        return;\r\n      }\r\n\r\n      if (!isLoggingOutRef.current) {\r\n        isLoggingOutRef.current = true;\r\n\r\n        const operationId = 'critical-refresh-failure';\r\n        if (SecurityUtils.startSecurityOperation(operationId)) {\r\n          SecurityUtils.recordSecurityAttempt();\r\n          auth.signOut().finally(() => {\r\n            SecurityUtils.endSecurityOperation(operationId);\r\n            isLoggingOutRef.current = false;\r\n          });\r\n        } else {\r\n          console.debug(\r\n            '🔄 Critical refresh failure handling already in progress'\r\n          );\r\n          isLoggingOutRef.current = false;\r\n        }\r\n      }\r\n    };\r\n\r\n    tokenRefreshService.subscribe(event => {\r\n      if (event === 'critical_refresh_failed') {\r\n        handleCriticalRefreshFailed();\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      // No direct unsubscribe method on the service, but the service is a singleton\r\n      // and callbacks are managed by the Set. For a more robust solution,\r\n      // TokenRefreshService would need an unsubscribe method that takes the specific callback.\r\n      // For now, relying on the singleton nature and the fact that AuthContext is long-lived.\r\n    };\r\n  }, [auth.signOut, tokenRefreshService]);\r\n\r\n  // Phase 3: Token management now handled by TokenManager - no manual sync needed\r\n  useEffect(() => {\r\n    const token = auth.session?.access_token || null;\r\n\r\n    globalThis.addEventListener('storage', handleStorageChange);\r\n\r\n    // When this tab logs out, signal other tabs\r\n    if (!auth.session && !auth.loading && !isLoggingOutRef.current) {\r\n      // Only set if not already logging out to prevent loop\r\n      localStorage.setItem(LOGOUT_EVENT_KEY, 'true');\r\n    } else if (\r\n      auth.session &&\r\n      localStorage.getItem(LOGOUT_EVENT_KEY) === 'true'\r\n    ) {\r\n      // If a session exists but a logout signal is present, clear the signal\r\n      // This handles cases where a tab might have been open during a logout,\r\n      // then refreshed or re-authenticated.\r\n      localStorage.removeItem(LOGOUT_EVENT_KEY);\r\n    }\r\n\r\n    // Development-only authentication debugging\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      console.debug('🔐 AuthProvider: Token sync signal sent', {\r\n        authLoading: auth.loading,\r\n        tokenAvailable: !!token,\r\n        userEmail: auth.user?.email,\r\n      });\r\n\r\n      if (token && auth.user) {\r\n        console.debug(\r\n          '✅ Authentication ready for API calls (via httpOnly cookies)',\r\n          {\r\n            hasToken: true,\r\n            userEmail: auth.user.email,\r\n          }\r\n        );\r\n      } else if (!auth.loading) {\r\n        console.warn(\r\n          '⚠️ No token available client-side for direct access (expected for httpOnly cookies)',\r\n          {\r\n            hasSession: !!auth.session,\r\n            hasUser: !!auth.user,\r\n            loading: auth.loading,\r\n          }\r\n        );\r\n      }\r\n    }\r\n  }, [\r\n    auth.session?.access_token,\r\n    auth.user,\r\n    auth.loading,\r\n    handleStorageChange,\r\n  ]); // Add handleStorageChange to dependencies\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      globalThis.removeEventListener('storage', handleStorageChange);\r\n    };\r\n  }, [handleStorageChange]); // Depend on handleStorageChange\r\n\r\n  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>;\r\n}\r\n\r\n/**\r\n * Hook to use the authentication context\r\n *\r\n * @throws {Error} If used outside of AuthProvider\r\n */\r\nexport function useAuthContext(): AuthContextType {\r\n  const context = useContext(AuthContext);\r\n\r\n  if (context === undefined) {\r\n    throw new Error('useAuthContext must be used within an AuthProvider');\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n// Export the context for advanced use cases\r\nexport { AuthContext };\r\n"], "names": [], "mappings": ";;;;;AAqUQ;;AAhUR,mRAOgB,uCAAuC;AAEvD,6DAA6D;AAC7D;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;AAlBA;;;;;;AAoBA,2CAA2C;AAC3C,MAAM,mBAAmB;AAgCzB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAYxD,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,sCAAsC;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mEAAmE;IACnE,MAAM,gBAAgB,CAAC,eAAqC,CAAC;YAC3D,cAAc,aAAa,YAAY;YACvC,YAAY,aAAa,UAAU;YACnC,OAAO,aAAa,KAAK;YACzB,oBAAoB,aAAa,kBAAkB;YACnD,IAAI,aAAa,EAAE;YACnB,cAAc,aAAa,YAAY;YACvC,aAAa,aAAa,YAAY,EAAE,aAAa;YACrD,iBAAiB,aAAa,eAAe;YAC7C,YAAY,aAAa,UAAU;YACnC,eAAe,aAAa,aAAa;QAC3C,CAAC;IAED,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;YAEd,uDAAuD;YACvD,MAAM;yDAAiB;oBACrB,mCAAmC;oBACnC,kJAAA,CAAA,gBAAa,CAAC,wBAAwB;oBAEtC,gDAAgD;oBAChD,IAAI,CAAC,kJAAA,CAAA,gBAAa,CAAC,uBAAuB,IAAI;wBAC5C,QAAQ,KAAK,CAAC;wBACd,IAAI,SAAS;4BACX,WAAW;4BACX,iBAAiB;4BACjB,SAAS;wBACX;wBACA;oBACF;oBAEA,MAAM,cAAc;oBACpB,IAAI,CAAC,kJAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,cAAc;wBACtD,QAAQ,KAAK,CAAC;wBACd;oBACF;oBAEA,IAAI;wBACF,iFAAiF;wBACjF,MAAM,EACJ,MAAM,EAAE,SAAS,cAAc,EAAE,EACjC,KAAK,EACN,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;wBAElC,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,kCAAkC;4BAChD,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;4BACnC,SAAS,MAAM,OAAO;wBACxB,OAAO,IAAI,SAAS;4BAClB,QAAQ,GAAG,CAAC;4BACZ,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;4BAEnC,WAAW;4BACX,QACE,gBAAgB,OAAO,cAAc,eAAe,IAAI,IAAI;4BAG9D,wDAAwD;4BACxD,IAAI,gBAAgB;gCAClB,2IAAA,CAAA,iBAAc,CAAC,cAAc;gCAE7B,sEAAsE;gCACtE;6EAAW;wCACT,IAAI;4CACF,MAAM,iBACJ,MAAM,2IAAA,CAAA,iBAAc,CAAC,qBAAqB;4CAC5C,IAAI,gBAAgB;gDAClB,QAAQ,GAAG,CACT;4CAEJ,OAAO;gDACL,QAAQ,GAAG,CACT;gDAEF,MAAM,YAAY,2IAAA,CAAA,iBAAc,CAAC,yBAAyB;gDAC1D,IAAI,CAAC,WAAW;oDACd,QAAQ,IAAI,CACV;gDAEJ;4CACF;wCACF,EAAE,OAAO,OAAO;4CACd,QAAQ,IAAI,CAAC,kCAAkC;wCAC/C,6DAA6D;wCAC/D;oCACF;4EAAG,OAAO,sDAAsD;4BAClE;wBACF;oBACF,EAAE,OAAO,QAAQ;wBACf,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;wBACnC,IAAI,SAAS;4BACX,SAAS;wBACX;oBACF,SAAU;wBACR,kJAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;wBACnC,IAAI,SAAS;4BACX,WAAW;4BACX,iBAAiB;wBACnB;oBACF;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,IAAI,CAAC,SAAS;oBAEd,QAAQ,GAAG,CAAC,uBAAuB,OAAO,SAAS,MAAM;oBAEzD,WAAW;oBACX,QAAQ,SAAS,OAAO,cAAc,QAAQ,IAAI,IAAI;oBACtD,WAAW;oBACX,iBAAiB;oBAEjB,IAAI,UAAU,cAAc;wBAC1B,SAAS;oBACX;gBACF;;YAEA;0CAAO;oBACL,UAAU;oBACV,aAAa,WAAW;gBAC1B;;QACF;iCAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,aAAa,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ;IAChE;IAEA,eAAe;IACf,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,EAAE,IAAI,EAAE,OAAO,WAAW,EAAE,GAChC,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACrC;gBACA;YACF;YAEF,IAAI,aAAa;gBACf,SAAS,YAAY,OAAO;gBAC5B,OAAO;oBAAE,OAAO,YAAY,OAAO;gBAAC;YACtC;YAEA,OAAO,CAAC;QACV,EAAE,OAAO,QAAQ;YACf,MAAM,eACJ,kBAAkB,QACd,OAAO,OAAO,GACd;YACN,SAAS;YACT,OAAO;gBAAE,OAAO;YAAa;QAC/B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,qCAAqC;QACrC,IAAI,CAAC,kJAAA,CAAA,gBAAa,CAAC,uBAAuB,IAAI;YAC5C,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,MAAM,cAAc;QACpB,IAAI,CAAC,kJAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,cAAc;YACtD,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC;YAEZ,4BAA4B;YAC5B,2IAAA,CAAA,iBAAc,CAAC,iBAAiB;YAEhC,2BAA2B;YAC3B,kJAAA,CAAA,gBAAa,CAAC,eAAe;YAE7B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3D,IAAI,cAAc;gBAChB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;gBACnC,SAAS,aAAa,OAAO;YAC/B,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;gBAEnC,2CAA2C;gBAC3C,WAAW;gBACX,QAAQ;gBACR,SAAS;YACX;QACF,EAAE,OAAO,QAAQ;YACf,QAAQ,KAAK,CAAC,mBAAmB;YACjC,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;YACnC,SAAS;QACX,SAAU;YACR,kJAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;YACnC,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,SAAS;IACX;IAEA,qBAAqB;IACrB,4CAA4C;IAC5C,4FAA4F;IAC5F,uEAAuE;IACvE,yFAAyF;IACzF,MAAM,OAAwB;QAC5B;QACA;QACA;QACA;QACA,SAAS,UACL;YACE,yFAAyF;YACzF,cAAc,QAAQ,YAAY;YAClC,MAAM,QAAQ;QAChB,IACA;QACJ;QACA;QACA;QACA,UAAU,YAAY;IACxB;IACA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;IAEjD,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB,IAAM,KAAK,OAAO,EAAE,gBAAgB;;YAE3D,kEAAkE;YAClE,kFAAkF;YAClF,CAAA,GAAA,6IAAA,CAAA,6BAA0B,AAAD,EAAE;YAE3B,wCAA4C;gBAC1C,QAAQ,GAAG,CAAC,iDAAiD;oBAC3D,UAAU,CAAC,CAAC,KAAK,OAAO,EAAE;oBAC1B,aAAa,KAAK,OAAO,EAAE,cAAc,UAAU;gBACrD;YACF;QACF;iCAAG;QAAC,KAAK,OAAO,EAAE;KAAa;IAE/B,0DAA0D;IAC1D,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDACpC,CAAC;YACC,IAAI,MAAM,GAAG,KAAK,oBAAoB,MAAM,QAAQ,KAAK,QAAQ;gBAC/D,QAAQ,GAAG,CAAC;gBAEZ,6CAA6C;gBAC7C,IAAI,CAAC,kJAAA,CAAA,gBAAa,CAAC,uBAAuB,IAAI;oBAC5C,QAAQ,KAAK,CAAC;oBACd;gBACF;gBAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,gBAAgB,OAAO,GAAG;oBAE1B,MAAM,cAAc;oBACpB,IAAI,kJAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,cAAc;wBACrD,KAAK,OAAO,GAAG,OAAO;6EAAC;gCACrB,kJAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;gCACnC,gBAAgB,OAAO,GAAG;gCAC1B,oDAAoD;gCACpD,aAAa,UAAU,CAAC;4BAC1B;;oBACF,OAAO;wBACL,QAAQ,KAAK,CAAC;wBACd,gBAAgB,OAAO,GAAG;oBAC5B;gBACF;YACF;QACF;wDACA;QAAC,KAAK,OAAO;KAAC;IAGhB,yEAAyE;IACzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;sEAA8B;oBAClC,QAAQ,IAAI,CACV;oBAGF,qDAAqD;oBACrD,IAAI,CAAC,kJAAA,CAAA,gBAAa,CAAC,uBAAuB,IAAI;wBAC5C,QAAQ,KAAK,CACX;wBAEF;oBACF;oBAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;wBAC5B,gBAAgB,OAAO,GAAG;wBAE1B,MAAM,cAAc;wBACpB,IAAI,kJAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,cAAc;4BACrD,kJAAA,CAAA,gBAAa,CAAC,qBAAqB;4BACnC,KAAK,OAAO,GAAG,OAAO;sFAAC;oCACrB,kJAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;oCACnC,gBAAgB,OAAO,GAAG;gCAC5B;;wBACF,OAAO;4BACL,QAAQ,KAAK,CACX;4BAEF,gBAAgB,OAAO,GAAG;wBAC5B;oBACF;gBACF;;YAEA,oBAAoB,SAAS;0CAAC,CAAA;oBAC5B,IAAI,UAAU,2BAA2B;wBACvC;oBACF;gBACF;;YAEA;0CAAO;gBACL,8EAA8E;gBAC9E,oEAAoE;gBACpE,yFAAyF;gBACzF,wFAAwF;gBAC1F;;QACF;iCAAG;QAAC,KAAK,OAAO;QAAE;KAAoB;IAEtC,gFAAgF;IAChF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ,KAAK,OAAO,EAAE,gBAAgB;YAE5C,WAAW,gBAAgB,CAAC,WAAW;YAEvC,4CAA4C;YAC5C,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC9D,sDAAsD;gBACtD,aAAa,OAAO,CAAC,kBAAkB;YACzC,OAAO,IACL,KAAK,OAAO,IACZ,aAAa,OAAO,CAAC,sBAAsB,QAC3C;gBACA,uEAAuE;gBACvE,uEAAuE;gBACvE,sCAAsC;gBACtC,aAAa,UAAU,CAAC;YAC1B;YAEA,4CAA4C;YAC5C,wCAA2C;gBACzC,QAAQ,KAAK,CAAC,2CAA2C;oBACvD,aAAa,KAAK,OAAO;oBACzB,gBAAgB,CAAC,CAAC;oBAClB,WAAW,KAAK,IAAI,EAAE;gBACxB;gBAEA,IAAI,SAAS,KAAK,IAAI,EAAE;oBACtB,QAAQ,KAAK,CACX,+DACA;wBACE,UAAU;wBACV,WAAW,KAAK,IAAI,CAAC,KAAK;oBAC5B;gBAEJ,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE;oBACxB,QAAQ,IAAI,CACV,uFACA;wBACE,YAAY,CAAC,CAAC,KAAK,OAAO;wBAC1B,SAAS,CAAC,CAAC,KAAK,IAAI;wBACpB,SAAS,KAAK,OAAO;oBACvB;gBAEJ;YACF;QACF;iCAAG;QACD,KAAK,OAAO,EAAE;QACd,KAAK,IAAI;QACT,KAAK,OAAO;QACZ;KACD,GAAG,0CAA0C;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;0CAAO;oBACL,WAAW,mBAAmB,CAAC,WAAW;gBAC5C;;QACF;iCAAG;QAAC;KAAoB,GAAG,gCAAgC;IAE3D,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAO;;;;;;AAC7C;GA1ZgB;KAAA;AAiaT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;IARgB", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/forms/useLoginValidation.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\n\r\ninterface LoginFormData {\r\n  email: string;\r\n  password: string;\r\n  rememberMe: boolean;\r\n}\r\n\r\ninterface ValidationErrors {\r\n  email?: string;\r\n  password?: string;\r\n}\r\n\r\ninterface ValidationResult {\r\n  isValid: boolean;\r\n  errors: ValidationErrors;\r\n}\r\n\r\n/**\r\n * Custom hook for login form validation\r\n *\r\n * Provides real-time validation with enhanced UX features:\r\n * - Email format validation\r\n * - Password strength checking\r\n * - Real-time error clearing\r\n * - Enhanced user feedback\r\n */\r\nexport function useLoginValidation() {\r\n  const [errors, setErrors] = useState<ValidationErrors>({});\r\n  const [isFormTouched, setIsFormTouched] = useState(false);\r\n\r\n  // Enhanced email validation\r\n  const validateEmail = useCallback((email: string): string | undefined => {\r\n    if (!email) {\r\n      return 'Email address is required';\r\n    }\r\n\r\n    // More comprehensive email validation\r\n    const emailRegex =\r\n      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\r\n\r\n    if (!emailRegex.test(email)) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n\r\n    return undefined;\r\n  }, []);\r\n\r\n  // Enhanced password validation\r\n  const validatePassword = useCallback(\r\n    (password: string): string | undefined => {\r\n      if (!password) {\r\n        return 'Password is required';\r\n      }\r\n\r\n      if (password.length < 6) {\r\n        return 'Password must be at least 6 characters long';\r\n      }\r\n\r\n      // Additional password strength checks can be added here\r\n      return undefined;\r\n    },\r\n    []\r\n  );\r\n\r\n  // Validate entire form\r\n  const validateForm = useCallback(\r\n    (formData: LoginFormData): ValidationResult => {\r\n      const newErrors: ValidationErrors = {};\r\n\r\n      const emailError = validateEmail(formData.email);\r\n      const passwordError = validatePassword(formData.password);\r\n\r\n      if (emailError) newErrors.email = emailError;\r\n      if (passwordError) newErrors.password = passwordError;\r\n\r\n      setErrors(newErrors);\r\n\r\n      return {\r\n        isValid: Object.keys(newErrors).length === 0,\r\n        errors: newErrors,\r\n      };\r\n    },\r\n    [validateEmail, validatePassword]\r\n  );\r\n\r\n  // Validate single field\r\n  const validateField = useCallback(\r\n    (fieldName: keyof LoginFormData, value: string) => {\r\n      let fieldError: string | undefined;\r\n\r\n      switch (fieldName) {\r\n        case 'email':\r\n          fieldError = validateEmail(value);\r\n          break;\r\n        case 'password':\r\n          fieldError = validatePassword(value);\r\n          break;\r\n        default:\r\n          return;\r\n      }\r\n\r\n      setErrors(prev => ({\r\n        ...prev,\r\n        [fieldName]: fieldError,\r\n      }));\r\n    },\r\n    [validateEmail, validatePassword]\r\n  );\r\n\r\n  // Clear specific field error\r\n  const clearFieldError = useCallback((fieldName: keyof ValidationErrors) => {\r\n    setErrors(prev => {\r\n      const newErrors = { ...prev };\r\n      delete newErrors[fieldName];\r\n      return newErrors;\r\n    });\r\n  }, []);\r\n\r\n  // Clear all errors\r\n  const clearAllErrors = useCallback(() => {\r\n    setErrors({});\r\n  }, []);\r\n\r\n  // Mark form as touched\r\n  const markFormTouched = useCallback(() => {\r\n    setIsFormTouched(true);\r\n  }, []);\r\n\r\n  // Reset validation state\r\n  const resetValidation = useCallback(() => {\r\n    setErrors({});\r\n    setIsFormTouched(false);\r\n  }, []);\r\n\r\n  // Check if field has been validated successfully\r\n  const isFieldValid = useCallback(\r\n    (fieldName: keyof ValidationErrors, value: string) => {\r\n      return isFormTouched && value && !errors[fieldName];\r\n    },\r\n    [errors, isFormTouched]\r\n  );\r\n\r\n  return {\r\n    errors,\r\n    isFormTouched,\r\n    validateForm,\r\n    validateField,\r\n    clearFieldError,\r\n    clearAllErrors,\r\n    markFormTouched,\r\n    resetValidation,\r\n    isFieldValid,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AA2BO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,CAAC;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,4BAA4B;IAC5B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACjC,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,sCAAsC;YACtC,MAAM,aACJ;YAEF,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;gBAC3B,OAAO;YACT;YAEA,OAAO;QACT;wDAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DACjC,CAAC;YACC,IAAI,CAAC,UAAU;gBACb,OAAO;YACT;YAEA,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,OAAO;YACT;YAEA,wDAAwD;YACxD,OAAO;QACT;2DACA,EAAE;IAGJ,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAC7B,CAAC;YACC,MAAM,YAA8B,CAAC;YAErC,MAAM,aAAa,cAAc,SAAS,KAAK;YAC/C,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ;YAExD,IAAI,YAAY,UAAU,KAAK,GAAG;YAClC,IAAI,eAAe,UAAU,QAAQ,GAAG;YAExC,UAAU;YAEV,OAAO;gBACL,SAAS,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;gBAC3C,QAAQ;YACV;QACF;uDACA;QAAC;QAAe;KAAiB;IAGnC,wBAAwB;IACxB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAC9B,CAAC,WAAgC;YAC/B,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,aAAa,cAAc;oBAC3B;gBACF,KAAK;oBACH,aAAa,iBAAiB;oBAC9B;gBACF;oBACE;YACJ;YAEA;iEAAU,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE;oBACf,CAAC;;QACH;wDACA;QAAC;QAAe;KAAiB;IAGnC,6BAA6B;IAC7B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACnC;mEAAU,CAAA;oBACR,MAAM,YAAY;wBAAE,GAAG,IAAI;oBAAC;oBAC5B,OAAO,SAAS,CAAC,UAAU;oBAC3B,OAAO;gBACT;;QACF;0DAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACjC,UAAU,CAAC;QACb;yDAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YAClC,iBAAiB;QACnB;0DAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YAClC,UAAU,CAAC;YACX,iBAAiB;QACnB;0DAAG,EAAE;IAEL,iDAAiD;IACjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAC7B,CAAC,WAAmC;YAClC,OAAO,iBAAiB,SAAS,CAAC,MAAM,CAAC,UAAU;QACrD;uDACA;QAAC;QAAQ;KAAc;IAGzB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA/HgB", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/ui/useTheme.ts"], "sourcesContent": ["/**\r\n * @file Theme management hook using Zustand AppStore\r\n * @module hooks/useTheme\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\n\r\n/**\r\n * Hook for theme management with additional utilities\r\n * Provides convenient methods for theme switching and state checking\r\n */\r\nexport const useTheme = () => {\r\n  const currentTheme = useAppStore(state => state.currentTheme);\r\n  const setTheme = useAppStore(state => state.setTheme);\r\n\r\n  /**\r\n   * Toggle between light and dark themes\r\n   */\r\n  const toggleTheme = useCallback(() => {\r\n    setTheme(currentTheme === 'light' ? 'dark' : 'light');\r\n  }, [currentTheme, setTheme]);\r\n\r\n  /**\r\n   * Check if current theme is dark\r\n   */\r\n  const isDark = currentTheme === 'dark';\r\n\r\n  /**\r\n   * Check if current theme is light\r\n   */\r\n  const isLight = currentTheme === 'light';\r\n\r\n  /**\r\n   * Set theme to light\r\n   */\r\n  const setLightTheme = useCallback(() => {\r\n    setTheme('light');\r\n  }, [setTheme]);\r\n\r\n  /**\r\n   * Set theme to dark\r\n   */\r\n  const setDarkTheme = useCallback(() => {\r\n    setTheme('dark');\r\n  }, [setTheme]);\r\n\r\n  /**\r\n   * Get theme-specific CSS classes\r\n   */\r\n  const getThemeClasses = useCallback(() => {\r\n    return {\r\n      background: isDark ? 'bg-gray-900' : 'bg-white',\r\n      border: isDark ? 'border-gray-700' : 'border-gray-200',\r\n      isDark,\r\n      isLight,\r\n      root: currentTheme,\r\n      text: isDark ? 'text-white' : 'text-gray-900',\r\n    };\r\n  }, [currentTheme, isDark, isLight]);\r\n\r\n  return {\r\n    // State\r\n    currentTheme,\r\n    // Utilities\r\n    getThemeClasses,\r\n    isDark,\r\n\r\n    isLight,\r\n    setDarkTheme,\r\n    setLightTheme,\r\n    // Actions\r\n    setTheme,\r\n\r\n    toggleTheme,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAEA;;;;AAMO,MAAM,WAAW;;IACtB,MAAM,eAAe,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;8CAAE,CAAA,QAAS,MAAM,YAAY;;IAC5D,MAAM,WAAW,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;0CAAE,CAAA,QAAS,MAAM,QAAQ;;IAEpD;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC9B,SAAS,iBAAiB,UAAU,SAAS;QAC/C;4CAAG;QAAC;QAAc;KAAS;IAE3B;;GAEC,GACD,MAAM,SAAS,iBAAiB;IAEhC;;GAEC,GACD,MAAM,UAAU,iBAAiB;IAEjC;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAChC,SAAS;QACX;8CAAG;QAAC;KAAS;IAEb;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC/B,SAAS;QACX;6CAAG;QAAC;KAAS;IAEb;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAClC,OAAO;gBACL,YAAY,SAAS,gBAAgB;gBACrC,QAAQ,SAAS,oBAAoB;gBACrC;gBACA;gBACA,MAAM;gBACN,MAAM,SAAS,eAAe;YAChC;QACF;gDAAG;QAAC;QAAc;QAAQ;KAAQ;IAElC,OAAO;QACL,QAAQ;QACR;QACA,YAAY;QACZ;QACA;QAEA;QACA;QACA;QACA,UAAU;QACV;QAEA;IACF;AACF;GAhEa;;QACU,8IAAA,CAAA,cAAW;QACf,8IAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/ui/useUiPreferences.ts"], "sourcesContent": ["/**\r\n * @file UI preferences management hook using Zustand UiStore\r\n * @module hooks/useUiPreferences\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useUiStore } from '@/lib/stores/zustand/uiStore';\r\n\r\n/**\r\n * Hook for UI preferences management\r\n * Provides convenient methods for managing user interface preferences\r\n */\r\nexport const useUiPreferences = () => {\r\n  // Font size preferences\r\n  const fontSize = useUiStore(state => state.fontSize);\r\n  const setFontSize = useUiStore(state => state.setFontSize);\r\n\r\n  // Notification preferences\r\n  const notificationsEnabled = useUiStore(state => state.notificationsEnabled);\r\n  const toggleNotifications = useUiStore(state => state.toggleNotifications);\r\n\r\n  // WorkHub-specific preferences\r\n  const tableDensity = useUiStore(state => state.tableDensity);\r\n  const setTableDensity = useUiStore(state => state.setTableDensity);\r\n\r\n  const mapViewPreference = useUiStore(state => state.mapViewPreference);\r\n  const setMapViewPreference = useUiStore(state => state.setMapViewPreference);\r\n\r\n  const dashboardLayout = useUiStore(state => state.dashboardLayout);\r\n  const setDashboardLayout = useUiStore(state => state.setDashboardLayout);\r\n\r\n  const autoRefreshInterval = useUiStore(state => state.autoRefreshInterval);\r\n  const setAutoRefreshInterval = useUiStore(\r\n    state => state.setAutoRefreshInterval\r\n  );\r\n\r\n  /**\r\n   * Get font size CSS class\r\n   */\r\n  const getFontSizeClass = useCallback(() => {\r\n    switch (fontSize) {\r\n      case 'large': {\r\n        return 'text-lg';\r\n      }\r\n      case 'small': {\r\n        return 'text-sm';\r\n      }\r\n      default: {\r\n        return 'text-base';\r\n      }\r\n    }\r\n  }, [fontSize]);\r\n\r\n  /**\r\n   * Get table density CSS classes\r\n   */\r\n  const getTableDensityClasses = useCallback(() => {\r\n    switch (tableDensity) {\r\n      case 'compact': {\r\n        return {\r\n          cell: 'py-1 px-2',\r\n          row: 'h-8',\r\n          table: 'table-compact',\r\n        };\r\n      }\r\n      case 'spacious': {\r\n        return {\r\n          cell: 'py-4 px-4',\r\n          row: 'h-16',\r\n          table: 'table-spacious',\r\n        };\r\n      }\r\n      default: { // comfortable\r\n        return {\r\n          cell: 'py-2 px-3',\r\n          row: 'h-12',\r\n          table: 'table-comfortable',\r\n        };\r\n      }\r\n    }\r\n  }, [tableDensity]);\r\n\r\n  /**\r\n   * Get dashboard layout CSS classes\r\n   */\r\n  const getDashboardLayoutClasses = useCallback(() => {\r\n    switch (dashboardLayout) {\r\n      case 'cards': {\r\n        return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';\r\n      }\r\n      case 'grid': {\r\n        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';\r\n      }\r\n      case 'list': {\r\n        return 'flex flex-col space-y-4';\r\n      }\r\n      default: {\r\n        return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';\r\n      }\r\n    }\r\n  }, [dashboardLayout]);\r\n\r\n  /**\r\n   * Enable notifications\r\n   */\r\n  const enableNotifications = useCallback(() => {\r\n    if (!notificationsEnabled) {\r\n      toggleNotifications();\r\n    }\r\n  }, [notificationsEnabled, toggleNotifications]);\r\n\r\n  /**\r\n   * Disable notifications\r\n   */\r\n  const disableNotifications = useCallback(() => {\r\n    if (notificationsEnabled) {\r\n      toggleNotifications();\r\n    }\r\n  }, [notificationsEnabled, toggleNotifications]);\r\n\r\n  /**\r\n   * Reset all preferences to defaults\r\n   */\r\n  const resetPreferences = useCallback(() => {\r\n    setFontSize('medium');\r\n    setTableDensity('comfortable');\r\n    setMapViewPreference('roadmap');\r\n    setDashboardLayout('cards');\r\n    setAutoRefreshInterval(30);\r\n    // Note: We don't reset notifications as that's a user choice\r\n  }, [\r\n    setFontSize,\r\n    setTableDensity,\r\n    setMapViewPreference,\r\n    setDashboardLayout,\r\n    setAutoRefreshInterval,\r\n  ]);\r\n\r\n  /**\r\n   * Get all preferences as an object\r\n   */\r\n  const getAllPreferences = useCallback(() => {\r\n    return {\r\n      autoRefreshInterval,\r\n      dashboardLayout,\r\n      fontSize,\r\n      mapViewPreference,\r\n      notificationsEnabled,\r\n      tableDensity,\r\n    };\r\n  }, [\r\n    fontSize,\r\n    notificationsEnabled,\r\n    tableDensity,\r\n    mapViewPreference,\r\n    dashboardLayout,\r\n    autoRefreshInterval,\r\n  ]);\r\n\r\n  return {\r\n    // Auto-refresh preferences\r\n    autoRefreshInterval,\r\n    // Dashboard preferences\r\n    dashboardLayout,\r\n    disableNotifications,\r\n\r\n    enableNotifications,\r\n    // Font size\r\n    fontSize,\r\n    getAllPreferences,\r\n    getDashboardLayoutClasses,\r\n\r\n    getFontSizeClass,\r\n    getTableDensityClasses,\r\n    // Map preferences\r\n    mapViewPreference,\r\n\r\n    // Notifications\r\n    notificationsEnabled,\r\n    // Utilities\r\n    resetPreferences,\r\n\r\n    setAutoRefreshInterval,\r\n    setDashboardLayout,\r\n    setFontSize,\r\n\r\n    setMapViewPreference,\r\n    setTableDensity,\r\n\r\n    // Table preferences\r\n    tableDensity,\r\n    toggleNotifications,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAEA;;;;AAMO,MAAM,mBAAmB;;IAC9B,wBAAwB;IACxB,MAAM,WAAW,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;iDAAE,CAAA,QAAS,MAAM,QAAQ;;IACnD,MAAM,cAAc,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;oDAAE,CAAA,QAAS,MAAM,WAAW;;IAEzD,2BAA2B;IAC3B,MAAM,uBAAuB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;6DAAE,CAAA,QAAS,MAAM,oBAAoB;;IAC3E,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;4DAAE,CAAA,QAAS,MAAM,mBAAmB;;IAEzE,+BAA+B;IAC/B,MAAM,eAAe,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;qDAAE,CAAA,QAAS,MAAM,YAAY;;IAC3D,MAAM,kBAAkB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;wDAAE,CAAA,QAAS,MAAM,eAAe;;IAEjE,MAAM,oBAAoB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;0DAAE,CAAA,QAAS,MAAM,iBAAiB;;IACrE,MAAM,uBAAuB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;6DAAE,CAAA,QAAS,MAAM,oBAAoB;;IAE3E,MAAM,kBAAkB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;wDAAE,CAAA,QAAS,MAAM,eAAe;;IACjE,MAAM,qBAAqB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;2DAAE,CAAA,QAAS,MAAM,kBAAkB;;IAEvE,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;4DAAE,CAAA,QAAS,MAAM,mBAAmB;;IACzE,MAAM,yBAAyB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;+DACtC,CAAA,QAAS,MAAM,sBAAsB;;IAGvC;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACnC,OAAQ;gBACN,KAAK;oBAAS;wBACZ,OAAO;oBACT;gBACA,KAAK;oBAAS;wBACZ,OAAO;oBACT;gBACA;oBAAS;wBACP,OAAO;oBACT;YACF;QACF;yDAAG;QAAC;KAAS;IAEb;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE;YACzC,OAAQ;gBACN,KAAK;oBAAW;wBACd,OAAO;4BACL,MAAM;4BACN,KAAK;4BACL,OAAO;wBACT;oBACF;gBACA,KAAK;oBAAY;wBACf,OAAO;4BACL,MAAM;4BACN,KAAK;4BACL,OAAO;wBACT;oBACF;gBACA;oBAAS;wBACP,OAAO;4BACL,MAAM;4BACN,KAAK;4BACL,OAAO;wBACT;oBACF;YACF;QACF;+DAAG;QAAC;KAAa;IAEjB;;GAEC,GACD,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEAAE;YAC5C,OAAQ;gBACN,KAAK;oBAAS;wBACZ,OAAO;oBACT;gBACA,KAAK;oBAAQ;wBACX,OAAO;oBACT;gBACA,KAAK;oBAAQ;wBACX,OAAO;oBACT;gBACA;oBAAS;wBACP,OAAO;oBACT;YACF;QACF;kEAAG;QAAC;KAAgB;IAEpB;;GAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACtC,IAAI,CAAC,sBAAsB;gBACzB;YACF;QACF;4DAAG;QAAC;QAAsB;KAAoB;IAE9C;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,IAAI,sBAAsB;gBACxB;YACF;QACF;6DAAG;QAAC;QAAsB;KAAoB;IAE9C;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACnC,YAAY;YACZ,gBAAgB;YAChB,qBAAqB;YACrB,mBAAmB;YACnB,uBAAuB;QACvB,6DAA6D;QAC/D;yDAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACpC,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;0DAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,2BAA2B;QAC3B;QACA,wBAAwB;QACxB;QACA;QAEA;QACA,YAAY;QACZ;QACA;QACA;QAEA;QACA;QACA,kBAAkB;QAClB;QAEA,gBAAgB;QAChB;QACA,YAAY;QACZ;QAEA;QACA;QACA;QAEA;QACA;QAEA,oBAAoB;QACpB;QACA;IACF;AACF;GArLa;;QAEM,6IAAA,CAAA,aAAU;QACP,6IAAA,CAAA,aAAU;QAGD,6IAAA,CAAA,aAAU;QACX,6IAAA,CAAA,aAAU;QAGjB,6IAAA,CAAA,aAAU;QACP,6IAAA,CAAA,aAAU;QAER,6IAAA,CAAA,aAAU;QACP,6IAAA,CAAA,aAAU;QAEf,6IAAA,CAAA,aAAU;QACP,6IAAA,CAAA,aAAU;QAET,6IAAA,CAAA,aAAU;QACP,6IAAA,CAAA,aAAU", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/ui/useModal.ts"], "sourcesContent": ["/**\r\n * @file Modal management hook using Zustand UiStore\r\n * @module hooks/useModal\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useUiStore } from '@/lib/stores/zustand/uiStore';\r\n\r\n/**\r\n * Modal content types for WorkHub\r\n */\r\nexport type ModalContent =\r\n  | 'delegation-form'\r\n  | 'employee-profile'\r\n  | 'login'\r\n  | 'settings'\r\n  | 'signup'\r\n  | 'task-assignment'\r\n  | 'vehicle-details'\r\n  | null;\r\n\r\n/**\r\n * Hook for modal management\r\n * Provides convenient methods for modal state control\r\n */\r\nexport const useModal = () => {\r\n  const isModalOpen = useUiStore(state => state.isModalOpen);\r\n  const modalContent = useUiStore(state => state.modalContent);\r\n  const openModal = useUiStore(state => state.openModal);\r\n  const closeModal = useUiStore(state => state.closeModal);\r\n\r\n  /**\r\n   * Open login modal\r\n   */\r\n  const openLoginModal = useCallback(() => {\r\n    openModal('login');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Open signup modal\r\n   */\r\n  const openSignupModal = useCallback(() => {\r\n    openModal('signup');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Open settings modal\r\n   */\r\n  const openSettingsModal = useCallback(() => {\r\n    openModal('settings');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Open delegation form modal\r\n   */\r\n  const openDelegationFormModal = useCallback(() => {\r\n    openModal('delegation-form');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Open vehicle details modal\r\n   */\r\n  const openVehicleDetailsModal = useCallback(() => {\r\n    openModal('vehicle-details');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Open task assignment modal\r\n   */\r\n  const openTaskAssignmentModal = useCallback(() => {\r\n    openModal('task-assignment');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Open employee profile modal\r\n   */\r\n  const openEmployeeProfileModal = useCallback(() => {\r\n    openModal('employee-profile');\r\n  }, [openModal]);\r\n\r\n  /**\r\n   * Check if a specific modal is open\r\n   */\r\n  const isModalOfTypeOpen = useCallback(\r\n    (type: ModalContent) => {\r\n      return isModalOpen && modalContent === type;\r\n    },\r\n    [isModalOpen, modalContent]\r\n  );\r\n\r\n  /**\r\n   * Get modal-specific CSS classes\r\n   */\r\n  const getModalClasses = useCallback(() => {\r\n    return {\r\n      backdrop: 'modal-backdrop',\r\n      container: isModalOpen\r\n        ? 'modal-container-visible'\r\n        : 'modal-container-hidden',\r\n      content: `modal-content modal-content-${modalContent || 'default'}`,\r\n      overlay: isModalOpen ? 'modal-overlay-visible' : 'modal-overlay-hidden',\r\n    };\r\n  }, [isModalOpen, modalContent]);\r\n\r\n  /**\r\n   * Get modal accessibility attributes\r\n   */\r\n  const getModalAriaAttributes = useCallback(() => {\r\n    return {\r\n      'aria-describedby': modalContent\r\n        ? `${modalContent}-modal-description`\r\n        : undefined,\r\n      'aria-hidden': !isModalOpen,\r\n      'aria-labelledby': modalContent\r\n        ? `${modalContent}-modal-title`\r\n        : undefined,\r\n      'aria-modal': isModalOpen,\r\n      role: 'dialog',\r\n    };\r\n  }, [isModalOpen, modalContent]);\r\n\r\n  /**\r\n   * Handle escape key press to close modal\r\n   */\r\n  const handleEscapeKey = useCallback(\r\n    (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isModalOpen) {\r\n        closeModal();\r\n      }\r\n    },\r\n    [isModalOpen, closeModal]\r\n  );\r\n\r\n  /**\r\n   * Handle backdrop click to close modal\r\n   */\r\n  const handleBackdropClick = useCallback(\r\n    (event: React.MouseEvent) => {\r\n      if (event.target === event.currentTarget && isModalOpen) {\r\n        closeModal();\r\n      }\r\n    },\r\n    [isModalOpen, closeModal]\r\n  );\r\n\r\n  /**\r\n   * Get modal title based on content type\r\n   */\r\n  const getModalTitle = useCallback(() => {\r\n    switch (modalContent) {\r\n      case 'delegation-form': {\r\n        return 'Create Delegation';\r\n      }\r\n      case 'employee-profile': {\r\n        return 'Employee Profile';\r\n      }\r\n      case 'login': {\r\n        return 'Sign In';\r\n      }\r\n      case 'settings': {\r\n        return 'Settings';\r\n      }\r\n      case 'signup': {\r\n        return 'Create Account';\r\n      }\r\n      case 'task-assignment': {\r\n        return 'Assign Task';\r\n      }\r\n      case 'vehicle-details': {\r\n        return 'Vehicle Details';\r\n      }\r\n      default: {\r\n        return 'Modal';\r\n      }\r\n    }\r\n  }, [modalContent]);\r\n\r\n  /**\r\n   * Check if modal content is WorkHub-specific\r\n   */\r\n  const isWorkHubModal = useCallback(() => {\r\n    return [\r\n      'delegation-form',\r\n      'employee-profile',\r\n      'task-assignment',\r\n      'vehicle-details',\r\n    ].includes(modalContent || '');\r\n  }, [modalContent]);\r\n\r\n  return {\r\n    closeModal,\r\n    getModalAriaAttributes,\r\n    getModalClasses,\r\n\r\n    getModalTitle,\r\n    handleBackdropClick,\r\n    handleEscapeKey,\r\n    // Utilities\r\n    isModalOfTypeOpen,\r\n    // State\r\n    isModalOpen,\r\n    isWorkHubModal: isWorkHubModal(),\r\n    modalContent,\r\n    openDelegationFormModal,\r\n    openEmployeeProfileModal,\r\n\r\n    openLoginModal,\r\n    // Actions\r\n    openModal,\r\n    openSettingsModal,\r\n    openSignupModal,\r\n    openTaskAssignmentModal,\r\n    openVehicleDetailsModal,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAEA;;;;AAmBO,MAAM,WAAW;;IACtB,MAAM,cAAc,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;4CAAE,CAAA,QAAS,MAAM,WAAW;;IACzD,MAAM,eAAe,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;6CAAE,CAAA,QAAS,MAAM,YAAY;;IAC3D,MAAM,YAAY,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;0CAAE,CAAA,QAAS,MAAM,SAAS;;IACrD,MAAM,aAAa,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD;2CAAE,CAAA,QAAS,MAAM,UAAU;;IAEvD;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACjC,UAAU;QACZ;+CAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAClC,UAAU;QACZ;gDAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACpC,UAAU;QACZ;kDAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC1C,UAAU;QACZ;wDAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC1C,UAAU;QACZ;wDAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC1C,UAAU;QACZ;wDAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YAC3C,UAAU;QACZ;yDAAG;QAAC;KAAU;IAEd;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAClC,CAAC;YACC,OAAO,eAAe,iBAAiB;QACzC;kDACA;QAAC;QAAa;KAAa;IAG7B;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAClC,OAAO;gBACL,UAAU;gBACV,WAAW,cACP,4BACA;gBACJ,SAAS,CAAC,4BAA4B,EAAE,gBAAgB,WAAW;gBACnE,SAAS,cAAc,0BAA0B;YACnD;QACF;gDAAG;QAAC;QAAa;KAAa;IAE9B;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACzC,OAAO;gBACL,oBAAoB,eAChB,GAAG,aAAa,kBAAkB,CAAC,GACnC;gBACJ,eAAe,CAAC;gBAChB,mBAAmB,eACf,GAAG,aAAa,YAAY,CAAC,GAC7B;gBACJ,cAAc;gBACd,MAAM;YACR;QACF;uDAAG;QAAC;QAAa;KAAa;IAE9B;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAChC,CAAC;YACC,IAAI,MAAM,GAAG,KAAK,YAAY,aAAa;gBACzC;YACF;QACF;gDACA;QAAC;QAAa;KAAW;IAG3B;;GAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDACpC,CAAC;YACC,IAAI,MAAM,MAAM,KAAK,MAAM,aAAa,IAAI,aAAa;gBACvD;YACF;QACF;oDACA;QAAC;QAAa;KAAW;IAG3B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAChC,OAAQ;gBACN,KAAK;oBAAmB;wBACtB,OAAO;oBACT;gBACA,KAAK;oBAAoB;wBACvB,OAAO;oBACT;gBACA,KAAK;oBAAS;wBACZ,OAAO;oBACT;gBACA,KAAK;oBAAY;wBACf,OAAO;oBACT;gBACA,KAAK;oBAAU;wBACb,OAAO;oBACT;gBACA,KAAK;oBAAmB;wBACtB,OAAO;oBACT;gBACA,KAAK;oBAAmB;wBACtB,OAAO;oBACT;gBACA;oBAAS;wBACP,OAAO;oBACT;YACF;QACF;8CAAG;QAAC;KAAa;IAEjB;;GAEC,GACD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACjC,OAAO;gBACL;gBACA;gBACA;gBACA;aACD,CAAC,QAAQ,CAAC,gBAAgB;QAC7B;+CAAG;QAAC;KAAa;IAEjB,OAAO;QACL;QACA;QACA;QAEA;QACA;QACA;QACA,YAAY;QACZ;QACA,QAAQ;QACR;QACA,gBAAgB;QAChB;QACA;QACA;QAEA;QACA,UAAU;QACV;QACA;QACA;QACA;QACA;IACF;AACF;GA7La;;QACS,6IAAA,CAAA,aAAU;QACT,6IAAA,CAAA,aAAU;QACb,6IAAA,CAAA,aAAU;QACT,6IAAA,CAAA,aAAU", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/hooks/utils/use-toast.ts"], "sourcesContent": ["'use client';\r\n\r\n// Inspired by react-hot-toast library\r\nimport * as React from 'react';\r\n\r\nimport type { ToastActionElement, ToastProps } from '@/components/ui/toast';\r\n\r\nconst TOAST_LIMIT = 1;\r\nconst TOAST_REMOVE_DELAY = 1_000_000;\r\n\r\ntype ToasterToast = ToastProps & {\r\n  action?: ToastActionElement;\r\n  description?: React.ReactNode;\r\n  id: string;\r\n  title?: React.ReactNode;\r\n};\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: 'ADD_TOAST',\r\n  DISMISS_TOAST: 'DISMISS_TOAST',\r\n  REMOVE_TOAST: 'REMOVE_TOAST',\r\n  UPDATE_TOAST: 'UPDATE_TOAST',\r\n} as const;\r\n\r\nlet count = 0;\r\n\r\ntype Action =\r\n  | {\r\n      toast: Partial<ToasterToast>;\r\n      type: ActionType['UPDATE_TOAST'];\r\n    }\r\n  | {\r\n      toast: ToasterToast;\r\n      type: ActionType['ADD_TOAST'];\r\n    }\r\n  | {\r\n      toastId?: ToasterToast['id'];\r\n      type: ActionType['DISMISS_TOAST'];\r\n    }\r\n  | {\r\n      toastId?: ToasterToast['id'];\r\n      type: ActionType['REMOVE_TOAST'];\r\n    };\r\n\r\ntype ActionType = typeof actionTypes;\r\n\r\ninterface State {\r\n  toasts: ToasterToast[];\r\n}\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\r\n  return count.toString();\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return;\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId);\r\n    dispatch({\r\n      toastId: toastId,\r\n      type: 'REMOVE_TOAST',\r\n    });\r\n  }, TOAST_REMOVE_DELAY);\r\n\r\n  toastTimeouts.set(toastId, timeout);\r\n};\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case 'ADD_TOAST': {\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      };\r\n    }\r\n\r\n    case 'DISMISS_TOAST': {\r\n      const { toastId } = action;\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId);\r\n      } else {\r\n        for (const toast of state.toasts) {\r\n          addToRemoveQueue(toast.id);\r\n        }\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map(t =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      };\r\n    }\r\n\r\n    case 'REMOVE_TOAST': {\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        };\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter(t => t.id !== action.toastId),\r\n      };\r\n    }\r\n    case 'UPDATE_TOAST': {\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map(t =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      };\r\n    }\r\n  }\r\n};\r\n\r\nconst listeners: ((state: State) => void)[] = [];\r\n\r\nlet memoryState: State = { toasts: [] };\r\n\r\ntype Toast = Omit<ToasterToast, 'id'>;\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action);\r\n  for (const listener of listeners) {\r\n    listener(memoryState);\r\n  }\r\n}\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId();\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      toast: { ...props, id },\r\n      type: 'UPDATE_TOAST',\r\n    });\r\n  const dismiss = () => dispatch({ toastId: id, type: 'DISMISS_TOAST' });\r\n\r\n  dispatch({\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      onOpenChange: open => {\r\n        if (!open) dismiss();\r\n      },\r\n      open: true,\r\n    },\r\n    type: 'ADD_TOAST',\r\n  });\r\n\r\n  return {\r\n    dismiss,\r\n    id: id,\r\n    update,\r\n  };\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState);\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState);\r\n    return () => {\r\n      const index = listeners.indexOf(setState);\r\n      if (index !== -1) {\r\n        listeners.splice(index, 1);\r\n      }\r\n    };\r\n  }, [state]);\r\n\r\n  return {\r\n    ...state,\r\n    dismiss: (toastId?: string) =>\r\n      dispatch({\r\n        type: 'DISMISS_TOAST',\r\n        ...(toastId && { toastId }),\r\n      }),\r\n    toast,\r\n  };\r\n}\r\n\r\nexport { toast, useToast };\r\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;;AAHA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,eAAe;IACf,cAAc;IACd,cAAc;AAChB;AAEA,IAAI,QAAQ;AA0BZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AAEA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,SAAS;YACT,MAAM;QACR;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAa;gBAChB,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ;wBAAC,OAAO,KAAK;2BAAK,MAAM,MAAM;qBAAC,CAAC,KAAK,CAAC,GAAG;gBACnD;YACF;QAEA,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,KAAK,MAAM,SAAS,MAAM,MAAM,CAAE;wBAChC,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,IACvB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QAEA,KAAK;YAAgB;gBACnB,IAAI,OAAO,OAAO,KAAK,WAAW;oBAChC,OAAO;wBACL,GAAG,KAAK;wBACR,QAAQ,EAAE;oBACZ;gBACF;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;gBAC1D;YACF;QACA,KAAK;YAAgB;gBACnB,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,IACvB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO,KAAK;wBAAC,IAAI;gBAE3D;YACF;IACF;AACF;AAEA,MAAM,YAAwC,EAAE;AAEhD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAItC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,KAAK,MAAM,YAAY,UAAW;QAChC,SAAS;IACX;AACF;AAEA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;YACtB,MAAM;QACR;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,SAAS;YAAI,MAAM;QAAgB;IAEpE,SAAS;QACP,OAAO;YACL,GAAG,KAAK;YACR;YACA,cAAc,CAAA;gBACZ,IAAI,CAAC,MAAM;YACb;YACA,MAAM;QACR;QACA,MAAM;IACR;IAEA,OAAO;QACL;QACA,IAAI;QACJ;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,UAAU,CAAC,GAAG;wBAChB,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR,SAAS,CAAC,UACR,SAAS;gBACP,MAAM;gBACN,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;IACF;AACF;GAtBS", "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/app/layout-client.tsx"], "sourcesContent": ["/**\r\n * @file Client Layout Component with CSP Support\r\n * @module app/layout-client\r\n *\r\n * Client component containing the original layout logic with CSP nonce support.\r\n * Follows 2025 security standards for script loading and CSP compliance.\r\n */\r\n\r\n'use client';\r\n\r\nimport type { ReactNode } from 'react';\r\nimport { QueryClientProvider } from '@tanstack/react-query';\r\nimport { usePathname } from 'next/navigation';\r\nimport Script from 'next/script';\r\n\r\nimport { AuthProvider } from '@/contexts/AuthContext';\r\nimport { ProtectedRoute } from '@/components/auth';\r\nimport { AppLayout } from '@/components/layout/AppLayout';\r\nimport Navbar from '@/components/global/Navbar';\r\nimport { SettingsModal } from '@/components/settings/SettingsModal';\r\nimport { ThemeProvider } from '@/components/theme-provider';\r\nimport { NotificationDisplay } from '@/components/ui/NotificationDisplay';\r\nimport { Toaster } from '@/components/ui/toaster';\r\nimport { QuickAccessFab } from '@/components/ui/QuickAccessFab';\r\nimport { useUiPreferences } from '@/hooks/ui/useUiPreferences';\r\nimport { queryClient } from '@/lib/stores/queryClient';\r\nimport { useAuthContext } from '@/contexts/AuthContext';\r\nimport { prefetchUtils } from '@/lib/stores/queryClient';\r\nimport { SecurityConfigProvider } from '@/lib/api/security';\r\n// Debug components imports removed for cleaner UI\r\n// import { TokenRefreshDebug } from '@/components/debug/TokenRefreshDebug';\r\n// import { EnhancedCSPDebug } from '@/components/debug/EnhancedCSPDebug';\r\nimport {\r\n  useNonce,\r\n  initializeCSPViolationReporting,\r\n  useCSPReporting,\r\n} from '@/lib/security/CSPProvider';\r\nimport { useEffect, useMemo } from 'react';\r\n\r\ninterface ClientLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\n/**\r\n * Client Layout Component\r\n *\r\n * Contains all client-side layout logic with CSP nonce support.\r\n * Single responsibility: Client-side layout rendering and CSP integration.\r\n */\r\nexport default function ClientLayout({ children }: ClientLayoutProps) {\r\n  const nonce = useNonce();\r\n  const reportViolation = useCSPReporting();\r\n\r\n  // Initialize CSP violation reporting\r\n  useEffect(() => {\r\n    initializeCSPViolationReporting(reportViolation);\r\n  }, [reportViolation]);\r\n\r\n  return (\r\n    <ThemeProvider\r\n      attribute=\"class\"\r\n      defaultTheme=\"system\"\r\n      disableTransitionOnChange\r\n      enableSystem\r\n    >\r\n      <QueryClientProvider client={queryClient}>\r\n        <AuthProvider>\r\n          <SecurityConfigProvider>\r\n            <ProtectedLayoutWrapper>{children}</ProtectedLayoutWrapper>\r\n          </SecurityConfigProvider>\r\n        </AuthProvider>\r\n\r\n        {/* Notification display */}\r\n        <NotificationDisplay />\r\n\r\n        {/* Settings Modal */}\r\n        <SettingsModal />\r\n\r\n        {/* Quick Access FAB for Reporting Dashboard */}\r\n        <QuickAccessFab />\r\n\r\n        {/* Debug components hidden for cleaner UI */}\r\n        {/* <TokenRefreshDebug /> */}\r\n        {/* <EnhancedCSPDebug /> */}\r\n      </QueryClientProvider>\r\n    </ThemeProvider>\r\n  );\r\n}\r\n\r\n/**\r\n * Protected Layout Wrapper Component\r\n *\r\n * This component determines which routes require authentication.\r\n * Public routes (like auth-test) are excluded from protection.\r\n * Also handles initial data prefetching when authentication is ready.\r\n */\r\nfunction ProtectedLayoutWrapper({ children }: { children: ReactNode }) {\r\n  const pathname = usePathname();\r\n  const { isInitialized, loading, user } = useAuthContext();\r\n\r\n  // Get UI preferences from Zustand stores (but not theme to avoid hydration issues)\r\n  const { getFontSizeClass } = useUiPreferences();\r\n\r\n  // Define public routes that don't require authentication\r\n  const publicRoutes = ['/auth-test', '/supabase-diagnostics', '/login'];\r\n\r\n  // Define auth routes that should have no layout wrapper\r\n  const authRoutes = ['/login'];\r\n\r\n  // Check if current route is public\r\n  const isPublicRoute = publicRoutes.some(route => pathname?.startsWith(route));\r\n\r\n  // Check if current route is an auth route (no layout needed)\r\n  const isAuthRoute = authRoutes.some(route => pathname?.startsWith(route));\r\n\r\n  // Determine if authentication system is ready for API calls\r\n  // CRITICAL: Must include user check to prevent API calls before authentication\r\n  // MEMOIZED to prevent infinite re-renders caused by user object reference changes\r\n  const isAuthReady = useMemo(() => {\r\n    return isInitialized && !loading && !!user;\r\n  }, [isInitialized, loading, user?.id]); // Use user.id instead of user object to prevent reference issues\r\n\r\n  // Trigger initial data prefetching when authentication is ready and on dashboard\r\n  useEffect(() => {\r\n    if (isAuthReady && pathname === '/') {\r\n      console.log('Authentication ready, triggering dashboard data prefetch.');\r\n      prefetchUtils.prefetchDashboardData(isAuthReady).catch(error => {\r\n        console.warn('Failed to prefetch dashboard data:', error);\r\n      });\r\n    }\r\n  }, [isAuthReady, pathname]);\r\n\r\n  // If it's an auth route, render without any layout wrapper\r\n  if (isAuthRoute) {\r\n    return (\r\n      <>\r\n        {children}\r\n        <Toaster />\r\n      </>\r\n    );\r\n  }\r\n\r\n  // If it's a public route, render without protection but with layout\r\n  if (isPublicRoute) {\r\n    return (\r\n      <div className={`app-layout ${getFontSizeClass()}`}>\r\n        <AppLayout>{children}</AppLayout>\r\n        <Toaster />\r\n        <footer className=\"no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground\">\r\n          <p>&copy; {new Date().getFullYear()} WorkHub. All rights reserved.</p>\r\n        </footer>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // For all other routes, require authentication\r\n  return (\r\n    <div className={`app-layout ${getFontSizeClass()}`}>\r\n      <ProtectedRoute requireEmailVerification={true}>\r\n        <AppLayout>{children}</AppLayout>\r\n        <Toaster />\r\n        <footer className=\"no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground\">\r\n          <p>&copy; {new Date().getFullYear()} WorkHub. All rights reserved.</p>\r\n        </footer>\r\n      </ProtectedRoute>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AAKD;AACA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AACA,kDAAkD;AAClD,4EAA4E;AAC5E,0EAA0E;AAC1E;AAKA;;;AA7BA;;;;;;;;;;;;;;;;;;AAyCe,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,QAAQ,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,kBAAkB,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD;IAEtC,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,CAAA,GAAA,yIAAA,CAAA,kCAA+B,AAAD,EAAE;QAClC;iCAAG;QAAC;KAAgB;IAEpB,qBACE,6LAAC,0IAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAa;QACb,yBAAyB;QACzB,YAAY;kBAEZ,cAAA,6LAAC,yLAAA,CAAA,sBAAmB;YAAC,QAAQ,sIAAA,CAAA,cAAW;;8BACtC,6LAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,wKAAA,CAAA,yBAAsB;kCACrB,cAAA,6LAAC;sCAAwB;;;;;;;;;;;;;;;;8BAK7B,6LAAC,kJAAA,CAAA,sBAAmB;;;;;8BAGpB,6LAAC,kJAAA,CAAA,gBAAa;;;;;8BAGd,6LAAC,6IAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;AAQvB;GAtCwB;;QACR,yIAAA,CAAA,WAAQ;QACE,yIAAA,CAAA,kBAAe;;;KAFjB;AAwCxB;;;;;;CAMC,GACD,SAAS,uBAAuB,EAAE,QAAQ,EAA2B;;IACnE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEtD,mFAAmF;IACnF,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAE5C,yDAAyD;IACzD,MAAM,eAAe;QAAC;QAAc;QAAyB;KAAS;IAEtE,wDAAwD;IACxD,MAAM,aAAa;QAAC;KAAS;IAE7B,mCAAmC;IACnC,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,QAAS,UAAU,WAAW;IAEtE,6DAA6D;IAC7D,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAAS,UAAU,WAAW;IAElE,4DAA4D;IAC5D,+EAA+E;IAC/E,kFAAkF;IAClF,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE;YAC1B,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACxC;sDAAG;QAAC;QAAe;QAAS,MAAM;KAAG,GAAG,iEAAiE;IAEzG,iFAAiF;IACjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,eAAe,aAAa,KAAK;gBACnC,QAAQ,GAAG,CAAC;gBACZ,sIAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,aAAa,KAAK;wDAAC,CAAA;wBACrD,QAAQ,IAAI,CAAC,sCAAsC;oBACrD;;YACF;QACF;2CAAG;QAAC;QAAa;KAAS;IAE1B,2DAA2D;IAC3D,IAAI,aAAa;QACf,qBACE;;gBACG;8BACD,6LAAC,sIAAA,CAAA,UAAO;;;;;;;IAGd;IAEA,oEAAoE;IACpE,IAAI,eAAe;QACjB,qBACE,6LAAC;YAAI,WAAW,CAAC,WAAW,EAAE,oBAAoB;;8BAChD,6LAAC,4IAAA,CAAA,YAAS;8BAAE;;;;;;8BACZ,6LAAC,sIAAA,CAAA,UAAO;;;;;8BACR,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;;4BAAE;4BAAQ,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;IAI5C;IAEA,+CAA+C;IAC/C,qBACE,6LAAC;QAAI,WAAW,CAAC,WAAW,EAAE,oBAAoB;kBAChD,cAAA,6LAAC,+IAAA,CAAA,iBAAc;YAAC,0BAA0B;;8BACxC,6LAAC,4IAAA,CAAA,YAAS;8BAAE;;;;;;8BACZ,6LAAC,sIAAA,CAAA,UAAO;;;;;8BACR,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;;4BAAE;4BAAQ,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAK9C;IAvES;;QACU,qIAAA,CAAA,cAAW;QACa,kIAAA,CAAA,iBAAc;QAG1B,yIAAA,CAAA,mBAAgB;;;MALtC", "debugId": null}}]}