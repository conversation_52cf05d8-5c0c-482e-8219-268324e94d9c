(()=>{var e={};e.id=3015,e.ids=[3015],e.modules={997:(e,t,r)=>{"use strict";r.d(t,{k:()=>m});var s=r(60687),i=r(28946),a=r(11516),n=r(20620),o=r(36644);let l=(0,r(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var c=r(43210),d=r(68752),u=r(21342),h=r(3940),p=r(22482),x=r(22364);function m({className:e,csvData:t,enableCsv:r=!1,entityId:m,fileName:y,reportContentId:v,reportType:b,tableId:g}){let[f,j]=(0,c.useState)(!1),[N,k]=(0,c.useState)(!1),{showFormSuccess:C,showFormError:w}=(0,h.t6)(),P=async()=>{j(!0);try{let e=`/api/reports/${b}${m?`/${m}`:""}`,t=document.createElement("a");t.href=e,t.download=`${y}.pdf`,t.target="_blank",document.body.append(t),t.click(),t.remove(),C({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),w(`PDF download failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{j(!1)}},S=async()=>{if(r){k(!0);try{if(t?.data&&t.headers)(0,x.og)(t.data,t.headers,`${y}.csv`);else if(g){let e=(0,x.tL)(g);(0,x.og)(e.data,e.headers,`${y}.csv`)}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");C({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),w(`CSV generation failed: ${e.message||"Please try again."}`,{errorTitle:"Download Failed"})}finally{k(!1)}}},q=f||N;return(0,s.jsxs)("div",{className:(0,p.cn)("flex items-center gap-2 no-print",e),children:[(0,s.jsx)(d.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,s.jsx)(i.A,{className:"size-4"})}),(0,s.jsxs)(u.rI,{children:[(0,s.jsx)(u.ty,{asChild:!0,children:(0,s.jsx)(d.r,{actionType:"secondary","aria-label":"Download report",disabled:q,size:"icon",title:"Download Report",children:q?(0,s.jsx)(a.A,{className:"size-4 animate-spin"}):(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsxs)(u.SQ,{align:"end",children:[(0,s.jsxs)(u._2,{disabled:f,onClick:P,children:[f?(0,s.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(o.A,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download PDF"})]}),r&&(0,s.jsxs)(u._2,{disabled:N,onClick:S,children:[N?(0,s.jsx)(a.A,{className:"mr-2 size-4 animate-spin"}):(0,s.jsx)(l,{className:"mr-2 size-4"}),(0,s.jsx)("span",{children:"Download CSV"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20620:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},21820:e=>{"use strict";e.exports=require("os")},23307:(e,t,r)=>{Promise.resolve().then(r.bind(r,74939))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28946:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},54050:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var s=r(43210),i=r(65406),a=r(33465),n=r(35536),o=r(31212),l=class extends n.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#a(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#i(),this.#a()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#i(){let e=this.#r?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#a(e){a.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},c=r(8693);function d(e,t){let r=(0,c.jE)(t),[i]=s.useState(()=>new l(r,e));s.useEffect(()=>{i.setOptions(e)},[i,e]);let n=s.useSyncExternalStore(s.useCallback(e=>i.subscribe(a.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),d=s.useCallback((e,t)=>{i.mutate(e,t).catch(o.lQ)},[i]);if(n.error&&(0,o.GU)(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72273:(e,t,r)=>{"use strict";r.d(t,{NS:()=>x,T$:()=>d,W_:()=>u,Y1:()=>h,lR:()=>p});var s=r(8693),i=r(54050),a=r(46349),n=r(87676),o=r(48839),l=r(49603);let c={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,a.GK)([...c.all],async()=>(await l.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>(0,a.GK)([...c.detail(e)],()=>l.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(t?.enabled??!0),staleTime:3e5,...t}),h=()=>{let e=(0,s.jE)(),{showError:t,showSuccess:r}=(0,n.useNotifications)();return(0,i.n)({mutationFn:e=>{let t=o.M.toCreateRequest(e);return l.vehicleApiService.create(t)},onError:e=>{t(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:c.all}),r(`Vehicle "${t.licensePlate}" has been created successfully!`)}})},p=()=>{let e=(0,s.jE)(),{showError:t,showSuccess:r}=(0,n.useNotifications)();return(0,i.n)({mutationFn:({data:e,id:t})=>{let r=o.M.toUpdateRequest(e);return l.vehicleApiService.update(t,r)},onError:e=>{t(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:t=>{e.invalidateQueries({queryKey:c.all}),e.invalidateQueries({queryKey:c.detail(t.id)}),r(`Vehicle "${t.licensePlate}" has been updated successfully!`)}})},x=()=>{let e=(0,s.jE)(),{showError:t,showSuccess:r}=(0,n.useNotifications)();return(0,i.n)({mutationFn:e=>l.vehicleApiService.delete(e),onError:e=>{t(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(t,s)=>{e.invalidateQueries({queryKey:c.all}),e.removeQueries({queryKey:c.detail(s)}),r("Vehicle has been deleted successfully!")}})}},74075:e=>{"use strict";e.exports=require("zlib")},74939:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\vehicles\\\\[id]\\\\report\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\page.tsx","default")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},82196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>i});var s=r(37413);let i={title:"Vehicle Report"};function a({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},83997:e=>{"use strict";e.exports=require("tty")},86529:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687),i=r(44610),a=r(30474),n=r(85814),o=r.n(n),l=r(16189);r(43210);var c=r(997),d=r(68752),u=r(52027),h=r(72273),p=r(48184);function x(){let e=(0,l.useParams)(),t=e?.id,{data:r,error:n,isLoading:x,refetch:m}=(0,h.W_)(Number(t),{enabled:!!t});return(0,s.jsx)("div",{className:"mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4",children:(0,s.jsx)(u.gO,{data:r,emptyComponent:(0,s.jsx)("div",{className:"py-10 text-center",children:"Vehicle not found or could not be loaded."}),error:(0,p.u1)(n),isLoading:x,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-center text-3xl font-bold text-gray-800",children:"Loading Report..."}),(0,s.jsx)(u.jt,{count:1,variant:"card"}),(0,s.jsx)(u.jt,{className:"mt-6",count:3,variant:"table"})]}),onRetry:()=>{m()},children:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"no-print mb-4 flex items-center justify-between",children:[(0,s.jsx)(d.r,{actionType:"tertiary",asChild:!0,icon:(0,s.jsx)(i.A,{className:"size-4"}),children:(0,s.jsx)(o(),{href:`/vehicles/${t}/report/service-history`,children:"Detailed Service History"})}),(0,s.jsx)(c.k,{enableCsv:(e.serviceHistory?.length||0)>0,entityId:t,fileName:`vehicle-report-${e.make}-${e.model}`,reportContentId:"#vehicle-report-content",reportType:"vehicle",tableId:"#service-history-table"})]}),(0,s.jsxs)("div",{className:"report-content",id:"vehicle-report-content",children:[(0,s.jsxs)("header",{className:"report-header mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Maintenance Report"}),(0,s.jsxs)("p",{className:"text-xl text-gray-600",children:[e.make," ",e.model," (",e.year,")"]}),e.licensePlate&&(0,s.jsxs)("p",{className:"text-md text-gray-500",children:["Plate: ",e.licensePlate]})]}),(0,s.jsxs)("section",{className:"card-print mb-8 rounded border border-gray-200 p-4",children:[(0,s.jsx)("h2",{className:"mb-4 border-b border-gray-200 pb-2 text-2xl font-semibold text-gray-700",children:"Vehicle Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Make:"})," ",e.make]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Model:"})," ",e.model]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Year:"})," ",e.year]}),e.licensePlate&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Plate Number:"})," ",e.licensePlate]}),e.color&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Color:"})," ",e.color]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Initial Odometer:"})," ",null!==e.initialOdometer&&void 0!==e.initialOdometer?`${e.initialOdometer.toLocaleString()} miles`:"Not recorded"]})]}),e.imageUrl&&(0,s.jsx)("div",{className:"no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded",children:(0,s.jsx)(a.default,{alt:`${e.make} ${e.model}`,"data-ai-hint":"car side",layout:"fill",objectFit:"contain",src:e.imageUrl})})]}),(0,s.jsxs)("section",{className:"card-print rounded border border-gray-200 p-4",children:[(0,s.jsxs)("div",{className:"mb-4 flex items-center justify-between border-b border-gray-200 pb-2",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700",children:"Service History"}),(0,s.jsx)("p",{className:"no-print text-sm text-gray-500",children:(0,s.jsx)(o(),{className:"text-blue-600 hover:underline",href:`/vehicles/${t}/report/service-history`,children:"View detailed service history"})})]}),e.serviceHistory?.length===0?(0,s.jsx)("p",{className:"text-gray-500",children:"No service records available for this vehicle."}):(0,s.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"service-history-table",children:[(0,s.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Date"}),(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Odometer"}),(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Service Performed"}),(0,s.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Notes"}),(0,s.jsx)("th",{className:"px-3 py-2 text-right",scope:"col",children:"Cost"})]})}),(0,s.jsx)("tbody",{children:e.serviceHistory?.map(e=>(0,s.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-3 py-2",children:new Date(e.date).toLocaleDateString()}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.odometer.toLocaleString()}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.servicePerformed.join(", ")}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"}),(0,s.jsx)("td",{className:"px-3 py-2 text-right",children:e.cost?`$${Number(e.cost).toFixed(2)}`:"-"})]},e.id))})]})]}),(0,s.jsxs)("footer",{className:"report-footer mt-12 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,s.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,s.jsx)("p",{children:"WorkHub"})]})]})]})})})}},87676:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useNotifications:()=>a,useWorkHubNotifications:()=>n});var s=r(43210),i=r(94538);let a=()=>{let e=(0,i.C)(e=>e.addNotification),t=(0,i.C)(e=>e.removeNotification),r=(0,i.C)(e=>e.clearAllNotifications),a=(0,i.C)(e=>e.unreadNotificationCount),n=(0,s.useCallback)(t=>{e({message:t,type:"success"})},[e]),o=(0,s.useCallback)(t=>{e({message:t,type:"error"})},[e]),l=(0,s.useCallback)(t=>{e({message:t,type:"warning"})},[e]),c=(0,s.useCallback)(t=>{e({message:t,type:"info"})},[e]),d=(0,s.useCallback)((e,t,r)=>{e?n(t):o(r)},[n,o]),u=(0,s.useCallback)((r,s,a=5e3)=>{e({message:s,type:r}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===s&&t(e.id)},a)},[e,t]),h=(0,s.useCallback)((t="Loading...")=>{e({message:t,type:"info"});let r=i.C.getState().notifications;return r.at(-1)?.id},[e]),p=(0,s.useCallback)((e,r,s)=>{t(e),r?n(s):o(s)},[t,n,o]);return{clearAllNotifications:r,removeNotification:t,showApiResult:d,showError:o,showInfo:c,showLoading:h,showSuccess:n,showTemporary:u,showWarning:l,unreadCount:a,updateLoadingNotification:p}},n=()=>{let{clearAllNotifications:e,removeNotification:t,showError:r,showInfo:n,showSuccess:o,showWarning:l,unreadCount:c}=a(),d=(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),h=(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:d,showEmployeeUpdate:(0,s.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:r,showInfo:n,showSuccess:o,showTaskAssigned:h,showVehicleMaintenance:u,showWarning:l,unreadCount:c}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95820:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["vehicles",{children:["[id]",{children:["report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74939)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,82196)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\WorkHub\\frontend\\src\\app\\vehicles\\[id]\\report\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/vehicles/[id]/report/page",pathname:"/vehicles/[id]/report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96487:()=>{},99331:(e,t,r)=>{Promise.resolve().then(r.bind(r,86529))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,211,1658,8390,474,101,7055,5782],()=>r(95820));module.exports=s})();