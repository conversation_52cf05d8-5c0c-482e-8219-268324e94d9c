/**
 * @file Centralized exports for the API service layer.
 * @module api
 */

// Core infrastructure
export * from './core/apiClient';
export * from './core/baseApiService';
export * from './core/errors';
export * from './core/types';
export * from './core/interfaces';

// Create and export a configured API client instance
import { ApiClient } from './core/apiClient';
import { getEnvironmentConfig } from '../config/environment';

/**
 * Unified Authentication Token Provider
 * Single source of truth for authentication tokens across the entire application
 */
let unifiedAuthTokenProvider: (() => string | null) | null = null;

/**
 * Set the unified authentication token provider
 * This should ONLY be called by the AuthContext
 * Replaces both setGlobalAuthTokenProvider and setFactoryAuthTokenProvider
 */
export function setUnifiedAuthTokenProvider(
  provider: () => string | null
): void {
  unifiedAuthTokenProvider = provider;

  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Unified Auth Token Provider initialized');
  }
}

/**
 * Get the current authentication token from the unified provider
 * This is used by ALL API clients throughout the application
 */
function getAuthToken(): string | null {
  if (!unifiedAuthTokenProvider) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Unified Auth Token Provider not initialized');
    }
    return null;
  }

  try {
    return unifiedAuthTokenProvider();
  } catch (error) {
    console.error('❌ Error getting auth token from unified provider:', error);
    return null;
  }
}

/**
 * Get the unified token provider function (for debugging/testing)
 */
export function getUnifiedAuthTokenProvider(): (() => string | null) | null {
  return unifiedAuthTokenProvider;
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use setUnifiedAuthTokenProvider instead
 */
export function setGlobalAuthTokenProvider(
  provider: () => string | null
): void {
  console.warn(
    '⚠️ setGlobalAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider instead.'
  );
  setUnifiedAuthTokenProvider(provider);
}

/**
 * Legacy compatibility - maintains backward compatibility
 * @deprecated Use getUnifiedAuthTokenProvider instead
 */
export function getGlobalAuthTokenProvider(): (() => string | null) | null {
  console.warn(
    '⚠️ getGlobalAuthTokenProvider is deprecated. Use getUnifiedAuthTokenProvider instead.'
  );
  return getUnifiedAuthTokenProvider();
}

// Get environment-aware configuration
const envConfig = getEnvironmentConfig();

export const apiClient = new ApiClient({
  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration
  getAuthToken, // Provide the auth token getter
  headers: {
    'Content-Type': 'application/json',
  },
  retryAttempts: 3,
  timeout: 10_000,
});

// Security architecture (selective exports to avoid conflicts)
export {
  // Security hooks
  useSecureApiClient,
  useSecureApiReplacement,
  useSecureHttpClient,
  useCSRFProtection,
  useInputValidation,
  useSecurityMonitoring,
  useSessionSecurity,
  useTokenManagement,
  // Security providers
  SecurityConfigProvider,
  useSecurityConfig,
  useSecurityConfigValue,
  // Security composer
  SecurityComposer,
  createSecurityComposer,
  // Secure API client
  SecureApiClient,
  createSecureApiClient,
} from './security';

export type {
  // Security types (avoid RequestConfig conflict)
  UseSecureApiClientReturn,
  SecureApiRequestConfig,
  UseSecureHttpClientReturn,
  SecurityFeatures,
  SecureApiClientConfig,
} from './security';

// Domain-specific API services
export * from './services/domain/delegationApi';
export * from './services/domain/employeeApi';
export * from './services/domain/taskApi';
export * from './services/domain/vehicleApi';

// External API services
export * from './services/external/flightApi';
export * from './services/external/flightDetailsApi';
