'use client';

import type { Session, User as SupabaseUser } from '@supabase/supabase-js';
import type { ReactNode } from 'react';

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'; // Import useRef, useCallback, useState

// Remove circular dependency - auth logic will be moved here
import { setSecureAuthTokenProvider } from '../lib/api';
import { SecurityUtils, SessionManager } from '../lib/security';
import { getTokenRefreshService } from '../lib/services/TokenRefreshService';
import { supabase } from '../lib/supabase';

// Define a key for cross-tab logout events
const LOGOUT_EVENT_KEY = 'workhub-logout-event';

// Define AuthContextType interface directly
interface AuthContextType {
  clearError: () => void;
  // Auth state
  error: null | string;
  isInitialized: boolean;
  loading: boolean;
  session: null | { access_token?: string; user?: null | User };
  // Auth actions
  signIn: (email: string, password: string) => Promise<{ error?: string }>;

  signOut: () => Promise<void>;
  user: null | User;
  userRole: null | string;
}

// Define comprehensive user interface that matches Supabase User exactly
interface User {
  app_metadata?: any;
  created_at?: string | undefined;
  email?: string | undefined;
  email_confirmed_at?: string | undefined;
  id: string;
  is_anonymous?: boolean | undefined;
  is_sso_user?: boolean;
  last_sign_in_at?: string | undefined;
  updated_at?: string | undefined;
  user_metadata?: any;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication Context Provider
 *
 * This provider wraps the application and provides authentication state
 * and methods to all child components with integrated auth logic.
 */
export function AuthProvider({ children }: AuthProviderProps) {
  // State management for authentication
  const [session, setSession] = useState<null | Session>(null);
  const [user, setUser] = useState<null | User>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<null | string>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Helper function to transform Supabase user to our User interface
  const transformUser = (supabaseUser: SupabaseUser): User => ({
    app_metadata: supabaseUser.app_metadata,
    created_at: supabaseUser.created_at,
    email: supabaseUser.email,
    email_confirmed_at: supabaseUser.email_confirmed_at,
    id: supabaseUser.id,
    is_anonymous: supabaseUser.is_anonymous,
    is_sso_user: supabaseUser.app_metadata?.provider !== 'email',
    last_sign_in_at: supabaseUser.last_sign_in_at,
    updated_at: supabaseUser.updated_at,
    user_metadata: supabaseUser.user_metadata,
  });

  // Initialize Supabase auth state
  useEffect(() => {
    let mounted = true;

    // Get initial session with circuit breaker integration
    const initializeAuth = async () => {
      // Initialize circuit breaker first
      SecurityUtils.initializeCircuitBreaker();

      // Circuit breaker check for auth initialization
      if (!SecurityUtils.canPerformSecurityCheck()) {
        console.debug('🔒 Auth initialization blocked by circuit breaker');
        if (mounted) {
          setLoading(false);
          setIsInitialized(true);
          setError('Authentication system temporarily unavailable');
        }
        return;
      }

      const operationId = 'auth-initialization';
      if (!SecurityUtils.startSecurityOperation(operationId)) {
        console.debug('🔄 Auth initialization already in progress');
        return;
      }

      try {
        // First, get the initial session without integrity check to avoid race condition
        const {
          data: { session: initialSession },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting initial session:', error);
          SecurityUtils.recordSecurityAttempt();
          setError(error.message);
        } else if (mounted) {
          console.log('✅ Auth initialization successful');
          SecurityUtils.recordSecuritySuccess();

          setSession(initialSession);
          setUser(
            initialSession?.user ? transformUser(initialSession.user) : null
          );

          // Update session manager with successful authentication
          if (initialSession) {
            SessionManager.updateActivity();

            // Perform session integrity check AFTER authentication is established
            setTimeout(async () => {
              try {
                const integrityCheck =
                  await SessionManager.performIntegrityCheck();
                if (integrityCheck) {
                  console.log(
                    '✅ Session integrity check passed after auth initialization'
                  );
                } else {
                  console.log(
                    '📊 Session integrity check failed - automatic recovery will handle this'
                  );
                  const recovered = SessionManager.recoverFromCorruptedState();
                  if (!recovered) {
                    console.warn(
                      '⚠️ Session recovery completed with warnings after auth initialization'
                    );
                  }
                }
              } catch (error) {
                console.warn('Session integrity check error:', error);
                // Don't treat this as a critical error during initialization
              }
            }, 1000); // Increased delay to allow cookies to be properly set
          }
        }
      } catch (error_) {
        console.error('Error initializing auth:', error_);
        SecurityUtils.recordSecurityAttempt();
        if (mounted) {
          setError('Failed to initialize authentication');
        }
      } finally {
        SecurityUtils.endSecurityOperation(operationId);
        if (mounted) {
          setLoading(false);
          setIsInitialized(true);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      console.log('Auth state changed:', event, session?.user?.email);

      setSession(session);
      setUser(session?.user ? transformUser(session.user) : null);
      setLoading(false);
      setIsInitialized(true);

      if (event === 'SIGNED_OUT') {
        setError(null);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Get user role from metadata
  const getUserRole = (user: null | User): null | string => {
    if (!user) return null;
    return user.user_metadata?.role || user.app_metadata?.role || 'USER';
  };

  // Auth actions
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: signInError } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      if (signInError) {
        setError(signInError.message);
        return { error: signInError.message };
      }

      return {};
    } catch (error_) {
      const errorMessage =
        error_ instanceof Error
          ? error_.message
          : 'An unexpected error occurred';
      setError(errorMessage);
      return { error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    // Circuit breaker check for sign out
    if (!SecurityUtils.canPerformSecurityCheck()) {
      console.debug('🔒 Sign out blocked by circuit breaker');
      return;
    }

    const operationId = 'auth-signout';
    if (!SecurityUtils.startSecurityOperation(operationId)) {
      console.debug('🔄 Sign out already in progress');
      return;
    }

    try {
      setLoading(true);
      console.log('🔐 Starting sign out process...');

      // Clear session state first
      SessionManager.clearSessionState();

      // Clear all secure storage
      SecurityUtils.clearAllCookies();

      const { error: signOutError } = await supabase.auth.signOut();
      if (signOutError) {
        console.error('❌ Supabase sign out error:', signOutError);
        SecurityUtils.recordSecurityAttempt();
        setError(signOutError.message);
      } else {
        console.log('✅ Sign out successful');
        SecurityUtils.recordSecuritySuccess();

        // Clear any remaining authentication state
        setSession(null);
        setUser(null);
        setError(null);
      }
    } catch (error_) {
      console.error('Sign out error:', error_);
      SecurityUtils.recordSecurityAttempt();
      setError('Sign out failed');
    } finally {
      SecurityUtils.endSecurityOperation(operationId);
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  // Create auth object
  // SECURITY NOTE: HttpOnly Cookie Compliance
  // The access_token exposed here is for client-side validation and header construction only.
  // Actual authentication relies on HttpOnly cookies set by the backend.
  // This token should NOT be stored in localStorage or used for direct API authentication.
  const auth: AuthContextType = {
    clearError,
    error,
    isInitialized,
    loading,
    session: session
      ? {
          // SECURITY: Token exposed for compatibility but HttpOnly cookies are primary auth method
          access_token: session.access_token,
          user: user ?? null,
        }
      : null,
    signIn,
    signOut,
    user,
    userRole: getUserRole(user),
  };
  const isLoggingOutRef = useRef(false);
  const tokenRefreshService = getTokenRefreshService();

  // Set up secure auth token provider for ALL API clients
  useEffect(() => {
    const getSecureToken = () => auth.session?.access_token || null;

    // Set the SINGLE secure token provider for the entire application
    // This replaces legacy setGlobalAuthTokenProvider and setFactoryAuthTokenProvider
    setSecureAuthTokenProvider(getSecureToken);

    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 AuthContext: Secure token provider updated', {
        hasToken: !!auth.session?.access_token,
        tokenLength: auth.session?.access_token?.length || 0,
      });
    }
  }, [auth.session?.access_token]);

  // Handle cross-tab logout with circuit breaker protection
  const handleStorageChange = useCallback(
    (event: StorageEvent) => {
      if (event.key === LOGOUT_EVENT_KEY && event.newValue === 'true') {
        console.log('🔐 Cross-tab logout detected. Signing out...');

        // Circuit breaker check for cross-tab logout
        if (!SecurityUtils.canPerformSecurityCheck()) {
          console.debug('🔒 Cross-tab logout blocked by circuit breaker');
          return;
        }

        if (!isLoggingOutRef.current) {
          isLoggingOutRef.current = true;

          const operationId = 'cross-tab-logout';
          if (SecurityUtils.startSecurityOperation(operationId)) {
            auth.signOut().finally(() => {
              SecurityUtils.endSecurityOperation(operationId);
              isLoggingOutRef.current = false;
              // Clear the event key to allow future logout events
              localStorage.removeItem(LOGOUT_EVENT_KEY);
            });
          } else {
            console.debug('🔄 Cross-tab logout already in progress');
            isLoggingOutRef.current = false;
          }
        }
      }
    },
    [auth.signOut]
  );

  // Handle critical token refresh failures with circuit breaker protection
  useEffect(() => {
    const handleCriticalRefreshFailed = () => {
      console.warn(
        '🔐 Critical token refresh failure detected, signing out user'
      );

      // Circuit breaker check for critical refresh failure
      if (!SecurityUtils.canPerformSecurityCheck()) {
        console.debug(
          '🔒 Critical refresh failure handling blocked by circuit breaker'
        );
        return;
      }

      if (!isLoggingOutRef.current) {
        isLoggingOutRef.current = true;

        const operationId = 'critical-refresh-failure';
        if (SecurityUtils.startSecurityOperation(operationId)) {
          SecurityUtils.recordSecurityAttempt();
          auth.signOut().finally(() => {
            SecurityUtils.endSecurityOperation(operationId);
            isLoggingOutRef.current = false;
          });
        } else {
          console.debug(
            '🔄 Critical refresh failure handling already in progress'
          );
          isLoggingOutRef.current = false;
        }
      }
    };

    tokenRefreshService.subscribe(event => {
      if (event === 'critical_refresh_failed') {
        handleCriticalRefreshFailed();
      }
    });

    return () => {
      // No direct unsubscribe method on the service, but the service is a singleton
      // and callbacks are managed by the Set. For a more robust solution,
      // TokenRefreshService would need an unsubscribe method that takes the specific callback.
      // For now, relying on the singleton nature and the fact that AuthContext is long-lived.
    };
  }, [auth.signOut, tokenRefreshService]);

  // Phase 3: Token management now handled by TokenManager - no manual sync needed
  useEffect(() => {
    const token = auth.session?.access_token || null;

    globalThis.addEventListener('storage', handleStorageChange);

    // When this tab logs out, signal other tabs
    if (!auth.session && !auth.loading && !isLoggingOutRef.current) {
      // Only set if not already logging out to prevent loop
      localStorage.setItem(LOGOUT_EVENT_KEY, 'true');
    } else if (
      auth.session &&
      localStorage.getItem(LOGOUT_EVENT_KEY) === 'true'
    ) {
      // If a session exists but a logout signal is present, clear the signal
      // This handles cases where a tab might have been open during a logout,
      // then refreshed or re-authenticated.
      localStorage.removeItem(LOGOUT_EVENT_KEY);
    }

    // Development-only authentication debugging
    if (process.env.NODE_ENV !== 'production') {
      console.debug('🔐 AuthProvider: Token sync signal sent', {
        authLoading: auth.loading,
        tokenAvailable: !!token,
        userEmail: auth.user?.email,
      });

      if (token && auth.user) {
        console.debug(
          '✅ Authentication ready for API calls (via httpOnly cookies)',
          {
            hasToken: true,
            userEmail: auth.user.email,
          }
        );
      } else if (!auth.loading) {
        console.warn(
          '⚠️ No token available client-side for direct access (expected for httpOnly cookies)',
          {
            hasSession: !!auth.session,
            hasUser: !!auth.user,
            loading: auth.loading,
          }
        );
      }
    }
  }, [
    auth.session?.access_token,
    auth.user,
    auth.loading,
    handleStorageChange,
  ]); // Add handleStorageChange to dependencies

  useEffect(() => {
    return () => {
      globalThis.removeEventListener('storage', handleStorageChange);
    };
  }, [handleStorageChange]); // Depend on handleStorageChange

  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>;
}

/**
 * Hook to use the authentication context
 *
 * @throws {Error} If used outside of AuthProvider
 */
export function useAuthContext(): AuthContextType {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }

  return context;
}

// Export the context for advanced use cases
export { AuthContext };
