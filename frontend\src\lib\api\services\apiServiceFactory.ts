/**
 * @file API Service Factory - Backward Compatibility Export
 * @module api/services/apiServiceFactory
 *
 * This file provides backward compatibility for imports that expect
 * apiServiceFactory.ts instead of factory.ts
 */

// Re-export everything from the factory module
export * from './factory';

// Ensure all the commonly used exports are available
export {
  ApiServiceFactory,
  apiServiceFactory,
  setFactoryAuthTokenProvider, // Legacy compatibility - deprecated
  vehicleApiService,
  delegationApiService,
  taskApiService,
  employeeApiService,
  reliabilityApiService,
} from './factory';

// Re-export unified auth provider for convenience
export { setUnifiedAuthTokenProvider } from '../index';

export type { ApiServiceFactoryConfig } from './factory';
