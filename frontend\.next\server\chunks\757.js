"use strict";exports.id=757,exports.ids=[757],exports.modules={80757:(e,t,a)=>{a.d(t,{aN:()=>d,adminService:()=>h});var i=a(77312),r=a(49603);let s={fromApi:e=>({action:e.action||"",details:e.details||"",id:e.id||"",timestamp:new Date(e.created_at||e.timestamp||new Date),userId:e.user_id||e.userId||e.auth_user_id||"",auth_user_id:e.auth_user_id||"",auth_user:e.auth_user||null}),toApi:e=>e};class n extends i.v{constructor(e,t){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/admin/audit-logs",this.transformer=s}async getByAction(e,t){return(await this.getAll({...t,action:e})).data}async getByDateRange(e,t,a){let i={endDate:t.toISOString(),startDate:e.toISOString(),...a};return this.getAll(i)}async getByUserId(e,t){return(await this.getAll({...t,userId:e})).data}}let c={fromApi:e=>{let t=e.users?.[0]||{};return{created_at:e.created_at||"",email:t.email||e.email||"",email_confirmed_at:t.email_confirmed_at||e.email_confirmed_at||null,employee_id:e.employee_id||null,full_name:e.full_name||e.name||"",id:e.id,isActive:e.is_active??!0,last_sign_in_at:e.last_sign_in_at||null,phone:e.phone||null,phone_confirmed_at:e.phone_confirmed_at||null,role:e.role||"USER",updated_at:e.updated_at||"",users:e.users}},toApi:e=>e};class l extends i.v{constructor(e,t){super(e,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/admin/users",this.transformer=c}async getAll(e={}){let t=new URLSearchParams(e).toString(),a=`${this.endpoint}?${t}`,i=`${this.endpoint}:getAll:${JSON.stringify(e)}`;return this.executeWithInfrastructure(i,async()=>{let t=await this.apiClient.get(a),i=t.data?.data||[],r=t.data?.pagination||{hasNext:!1,hasPrevious:!1,limit:e.limit||10,page:1,total:0,totalPages:0};return{data:i.map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e),pagination:r}})}async getUsersByRole(e,t={}){return(await this.getAll({...t,role:e})).data}async toggleActivation(e,t){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch(`${this.endpoint}/${e}/toggle-activation`,{isActive:t});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),a})}}let u={fromApi:e=>e,toApi:e=>e};class o extends i.v{get cacheUtils(){return{clearAll:()=>this.clearCache(),forceRefreshHealth:async()=>(this.cache.invalidate("admin:health"),this.getSystemHealthStatus()),forceRefreshPerformance:async()=>(this.cache.invalidate("admin:performance"),this.getPerformanceMetrics()),getStats:()=>this.cache.getStats(),invalidateAll:()=>this.cache.invalidatePattern(/^admin:/)}}constructor(e,t){let a=e||r.cl.getApiClient();super(a,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...t}),this.endpoint="/admin",this.transformer=u,this.auditService=new n(a),this.userService=new l(a)}async createAuditLog(e){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.post("/admin/audit-logs",e);return this.cache.invalidatePattern(/^admin:audit:/),t})}async createUser(e){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.post("/admin/users",e);return this.cache.invalidatePattern(/^admin:users:/),t})}async deleteUser(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`/admin/users/${e}`),this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${e}`)})}async getAllUsers(e={}){return this.userService.getAll(e)}async getAuditLogs(e={}){return this.auditService.getAll(e)}getMockHealthStatus(){return{services:{api:{responseTime:23,status:"healthy"},cache:{responseTime:12,status:"healthy"},database:{responseTime:45,status:"healthy"}},status:"healthy",timestamp:new Date().toISOString(),uptime:3600}}getMockPerformanceMetrics(){return{cpu:{cores:4,usage:100*Math.random()},errors:{rate:5*Math.random(),total:Math.floor(500*Math.random())},memory:{percentage:100*Math.random(),total:8e3,used:8e3*Math.random()},requests:{averageResponseTime:1e3*Math.random(),perSecond:100*Math.random(),total:Math.floor(1e4*Math.random())},timestamp:new Date().toISOString()}}getMockRecentErrors(){let e=[{details:{component:"database",errorType:"timeout"},id:"1",level:"ERROR",message:"Database connection timeout",requestId:"req-456",source:"database.service.ts",stack:"Error: Connection timeout\n    at Database.connect...",timestamp:new Date().toISOString(),userId:"user123"},{details:{component:"system",metric:"memory"},id:"2",level:"WARNING",message:"High memory usage detected",requestId:"req-789",source:"monitoring.service.ts",timestamp:new Date(Date.now()-3e5).toISOString()}];return{data:e,pagination:{hasNext:!1,hasPrevious:!1,limit:10,page:1,total:e.length,totalPages:Math.ceil(e.length/10)}}}async getPerformanceMetrics(){return this.executeWithInfrastructure("admin:performance",async()=>this.apiClient.get("/admin/performance"))}async getRecentErrors(e=1,t=10,a){let i=new URLSearchParams;i.append("page",e.toString()),i.append("limit",t.toString()),a&&i.append("level",a);let r=`/admin/logs/errors?${i.toString()}`,s=`admin:errors:${e}:${t}:${a||"all"}`;return this.executeWithInfrastructure(s,async()=>this.apiClient.get(r))}async getSystemHealthStatus(){return this.executeWithInfrastructure("admin:health",async()=>this.apiClient.get("/admin/health"))}async toggleUserActivation(e,t){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch(`/admin/users/${e}/toggle-activation`,{isActive:t});return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${e}`),a})}async updateUser(e,t){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.put(`/admin/users/${e}`,t);return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${e}`),a})}}let h=new o,d=h.cacheUtils}};