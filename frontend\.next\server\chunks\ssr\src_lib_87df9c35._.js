module.exports = {

"[project]/src/lib/security/cspConfig.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * PHASE 3 SECURITY HARDENING: Enhanced CSP Configuration - 2025 Best Practices
 *
 * Implements strict Content Security Policy with:
 * - Nonce-based script execution
 * - strict-dynamic for trusted scripts
 * - Comprehensive security directives
 * - CSP violation reporting
 * - Supply chain security for third-party scripts
 * - Enhanced monitoring and alerting
 */ __turbopack_context__.s({
    "TRUSTED_SCRIPTS": (()=>TRUSTED_SCRIPTS),
    "analyzeCSPViolationRisk": (()=>analyzeCSPViolationRisk),
    "generateCSPReportingConfig": (()=>generateCSPReportingConfig),
    "generateSRIPolicy": (()=>generateSRIPolicy),
    "generateSecureNonce": (()=>generateSecureNonce),
    "generateSecurityHeaders": (()=>generateSecurityHeaders),
    "generateStrictCSP": (()=>generateStrictCSP),
    "validateNonce": (()=>validateNonce),
    "validateScriptIntegrity": (()=>validateScriptIntegrity)
});
function generateStrictCSP(config) {
    const { nonce, reportUri, isDevelopment } = config;
    // Base strict CSP directives
    const directives = {
        // Script sources - strict nonce-based approach
        'script-src': [
            "'self'",
            `'nonce-${nonce}'`,
            "'strict-dynamic'",
            // Allow specific trusted domains for production
            ...isDevelopment ? [] : [
                'https://cdn.jsdelivr.net',
                'https://unpkg.com'
            ]
        ],
        // Style sources - nonce-based with fallbacks
        'style-src': [
            "'self'",
            `'nonce-${nonce}'`,
            // Allow inline styles for CSS-in-JS libraries in development
            ...isDevelopment ? [
                "'unsafe-inline'"
            ] : [],
            // Trusted style CDNs
            'https://fonts.googleapis.com',
            'https://cdn.jsdelivr.net'
        ],
        // Font sources
        'font-src': [
            "'self'",
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'data:'
        ],
        // Image sources
        'img-src': [
            "'self'",
            'data:',
            'blob:',
            'https:',
            // Supabase storage
            'https://*.supabase.co',
            // Common image CDNs
            'https://images.unsplash.com',
            'https://via.placeholder.com'
        ],
        // Connect sources for API calls
        'connect-src': [
            "'self'",
            // Supabase endpoints
            'https://*.supabase.co',
            'wss://*.supabase.co',
            // Development WebSocket
            ...isDevelopment ? [
                'ws://localhost:*',
                'wss://localhost:*'
            ] : [],
            // Backend API endpoints (for development and Docker environments)
            ...isDevelopment || ("TURBOPACK compile-time value", "false") === 'true' ? [
                'http://localhost:3001',
                'http://backend:3001'
            ] : [],
            // Additional connect sources from environment variable
            ...("TURBOPACK compile-time truthy", 1) ? ("TURBOPACK compile-time value", "http://localhost:3001").split(',').map((s)=>s.trim()).filter(Boolean) : ("TURBOPACK unreachable", undefined),
            // API endpoints
            'https://api.github.com'
        ],
        // Frame sources
        'frame-src': [
            "'self'",
            // Allow specific trusted frames
            'https://www.youtube.com',
            'https://player.vimeo.com'
        ],
        // Object and embed restrictions
        'object-src': [
            "'none'"
        ],
        'embed-src': [
            "'none'"
        ],
        // Base URI restriction
        'base-uri': [
            "'self'"
        ],
        // Form action restriction
        'form-action': [
            "'self'"
        ],
        // Frame ancestors (clickjacking protection)
        'frame-ancestors': [
            "'none'"
        ],
        // Block mixed content
        'block-all-mixed-content': [],
        // Default fallback
        'default-src': [
            "'self'"
        ]
    };
    // Add upgrade insecure requests in production
    if (!isDevelopment) {
        directives['upgrade-insecure-requests'] = [];
    }
    // Add reporting if configured
    if (reportUri) {
        directives['report-uri'] = [
            reportUri
        ];
        directives['report-to'] = [
            'csp-endpoint'
        ];
    }
    // Convert directives to CSP string
    return Object.entries(directives).map(([directive, sources])=>{
        if (sources.length === 0) {
            return directive;
        }
        return `${directive} ${sources.join(' ')}`;
    }).join('; ');
}
function generateSecurityHeaders() {
    return {
        // Strict Transport Security
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
        // X-Frame-Options (backup for frame-ancestors)
        'X-Frame-Options': 'DENY',
        // X-Content-Type-Options
        'X-Content-Type-Options': 'nosniff',
        // Referrer Policy
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        // X-XSS-Protection (legacy browsers)
        'X-XSS-Protection': '1; mode=block',
        // Permissions Policy (formerly Feature Policy)
        'Permissions-Policy': [
            'camera=()',
            'microphone=()',
            'geolocation=()',
            'payment=()',
            'usb=()',
            'magnetometer=()',
            'accelerometer=()',
            'gyroscope=()'
        ].join(', '),
        // Cross-Origin Policies
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Resource-Policy': 'same-origin'
    };
}
function generateCSPReportingConfig() {
    return {
        group: 'csp-endpoint',
        max_age: 10886400,
        endpoints: [
            {
                url: '/api/csp-report',
                priority: 1,
                weight: 1
            }
        ]
    };
}
const TRUSTED_SCRIPTS = [
    {
        url: 'https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js',
        integrity: 'sha384-/bQdsTh/da6pkI1MST/rWKFNjaCP5gBSY4sEBT38Q/9RBh9AH40zEOg7Hlq2THRZ',
        crossorigin: 'anonymous',
        purpose: 'React library for production builds',
        lastVerified: '2025-01-24'
    }
];
function validateScriptIntegrity(url, expectedIntegrity) {
    const trustedScript = TRUSTED_SCRIPTS.find((script)=>script.url === url);
    return trustedScript?.integrity === expectedIntegrity;
}
function generateSRIPolicy() {
    return TRUSTED_SCRIPTS.map((script)=>`'${script.integrity}'`).join(' ');
}
function validateNonce(nonce) {
    // Nonce should be at least 16 characters, base64 encoded
    const base64Regex = /^[A-Za-z0-9+/]+=*$/;
    const hasMinLength = nonce.length >= 16;
    const isValidBase64 = base64Regex.test(nonce);
    // PHASE 3: Check for sufficient entropy (no repeated patterns)
    const hasEntropy = !/(.)\1{3,}/.test(nonce); // No character repeated 4+ times
    return hasMinLength && isValidBase64 && hasEntropy;
}
function analyzeCSPViolationRisk(violation) {
    const { violatedDirective, blockedUri } = violation;
    // Critical: Script injection attempts
    if (violatedDirective.includes('script-src') && (blockedUri.includes('javascript:') || blockedUri.includes('data:') || blockedUri.includes('blob:'))) {
        return 'CRITICAL';
    }
    // High: External script loading from untrusted domains
    if (violatedDirective.includes('script-src') && !TRUSTED_SCRIPTS.some((script)=>blockedUri.startsWith(script.url.split('/').slice(0, 3).join('/')))) {
        return 'HIGH';
    }
    // Medium: Style or image violations
    if (violatedDirective.includes('style-src') || violatedDirective.includes('img-src')) {
        return 'MEDIUM';
    }
    return 'LOW';
}
function generateSecureNonce() {
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        // Browser environment
        const array = new Uint8Array(24); // 192 bits
        crypto.getRandomValues(array);
        return btoa(String.fromCharCode(...array));
    } else {
        // Node.js environment
        const crypto1 = __turbopack_context__.r("[externals]/crypto [external] (crypto, cjs)");
        return crypto1.randomBytes(24).toString('base64');
    }
}
}}),
"[project]/src/lib/security/CSPProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file CSP Provider for 2025 Security Standards
 * @module lib/security/CSPProvider
 *
 * Implements nonce-based Content Security Policy following OWASP 2025 guidelines.
 * Provides secure nonce propagation from middleware to client components.
 */ __turbopack_context__.s({
    "CSPProvider": (()=>CSPProvider),
    "createSecureScriptProps": (()=>createSecureScriptProps),
    "initializeCSPViolationReporting": (()=>initializeCSPViolationReporting),
    "useCSP": (()=>useCSP),
    "useCSPReporting": (()=>useCSPReporting),
    "useNonce": (()=>useNonce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$cspConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/cspConfig.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const CSPContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({
    nonce: null,
    isStrictCSP: false,
    violationCount: 0,
    isNonceValid: false,
    reportViolation: ()=>{},
    getSecureNonce: ()=>null,
    resetViolationCount: ()=>{}
});
function CSPProvider({ children, nonce }) {
    const [violationCount, setViolationCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    // Validate nonce strength and format
    const isNonceValid = nonce ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$cspConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateNonce"])(nonce) : false;
    // Determine if we're using strict CSP (no unsafe directives)
    const isStrictCSP = isNonceValid && !("TURBOPACK compile-time value", "development")?.includes('development');
    const reportViolation = (violation)=>{
        // Increment violation counter
        setViolationCount((prev)=>prev + 1);
        // Enhanced violation reporting with additional context
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    };
    const getSecureNonce = ()=>{
        return isNonceValid ? nonce : null;
    };
    const resetViolationCount = ()=>{
        setViolationCount(0);
    };
    const contextValue = {
        nonce,
        isStrictCSP,
        violationCount,
        isNonceValid,
        reportViolation,
        getSecureNonce,
        resetViolationCount
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CSPContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/security/CSPProvider.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, this);
}
function useCSP() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(CSPContext);
    if (!context) {
        throw new Error('useCSP must be used within a CSPProvider');
    }
    return context;
}
function useNonce() {
    const { nonce } = useCSP();
    return nonce;
}
function useCSPReporting() {
    const { reportViolation } = useCSP();
    return reportViolation;
}
function createSecureScriptProps(nonce) {
    return nonce ? {
        nonce
    } : {};
}
function initializeCSPViolationReporting(reportViolation) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
}}),
"[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Type Helper Utilities
 * @description Utilities to handle exactOptionalPropertyTypes issues
 */ /**
 * Converts undefined to null for properties that expect null
 */ __turbopack_context__.s({
    "createPartial": (()=>createPartial),
    "ensureNotUndefined": (()=>ensureNotUndefined),
    "isDefined": (()=>isDefined),
    "isNotNull": (()=>isNotNull),
    "isNotNullOrUndefined": (()=>isNotNullOrUndefined),
    "mergeWithUndefined": (()=>mergeWithUndefined),
    "nullToUndefined": (()=>nullToUndefined),
    "safeOptional": (()=>safeOptional),
    "undefinedToNull": (()=>undefinedToNull)
});
function undefinedToNull(value) {
    return value === undefined ? null : value;
}
function nullToUndefined(value) {
    return value === null ? undefined : value;
}
function ensureNotUndefined(value) {
    return value === undefined ? null : value;
}
function safeOptional(value) {
    return value === null ? undefined : value;
}
function createPartial(obj) {
    const result = {};
    for (const [key, value] of Object.entries(obj)){
        if (value !== undefined) {
            result[key] = value;
        }
    }
    return result;
}
function mergeWithUndefined(target, source) {
    const result = {
        ...target
    };
    for (const [key, value] of Object.entries(source)){
        if (value !== undefined) {
            result[key] = value;
        }
    }
    return result;
}
function isDefined(value) {
    return value !== undefined;
}
function isNotNull(value) {
    return value !== null;
}
function isNotNullOrUndefined(value) {
    return value !== null && value !== undefined;
}
}}),
"[project]/src/lib/types/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * API response and error types
 * Re-exports from apiContracts.ts for API wire format types
 */ __turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "ApiErrorType": (()=>ApiErrorType)
});
var ApiErrorType = /*#__PURE__*/ function(ApiErrorType) {
    ApiErrorType["AUTHENTICATION_ERROR"] = "authentication_error";
    ApiErrorType["AUTHORIZATION_ERROR"] = "authorization_error";
    // Client errors
    ApiErrorType["CLIENT_ERROR"] = "client_error";
    // Network-related errors
    ApiErrorType["NETWORK_ERROR"] = "network_error";
    ApiErrorType["NOT_FOUND"] = "not_found";
    // Parsing errors
    ApiErrorType["PARSING_ERROR"] = "parsing_error";
    ApiErrorType["RATE_LIMIT"] = "rate_limit";
    // Server errors
    ApiErrorType["SERVER_ERROR"] = "server_error";
    ApiErrorType["TIMEOUT"] = "timeout";
    // Other
    ApiErrorType["UNKNOWN"] = "unknown";
    ApiErrorType["VALIDATION_ERROR"] = "validation_error";
    return ApiErrorType;
}({});
class ApiError extends Error {
    code;
    details;
    endpoint;
    errorType;
    receivedData;
    retryable;
    status;
    validationErrors;
    constructor(message, options){
        super(message);
        this.name = 'ApiError';
        this.status = options.status;
        if (options.code) this.code = options.code;
        if (options.endpoint) this.endpoint = options.endpoint;
        if (options.validationErrors) this.validationErrors = options.validationErrors;
        this.receivedData = options.receivedData;
        this.details = options.details;
        // Determine error type based on status code if not provided
        this.errorType = options.errorType || this.determineErrorType();
        // Determine if error is retryable
        this.retryable = this.isRetryable();
        // Ensure instanceof works correctly
        Object.setPrototypeOf(this, ApiError.prototype);
    }
    // Legacy constructor support for backward compatibility
    static create(message, status, options) {
        return new ApiError(message, {
            details: options?.details,
            ...options?.errorType && {
                errorType: options.errorType
            },
            receivedData: options?.receivedData,
            status,
            ...options?.validationErrors && {
                validationErrors: options.validationErrors
            }
        });
    }
    /**
   * Get a user-friendly formatted message for display
   */ getFormattedMessage() {
        if (this.isValidationError()) {
            const errorDetails = this.validationErrors.map((err)=>`${err.path}: ${err.message}`).join('; ');
            return `Validation failed: ${errorDetails}`;
        }
        switch(this.errorType){
            case "authentication_error":
                {
                    return 'Authentication required. Please log in and try again.';
                }
            case "authorization_error":
                {
                    return 'You do not have permission to perform this action.';
                }
            case "network_error":
                {
                    return 'Network error: Unable to connect to the server. Please check your internet connection.';
                }
            case "not_found":
                {
                    return `Resource not found: ${this.endpoint || 'The requested resource'} could not be found.`;
                }
            case "parsing_error":
                {
                    return 'Could not parse the server response. Please try again or contact support.';
                }
            case "rate_limit":
                {
                    return 'Too many requests. Please try again later.';
                }
            case "server_error":
                {
                    return `Server error (${this.status}): ${this.message}. Please try again later.`;
                }
            case "timeout":
                {
                    return 'Request timed out. The server is taking too long to respond.';
                }
            default:
                {
                    return this.message;
                }
        }
    }
    /**
   * Get technical details for logging
   */ getTechnicalDetails() {
        const details = [
            `Status: ${this.status}`,
            `Type: ${this.errorType}`,
            `Message: ${this.message}`
        ];
        if (this.details) {
            details.push(`Details: ${JSON.stringify(this.details)}`);
        }
        if (this.validationErrors) {
            details.push(`Validation Errors: ${JSON.stringify(this.validationErrors)}`);
        }
        return details.join('\n');
    }
    /**
   * Check if this is a validation error
   */ isValidationError() {
        return this.status === 400 && Array.isArray(this.validationErrors) && this.validationErrors.length > 0;
    }
    /**
   * Determine error type based on status code
   */ determineErrorType() {
        if (this.status >= 500) {
            return "server_error";
        }
        if (this.status === 429) {
            return "rate_limit";
        }
        if (this.status === 401) {
            return "authentication_error";
        }
        if (this.status === 403) {
            return "authorization_error";
        }
        if (this.status === 400 && this.isValidationError()) {
            return "validation_error";
        }
        if (this.status >= 400 && this.status < 500) {
            return "client_error";
        }
        return "unknown";
    }
    /**
   * Determine if error is retryable
   */ isRetryable() {
        return this.errorType === "server_error" || this.errorType === "network_error" || this.errorType === "timeout" || this.errorType === "rate_limit";
    }
}
}}),
"[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Custom error classes for the API client.
 * @module api/base/errors
 */ // Import the enhanced ApiError and ApiErrorType from the central types definition
__turbopack_context__.s({
    "AuthenticationError": (()=>AuthenticationError),
    "BadRequestError": (()=>BadRequestError),
    "InternalServerError": (()=>InternalServerError),
    "NetworkError": (()=>NetworkError),
    "NotFoundError": (()=>NotFoundError),
    "ServiceError": (()=>ServiceError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/api.ts [app-ssr] (ecmascript)");
;
class AuthenticationError extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] {
    constructor(message = 'Authentication Failed', details){
        super(message, {
            details,
            errorType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"].AUTHENTICATION_ERROR,
            status: 401
        });
        this.name = 'AuthenticationError';
        Object.setPrototypeOf(this, AuthenticationError.prototype);
    }
}
class BadRequestError extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] {
    constructor(message = 'Bad Request', details){
        super(message, {
            details,
            errorType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"].CLIENT_ERROR,
            status: 400
        });
        this.name = 'BadRequestError';
        Object.setPrototypeOf(this, BadRequestError.prototype);
    }
}
class InternalServerError extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] {
    constructor(message = 'Internal Server Error', details){
        super(message, {
            details,
            errorType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"].SERVER_ERROR,
            status: 500
        });
        this.name = 'InternalServerError';
        Object.setPrototypeOf(this, InternalServerError.prototype);
    }
}
class NetworkError extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] {
    constructor(message = 'Network Error', details){
        super(message, {
            details,
            errorType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"].NETWORK_ERROR,
            status: 0
        });
        this.name = 'NetworkError';
        Object.setPrototypeOf(this, NetworkError.prototype);
    }
}
class NotFoundError extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] {
    constructor(message = 'Resource Not Found', details){
        super(message, {
            details,
            errorType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"].NOT_FOUND,
            status: 404
        });
        this.name = 'NotFoundError';
        Object.setPrototypeOf(this, NotFoundError.prototype);
    }
}
class ServiceError extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] {
    code;
    statusCode;
    context;
    constructor(message, code, statusCode, context){
        super(message, {
            details: context,
            errorType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"].CLIENT_ERROR,
            status: statusCode || 500
        }), this.code = code, this.statusCode = statusCode, this.context = context;
        this.name = 'ServiceError';
        Object.setPrototypeOf(this, ServiceError.prototype);
    }
}
;
}}),
"[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Base HTTP client for making API requests.
 * @module api/base/apiClient
 */ __turbopack_context__.s({
    "ApiClient": (()=>ApiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
;
;
// Helper functions (can be private static or module-level)
const sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));
const getRetryDelay = (attempt, baseDelay = 1000)=>{
    return Math.min(baseDelay * Math.pow(2, attempt), 30_000 // Max 30 seconds
    );
};
class ApiClient {
    baseURL;
    defaultHeaders;
    retryAttempts;
    timeout;
    getAuthToken;
    /**
   * Creates an instance of ApiClient.
   * @param config - Configuration object for the API client.
   */ constructor(config){
        this.baseURL = config.baseURL;
        this.timeout = config.timeout || 10_000; // Default to 10 seconds
        this.retryAttempts = config.retryAttempts || 3; // Default to 3 retry attempts
        this.getAuthToken = config.getAuthToken;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            ...config.headers
        };
    }
    /**
   * Performs a DELETE request.
   * @template T - The expected response data type (can be void if no content).
   * @param endpoint - The API endpoint.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves when the request is complete.
   */ async delete(endpoint, config) {
        return this.request('DELETE', endpoint, undefined, config);
    }
    /**
   * Performs a GET request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint (e.g., '/users').
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */ async get(endpoint, config) {
        return this.request('GET', endpoint, undefined, config);
    }
    /**
   * Performs a PATCH request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint.
   * @param data - The request body.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */ async patch(endpoint, data, config) {
        return this.request('PATCH', endpoint, data, config);
    }
    /**
   * Performs a POST request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint.
   * @param data - The request body.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */ async post(endpoint, data, config) {
        return this.request('POST', endpoint, data, config);
    }
    /**
   * Performs a PUT request.
   * @template T - The expected response data type.
   * @param endpoint - The API endpoint.
   * @param data - The request body.
   * @param config - Optional request configuration.
   * @returns A Promise that resolves with the response data.
   */ async put(endpoint, data, config) {
        return this.request('PUT', endpoint, data, config);
    }
    /**
   * Internal method to perform the actual HTTP request with retry logic and error handling.
   * @private
   * @template T - The expected response data type.
   * @param method - The HTTP method.
   * @param endpoint - The API endpoint.
   * @param data - The request body for POST, PUT, PATCH.
   * @param config - Optional request configuration, including specific timeout or retry parameters.
   * @returns A Promise that resolves with the (unwrapped, if applicable) response data of type T.
   * @throws {ApiError} For API-specific errors (e.g., 4xx, 5xx status codes).
   * @throws {NetworkError} For network connectivity issues or if the request times out after retries.
   * @throws {AuthenticationError} Specifically for 401 Unauthorized errors.
   */ async request(method, endpoint, data, config) {
        const url = `${this.baseURL}${endpoint}`;
        const headers = {
            ...this.defaultHeaders,
            ...config?.headers
        };
        // 🔑 JWT Authentication: Add Authorization header if token is available
        if (this.getAuthToken) {
            const token = this.getAuthToken();
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }
        // 🔑 JWT Authentication: Using JWT tokens for authentication instead of CSRF tokens
        // CSRF protection is not needed when using stateless JWT authentication
        // as JWTs are immune to CSRF attacks when stored properly (httpOnly cookies)
        if ("TURBOPACK compile-time truthy", 1) {
            console.debug('🔐 Using JWT authentication for request:', method, endpoint);
        }
        const requestOptions = {
            body: data ? JSON.stringify(data) : null,
            credentials: 'include',
            headers,
            method,
            signal: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(config?.signal)
        };
        // Implement timeout logic
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), config?.timeout || this.timeout);
        requestOptions.signal = config?.signal || controller.signal;
        for(let attempt = 0; attempt < this.retryAttempts; attempt++){
            try {
                const response = await fetch(url, requestOptions);
                clearTimeout(timeoutId);
                const responseBody = await response.json().catch(()=>null);
                if (!response.ok) {
                    // Use the standardized error structure if available
                    if (responseBody && responseBody.status === 'error') {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](responseBody.message, {
                            code: responseBody.code,
                            details: responseBody.error,
                            status: response.status
                        });
                    }
                    // Fallback to old error handling for non-standard responses
                    const errorMessage = responseBody?.message || response.statusText;
                    switch(response.status){
                        case 400:
                            {
                                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["BadRequestError"](errorMessage, responseBody);
                            }
                        case 401:
                            {
                                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"](errorMessage, responseBody);
                            }
                        case 404:
                            {
                                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NotFoundError"](errorMessage, responseBody);
                            }
                        case 500:
                            {
                                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["InternalServerError"](errorMessage, responseBody);
                            }
                        default:
                            {
                                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](errorMessage, {
                                    details: responseBody,
                                    status: response.status
                                });
                            }
                    }
                }
                // Handle standardized success response format
                if (responseBody && typeof responseBody === 'object' && responseBody.status === 'success') {
                    // Return the data property for standardized success responses
                    return responseBody.data;
                }
                // Handle 204 No Content responses
                if (response.status === 204) {
                    return undefined;
                }
                // Fallback for legacy non-standard success responses
                // This should become increasingly rare as backend migration completes
                if ("TURBOPACK compile-time truthy", 1) {
                    console.warn('⚠️ Non-standard success response detected:', {
                        endpoint,
                        method,
                        responseBody,
                        status: response.status
                    });
                }
                return responseBody;
            } catch (error) {
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                    // Re-throw known API errors immediately
                    throw error;
                }
                // Handle network errors or other fetch-related issues
                if (attempt === this.retryAttempts - 1) {
                    const errorMessage = error instanceof Error ? error.message : String(error);
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NetworkError"](`Request failed after ${this.retryAttempts} attempts: ${errorMessage}`);
                }
                const delay = getRetryDelay(attempt);
                await sleep(delay);
            }
        }
        // This part should be unreachable if retry logic is correct
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NetworkError"]('Request failed after multiple retries.');
    }
}
}}),
"[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced BaseApiService - Production-Ready Service Foundation
 *
 * This service provides a standardized foundation for all API services with:
 * - Circuit breaker protection (from AdminService patterns)
 * - Request caching and deduplication
 * - Comprehensive error handling with retry logic
 * - Performance monitoring and metrics
 * - Data transformation and validation
 *
 * Built upon proven patterns from the consolidated AdminService.
 */ __turbopack_context__.s({
    "BaseApiService": (()=>BaseApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
;
/**
 * Simple circuit breaker implementation
 */ class CircuitBreaker {
    name;
    threshold;
    timeout;
    failures;
    lastFailureTime;
    state;
    constructor(name, threshold = 5, timeout = 60_000){
        this.name = name;
        this.threshold = threshold;
        this.timeout = timeout;
        this.failures = 0;
        this.lastFailureTime = 0;
        this.state = 'CLOSED';
    }
    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'HALF_OPEN';
            } else {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]('Circuit breaker is OPEN', 'CIRCUIT_BREAKER_OPEN');
            }
        }
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
    getState() {
        return {
            failures: this.failures,
            lastFailureTime: this.lastFailureTime,
            name: this.name,
            state: this.state
        };
    }
    onFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        if (this.failures >= this.threshold) {
            this.state = 'OPEN';
        }
    }
    onSuccess() {
        this.failures = 0;
        this.state = 'CLOSED';
    }
}
/**
 * Simple request cache implementation
 */ class RequestCache {
    cache = new Map();
    clear() {
        this.cache.clear();
    }
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        return item.data;
    }
    getStats() {
        return {
            keys: [
                ...this.cache.keys()
            ],
            size: this.cache.size
        };
    }
    invalidate(key) {
        this.cache.delete(key);
    }
    invalidatePattern(pattern) {
        for (const key of this.cache.keys()){
            if (pattern.test(key)) {
                this.cache.delete(key);
            }
        }
    }
    set(key, data, duration = 300_000) {
        this.cache.set(key, {
            data,
            expiry: Date.now() + duration
        });
    }
}
class BaseApiService {
    apiClient;
    cache;
    // Service infrastructure (enhanced from AdminService patterns)
    circuitBreaker;
    config;
    metrics;
    constructor(apiClient, config = {}){
        this.apiClient = apiClient;
        this.config = {
            cacheDuration: 5 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        };
        // Initialize service infrastructure
        this.circuitBreaker = new CircuitBreaker(`${this.constructor.name}`, this.config.circuitBreakerThreshold);
        this.cache = new RequestCache();
        this.metrics = {
            averageResponseTime: 0,
            cacheHitRatio: 0,
            errorCount: 0,
            requestCount: 0
        };
    }
    /**
   * Clear service cache
   */ clearCache() {
        this.cache.clear();
    }
    /**
   * Create new entity
   */ async create(data) {
        return this.executeWithInfrastructure(null, async ()=>{
            const transformedData = this.transformer.toApi ? this.transformer.toApi(data) : data;
            const response = await this.apiClient.post(this.endpoint, transformedData);
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    /**
   * Delete entity
   */ async delete(id) {
        return this.executeWithInfrastructure(null, async ()=>{
            await this.apiClient.delete(`${this.endpoint}/${id}`);
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
        });
    }
    /**
   * Get all entities with pagination and filtering
   */ async getAll(filters) {
        const cacheKey = `${this.endpoint}:getAll:${JSON.stringify(filters || {})}`;
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            const params = new URLSearchParams();
            if (filters) {
                for (const [key, value] of Object.entries(filters)){
                    if (value !== undefined && value !== null) {
                        params.append(key, String(value));
                    }
                }
            }
            const queryString = params.toString();
            const url = queryString ? `${this.endpoint}?${queryString}` : this.endpoint;
            const response = await this.apiClient.get(url);
            // Handle different response formats from backend
            let responseData;
            let paginationInfo = {};
            // Handle wrapped response format from responseWrapper middleware
            if (response && response.status === 'success' && response.data) {
                // Backend returns wrapped response: {status: 'success', data: {...}, pagination?: {...}}
                const wrappedData = response.data;
                if (Array.isArray(wrappedData)) {
                    // Direct array in wrapped response
                    responseData = wrappedData;
                    // Check for pagination at the top level of wrapped response
                    if (response.pagination) {
                        paginationInfo = {
                            pagination: {
                                hasNext: response.pagination.hasNext ?? false,
                                hasPrevious: response.pagination.hasPrevious ?? false,
                                limit: response.pagination.limit,
                                page: response.pagination.page,
                                total: response.pagination.total,
                                totalPages: response.pagination.totalPages ?? Math.ceil(response.pagination.total / response.pagination.limit)
                            }
                        };
                    }
                } else if (wrappedData && Array.isArray(wrappedData.data)) {
                    // Nested data structure: {status: 'success', data: {data: [], pagination: {}}}
                    responseData = wrappedData.data;
                    // Handle nested pagination object
                    if (wrappedData.pagination) {
                        paginationInfo = {
                            pagination: {
                                hasNext: wrappedData.pagination.hasNext ?? false,
                                hasPrevious: wrappedData.pagination.hasPrevious ?? false,
                                limit: wrappedData.pagination.limit,
                                page: wrappedData.pagination.page,
                                total: wrappedData.pagination.total,
                                totalPages: wrappedData.pagination.totalPages ?? Math.ceil(wrappedData.pagination.total / wrappedData.pagination.limit)
                            }
                        };
                    } else if (response.pagination) {
                        // Pagination at wrapper level
                        paginationInfo = {
                            pagination: {
                                hasNext: response.pagination.hasNext ?? false,
                                hasPrevious: response.pagination.hasPrevious ?? false,
                                limit: response.pagination.limit,
                                page: response.pagination.page,
                                total: response.pagination.total,
                                totalPages: response.pagination.totalPages ?? Math.ceil(response.pagination.total / response.pagination.limit)
                            }
                        };
                    }
                } else {
                    // Single item wrapped response - convert to array for consistency
                    responseData = [
                        wrappedData
                    ];
                }
            } else if (Array.isArray(response)) {
                // Direct array response (common backend pattern)
                responseData = response;
            } else if (response && (response.error || response.status === 'error')) {
                // Backend returned an error response
                throw new Error(response.message || response.error || 'API request failed');
            } else if (response && typeof response === 'object') {
                // Single object response - convert to array for consistency
                responseData = [
                    response
                ];
            } else {
                // Unexpected response format
                throw new Error(`Invalid response format from API: ${JSON.stringify(response)}`);
            }
            // Transform data using the service's transformer
            const transformedData = responseData.map((item)=>this.transformer.fromApi ? this.transformer.fromApi(item) : item);
            return {
                data: transformedData,
                ...paginationInfo
            };
        });
    }
    /**
   * Get entity by ID
   */ async getById(id) {
        const cacheKey = `${this.endpoint}:getById:${id}`;
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/${id}`);
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    /**
   * Get service health status
   */ getHealthStatus() {
        return {
            cacheStats: this.cache.getStats(),
            circuitBreakerState: this.circuitBreaker.getState(),
            endpoint: this.endpoint,
            metrics: this.metrics,
            service: this.constructor.name
        };
    }
    /**
   * Reset service metrics
   */ resetMetrics() {
        this.metrics = {
            averageResponseTime: 0,
            cacheHitRatio: 0,
            errorCount: 0,
            requestCount: 0
        };
    }
    /**
   * Update existing entity
   */ async update(id, data) {
        return this.executeWithInfrastructure(null, async ()=>{
            const transformedData = this.transformer.toApi ? this.transformer.toApi(data) : data;
            const response = await this.apiClient.put(`${this.endpoint}/${id}`, transformedData);
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    /**
   * Execute operation with full service infrastructure
   * (circuit breaker, caching, error handling, metrics)
   */ async executeWithInfrastructure(cacheKey, operation) {
        const startTime = Date.now();
        try {
            this.metrics.requestCount++;
            // Try cache first if cacheKey provided
            if (cacheKey) {
                const cached = this.cache.get(cacheKey);
                if (cached) {
                    this.metrics.cacheHitRatio = (this.metrics.cacheHitRatio * (this.metrics.requestCount - 1) + 1) / this.metrics.requestCount;
                    return cached;
                }
            }
            // Execute with circuit breaker and retry logic
            const result = await this.circuitBreaker.execute(async ()=>{
                return withRetry(operation, this.config.retryAttempts);
            });
            // Cache result if cacheKey provided
            if (cacheKey && result) {
                this.cache.set(cacheKey, result, this.config.cacheDuration);
            }
            // Update metrics
            const responseTime = Date.now() - startTime;
            this.metrics.averageResponseTime = (this.metrics.averageResponseTime * (this.metrics.requestCount - 1) + responseTime) / this.metrics.requestCount;
            return result;
        } catch (error) {
            this.metrics.errorCount++;
            // Log the error for debugging with better error serialization
            console.error(`Service error in ${this.constructor.name}:`, {
                endpoint: this.endpoint,
                errorDetails: error instanceof Error ? {
                    message: error.message,
                    name: error.name,
                    stack: error.stack
                } : error,
                errorMessage: error instanceof Error ? error.message : String(error),
                errorType: error?.constructor?.name || typeof error,
                timestamp: new Date().toISOString()
            });
            // Transform error to ServiceError
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]) {
                throw error;
            }
            // Handle specific API errors
            if (error instanceof Error) {
                // Check if it's a network connectivity issue
                if (error.message.includes('fetch') || error.message.includes('network')) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR', undefined, {
                        endpoint: this.endpoint,
                        service: this.constructor.name
                    });
                }
                // Check if it's a backend server error
                if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]('Server error occurred. Please try again later.', 'SERVER_ERROR', undefined, {
                        endpoint: this.endpoint,
                        service: this.constructor.name
                    });
                }
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"](error instanceof Error ? error.message : 'Unknown service error', 'SERVICE_ERROR', undefined, {
                endpoint: this.endpoint,
                service: this.constructor.name
            });
        }
    }
}
/**
 * Simple retry utility
 */ async function withRetry(operation, maxAttempts = 3, delay = 1000) {
    let lastError;
    for(let attempt = 1; attempt <= maxAttempts; attempt++){
        try {
            return await operation();
        } catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
            if (attempt === maxAttempts) {
                throw lastError;
            }
            // Wait before retry
            await new Promise((resolve)=>setTimeout(resolve, delay * attempt));
        }
    }
    throw lastError;
}
}}),
"[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Common API types and interfaces.
 * @module api/base/types
 */ /**
 * Configuration interface for the ApiClient.
 * @property baseURL - The base URL for API requests.
 * @property timeout - Optional. The request timeout in milliseconds. Defaults to 10000 (10 seconds).
 * @property retryAttempts - Optional. The number of retry attempts for failed requests. Defaults to 3.
 * @property headers - Optional. Default headers to be sent with every request.
 * @property authToken - Optional. Authentication token to be included in requests.
 * @property getAuthToken - Optional. Function to get the current authentication token dynamically.
 */ __turbopack_context__.s({});
;
}}),
"[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Core interfaces for the API layer
 * @module api/core/interfaces
 */ __turbopack_context__.s({});
;
}}),
"[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Environment Configuration Manager
 * @module lib/config/environment
 * 
 * Production-ready environment configuration system that automatically
 * detects deployment context and provides appropriate API endpoints.
 */ /**
 * Environment types supported by the application
 */ __turbopack_context__.s({
    "environmentConfig": (()=>environmentConfig),
    "getEnvironmentConfig": (()=>getEnvironmentConfig),
    "logEnvironmentConfig": (()=>logEnvironmentConfig),
    "validateEnvironmentConfig": (()=>validateEnvironmentConfig)
});
/**
 * Detect the current deployment context based on window location
 */ function detectDeploymentContext() {
    if ("TURBOPACK compile-time truthy", 1) {
        // Server-side rendering - use environment variables
        return 'localhost';
    }
    "TURBOPACK unreachable";
    const hostname = undefined;
    const port = undefined;
}
/**
 * Generate API URLs based on deployment context
 */ function generateApiUrls(deploymentContext) {
    // Use environment variables if explicitly set
    if ("TURBOPACK compile-time truthy", 1) {
        return {
            apiUrl: ("TURBOPACK compile-time value", "http://localhost:3001") || ("TURBOPACK compile-time value", "http://localhost:3001/api").replace('/api', ''),
            apiBaseUrl: ("TURBOPACK compile-time value", "http://localhost:3001/api"),
            wsUrl: ("TURBOPACK compile-time value", "ws://localhost:3001")
        };
    }
    "TURBOPACK unreachable";
    const hostname = undefined;
    const protocol = undefined;
    const wsProtocol = undefined;
    // Use environment variables or infer from hostname
    const baseUrl = undefined;
}
/**
 * Create environment configuration based on current context
 */ function createEnvironmentConfig() {
    const nodeEnv = ("TURBOPACK compile-time value", "development") || 'development';
    const deploymentContext = detectDeploymentContext();
    const { apiUrl, apiBaseUrl, wsUrl } = generateApiUrls(deploymentContext);
    return {
        apiUrl,
        apiBaseUrl,
        wsUrl,
        environment: nodeEnv,
        deploymentContext,
        isProduction: nodeEnv === 'production',
        isDevelopment: nodeEnv === 'development',
        enableDebugLogging: ("TURBOPACK compile-time value", "true") === 'true' || nodeEnv === 'development'
    };
}
const environmentConfig = createEnvironmentConfig();
function getEnvironmentConfig() {
    return environmentConfig;
}
function logEnvironmentConfig() {
    if (environmentConfig.enableDebugLogging) {
        console.group('🌍 Environment Configuration');
        console.log('Environment:', environmentConfig.environment);
        console.log('Deployment Context:', environmentConfig.deploymentContext);
        console.log('API URL:', environmentConfig.apiUrl);
        console.log('API Base URL:', environmentConfig.apiBaseUrl);
        console.log('WebSocket URL:', environmentConfig.wsUrl);
        console.log('Is Production:', environmentConfig.isProduction);
        console.groupEnd();
    }
}
function validateEnvironmentConfig() {
    const errors = [];
    if (!environmentConfig.apiBaseUrl) {
        errors.push('API Base URL is not configured');
    }
    if (!environmentConfig.wsUrl) {
        errors.push('WebSocket URL is not configured');
    }
    if (!environmentConfig.apiBaseUrl.startsWith('http')) {
        errors.push('API Base URL must start with http or https');
    }
    if (!environmentConfig.wsUrl.startsWith('ws')) {
        errors.push('WebSocket URL must start with ws or wss');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
}}),
"[project]/src/lib/security/CSRFProtection.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file CSRF Protection Service - Single Responsibility Principle (SRP)
 * @module lib/security/CSRFProtection
 * 
 * This class handles ONLY CSRF protection operations following SRP principles.
 * It provides CSRF token generation, validation, and request modification.
 * 
 * SECURITY NOTE: This manages CSRF protection without handling authentication logic.
 */ __turbopack_context__.s({
    "CSRFProtection": (()=>CSRFProtection)
});
class CSRFProtection {
    static CSRF_HEADER_NAME = 'X-CSRF-Token';
    static CSRF_COOKIE_NAME = 'csrf-token';
    static CSRF_STORAGE_KEY = 'workhub_csrf_token';
    static TOKEN_LIFETIME_MINUTES = 60;
    /**
   * Generate CSRF token
   * Single responsibility: CSRF token generation only
   */ static generateToken() {
        // Generate cryptographically secure random token
        const array = new Uint8Array(32);
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            // Fallback for environments without crypto API
            for(let i = 0; i < array.length; i++){
                array[i] = Math.floor(Math.random() * 256);
            }
        }
        // Convert to base64 string
        return btoa(String.fromCharCode(...array)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    }
    /**
   * Validate CSRF token
   * Single responsibility: CSRF token validation only
   */ static validateToken(token) {
        if (!token || typeof token !== 'string') {
            return {
                isValid: false,
                error: 'Invalid token format'
            };
        }
        // Check token format (base64url)
        const base64UrlPattern = /^[A-Za-z0-9_-]+$/;
        if (!base64UrlPattern.test(token)) {
            return {
                isValid: false,
                error: 'Invalid token format'
            };
        }
        // Check token length (should be 43 characters for 32-byte token)
        if (token.length < 32) {
            return {
                isValid: false,
                error: 'Token too short'
            };
        }
        // Get stored token for comparison
        const storedToken = this.getStoredToken();
        if (!storedToken) {
            return {
                isValid: false,
                error: 'No stored token found'
            };
        }
        // Check if stored token is expired
        if (!storedToken.isValid || storedToken.expiresAt < new Date()) {
            return {
                isValid: false,
                error: 'Stored token expired'
            };
        }
        // Compare tokens
        if (token !== storedToken.token) {
            return {
                isValid: false,
                error: 'Token mismatch'
            };
        }
        return {
            isValid: true,
            token
        };
    }
    /**
   * Attach CSRF token to request
   * Single responsibility: Request modification only
   */ static attachToRequest(config) {
        // Only attach to state-changing requests
        const stateChangingMethods = [
            'POST',
            'PUT',
            'PATCH',
            'DELETE'
        ];
        const method = (config.method || 'GET').toUpperCase();
        if (!stateChangingMethods.includes(method)) {
            return config;
        }
        // Get current token
        const csrfToken = this.getCurrentToken();
        if (!csrfToken) {
            console.warn('No CSRF token available for request');
            return config;
        }
        // Add CSRF header
        const headers = {
            ...config.headers,
            [this.CSRF_HEADER_NAME]: csrfToken.token
        };
        return {
            ...config,
            headers
        };
    }
    /**
   * Get current valid CSRF token
   * Single responsibility: Current token retrieval only
   */ static getCurrentToken() {
        const storedToken = this.getStoredToken();
        if (!storedToken || !storedToken.isValid || storedToken.expiresAt < new Date()) {
            // Generate new token if none exists or expired
            return this.refreshToken();
        }
        return storedToken;
    }
    /**
   * Refresh CSRF token
   * Single responsibility: Token refresh only
   */ static refreshToken() {
        const token = this.generateToken();
        const expiresAt = new Date(Date.now() + this.TOKEN_LIFETIME_MINUTES * 60 * 1000);
        const csrfToken = {
            token,
            expiresAt,
            isValid: true
        };
        this.storeToken(csrfToken);
        return csrfToken;
    }
    /**
   * Clear CSRF token
   * Single responsibility: Token clearing only
   */ static clearToken() {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    /**
   * Initialize CSRF protection
   * Single responsibility: CSRF initialization only
   */ static initialize() {
        // Generate initial token
        return this.refreshToken();
    }
    /**
   * Check if CSRF protection is required for request
   * Single responsibility: CSRF requirement check only
   */ static isProtectionRequired(config) {
        const stateChangingMethods = [
            'POST',
            'PUT',
            'PATCH',
            'DELETE'
        ];
        const method = (config.method || 'GET').toUpperCase();
        return stateChangingMethods.includes(method);
    }
    /**
   * Extract CSRF token from response headers
   * Single responsibility: Token extraction only
   */ static extractTokenFromResponse(headers) {
        return headers[this.CSRF_HEADER_NAME.toLowerCase()] || headers[this.CSRF_HEADER_NAME] || null;
    }
    // Private helper methods
    /**
   * Get stored CSRF token
   * Single responsibility: Token storage retrieval only
   */ static getStoredToken() {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    }
    /**
   * Store CSRF token
   * Single responsibility: Token storage only
   */ static storeToken(csrfToken) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
}
}}),
"[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file CSRF Protection Hook - DRY + Single Responsibility Principle (SRP)
 * @module hooks/useCSRFProtection
 *
 * This hook provides centralized CSRF protection functionality following DRY principles.
 * It handles CSRF token management and request protection for all components.
 *
 * SECURITY NOTE: This is the single source of CSRF protection for all state-changing operations.
 */ __turbopack_context__.s({
    "useCSRFProtection": (()=>useCSRFProtection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/CSRFProtection.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useCSRFProtection() {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        csrfToken: null,
        isTokenValid: false,
        tokenExpiresAt: null,
        isInitialized: false
    });
    /**
   * Attach CSRF token to request configuration
   * Single responsibility: Request CSRF attachment only
   */ const attachCSRF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((config)=>{
        // Check if CSRF protection is required
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].isProtectionRequired(config)) {
            return config;
        }
        // Ensure we have a valid token
        const currentToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].getCurrentToken();
        if (!currentToken) {
            console.warn('No valid CSRF token available for request');
            return config;
        }
        // Update state if token changed
        if (currentToken.token !== state.csrfToken) {
            setState((prev)=>({
                    ...prev,
                    csrfToken: currentToken.token,
                    isTokenValid: currentToken.isValid,
                    tokenExpiresAt: currentToken.expiresAt
                }));
        }
        // Attach token to request
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].attachToRequest(config);
    }, [
        state.csrfToken
    ]);
    /**
   * Validate CSRF token
   * Single responsibility: CSRF token validation only
   */ const validateCSRF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((token)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].validateToken(token);
    }, []);
    /**
   * Refresh CSRF token
   * Single responsibility: CSRF token refresh only
   */ const refreshCSRFToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const newToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].refreshToken();
        setState((prev)=>({
                ...prev,
                csrfToken: newToken.token,
                isTokenValid: newToken.isValid,
                tokenExpiresAt: newToken.expiresAt
            }));
        return newToken;
    }, []);
    /**
   * Clear CSRF token
   * Single responsibility: CSRF token clearing only
   */ const clearCSRFToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].clearToken();
        setState((prev)=>({
                ...prev,
                csrfToken: null,
                isTokenValid: false,
                tokenExpiresAt: null
            }));
    }, []);
    /**
   * Check if CSRF protection is required for request
   * Single responsibility: CSRF requirement check only
   */ const isProtectionRequired = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((config)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].isProtectionRequired(config);
    }, []);
    // Initialize CSRF protection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeCSRF = ()=>{
            try {
                const initialToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].initialize();
                setState({
                    csrfToken: initialToken.token,
                    isTokenValid: initialToken.isValid,
                    tokenExpiresAt: initialToken.expiresAt,
                    isInitialized: true
                });
            } catch (error) {
                console.error('Failed to initialize CSRF protection:', error);
                setState((prev)=>({
                        ...prev,
                        isInitialized: true
                    }));
            }
        };
        initializeCSRF();
    }, []);
    // Auto-refresh token before expiration
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!state.isInitialized || !state.tokenExpiresAt) return;
        const checkAndRefreshToken = ()=>{
            const now = new Date();
            const timeUntilExpiry = state.tokenExpiresAt.getTime() - now.getTime();
            const refreshThreshold = 5 * 60 * 1000; // 5 minutes
            if (timeUntilExpiry <= refreshThreshold) {
                refreshCSRFToken();
            }
        };
        // Check every minute
        const interval = setInterval(checkAndRefreshToken, 60 * 1000);
        // Initial check
        checkAndRefreshToken();
        return ()=>clearInterval(interval);
    }, [
        state.isInitialized,
        state.tokenExpiresAt,
        refreshCSRFToken
    ]);
    // Validate current token periodically
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!state.csrfToken) return;
        const validateCurrentToken = ()=>{
            const validation = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].validateToken(state.csrfToken);
            if (!validation.isValid && state.isTokenValid) {
                // Token became invalid, refresh it
                refreshCSRFToken();
            }
        };
        // Validate every 5 minutes
        const interval = setInterval(validateCurrentToken, 5 * 60 * 1000);
        return ()=>clearInterval(interval);
    }, [
        state.csrfToken,
        state.isTokenValid,
        refreshCSRFToken
    ]);
    return {
        // State
        csrfToken: state.csrfToken,
        isTokenValid: state.isTokenValid,
        tokenExpiresAt: state.tokenExpiresAt,
        isInitialized: state.isInitialized,
        // Actions
        attachCSRF,
        validateCSRF,
        refreshCSRFToken,
        clearCSRFToken,
        isProtectionRequired
    };
}
}}),
"[project]/src/lib/security/InputValidator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Input Validation Service - Single Responsibility Principle (SRP)
 * @module lib/security/InputValidator
 *
 * This class handles ONLY input validation and sanitization following SRP principles.
 * It provides comprehensive validation for different data types and security threats.
 *
 * SECURITY NOTE: This is the single source of truth for input validation across the application.
 */ __turbopack_context__.s({
    "InputValidator": (()=>InputValidator)
});
class InputValidator {
    /**
   * Common validation patterns
   */ static PATTERNS = {
        EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        PHONE: /^\+?[\d\s\-\(\)]{10,}$/,
        URL: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
        ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
        ALPHA: /^[a-zA-Z]+$/,
        NUMERIC: /^\d+$/,
        UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
        SQL_INJECTION: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)|('|('')|;|--|\/\*|\*\/)/i,
        XSS: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        HTML_TAGS: /<[^>]*>/g
    };
    /**
   * Dangerous strings that should be blocked
   */ static DANGEROUS_STRINGS = [
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        'onclick=',
        'onmouseover=',
        'onfocus=',
        'onblur=',
        'onchange=',
        'onsubmit=',
        'data:text/html',
        'eval(',
        'expression(',
        'setTimeout(',
        'setInterval('
    ];
    /**
   * Validate single value against rules
   * Single responsibility: Single value validation only
   */ static validateValue(value, rules) {
        const errors = [];
        let sanitizedValue = value;
        // Apply sanitizer first if provided
        if (rules.sanitizer) {
            sanitizedValue = rules.sanitizer(value);
        }
        // Required check
        if (rules.required && (value === null || value === undefined || value === '')) {
            errors.push('This field is required');
            return {
                isValid: false,
                errors
            };
        }
        // Skip other validations if value is empty and not required
        if (!rules.required && (value === null || value === undefined || value === '')) {
            return {
                isValid: true,
                errors: [],
                sanitizedValue
            };
        }
        // String validations
        if (typeof value === 'string') {
            // Length validations
            if (rules.minLength && value.length < rules.minLength) {
                errors.push(`Minimum length is ${rules.minLength} characters`);
            }
            if (rules.maxLength && value.length > rules.maxLength) {
                errors.push(`Maximum length is ${rules.maxLength} characters`);
            }
            // Pattern validation
            if (rules.pattern && !rules.pattern.test(value)) {
                errors.push('Invalid format');
            }
            // Security checks
            if (this.containsDangerousContent(value)) {
                errors.push('Contains potentially dangerous content');
            }
        }
        // Custom validator
        if (rules.customValidator && !rules.customValidator(sanitizedValue)) {
            errors.push('Custom validation failed');
        }
        return {
            isValid: errors.length === 0,
            errors,
            sanitizedValue
        };
    }
    /**
   * Validate object against schema
   * Single responsibility: Object validation only
   */ static validateObject(data, schema) {
        const errors = [];
        const sanitizedValue = {};
        for (const [field, rules] of Object.entries(schema)){
            const fieldValue = data[field];
            const fieldResult = this.validateValue(fieldValue, rules);
            if (!fieldResult.isValid) {
                errors.push(...fieldResult.errors.map((error)=>`${field}: ${error}`));
            } else {
                sanitizedValue[field] = fieldResult.sanitizedValue;
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
            sanitizedValue
        };
    }
    /**
   * Sanitize string for XSS prevention
   * Single responsibility: XSS sanitization only
   */ static sanitizeForXSS(input) {
        if (typeof input !== 'string') return input;
        return input// Remove script tags
        .replace(this.PATTERNS.XSS, '')// Remove dangerous protocols
        .replace(/javascript:/gi, '').replace(/vbscript:/gi, '').replace(/data:text\/html/gi, 'data:text/plain')// Remove event handlers
        .replace(/on\w+\s*=/gi, '')// Encode HTML entities
        .replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
    }
    /**
   * SECURITY FIX: Renamed from sanitizeForSQL to clarify purpose
   *
   * ⚠️  CRITICAL SECURITY WARNING:
   * - This method is for DISPLAY PURPOSES ONLY
   * - This does NOT provide SQL injection protection
   * - Client-side sanitization can ALWAYS be bypassed by attackers
   * - SQL injection protection MUST be handled server-side with parameterized queries
   *
   * Escapes SQL-like characters for safe display in UI
   * Single responsibility: Display formatting only
   */ static escapeForDisplay(input) {
        if (typeof input !== 'string') return input;
        return input// Escape single quotes for display
        .replace(/'/g, "''")// Remove potentially confusing SQL-like characters for display
        .replace(/;/g, '').replace(/--/g, '').replace(/\/\*/g, '').replace(/\*\//g, '')// Remove SQL keywords that might confuse users in display
        .replace(/\b(UNION|SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b/gi, '');
    }
    /**
   * Validate email format
   * Single responsibility: Email validation only
   */ static validateEmail(email) {
        return this.validateValue(email, {
            required: true,
            pattern: this.PATTERNS.EMAIL,
            maxLength: 254,
            sanitizer: (value)=>value.toLowerCase().trim()
        });
    }
    /**
   * Validate phone number format
   * Single responsibility: Phone validation only
   */ static validatePhone(phone) {
        return this.validateValue(phone, {
            required: true,
            pattern: this.PATTERNS.PHONE,
            sanitizer: (value)=>value.replace(/\s/g, '')
        });
    }
    /**
   * Validate URL format
   * Single responsibility: URL validation only
   */ static validateURL(url) {
        return this.validateValue(url, {
            required: true,
            pattern: this.PATTERNS.URL,
            customValidator: (value)=>{
                try {
                    new URL(value);
                    return true;
                } catch  {
                    return false;
                }
            }
        });
    }
    /**
   * Validate UUID format
   * Single responsibility: UUID validation only
   */ static validateUUID(uuid) {
        return this.validateValue(uuid, {
            required: true,
            pattern: this.PATTERNS.UUID
        });
    }
    /**
   * Check if string contains dangerous content
   * Single responsibility: Dangerous content detection only
   */ static containsDangerousContent(input) {
        const lowerInput = input.toLowerCase();
        // Check for dangerous strings
        for (const dangerous of this.DANGEROUS_STRINGS){
            if (lowerInput.includes(dangerous.toLowerCase())) {
                return true;
            }
        }
        // Check for SQL injection patterns
        if (this.PATTERNS.SQL_INJECTION.test(input)) {
            return true;
        }
        // Check for XSS patterns
        if (this.PATTERNS.XSS.test(input)) {
            return true;
        }
        return false;
    }
    /**
   * Create validation schema for common WorkHub entities
   * Single responsibility: Schema creation only
   */ static createEmployeeValidationSchema() {
        return {
            firstName: {
                required: true,
                minLength: 2,
                maxLength: 50,
                pattern: this.PATTERNS.ALPHA,
                sanitizer: this.sanitizeForXSS
            },
            lastName: {
                required: true,
                minLength: 2,
                maxLength: 50,
                pattern: this.PATTERNS.ALPHA,
                sanitizer: this.sanitizeForXSS
            },
            email: {
                required: true,
                pattern: this.PATTERNS.EMAIL,
                maxLength: 254,
                sanitizer: (value)=>this.sanitizeForXSS(value.toLowerCase().trim())
            },
            phone: {
                required: false,
                pattern: this.PATTERNS.PHONE,
                sanitizer: (value)=>value?.replace(/\s/g, '') || ''
            },
            position: {
                required: true,
                minLength: 2,
                maxLength: 100,
                sanitizer: this.sanitizeForXSS
            }
        };
    }
    /**
   * Create validation schema for vehicle entities
   * Single responsibility: Vehicle schema creation only
   */ static createVehicleValidationSchema() {
        return {
            make: {
                required: true,
                minLength: 2,
                maxLength: 50,
                sanitizer: this.sanitizeForXSS
            },
            model: {
                required: true,
                minLength: 1,
                maxLength: 50,
                sanitizer: this.sanitizeForXSS
            },
            year: {
                required: true,
                pattern: /^\d{4}$/,
                customValidator: (value)=>{
                    const year = parseInt(value.toString());
                    return year >= 1900 && year <= new Date().getFullYear() + 1;
                }
            },
            licensePlate: {
                required: true,
                minLength: 2,
                maxLength: 20,
                sanitizer: (value)=>this.sanitizeForXSS(value.toUpperCase().trim())
            },
            vin: {
                required: false,
                minLength: 17,
                maxLength: 17,
                pattern: /^[A-HJ-NPR-Z0-9]{17}$/i,
                sanitizer: (value)=>value?.toUpperCase().trim() || ''
            }
        };
    }
}
}}),
"[project]/src/lib/api/security/hooks/useInputValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Input Validation Hook - DRY + Single Responsibility Principle (SRP)
 * @module hooks/useInputValidation
 *
 * This hook provides centralized input validation functionality following DRY principles.
 * It handles form validation, real-time validation, and security sanitization.
 *
 * SECURITY NOTE: This is the single source of input validation for all forms and user inputs.
 */ __turbopack_context__.s({
    "useEmployeeValidation": (()=>useEmployeeValidation),
    "useFieldValidation": (()=>useFieldValidation),
    "useInputValidation": (()=>useInputValidation),
    "useVehicleValidation": (()=>useVehicleValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/InputValidator.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useInputValidation(initialSchema) {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isValid: true,
        errors: {},
        touched: {},
        isValidating: false
    });
    /**
   * Validate single field
   * Single responsibility: Field validation only
   */ const validateField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field, value, rules)=>{
        setState((prev)=>({
                ...prev,
                isValidating: true
            }));
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateValue(value, rules);
        setState((prev)=>({
                ...prev,
                isValidating: false,
                errors: {
                    ...prev.errors,
                    [field]: result.isValid ? [] : result.errors
                },
                isValid: result.isValid && Object.values({
                    ...prev.errors,
                    [field]: result.isValid ? [] : result.errors
                }).every((fieldErrors)=>fieldErrors.length === 0)
            }));
        return result;
    }, []);
    /**
   * Validate entire form
   * Single responsibility: Form validation only
   */ const validateForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data, schema)=>{
        setState((prev)=>({
                ...prev,
                isValidating: true
            }));
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateObject(data, schema);
        // Parse field-specific errors from result
        const fieldErrors = {};
        result.errors.forEach((error)=>{
            const [field, ...messageParts] = error.split(': ');
            const message = messageParts.join(': ');
            if (field) {
                if (!fieldErrors[field]) {
                    fieldErrors[field] = [];
                }
                fieldErrors[field].push(message);
            }
        });
        setState((prev)=>({
                ...prev,
                isValidating: false,
                errors: fieldErrors,
                isValid: result.isValid
            }));
        return result;
    }, []);
    /**
   * Set field as touched
   * Single responsibility: Touch state management only
   */ const setFieldTouched = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field, touched = true)=>{
        setState((prev)=>({
                ...prev,
                touched: {
                    ...prev.touched,
                    [field]: touched
                }
            }));
    }, []);
    /**
   * Clear errors for specific field
   * Single responsibility: Field error clearing only
   */ const clearFieldErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field)=>{
        setState((prev)=>({
                ...prev,
                errors: {
                    ...prev.errors,
                    [field]: []
                },
                isValid: Object.values({
                    ...prev.errors,
                    [field]: []
                }).every((fieldErrors)=>fieldErrors.length === 0)
            }));
    }, []);
    /**
   * Clear all validation errors
   * Single responsibility: All errors clearing only
   */ const clearAllErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState((prev)=>({
                ...prev,
                errors: {},
                isValid: true
            }));
    }, []);
    /**
   * Reset validation state
   * Single responsibility: State reset only
   */ const resetValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState({
            isValid: true,
            errors: {},
            touched: {},
            isValidating: false
        });
    }, []);
    /**
   * Sanitize input for security
   * Single responsibility: Input sanitization only
   */ const sanitizeInput = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((input)=>{
        if (typeof input === 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].sanitizeForXSS(input);
        }
        if (Array.isArray(input)) {
            return input.map((item)=>sanitizeInput(item));
        }
        if (input && typeof input === 'object') {
            const sanitized = {};
            for (const [key, value] of Object.entries(input)){
                sanitized[key] = sanitizeInput(value);
            }
            return sanitized;
        }
        return input;
    }, []);
    /**
   * Get error message for specific field
   * Single responsibility: Error retrieval only
   */ const getFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field)=>{
        const fieldErrors = state.errors[field];
        return fieldErrors && fieldErrors.length > 0 ? fieldErrors[0] || null : null;
    }, [
        state.errors
    ]);
    /**
   * Check if field has errors
   * Single responsibility: Error existence check only
   */ const hasFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field)=>{
        const fieldErrors = state.errors[field];
        return fieldErrors ? fieldErrors.length > 0 : false;
    }, [
        state.errors
    ]);
    /**
   * Check if field has been touched
   * Single responsibility: Touch state check only
   */ const isFieldTouched = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((field)=>{
        return state.touched[field] || false;
    }, [
        state.touched
    ]);
    return {
        // State
        isValid: state.isValid,
        errors: state.errors,
        touched: state.touched,
        isValidating: state.isValidating,
        // Actions
        validateField,
        validateForm,
        setFieldTouched,
        clearFieldErrors,
        clearAllErrors,
        resetValidation,
        sanitizeInput,
        // Utility functions
        getFieldError,
        hasFieldError,
        isFieldTouched
    };
}
function useEmployeeValidation() {
    const schema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].createEmployeeValidationSchema();
    return useInputValidation(schema);
}
function useVehicleValidation() {
    const schema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].createVehicleValidationSchema();
    return useInputValidation(schema);
}
function useFieldValidation(rules) {
    const [fieldState, setFieldState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        value: '',
        error: null,
        isValid: true,
        isTouched: false
    });
    const validateAndSet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((value)=>{
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateValue(value, rules);
        setFieldState({
            value: result.sanitizedValue || value,
            error: result.errors.length > 0 ? result.errors[0] || null : null,
            isValid: result.isValid,
            isTouched: true
        });
        return result;
    }, [
        rules
    ]);
    const setValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((value)=>{
        setFieldState((prev)=>({
                ...prev,
                value,
                isTouched: true
            }));
    }, []);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setFieldState({
            value: '',
            error: null,
            isValid: true,
            isTouched: false
        });
    }, []);
    return {
        ...fieldState,
        validateAndSet,
        setValue,
        reset
    };
}
}}),
"[project]/src/lib/security/PermissionManager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Permission Management Service - Single Responsibility Principle (SRP)
 * @module lib/security/PermissionManager
 *
 * This class handles ONLY permission checking logic following SRP principles.
 * It provides centralized permission validation and role-based access control.
 *
 * SECURITY NOTE: This is the single source of truth for permission logic.
 */ __turbopack_context__.s({
    "PermissionManager": (()=>PermissionManager)
});
class PermissionManager {
    /**
   * Role hierarchy definition (higher roles inherit lower role permissions)
   */ static ROLE_HIERARCHY = {
        READONLY: 0,
        USER: 1,
        MANAGER: 2,
        ADMIN: 3,
        SUPER_ADMIN: 4
    };
    /**
   * Permission mappings for each role
   */ static ROLE_PERMISSIONS = {
        READONLY: [
            'read',
            'employees:read',
            'vehicles:read',
            'delegations:read',
            'tasks:read',
            'reports:read'
        ],
        USER: [
            'read',
            'employees:read',
            'vehicles:read',
            'delegations:read',
            'tasks:read',
            'reports:read',
            'settings:read'
        ],
        MANAGER: [
            'read',
            'write',
            'employees:read',
            'employees:write',
            'vehicles:read',
            'vehicles:write',
            'delegations:read',
            'delegations:write',
            'tasks:read',
            'tasks:write',
            'reports:read',
            'reports:write',
            'settings:read'
        ],
        ADMIN: [
            'read',
            'write',
            'delete',
            'employees:read',
            'employees:write',
            'employees:delete',
            'vehicles:read',
            'vehicles:write',
            'vehicles:delete',
            'delegations:read',
            'delegations:write',
            'delegations:delete',
            'tasks:read',
            'tasks:write',
            'tasks:delete',
            'reports:read',
            'reports:write',
            'settings:read',
            'settings:write'
        ],
        SUPER_ADMIN: [
            'read',
            'write',
            'delete',
            'admin',
            'employees:read',
            'employees:write',
            'employees:delete',
            'employees:admin',
            'vehicles:read',
            'vehicles:write',
            'vehicles:delete',
            'vehicles:admin',
            'delegations:read',
            'delegations:write',
            'delegations:delete',
            'delegations:admin',
            'tasks:read',
            'tasks:write',
            'tasks:delete',
            'tasks:admin',
            'reports:read',
            'reports:write',
            'reports:admin',
            'settings:read',
            'settings:write',
            'settings:admin',
            'system:admin',
            'system:audit',
            'system:backup'
        ]
    };
    /**
   * Check if user role has specific permission
   * Single responsibility: Permission checking only
   */ static hasPermission(userRole, requiredPermission) {
        // Normalize role
        const normalizedRole = this.normalizeRole(userRole);
        if (!normalizedRole) {
            return {
                hasPermission: false,
                reason: 'Invalid user role'
            };
        }
        // Get permissions for role
        const rolePermissions = this.ROLE_PERMISSIONS[normalizedRole];
        // Check direct permission
        if (rolePermissions.includes(requiredPermission)) {
            return {
                hasPermission: true
            };
        }
        // Check if higher role has permission through hierarchy
        const hasHierarchicalPermission = this.checkHierarchicalPermission(normalizedRole, requiredPermission);
        if (hasHierarchicalPermission) {
            return {
                hasPermission: true
            };
        }
        return {
            hasPermission: false,
            reason: `Access denied. Required role: ${normalizedRole || 'Unknown'}`,
            requiredRole: normalizedRole || 'USER'
        };
    }
    /**
   * Check if user role meets minimum role requirement
   * Single responsibility: Role hierarchy checking only
   */ static hasMinimumRole(userRole, minimumRole) {
        const normalizedUserRole = this.normalizeRole(userRole);
        if (!normalizedUserRole) {
            return false;
        }
        const userRoleLevel = this.ROLE_HIERARCHY[normalizedUserRole];
        const minimumRoleLevel = this.ROLE_HIERARCHY[minimumRole];
        return userRoleLevel >= minimumRoleLevel;
    }
    /**
   * Get all permissions for a user role
   * Single responsibility: Permission enumeration only
   */ static getPermissionsForRole(userRole) {
        const normalizedRole = this.normalizeRole(userRole);
        if (!normalizedRole) {
            return [];
        }
        return [
            ...this.ROLE_PERMISSIONS[normalizedRole]
        ];
    }
    /**
   * Check multiple permissions at once
   * Single responsibility: Batch permission checking only
   */ static hasAllPermissions(userRole, requiredPermissions) {
        for (const permission of requiredPermissions){
            const check = this.hasPermission(userRole, permission);
            if (!check.hasPermission) {
                return {
                    hasPermission: false,
                    reason: `Missing permission: ${permission}`,
                    requiredRole: check.requiredRole || 'USER'
                };
            }
        }
        return {
            hasPermission: true
        };
    }
    /**
   * Check if user has any of the specified permissions
   * Single responsibility: Alternative permission checking only
   */ static hasAnyPermission(userRole, requiredPermissions) {
        for (const permission of requiredPermissions){
            const check = this.hasPermission(userRole, permission);
            if (check.hasPermission) {
                return {
                    hasPermission: true
                };
            }
        }
        return {
            hasPermission: false,
            reason: `None of the required permissions found: ${requiredPermissions.join(', ')}`
        };
    }
    /**
   * Normalize role string to valid UserRole
   * Single responsibility: Role normalization only
   */ static normalizeRole(role) {
        if (!role || typeof role !== 'string') {
            return null;
        }
        const upperRole = role.toUpperCase();
        if (Object.keys(this.ROLE_HIERARCHY).includes(upperRole)) {
            return upperRole;
        }
        return null;
    }
    /**
   * Check hierarchical permissions
   * Single responsibility: Hierarchy-based permission checking only
   */ static checkHierarchicalPermission(userRole, permission) {
        // Check if any higher role has this permission
        const userRoleLevel = this.ROLE_HIERARCHY[userRole];
        for (const [role, level] of Object.entries(this.ROLE_HIERARCHY)){
            if (level > userRoleLevel) {
                const higherRolePermissions = this.ROLE_PERMISSIONS[role];
                if (higherRolePermissions.includes(permission)) {
                    return false; // Permission exists in higher role, user doesn't have it
                }
            }
        }
        return false;
    }
    /**
   * Get minimum role required for permission
   * Single responsibility: Minimum role determination only
   */ static getMinimumRoleForPermission(permission) {
        for (const [role, permissions] of Object.entries(this.ROLE_PERMISSIONS)){
            if (permissions.includes(permission)) {
                return role;
            }
        }
        return undefined;
    }
}
}}),
"[project]/src/lib/api/security/hooks/usePermissions.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Permissions Hook - Single Responsibility Principle (SRP)
 * @module lib/api/security/hooks/usePermissions
 *
 * This hook handles ONLY permission state management following SRP principles.
 * It provides permission checking functionality for components.
 *
 * SECURITY NOTE: This is the primary interface for permission checking in components.
 */ __turbopack_context__.s({
    "usePermissions": (()=>usePermissions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/PermissionManager.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function usePermissions() {
    const { user, userRole: contextUserRole, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    const isAuthenticated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return !!user && !loading;
    }, [
        user,
        loading
    ]);
    const userRole = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!isAuthenticated || !contextUserRole) {
            return null;
        }
        // Normalize role from context
        const normalizedRole = contextUserRole.toUpperCase();
        if ([
            'USER',
            'ADMIN',
            'SUPER_ADMIN'
        ].includes(normalizedRole)) {
            return normalizedRole;
        }
        return 'USER'; // Default fallback
    }, [
        isAuthenticated,
        contextUserRole
    ]);
    /**
   * Check if user has specific permission
   * Single responsibility: Permission checking only
   */ const hasPermission = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((permission)=>{
        if (!userRole) return false;
        const check = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasPermission(userRole, permission);
        return check.hasPermission;
    }, [
        userRole
    ]);
    /**
   * Check if user has all specified permissions
   * Single responsibility: Multiple permission checking only
   */ const hasAllPermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((permissions)=>{
        if (!userRole) return false;
        const check = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasAllPermissions(userRole, permissions);
        return check.hasPermission;
    }, [
        userRole
    ]);
    /**
   * Check if user has any of the specified permissions
   * Single responsibility: Alternative permission checking only
   */ const hasAnyPermission = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((permissions)=>{
        if (!userRole) return false;
        const check = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasAnyPermission(userRole, permissions);
        return check.hasPermission;
    }, [
        userRole
    ]);
    /**
   * Check if user meets minimum role requirement
   * Single responsibility: Role hierarchy checking only
   */ const hasMinimumRole = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((minimumRole)=>{
        if (!userRole) return false;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasMinimumRole(userRole, minimumRole);
    }, [
        userRole
    ]);
    /**
   * Get detailed permission check result
   * Single responsibility: Detailed permission information only
   */ const getPermissionCheck = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((permission)=>{
        if (!userRole) {
            return {
                hasPermission: false,
                reason: 'User not authenticated'
            };
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasPermission(userRole, permission);
    }, [
        userRole
    ]);
    /**
   * Get all permissions for current user role
   * Single responsibility: Permission enumeration only
   */ const getAllPermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!userRole) return [];
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].getPermissionsForRole(userRole);
    }, [
        userRole
    ]);
    // Convenience role checks
    const isUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>userRole === 'USER', [
        userRole
    ]);
    const isAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>userRole === 'ADMIN', [
        userRole
    ]);
    const isSuperAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>userRole === 'SUPER_ADMIN', [
        userRole
    ]);
    return {
        // Current user info
        userRole,
        isAuthenticated,
        // Permission checking functions
        hasPermission,
        hasAllPermissions,
        hasAnyPermission,
        hasMinimumRole,
        // Permission details
        getPermissionCheck,
        getAllPermissions,
        // Convenience role checks
        isUser,
        isAdmin,
        isSuperAdmin
    };
}
}}),
"[project]/src/lib/security/SecureStorage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Secure Storage Service - Single Responsibility Principle (SRP)
 * @module lib/security/SecureStorage
 *
 * This class handles ONLY secure storage operations following SRP principles.
 * It provides a centralized interface for secure cookie management and storage operations.
 *
 * SECURITY NOTE: This class manages httpOnly cookie interactions and secure storage patterns.
 * It does NOT handle token validation or authentication logic.
 */ __turbopack_context__.s({
    "SecureStorage": (()=>SecureStorage)
});
class SecureStorage {
    static DEFAULT_COOKIE_OPTIONS = {
        httpOnly: true,
        secure: ("TURBOPACK compile-time value", "development") === 'production',
        sameSite: 'lax',
        path: '/'
    };
    /**
   * SECURITY FIX: Renamed from setSecureItem to clarify limitations
   *
   * Sets a CLIENT-SIDE ONLY cookie (NOT httpOnly)
   *
   * ⚠️  CRITICAL SECURITY WARNING:
   * - This method CANNOT set httpOnly cookies (browser security restriction)
   * - This method should NEVER be used for sensitive data like tokens
   * - Sensitive tokens MUST be set by the server as httpOnly cookies
   * - This is only for non-sensitive client-side preferences/settings
   *
   * Single responsibility: Non-httpOnly cookie setting only
   */ static setClientSideCookie(key, value, options) {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return {
                    success: false,
                    error: 'Not available in server-side environment'
                };
            }
            "TURBOPACK unreachable";
            const cookieOptions = undefined;
            // Build cookie string (for non-httpOnly cookies only)
            let cookieString;
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to set client-side cookie'
            };
        }
    }
    /**
   * Get secure item from cookie
   * Single responsibility: Cookie retrieval only
   */ static getSecureItem(key) {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return null;
            }
            "TURBOPACK unreachable";
            const cookies = undefined;
        } catch (error) {
            console.error('Failed to get secure item:', error);
            return null;
        }
    }
    /**
   * Remove secure item from cookie
   * Single responsibility: Cookie removal only
   */ static removeSecureItem(key, options) {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return {
                    success: false,
                    error: 'Not available in server-side environment'
                };
            }
            "TURBOPACK unreachable";
            const cookieOptions = undefined;
            // Set cookie with past expiration date to remove it
            let cookieString;
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to remove secure item'
            };
        }
    }
    /**
   * Check if secure storage is available
   * Single responsibility: Storage availability check only
   */ static isAvailable() {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return false;
            }
            "TURBOPACK unreachable";
            // Test cookie functionality
            const testKey = undefined;
            const testValue = undefined;
            const retrieved = undefined;
        } catch  {
            return false;
        }
    }
    /**
   * Clear all non-httpOnly cookies (for logout)
   * Single responsibility: Cookie clearing only
   */ static clearAllCookies() {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return {
                    success: false,
                    error: 'Not available in server-side environment'
                };
            }
            "TURBOPACK unreachable";
            const cookies = undefined;
            const cookie = undefined;
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to clear cookies'
            };
        }
    }
    /**
   * Get all available cookies as key-value pairs
   * Single responsibility: Cookie enumeration only
   */ static getAllCookies() {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return {};
            }
            "TURBOPACK unreachable";
        } catch  {
            return {};
        }
    }
    /**
   * Check if a specific cookie exists
   * Single responsibility: Cookie existence check only
   */ static hasCookie(key) {
        return this.getSecureItem(key) !== null;
    }
    /**
   * Get cookie expiration time (if available in cookie value)
   * Single responsibility: Cookie expiration check only
   */ static getCookieExpiration(_key) {
        // Note: Cookie expiration is not accessible from client-side JavaScript
        // This method is for future extensibility if needed
        return null;
    }
}
}}),
"[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Session Management Service - Single Responsibility Principle (SRP)
 * @module lib/security/SessionManager
 *
 * This class handles ONLY session security operations following SRP principles.
 * It provides session timeout detection, cross-tab communication, and concurrent session management.
 *
 * SECURITY NOTE: This manages session security without handling authentication logic.
 */ __turbopack_context__.s({
    "SessionManager": (()=>SessionManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
;
// Define constants locally to avoid circular dependency
const SECURITY_CONSTANTS = {
    MAX_CONCURRENT_SESSIONS: 5,
    SESSION_TIMEOUT_MINUTES: 30
};
const SECURITY_EVENTS = {
    CROSS_TAB_LOGOUT: 'cross_tab_logout',
    SESSION_INVALID: 'session_invalid',
    SESSION_TIMEOUT: 'session_timeout',
    SESSION_VALIDATED: 'session_validated',
    TOKEN_REFRESH_FAILED: 'token_refresh_failed',
    TOKEN_REFRESH_SUCCESS: 'token_refresh_success'
};
class SessionManager {
    static activityListeners = [];
    static BROADCAST_CHANNEL_NAME = 'workhub_session_events';
    static broadcastChannel = null;
    static sessionCheckInterval = null;
    static STORAGE_KEYS = {
        CONCURRENT_SESSIONS: 'workhub_concurrent_sessions',
        LAST_ACTIVITY: 'workhub_last_activity',
        SESSION_ID: 'workhub_session_id',
        SESSION_STATE: 'workhub_session_state'
    };
    /**
   * Add session event listener
   * Single responsibility: Event listener management only
   */ static addSessionEventListener(callback) {
        if (globalThis.window === undefined) return ()=>{};
        const handler = (event)=>{
            callback(event.data);
        };
        this.broadcastChannel?.addEventListener('message', handler);
        // Return cleanup function
        return ()=>{
            this.broadcastChannel?.removeEventListener('message', handler);
        };
    }
    /**
   * Cleanup session management
   * Single responsibility: Cleanup only
   */ static cleanup() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
            this.sessionCheckInterval = null;
        }
        this.broadcastChannel?.close();
        this.broadcastChannel = null;
        // Remove activity listeners
        for (const cleanup of this.activityListeners)cleanup();
        this.activityListeners = [];
    }
    /**
   * Clear session state
   * Single responsibility: Session state clearing only
   */ static clearSessionState() {
        if (globalThis.window === undefined) return;
        for (const key of Object.values(this.STORAGE_KEYS)){
            localStorage.removeItem(key);
        }
    }
    /**
   * Detect and handle session state conflicts
   * Single responsibility: Conflict detection and resolution only
   */ static detectAndResolveConflicts() {
        if (globalThis.window === undefined) return true;
        try {
            const sessionState = this.getSessionState();
            const lastActivity = this.getLastActivity();
            const concurrentSessions = this.getConcurrentSessions();
            // Check for timestamp conflicts
            if (sessionState && lastActivity) {
                const stateDiff = Math.abs(sessionState.lastActivity.getTime() - lastActivity.getTime());
                // If timestamps differ by more than 5 minutes, there's a conflict
                if (stateDiff > 5 * 60 * 1000) {
                    console.warn('⚠️ Session timestamp conflict detected, resolving...');
                    // Use the more recent timestamp (proper Date comparison)
                    const recentTime = sessionState.lastActivity.getTime() > lastActivity.getTime() ? sessionState.lastActivity : lastActivity;
                    // Update both to the more recent time
                    localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY, recentTime.toISOString());
                    this.setSessionState({
                        ...sessionState,
                        lastActivity: recentTime
                    });
                    console.log('✅ Session timestamp conflict resolved');
                }
            }
            // Check for duplicate sessions
            const currentSessionId = this.getCurrentSessionId();
            const duplicates = concurrentSessions.filter((s)=>s.sessionId === currentSessionId);
            if (duplicates.length > 1) {
                console.warn('⚠️ Duplicate session entries detected, cleaning up...');
                // Keep only the most recent one
                const mostRecent = duplicates.reduce((latest, current)=>current.lastActivity > latest.lastActivity ? current : latest);
                const cleanedSessions = concurrentSessions.filter((s)=>s.sessionId !== currentSessionId);
                cleanedSessions.push(mostRecent);
                this.setConcurrentSessions(cleanedSessions);
                console.log('✅ Duplicate sessions cleaned up');
            }
            return true;
        } catch (error) {
            console.error('Failed to detect and resolve conflicts:', error);
            return false;
        }
    }
    /**
   * Detect session timeout
   * Single responsibility: Timeout detection only
   */ static detectTimeout() {
        if (globalThis.window === undefined) return false;
        const lastActivity = this.getLastActivity();
        if (!lastActivity) return true;
        const timeoutThreshold = SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000;
        const timeSinceActivity = Date.now() - lastActivity.getTime();
        return timeSinceActivity > timeoutThreshold;
    }
    /**
   * Get current session ID
   * Single responsibility: Session ID retrieval only
   */ static getCurrentSessionId() {
        if (globalThis.window === undefined) return '';
        let sessionId = localStorage.getItem(this.STORAGE_KEYS.SESSION_ID);
        if (!sessionId) {
            sessionId = this.generateSessionId();
            localStorage.setItem(this.STORAGE_KEYS.SESSION_ID, sessionId);
        }
        return sessionId;
    }
    /**
   * Get current session state
   * Single responsibility: Session state retrieval only
   */ static getSessionState() {
        if (globalThis.window === undefined) return null;
        try {
            const stateJson = localStorage.getItem(this.STORAGE_KEYS.SESSION_STATE);
            if (!stateJson) return null;
            const state = JSON.parse(stateJson);
            return {
                ...state,
                expiresAt: new Date(state.expiresAt),
                lastActivity: new Date(state.lastActivity)
            };
        } catch  {
            return null;
        }
    }
    /**
   * Handle cross-tab logout
   * Single responsibility: Cross-tab communication only
   */ static handleCrossTabLogout() {
        if (globalThis.window === undefined) return;
        const sessionEvent = {
            sessionId: this.getCurrentSessionId(),
            timestamp: new Date(),
            type: SECURITY_EVENTS.CROSS_TAB_LOGOUT
        };
        this.broadcastSessionEvent(sessionEvent);
        this.clearSessionState();
    }
    /**
   * Handle session validation events from TokenRefreshService
   * Single responsibility: Session validation event handling only
   */ static handleSessionValidation(isValid, data) {
        if (globalThis.window === undefined) return;
        const sessionEvent = {
            data,
            sessionId: this.getCurrentSessionId(),
            timestamp: new Date(),
            type: isValid ? SECURITY_EVENTS.SESSION_VALIDATED : SECURITY_EVENTS.SESSION_INVALID
        };
        this.broadcastSessionEvent(sessionEvent);
        if (isValid) {
            // If session is valid, update activity
            this.updateActivity();
        } else {
            // If session is invalid, clear session state
            this.clearSessionState();
        }
    }
    /**
   * Handle token refresh events from TokenRefreshService
   * Single responsibility: Token refresh event handling only
   */ static handleTokenRefresh(success, data) {
        if (globalThis.window === undefined) return;
        const sessionEvent = {
            data,
            sessionId: this.getCurrentSessionId(),
            timestamp: new Date(),
            type: success ? SECURITY_EVENTS.TOKEN_REFRESH_SUCCESS : SECURITY_EVENTS.TOKEN_REFRESH_FAILED
        };
        this.broadcastSessionEvent(sessionEvent);
        if (success) {
            // If token refresh succeeded, update activity and session state
            this.updateActivity();
            const currentState = this.getSessionState();
            if (currentState) {
                // Extend session expiration
                const now = new Date();
                const expiresAt = new Date(now.getTime() + SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000);
                this.setSessionState({
                    ...currentState,
                    expiresAt,
                    lastActivity: now
                });
            }
        }
    }
    /**
   * Initialize session management
   * Single responsibility: Session initialization only
   */ static initialize() {
        if (globalThis.window === undefined) return;
        // Initialize broadcast channel for cross-tab communication
        this.initializeBroadcastChannel();
        // Start session monitoring
        this.startSessionMonitoring();
        // Setup activity tracking
        this.setupActivityTracking();
        // Initialize session state
        this.initializeSessionState();
    }
    /**
   * Manage concurrent sessions
   * Single responsibility: Concurrent session tracking only
   */ static manageConcurrentSessions() {
        if (globalThis.window === undefined) return;
        const currentSessions = this.getConcurrentSessions();
        const currentSessionId = this.getCurrentSessionId();
        // Add current session if not exists
        if (!currentSessions.find((s)=>s.sessionId === currentSessionId)) {
            const newSession = {
                lastActivity: new Date(),
                sessionId: currentSessionId,
                startTime: new Date(),
                userAgent: navigator.userAgent
            };
            currentSessions.push(newSession);
        }
        // Update last activity for current session
        const currentSession = currentSessions.find((s)=>s.sessionId === currentSessionId);
        if (currentSession) {
            currentSession.lastActivity = new Date();
        }
        // Remove expired sessions
        const validSessions = currentSessions.filter((session)=>{
            const timeoutThreshold = SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000;
            const timeSinceActivity = Date.now() - session.lastActivity.getTime();
            return timeSinceActivity <= timeoutThreshold;
        });
        // Enforce maximum concurrent sessions
        if (validSessions.length > SECURITY_CONSTANTS.MAX_CONCURRENT_SESSIONS) {
            // Keep most recent sessions
            validSessions.sort((a, b)=>b.lastActivity.getTime() - a.lastActivity.getTime());
            validSessions.splice(SECURITY_CONSTANTS.MAX_CONCURRENT_SESSIONS);
        }
        this.setConcurrentSessions(validSessions);
    }
    /**
   * Perform comprehensive session integrity check
   * Single responsibility: Session integrity validation only
   */ static async performIntegrityCheck() {
        if (globalThis.window === undefined) return true;
        try {
            // 1. Check local session consistency
            const isConsistent = this.validateSessionConsistency();
            if (!isConsistent) {
                console.warn('📊 Local session state is inconsistent');
                return false;
            }
            // 2. Validate against backend (lightweight check)
            const backendValid = await this.validateWithBackend();
            if (!backendValid) {
                console.warn('🔗 Backend session validation failed');
                return false;
            }
            // 3. Check for stale concurrent sessions
            this.cleanupStaleSessions();
            console.log('✅ Session integrity check passed');
            return true;
        } catch (error) {
            console.error('❌ Session integrity check failed:', error);
            return false;
        }
    }
    // State Coordination and Validation Methods
    /**
   * Recover from corrupted session state
   * Single responsibility: Session state recovery only
   */ static recoverFromCorruptedState() {
        if (globalThis.window === undefined) return true;
        try {
            console.log('🔧 Attempting session state recovery...');
            // 1. Validate current state
            const isConsistent = this.validateSessionConsistency();
            if (isConsistent) {
                console.log('✅ Session state is already consistent');
                return true;
            }
            // 2. Perform selective cleanup
            const preservedData = this.preserveNonSecurityData();
            // 3. Clear corrupted session data
            this.clearSessionState();
            // 4. Restore preserved data
            this.restorePreservedData(preservedData);
            // 5. Re-initialize session state
            this.initializeSessionState();
            console.log('✅ Session state recovery completed');
            return true;
        } catch (error) {
            console.error('❌ Session state recovery failed:', error);
            return false;
        }
    }
    /**
   * Set session state
   * Single responsibility: Session state storage only
   */ static setSessionState(state) {
        if (globalThis.window === undefined) return;
        try {
            localStorage.setItem(this.STORAGE_KEYS.SESSION_STATE, JSON.stringify(state));
        } catch (error) {
            console.error('Failed to set session state:', error);
        }
    }
    /**
   * Update activity timestamp
   * Single responsibility: Activity tracking only
   */ static updateActivity() {
        if (globalThis.window === undefined) return;
        const now = new Date();
        localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY, now.toISOString());
        // Update concurrent sessions
        this.manageConcurrentSessions();
    }
    /**
   * Validate session state consistency
   * Single responsibility: Session state validation only
   */ static validateSessionConsistency() {
        if (globalThis.window === undefined) return true;
        try {
            const sessionState = this.getSessionState();
            const lastActivity = this.getLastActivity();
            const sessionId = this.getCurrentSessionId();
            // Check for missing or inconsistent data
            if (sessionState && !lastActivity) {
                console.warn('🔍 Session state exists but no last activity found');
                return false;
            }
            if (lastActivity && !sessionId) {
                console.warn('🔍 Last activity exists but no session ID found');
                return false;
            }
            if (sessionState && sessionState.sessionId !== sessionId) {
                console.warn('🔍 Session state ID mismatch with current session ID');
                return false;
            }
            // Check for expired session state
            if (sessionState && sessionState.expiresAt < new Date()) {
                console.warn('🔍 Session state has expired');
                return false;
            }
            return true;
        } catch (error) {
            console.error('Failed to validate session consistency:', error);
            return false;
        }
    }
    // Private helper methods for state coordination
    static broadcastSessionEvent(event) {
        this.broadcastChannel?.postMessage(event);
    }
    /**
   * Clean up stale concurrent sessions
   * Single responsibility: Stale session cleanup only
   */ static cleanupStaleSessions() {
        try {
            const sessions = this.getConcurrentSessions();
            const now = Date.now();
            const timeoutThreshold = SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000;
            const activeSessions = sessions.filter((session)=>{
                const timeSinceActivity = now - session.lastActivity.getTime();
                return timeSinceActivity <= timeoutThreshold;
            });
            if (activeSessions.length !== sessions.length) {
                console.log(`🧹 Cleaned up ${sessions.length - activeSessions.length} stale sessions`);
                this.setConcurrentSessions(activeSessions);
            }
        } catch (error) {
            console.warn('Failed to cleanup stale sessions:', error);
        }
    }
    static generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
    }
    static getConcurrentSessions() {
        try {
            const sessionsJson = localStorage.getItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS);
            if (!sessionsJson) return [];
            const sessions = JSON.parse(sessionsJson);
            return sessions.map((s)=>({
                    ...s,
                    lastActivity: new Date(s.lastActivity),
                    startTime: new Date(s.startTime)
                }));
        } catch  {
            return [];
        }
    }
    // Existing private helper methods
    static getLastActivity() {
        const activityString = localStorage.getItem(this.STORAGE_KEYS.LAST_ACTIVITY);
        return activityString ? new Date(activityString) : null;
    }
    static handleSessionTimeout() {
        const sessionEvent = {
            sessionId: this.getCurrentSessionId(),
            timestamp: new Date(),
            type: SECURITY_EVENTS.SESSION_TIMEOUT
        };
        this.broadcastSessionEvent(sessionEvent);
        this.clearSessionState();
    }
    static initializeBroadcastChannel() {
        if (typeof BroadcastChannel !== 'undefined') {
            this.broadcastChannel = new BroadcastChannel(this.BROADCAST_CHANNEL_NAME);
        }
    }
    static initializeSessionState() {
        const sessionId = this.getCurrentSessionId();
        const now = new Date();
        const expiresAt = new Date(now.getTime() + SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000);
        const initialState = {
            expiresAt,
            isActive: true,
            lastActivity: now,
            sessionId
        };
        this.setSessionState(initialState);
        this.updateActivity();
    }
    /**
   * Preserve non-security data during recovery
   * Single responsibility: Data preservation only
   */ static preserveNonSecurityData() {
        const preservedKeys = [
            'workhub-app-store',
            'workhub_user_preferences'
        ];
        const preserved = {};
        for (const key of preservedKeys){
            try {
                preserved[key] = localStorage.getItem(key);
            } catch (error) {
                console.warn(`Failed to preserve data for key ${key}:`, error);
            }
        }
        return preserved;
    }
    /**
   * Restore preserved data after recovery
   * Single responsibility: Data restoration only
   */ static restorePreservedData(preservedData) {
        for (const [key, value] of Object.entries(preservedData)){
            if (value !== null) {
                try {
                    localStorage.setItem(key, value);
                } catch (error) {
                    console.warn(`Failed to restore data for key ${key}:`, error);
                }
            }
        }
    }
    static setConcurrentSessions(sessions) {
        try {
            localStorage.setItem(this.STORAGE_KEYS.CONCURRENT_SESSIONS, JSON.stringify(sessions));
        } catch (error) {
            console.error('Failed to set concurrent sessions:', error);
        }
    }
    static setupActivityTracking() {
        const events = [
            'mousedown',
            'mousemove',
            'keypress',
            'scroll',
            'touchstart',
            'click'
        ];
        const activityHandler = ()=>{
            this.updateActivity();
        };
        for (const event of events){
            document.addEventListener(event, activityHandler, {
                passive: true
            });
            // Store cleanup function
            this.activityListeners.push(()=>{
                document.removeEventListener(event, activityHandler);
            });
        }
    }
    static startSessionMonitoring() {
        this.sessionCheckInterval = setInterval(()=>{
            if (this.detectTimeout()) {
                this.handleSessionTimeout();
            }
        }, 60_000); // Check every minute
    }
    /**
   * Validate session with backend (lightweight check)
   * Single responsibility: Backend validation only
   */ static async validateWithBackend() {
        try {
            const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
            const backendUrl = envConfig.apiBaseUrl;
            // Add a small delay to allow authentication cookies to be set
            await new Promise((resolve)=>setTimeout(resolve, 200));
            // Backend connectivity check - validate that backend is accessible
            // This is a lightweight check that doesn't require authentication
            // Use health endpoint for lightweight backend connectivity check
            // This validates that the backend is reachable without requiring authentication
            const response = await fetch(`${backendUrl}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                cache: 'no-cache'
            });
            if (response.ok) {
                console.log('✅ Backend connectivity validation successful');
                return true;
            } else {
                console.log(`🔍 Backend connectivity check failed with status: ${response.status}`);
                return false;
            }
        } catch (error) {
            console.warn('Backend validation failed:', error);
            return true; // Don't block authentication flow on network errors
        }
    }
} // NOTE: Any decryption of sensitive session data or JWTs should occur on the server-side
 // where cryptographic keys can be securely managed and protected.
 // Client-side base64 decoding (as was previously here) does NOT provide encryption.
}}),
"[project]/src/lib/security/TokenManager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Token Management Service - Single Responsibility Principle (SRP)
 * @module lib/security/TokenManager
 *
 * This class handles ONLY token-related operations following SRP principles.
 * It provides a centralized interface for token extraction, validation, and management.
 *
 * SECURITY NOTE: With httpOnly cookies, tokens are NOT accessible via JavaScript.
 * This class primarily serves as a validation and utility layer.
 */ __turbopack_context__.s({
    "TokenManager": (()=>TokenManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jwt-decode/build/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)");
;
;
class TokenManager {
    /**
   * Extract token from Authorization header
   * Single responsibility: Token extraction only
   */ static extractTokenFromHeader(authHeader) {
        if (!authHeader) return null;
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return null;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(parts[1]);
    }
    /**
   * Extract token from cookie string
   * Single responsibility: Cookie token extraction only
   */ static extractTokenFromCookie(cookieString, cookieName = 'sb-access-token') {
        if (!cookieString) return null;
        const cookies = {};
        cookieString.split(';').forEach((cookie)=>{
            const [key, value] = cookie.trim().split('=');
            if (key && value) {
                cookies[key] = decodeURIComponent(value);
            }
        });
        return cookies[cookieName] ?? null;
    }
    /**
   * Validate JWT token structure and expiration
   * Single responsibility: Token validation only
   */ static validateToken(token) {
        try {
            if (!token || typeof token !== 'string') {
                return {
                    isValid: false,
                    isExpired: true,
                    payload: null,
                    error: 'Invalid token format'
                };
            }
            const payload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jwt$2d$decode$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jwtDecode"])(token);
            const currentTime = Math.floor(Date.now() / 1000);
            const isExpired = payload.exp < currentTime;
            const result = {
                isValid: !isExpired,
                isExpired,
                payload
            };
            if (isExpired) {
                result.error = 'Token expired';
            }
            return result;
        } catch (error) {
            return {
                isValid: false,
                isExpired: true,
                payload: null,
                error: error instanceof Error ? error.message : 'Token validation failed'
            };
        }
    }
    /**
   * Extract user role from token payload
   * Single responsibility: Role extraction only
   *
   * Checks multiple locations for role data:
   * 1. custom_claims.user_role (Supabase auth hook structure)
   * 2. user_role (direct property)
   * 3. Defaults to 'USER'
   */ static extractUserRole(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid || !validation.payload) {
            return null;
        }
        // Check custom_claims first (Supabase auth hook structure)
        const customClaimsRole = validation.payload.custom_claims?.user_role;
        if (customClaimsRole) {
            return customClaimsRole;
        }
        // Fallback to direct property
        return validation.payload.user_role ?? 'USER';
    }
    /**
   * Extract employee ID from token payload
   * Single responsibility: Employee ID extraction only
   */ static extractEmployeeId(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid || !validation.payload) {
            return null;
        }
        return validation.payload.custom_claims?.employee_id ?? validation.payload.employee_id ?? null;
    }
    /**
   * Check if user is active from token payload
   * Single responsibility: Active status check only
   */ static isUserActive(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid || !validation.payload) {
            return false;
        }
        return validation.payload.custom_claims?.is_active ?? validation.payload.is_active ?? true;
    }
    /**
   * Get token expiration time
   * Single responsibility: Expiration time extraction only
   */ static getTokenExpiration(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid || !validation.payload) {
            return null;
        }
        return new Date(validation.payload.exp * 1000);
    }
    /**
   * Check if token will expire within specified minutes
   * Single responsibility: Expiration proximity check only
   */ static willExpireSoon(token, minutesThreshold = 5) {
        const expiration = this.getTokenExpiration(token);
        if (!expiration) return true;
        const thresholdTime = new Date(Date.now() + minutesThreshold * 60 * 1000);
        return expiration <= thresholdTime;
    }
    /**
   * Generate a secure hash of token for logging purposes
   * Single responsibility: Token hashing for security logging only
   */ static hashTokenForLogging(token) {
        if (!token) return 'no-token';
        // Create a simple hash for logging (first 8 chars + last 8 chars)
        if (token.length < 16) return 'short-token';
        return `${token.substring(0, 8)}...${token.substring(token.length - 8)}`;
    }
    /**
   * Validate token format without decoding
   * Single responsibility: Format validation only
   */ static isValidTokenFormat(token) {
        if (!token || typeof token !== 'string') return false;
        // JWT tokens have 3 parts separated by dots
        const parts = token.split('.');
        return parts.length === 3 && parts.every((part)=>part.length > 0);
    }
}
}}),
"[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Module Exports - DRY Principle
 * @module lib/security
 *
 * Centralized exports for all security-related modules following DRY principles.
 * This provides a single import point for security functionality across the application.
 */ __turbopack_context__.s({
    "SECURITY_CONSTANTS": (()=>SECURITY_CONSTANTS),
    "SECURITY_EVENTS": (()=>SECURITY_EVENTS),
    "SecurityUtils": (()=>SecurityUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/CSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/InputValidator.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/PermissionManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SecureStorage.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
// Import classes for SecurityUtils object
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/TokenManager.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
// Internal circuit breaker state
let circuitBreakerState = {
    attempts: 0,
    lastAttemptTime: 0,
    isOpen: false,
    activeOperations: new Set(),
    lastOperationTime: 0
};
const SecurityUtils = {
    attachCSRFToRequest: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].attachToRequest,
    clearAllCookies: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecureStorage"].clearAllCookies,
    // Session utilities
    detectTimeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].detectTimeout,
    escapeForDisplay: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].escapeForDisplay,
    extractEmployeeId: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].extractEmployeeId,
    extractUserRole: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].extractUserRole,
    // CSRF utilities
    generateCSRFToken: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].generateToken,
    getCurrentSessionId: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getCurrentSessionId,
    getPermissionsForRole: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].getPermissionsForRole,
    // Storage utilities (SECURITY FIX: Updated method names for clarity)
    getSecureItem: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecureStorage"].getSecureItem,
    hasMinimumRole: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasMinimumRole,
    // Permission utilities
    hasPermission: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PermissionManager"].hasPermission,
    isCSRFRequired: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].isProtectionRequired,
    isStorageAvailable: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecureStorage"].isAvailable,
    isUserActive: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].isUserActive,
    removeSecureItem: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecureStorage"].removeSecureItem,
    sanitizeForXSS: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].sanitizeForXSS,
    setClientSideCookie: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecureStorage"].setClientSideCookie,
    updateActivity: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].updateActivity,
    validateCSRFToken: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CSRFProtection"].validateToken,
    validateEmail: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateEmail,
    validateObject: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateObject,
    validatePhone: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validatePhone,
    // Token utilities
    validateToken: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].validateToken,
    validateURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateURL,
    validateUUID: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateUUID,
    // Input validation utilities
    validateValue: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InputValidator"].validateValue,
    willExpireSoon: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].willExpireSoon,
    // Circuit Breaker Pattern - Verification Loop Prevention
    /**
   * Check if security operations can be performed (circuit breaker check)
   * Single responsibility: Circuit breaker state validation only
   */ canPerformSecurityCheck () {
        if ("TURBOPACK compile-time truthy", 1) return true;
        "TURBOPACK unreachable";
        const now = undefined;
    },
    /**
   * Record a security verification attempt (circuit breaker tracking)
   * Single responsibility: Attempt tracking only
   */ recordSecurityAttempt () {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const now = undefined;
    },
    /**
   * Record successful security operation (reset circuit breaker)
   * Single responsibility: Success tracking only
   */ recordSecuritySuccess () {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    /**
   * Start a security operation (coordination)
   * Single responsibility: Operation coordination only
   */ startSecurityOperation (operationId) {
        if ("TURBOPACK compile-time truthy", 1) return true;
        "TURBOPACK unreachable";
    },
    /**
   * End a security operation (coordination)
   * Single responsibility: Operation cleanup only
   */ endSecurityOperation (operationId) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    /**
   * Check if circuit breaker is currently open
   * Single responsibility: Circuit state check only
   */ isCircuitOpen () {
        return circuitBreakerState.isOpen;
    },
    /**
   * Get current circuit breaker state for monitoring
   * Single responsibility: State inspection only
   */ getCircuitBreakerState () {
        return {
            attemptCount: circuitBreakerState.attempts,
            lastAttempt: circuitBreakerState.lastAttemptTime,
            isOpen: circuitBreakerState.isOpen,
            activeOperations: Array.from(circuitBreakerState.activeOperations),
            lastOperationTime: circuitBreakerState.lastOperationTime
        };
    },
    /**
   * Force security state reset (emergency recovery)
   * Single responsibility: Emergency state reset only
   */ forceSecurityReset () {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    /**
   * Reset circuit breaker state for testing purposes
   * Single responsibility: Test state reset only
   */ resetCircuitBreakerForTesting () {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    /**
   * Initialize circuit breaker from stored state (on app startup)
   * Single responsibility: State initialization only
   */ initializeCircuitBreaker () {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
};
const SECURITY_CONSTANTS = {
    DEFAULT_COOKIE_NAME: 'sb-access-token',
    LOGOUT_EVENT_KEY: 'workhub-logout-event',
    MAX_CONCURRENT_SESSIONS: 5,
    REFRESH_COOKIE_NAME: 'sb-refresh-token',
    SESSION_TIMEOUT_MINUTES: 30,
    TOKEN_EXPIRY_THRESHOLD_MINUTES: 5,
    // Circuit breaker constants
    CIRCUIT_BREAKER_MAX_ATTEMPTS: 3,
    CIRCUIT_BREAKER_RESET_TIMEOUT: 30000,
    SECURITY_OPERATION_COOLDOWN: 5000,
    VERIFICATION_LOOP_STORAGE_KEY: 'workhub_verification_attempts',
    SECURITY_OPERATIONS_STORAGE_KEY: 'workhub_active_operations'
};
const SECURITY_EVENTS = {
    CROSS_TAB_LOGOUT: 'cross_tab_logout',
    SECURITY_VIOLATION: 'security_violation',
    SESSION_TIMEOUT: 'session_timeout',
    TOKEN_EXPIRED: 'token_expired',
    TOKEN_REFRESH_FAILED: 'token_refresh_failed',
    UNAUTHORIZED_ACCESS: 'unauthorized_access'
};
}}),
"[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/CSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$InputValidator$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/InputValidator.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$PermissionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/PermissionManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SecureStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SecureStorage.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/TokenManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/security/composer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Composer - Orchestrates security hooks directly
 * @module api/security/composer
 *
 * This composer uses the moved security hooks directly without wrapper layers,
 * following DRY principles by leveraging existing security infrastructure.
 *
 * Phase 2 Enhancement: Now integrates with SecurityUtils and SECURITY_CONSTANTS
 */ __turbopack_context__.s({
    "SecurityComposer": (()=>SecurityComposer),
    "createSecurityComposer": (()=>createSecurityComposer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
;
class SecurityComposer {
    securityFeatures;
    config;
    constructor(securityFeatures, config = {}){
        this.securityFeatures = securityFeatures;
        this.config = {
            csrf: {
                enabled: true,
                tokenHeader: 'X-CSRF-Token',
                excludePaths: []
            },
            tokenValidation: {
                enabled: true,
                refreshThreshold: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].TOKEN_EXPIRY_THRESHOLD_MINUTES * 60,
                autoRefresh: true
            },
            inputSanitization: {
                enabled: true,
                sanitizers: [
                    'xss',
                    'sql'
                ]
            },
            authentication: {
                enabled: true,
                autoLogout: true,
                redirectOnFailure: true
            },
            ...config
        };
    }
    /**
   * Process request with all enabled security features
   * Uses the original security hooks directly - no wrapper layers
   */ async processRequest(config, context) {
        let processedConfig = {
            ...config
        };
        try {
            // 1. Authentication validation using moved hooks
            if (this.config.authentication?.enabled && this.securityFeatures.sessionSecurity) {
                if (!this.securityFeatures.sessionSecurity.isSessionActive) {
                    throw new Error('Authentication required for secure API calls');
                }
            }
            // 2. Token validation & refresh using moved hooks
            if (this.config.tokenValidation?.enabled && this.securityFeatures.tokenManagement) {
                const { isTokenValid, isTokenExpired, refreshToken } = this.securityFeatures.tokenManagement;
                if (!isTokenValid || isTokenExpired) {
                    console.log('🔄 SecurityComposer: Token invalid/expired, attempting refresh...');
                    const refreshSuccess = await refreshToken();
                    if (!refreshSuccess) {
                        throw new Error('Token refresh failed - authentication required');
                    }
                    console.log('✅ SecurityComposer: Token refreshed successfully');
                }
            }
            // 3. Input sanitization using moved hooks
            if (this.config.inputSanitization?.enabled && this.securityFeatures.inputValidation && processedConfig.body) {
                const { sanitizeInput } = this.securityFeatures.inputValidation;
                processedConfig.body = sanitizeInput(processedConfig.body);
                console.debug('🧹 SecurityComposer: Input sanitized using moved hooks');
            }
            // 4. CSRF protection using moved hooks (for state-changing operations)
            if (this.config.csrf?.enabled && this.securityFeatures.csrfProtection) {
                const method = processedConfig.method?.toUpperCase();
                if ([
                    'POST',
                    'PUT',
                    'PATCH',
                    'DELETE'
                ].includes(method || '')) {
                    const { attachCSRF } = this.securityFeatures.csrfProtection;
                    // Convert to CSRF RequestConfig format
                    const csrfConfig = {
                        url: processedConfig.url || '',
                        method: processedConfig.method || 'GET',
                        headers: processedConfig.headers || {},
                        body: processedConfig.body
                    };
                    const csrfResult = attachCSRF(csrfConfig);
                    processedConfig = {
                        ...processedConfig,
                        ...csrfResult
                    };
                    console.debug('🛡️ SecurityComposer: CSRF protection applied using moved hooks');
                }
            }
            return processedConfig;
        } catch (error) {
            console.error('SecurityComposer: Error processing request:', error);
            throw error;
        }
    }
    /**
   * Handle errors using moved security hooks
   */ async handleError(error, context) {
        try {
            // Auto-logout on authentication errors using moved hooks
            if (this.config.authentication?.autoLogout && this.securityFeatures.sessionSecurity) {
                if (error instanceof Error && (error.message.includes('401') || error.message.includes('Authentication') || error.message.includes('Unauthorized'))) {
                    console.warn('🔐 SecurityComposer: Authentication error detected, clearing session...');
                    this.securityFeatures.sessionSecurity.clearSession();
                }
            }
            // Update activity timestamp using moved hooks
            if (this.securityFeatures.sessionSecurity) {
                this.securityFeatures.sessionSecurity.updateActivity();
            }
        } catch (handlingError) {
            console.error('SecurityComposer: Error in error handling:', handlingError);
        }
    }
    /**
   * Get current security status using moved hooks
   */ getSecurityStatus() {
        return {
            isAuthenticated: this.securityFeatures.sessionSecurity?.isSessionActive ?? false,
            hasValidToken: this.securityFeatures.tokenManagement?.isTokenValid ?? false,
            sessionActive: this.securityFeatures.sessionSecurity?.isSessionActive ?? false,
            securityFeaturesEnabled: this.config,
            securityFeaturesInitialized: !!(this.securityFeatures.csrfProtection || this.securityFeatures.tokenManagement || this.securityFeatures.inputValidation || this.securityFeatures.sessionSecurity)
        };
    }
    /**
   * Update security configuration
   */ updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };
    }
    /**
   * Update security features (when hooks change)
   */ updateSecurityFeatures(newFeatures) {
        this.securityFeatures = {
            ...this.securityFeatures,
            ...newFeatures
        };
    }
}
function createSecurityComposer(securityFeatures, config) {
    return new SecurityComposer(securityFeatures, config);
}
}}),
"[project]/src/lib/api/security/secureApiClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file SecureApiClient - Enhanced API client with comprehensive security features
 * @module api/security/secureApiClient
 *
 * Enhanced to use SecurityComposer with moved security hooks directly:
 * - CSRF Protection (via moved useCSRFProtection hook)
 * - Input Sanitization (via moved useInputValidation hook)
 * - Token Validation & Refresh (via moved useTokenManagement hook)
 * - Session Security (via moved useSessionSecurity hook)
 * - Automatic Logout on Auth Failures
 * - Enhanced Error Handling
 *
 * Phase 2 Enhancement: Now uses SecurityComposer with existing security infrastructure
 * - Integrates with SecurityUtils and SECURITY_CONSTANTS
 * - Enhanced configuration management and validation
 * - Improved error handling and recovery mechanisms
 * - Comprehensive security status reporting
 */ __turbopack_context__.s({
    "SecureApiClient": (()=>SecureApiClient),
    "createSecureApiClient": (()=>createSecureApiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/composer.ts [app-ssr] (ecmascript)");
;
;
;
;
class SecureApiClient extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"] {
    enhancedSecurityConfig;
    lastSecurityCheck = new Date();
    securityComposer;
    securityConfig;
    securityFeatures;
    securityInitialized = false;
    constructor(config, securityComposer){
        // Initialize base ApiClient
        super(config);
        // Security configuration with defaults using SECURITY_CONSTANTS
        this.securityConfig = {
            enableAutoLogout: config.enableAutoLogout ?? true,
            enableCSRF: config.enableCSRF ?? true,
            enableInputSanitization: config.enableInputSanitization ?? true,
            enableTokenValidation: config.enableTokenValidation ?? true,
            securityConfig: config.securityConfig ?? {},
            validateSecurityFeatures: config.validateSecurityFeatures ?? true
        };
        // Enhanced security configuration using existing infrastructure
        this.enhancedSecurityConfig = {
            authentication: {
                autoLogout: true,
                enabled: this.securityConfig.enableAutoLogout,
                redirectOnFailure: true
            },
            csrf: {
                enabled: this.securityConfig.enableCSRF,
                excludePaths: [],
                tokenHeader: 'X-CSRF-Token'
            },
            http: {
                baseURL: config.baseURL || '/api',
                retryAttempts: config.retryAttempts || 3,
                timeout: config.timeout || 10_000
            },
            inputSanitization: {
                enabled: this.securityConfig.enableInputSanitization,
                sanitizers: [
                    'xss',
                    'sql'
                ]
            },
            tokenValidation: {
                autoRefresh: true,
                enabled: this.securityConfig.enableTokenValidation,
                refreshThreshold: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].TOKEN_EXPIRY_THRESHOLD_MINUTES * 60
            },
            // Merge with user-provided security config
            ...config.securityConfig
        };
        this.securityComposer = securityComposer;
        console.log('🔐 SecureApiClient: Initialized with SecurityUtils integration', {
            constants: {
                sessionTimeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].SESSION_TIMEOUT_MINUTES,
                tokenThreshold: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].TOKEN_EXPIRY_THRESHOLD_MINUTES
            },
            securityFeatures: Object.keys(this.securityConfig).filter((key)=>this.securityConfig[key])
        });
    }
    /**
   * Get comprehensive security status information via SecurityComposer and SecurityUtils
   */ getSecurityStatus() {
        const baseStatus = {
            lastSecurityCheck: this.lastSecurityCheck,
            securityConstants: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"],
            securityFeaturesEnabled: this.securityConfig,
            securityFeaturesInitialized: this.securityInitialized,
            sessionInfo: this.getSessionInfo(),
            userInfo: this.extractUserFromToken()
        };
        if (this.securityComposer) {
            const composerStatus = this.securityComposer.getSecurityStatus();
            return {
                ...baseStatus,
                hasValidToken: composerStatus.hasValidToken,
                isAuthenticated: composerStatus.isAuthenticated,
                threatLevel: this.assessThreatLevel(composerStatus)
            };
        }
        // Fallback if SecurityComposer not initialized
        return {
            ...baseStatus,
            hasValidToken: false,
            isAuthenticated: false,
            threatLevel: 'critical'
        };
    }
    /**
   * Initialize security features from useSecureApi hook
   * Enhanced with validation and SecurityUtils integration
   */ initializeSecurity(features) {
        try {
            // Validate security features if enabled
            if (this.securityConfig.validateSecurityFeatures) {
                this.validateSecurityFeatures(features);
            }
            this.securityFeatures = features;
            // Create SecurityComposer if not provided in constructor
            if (this.securityComposer) {
                // Update existing SecurityComposer with new features
                this.securityComposer.updateSecurityFeatures(features);
                this.securityComposer.updateConfig(this.enhancedSecurityConfig);
            } else {
                this.securityComposer = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecurityComposer"](features, this.enhancedSecurityConfig);
            }
            this.securityInitialized = true;
            this.lastSecurityCheck = new Date();
            console.log('🔐 SecureApiClient: Security features initialized with SecurityUtils integration', {
                featuresInitialized: Object.keys(features).filter((key)=>features[key]),
                securityConfig: this.enhancedSecurityConfig,
                timestamp: this.lastSecurityCheck.toISOString()
            });
        } catch (error) {
            console.error('SecureApiClient: Failed to initialize security features:', error);
            throw new Error(`Security initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Force security feature refresh
   */ refreshSecurityFeatures() {
        if (this.securityFeatures && this.securityComposer) {
            this.securityComposer.updateSecurityFeatures(this.securityFeatures);
            this.lastSecurityCheck = new Date();
            console.log('🔄 SecureApiClient: Security features refreshed');
        } else {
            console.warn('SecureApiClient: Cannot refresh - security features not initialized');
        }
    }
    /**
   * Enhanced request method with security middleware via SecurityComposer
   * Integrates with SecurityUtils for enhanced security processing
   */ async secureRequest(method, endpoint, data, config) {
        // Ensure security is initialized
        if (!this.securityInitialized || !this.securityComposer) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"]('Security features not initialized. Call initializeSecurity() first.', 'SECURITY_NOT_INITIALIZED');
        }
        // Update last security check timestamp
        this.lastSecurityCheck = new Date();
        // Enhanced SecurityContext with SecurityUtils integration
        const securityContext = {
            hasValidToken: this.securityFeatures?.tokenManagement?.isTokenValid ?? false,
            isAuthenticated: this.securityFeatures?.sessionSecurity?.isSessionActive ?? false,
            session: this.getSessionInfo(),
            timestamp: this.lastSecurityCheck,
            user: this.extractUserFromToken()
        };
        // Create RequestConfig for SecurityComposer
        const requestConfig = {
            ...config,
            body: data,
            method: method,
            url: endpoint
        };
        try {
            // Pre-request security checks using SecurityUtils
            await this.performPreRequestSecurityChecks(securityContext);
            // Process request through SecurityComposer
            const processedConfig = await this.securityComposer.processRequest(requestConfig, securityContext);
            // Extract processed data and config for parent request
            const { body: processedData, ...restConfig } = processedConfig;
            // Use parent ApiClient for the actual HTTP request
            const response = await super[method.toLowerCase()](endpoint, processedData, restConfig);
            // Post-request security processing
            await this.performPostRequestSecurityChecks(securityContext);
            return response;
        } catch (error) {
            // Enhanced error handling via SecurityComposer and SecurityUtils
            await this.handleSecurityError(error, securityContext);
            throw error;
        }
    }
    /**
   * Update security configuration
   */ updateSecurityConfig(newConfig) {
        if (this.securityComposer) {
            this.securityComposer.updateConfig(newConfig);
            console.log('🔐 SecureApiClient: Security configuration updated', newConfig);
        } else {
            console.warn('SecureApiClient: Cannot update config - SecurityComposer not initialized');
        }
    }
    /**
   * Assess current threat level based on security status
   */ assessThreatLevel(status) {
        try {
            // Critical: Security not initialized or major auth issues
            if (!this.securityInitialized || !status.securityFeaturesInitialized) {
                return 'critical';
            }
            // High: Authentication issues
            if (!status.isAuthenticated || !status.hasValidToken) {
                return 'high';
            }
            // Medium: Session or token issues
            const currentToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].getSecureItem('auth_token');
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].detectTimeout() || currentToken && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].willExpireSoon(currentToken)) {
                return 'medium';
            }
            // Low: All security features working normally
            return 'low';
        } catch (error) {
            console.warn('SecureApiClient: Failed to assess threat level:', error);
            return 'medium'; // Default to medium on assessment failure
        }
    }
    /**
   * Extract user information from token using SecurityUtils
   */ extractUserFromToken() {
        try {
            if (!this.securityFeatures?.tokenManagement?.isTokenValid) {
                return undefined;
            }
            // Get current token from secure storage
            const currentToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].getSecureItem('auth_token');
            if (!currentToken) {
                return undefined;
            }
            // Use SecurityUtils to extract user information
            const employeeId = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].extractEmployeeId(currentToken);
            const userRole = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].extractUserRole(currentToken);
            return employeeId || userRole ? {
                employeeId,
                userRole
            } : undefined;
        } catch (error) {
            console.warn('SecureApiClient: Failed to extract user from token:', error);
            return undefined;
        }
    }
    /**
   * Get session information using SecurityUtils
   */ getSessionInfo() {
        try {
            const sessionId = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].getCurrentSessionId();
            const isTimeout = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].detectTimeout();
            return sessionId ? {
                isTimeout,
                sessionId
            } : undefined;
        } catch (error) {
            console.warn('SecureApiClient: Failed to get session info:', error);
            return undefined;
        }
    }
    /**
   * Enhanced error handling using SecurityComposer and SecurityUtils with circuit breaker integration
   */ async handleSecurityError(error, context) {
        // Circuit breaker check for error handling
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
            console.debug('🔒 Security error handling blocked by circuit breaker');
            return;
        }
        const operationId = 'api-error-handling';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
            console.debug('🔄 Security error handling already in progress');
            return;
        }
        try {
            // Use SecurityComposer for primary error handling
            if (this.securityComposer) {
                await this.securityComposer.handleError(error, context);
            }
            // Additional error handling using SecurityUtils
            if (error instanceof Error) {
                // Handle authentication errors
                if (error.message.includes('401') || error.message.includes('Unauthorized') || error.message.includes('Authentication')) {
                    console.warn('🔐 SecureApiClient: Authentication error detected');
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                    // Attempt session recovery first
                    const recovered = await this.attemptSessionRecovery();
                    if (!recovered) {
                        // Clear secure storage on auth failure
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].clearAllCookies();
                        console.log('🧹 Cleared secure storage due to auth failure');
                    }
                }
                // Handle CSRF errors
                if (error.message.includes('CSRF') || error.message.includes('403')) {
                    console.warn('🛡️ SecureApiClient: CSRF error detected');
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                // CSRF token might need refresh - this will be handled by the hook
                }
                // Handle network errors
                if (error.message.includes('Network') || error.message.includes('fetch')) {
                    console.warn('🌐 SecureApiClient: Network error detected');
                // Don't record as security attempt for network issues
                }
                // Handle timeout errors
                if (error.message.includes('timeout') || error.message.includes('Timeout')) {
                    console.warn('⏰ SecureApiClient: Timeout error detected');
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                }
            }
        } catch (handlingError) {
            console.error('SecureApiClient: Error in security error handling:', handlingError);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
        } finally{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
        }
    }
    /**
   * Perform post-request security checks using SecurityUtils
   */ async performPostRequestSecurityChecks(context) {
        try {
            // Update activity timestamp if session is active
            if (context.isAuthenticated && this.securityFeatures?.sessionSecurity) {
                this.securityFeatures.sessionSecurity.updateActivity();
            }
        } catch (error) {
            console.warn('SecureApiClient: Post-request security check failed:', error);
        // Don't throw here as the main request succeeded
        }
    }
    /**
   * Perform pre-request security checks using SecurityUtils with circuit breaker integration
   */ async performPreRequestSecurityChecks(context) {
        // Circuit breaker check for pre-request security checks
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
            console.debug('🔒 Pre-request security checks blocked by circuit breaker');
            return;
        }
        const operationId = 'pre-request-security-check';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
            console.debug('🔄 Pre-request security check already in progress');
            return;
        }
        try {
            // Check for session timeout using SecurityUtils
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].detectTimeout()) {
                console.warn('⏰ Session timeout detected in pre-request check');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                // Attempt session recovery before throwing error
                const recovered = await this.attemptSessionRecovery();
                if (!recovered) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"]('Session has timed out', 'SESSION_TIMEOUT');
                }
            }
            // Validate token if available using SecurityUtils
            if (context.hasValidToken && this.securityFeatures?.tokenManagement) {
                const { isTokenExpired } = this.securityFeatures.tokenManagement;
                const currentToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].getSecureItem('auth_token');
                if (isTokenExpired || currentToken && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].willExpireSoon(currentToken)) {
                    console.warn('🔄 SecureApiClient: Token will expire soon, attempting refresh');
                    // Attempt token refresh
                    try {
                        const refreshed = await this.securityFeatures.tokenManagement.refreshToken();
                        if (refreshed) {
                            console.log('✅ Token refreshed successfully');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                        } else {
                            console.warn('❌ Token refresh failed');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                        }
                    } catch (refreshError) {
                        console.error('❌ Token refresh error:', refreshError);
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                    }
                }
            }
            // Record successful pre-request check
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
        } catch (error) {
            console.error('SecureApiClient: Pre-request security check failed:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            throw error;
        } finally{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
        }
    }
    /**
   * Attempt session recovery using SessionManager
   */ async attemptSessionRecovery() {
        try {
            console.log('🔧 Attempting session recovery...');
            // Import SessionManager dynamically to avoid circular dependencies
            const { SessionManager } = await __turbopack_context__.r("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            // Attempt integrity check and recovery
            const integrityCheck = await SessionManager.performIntegrityCheck();
            if (integrityCheck) {
                console.log('✅ Session integrity check passed');
                return true;
            }
            // Attempt state recovery
            const recovered = SessionManager.recoverFromCorruptedState();
            if (recovered) {
                console.log('✅ Session state recovered successfully');
                return true;
            }
            console.warn('❌ Session recovery failed');
            return false;
        } catch (error) {
            console.error('❌ Session recovery error:', error);
            return false;
        }
    }
    /**
   * Validate security features using SecurityUtils
   */ validateSecurityFeatures(features) {
        const validationErrors = [];
        // Validate token management if enabled
        if (this.securityConfig.enableTokenValidation && features.tokenManagement) {
            const { isTokenExpired, isTokenValid } = features.tokenManagement;
            if (typeof isTokenValid !== 'boolean' || typeof isTokenExpired !== 'boolean') {
                validationErrors.push('Token management features must provide boolean status indicators');
            }
        }
        // Validate CSRF protection if enabled
        if (this.securityConfig.enableCSRF && features.csrfProtection && typeof features.csrfProtection.attachCSRF !== 'function') {
            validationErrors.push('CSRF protection must provide attachCSRF function');
        }
        // Validate input sanitization if enabled
        if (this.securityConfig.enableInputSanitization && features.inputValidation && typeof features.inputValidation.sanitizeInput !== 'function') {
            validationErrors.push('Input validation must provide sanitizeInput function');
        }
        // Validate session security if enabled
        if (this.securityConfig.enableAutoLogout && features.sessionSecurity) {
            const { clearSession, isSessionActive } = features.sessionSecurity;
            if (typeof isSessionActive !== 'boolean' || typeof clearSession !== 'function') {
                validationErrors.push('Session security must provide boolean status and clearSession function');
            }
        }
        if (validationErrors.length > 0) {
            throw new Error(`Security feature validation failed: ${validationErrors.join(', ')}`);
        }
    }
}
function createSecureApiClient(config, securityFeatures) {
    // Default security configuration
    const defaultSecurityConfig = {
        authentication: {
            autoLogout: true,
            enabled: true,
            redirectOnFailure: true
        },
        csrf: {
            enabled: true,
            excludePaths: [],
            tokenHeader: 'X-CSRF-Token'
        },
        http: {
            baseURL: '/api',
            retryAttempts: 3,
            timeout: 10_000
        },
        inputSanitization: {
            enabled: true,
            sanitizers: [
                'xss',
                'sql'
            ]
        },
        tokenValidation: {
            autoRefresh: true,
            enabled: true,
            refreshThreshold: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].TOKEN_EXPIRY_THRESHOLD_MINUTES * 60
        }
    };
    const mergedConfig = {
        enableAutoLogout: true,
        // Default security-focused configuration using SECURITY_CONSTANTS
        enableCSRF: true,
        enableInputSanitization: true,
        enableTokenValidation: true,
        validateSecurityFeatures: true,
        // Merge with provided config (user config takes precedence)
        ...config,
        // Deep merge security config if provided
        securityConfig: {
            ...defaultSecurityConfig,
            ...config.securityConfig
        }
    };
    // Create SecurityComposer if security features are provided
    let securityComposer;
    if (securityFeatures) {
        securityComposer = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecurityComposer"](securityFeatures, mergedConfig.securityConfig);
    }
    const client = new SecureApiClient(mergedConfig, securityComposer);
    console.log('🏭 SecureApiClient Factory: Created enhanced secure API client', {
        config: {
            autoLogout: mergedConfig.enableAutoLogout,
            csrf: mergedConfig.enableCSRF,
            inputSanitization: mergedConfig.enableInputSanitization,
            tokenValidation: mergedConfig.enableTokenValidation
        },
        securityConstants: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"],
        securityFeatures: securityFeatures ? Object.keys(securityFeatures) : []
    });
    return client;
}
}}),
"[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Configuration Provider - Centralized security configuration management
 * @module api/security/providers/SecurityConfigProvider
 *
 * Phase 3: React Integration Layer
 * Provides centralized security configuration management across the application
 * using React Context with type-safe defaults and validation.
 */ __turbopack_context__.s({
    "DEFAULT_SECURITY_CONFIG": (()=>DEFAULT_SECURITY_CONFIG),
    "SecurityConfigProvider": (()=>SecurityConfigProvider),
    "useSecurityConfig": (()=>useSecurityConfig),
    "useSecurityConfigValue": (()=>useSecurityConfigValue)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
/**
 * Default Security Configuration using SECURITY_CONSTANTS and environment-aware configuration
 */ const createDefaultSecurityConfig = ()=>{
    const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
    return {
        csrf: {
            enabled: true,
            tokenHeader: 'X-CSRF-Token',
            excludePaths: [
                '/api/health',
                '/api/status'
            ]
        },
        tokenValidation: {
            enabled: true,
            refreshThreshold: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].TOKEN_EXPIRY_THRESHOLD_MINUTES * 60,
            autoRefresh: true
        },
        inputSanitization: {
            enabled: true,
            sanitizers: [
                'xss',
                'sql'
            ]
        },
        authentication: {
            enabled: true,
            autoLogout: true,
            redirectOnFailure: true
        },
        http: {
            baseURL: envConfig.apiBaseUrl,
            timeout: 10000,
            retryAttempts: 3
        }
    };
};
const DEFAULT_SECURITY_CONFIG = createDefaultSecurityConfig();
/**
 * Security Configuration Context
 */ const SecurityConfigContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
function SecurityConfigProvider({ children, initialConfig = {}, configVersion = '1.0.0', onConfigChange, validateConfig = true }) {
    // Merge initial config with defaults
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const mergedConfig = {
            ...DEFAULT_SECURITY_CONFIG,
            ...initialConfig,
            // Deep merge nested objects
            csrf: {
                ...DEFAULT_SECURITY_CONFIG.csrf,
                ...initialConfig.csrf
            },
            tokenValidation: {
                ...DEFAULT_SECURITY_CONFIG.tokenValidation,
                ...initialConfig.tokenValidation
            },
            inputSanitization: {
                ...DEFAULT_SECURITY_CONFIG.inputSanitization,
                ...initialConfig.inputSanitization
            },
            authentication: {
                ...DEFAULT_SECURITY_CONFIG.authentication,
                ...initialConfig.authentication
            },
            http: {
                ...DEFAULT_SECURITY_CONFIG.http,
                ...initialConfig.http
            }
        };
        return mergedConfig;
    }, [
        initialConfig
    ]);
    // Validate configuration
    const isConfigValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!validateConfig) return true;
        try {
            // Validate required fields
            if (!config.http.baseURL) return false;
            if (config.http.timeout <= 0) return false;
            if (config.http.retryAttempts < 0) return false;
            if (config.tokenValidation.refreshThreshold <= 0) return false;
            // Validate CSRF configuration
            if (config.csrf.enabled && !config.csrf.tokenHeader) return false;
            // Validate sanitizers
            if (config.inputSanitization.enabled && config.inputSanitization.sanitizers.length === 0) {
                return false;
            }
            return true;
        } catch (error) {
            console.error('SecurityConfigProvider: Configuration validation failed:', error);
            return false;
        }
    }, [
        config,
        validateConfig
    ]);
    // Update configuration function
    const updateConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return (newConfig)=>{
            const updatedConfig = {
                ...config,
                ...newConfig,
                // Deep merge nested objects
                csrf: {
                    ...config.csrf,
                    ...newConfig.csrf
                },
                tokenValidation: {
                    ...config.tokenValidation,
                    ...newConfig.tokenValidation
                },
                inputSanitization: {
                    ...config.inputSanitization,
                    ...newConfig.inputSanitization
                },
                authentication: {
                    ...config.authentication,
                    ...newConfig.authentication
                },
                http: {
                    ...config.http,
                    ...newConfig.http
                }
            };
            onConfigChange?.(updatedConfig);
        };
    }, [
        config,
        onConfigChange
    ]);
    // Reset configuration function
    const resetConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return ()=>{
            onConfigChange?.(DEFAULT_SECURITY_CONFIG);
        };
    }, [
        onConfigChange
    ]);
    // Context value
    const contextValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            config,
            updateConfig,
            resetConfig,
            isConfigValid,
            configVersion
        }), [
        config,
        updateConfig,
        resetConfig,
        isConfigValid,
        configVersion
    ]);
    // Log configuration status
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        console.log('🔧 SecurityConfigProvider: Configuration initialized', {
            isValid: isConfigValid,
            version: configVersion,
            constants: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"],
            config: {
                csrf: config.csrf.enabled,
                tokenValidation: config.tokenValidation.enabled,
                inputSanitization: config.inputSanitization.enabled,
                authentication: config.authentication.enabled
            }
        });
    }, [
        config,
        isConfigValid,
        configVersion
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(SecurityConfigContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx",
        lineNumber: 234,
        columnNumber: 5
    }, this);
}
function useSecurityConfig() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(SecurityConfigContext);
    if (!context) {
        throw new Error('useSecurityConfig must be used within a SecurityConfigProvider');
    }
    return context;
}
function useSecurityConfigValue() {
    const { config } = useSecurityConfig();
    return config;
}
;
}}),
"[project]/src/lib/supabase.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Supabase client configuration
 * @module lib/supabase
 */ __turbopack_context__.s({
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-ssr] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://abylqjnpaegeqwktcukn.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o");
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey, {
    auth: {
        autoRefreshToken: true,
        detectSessionInUrl: true,
        // Configure to work with your backend's cookie expectations
        flowType: 'pkce',
        persistSession: true
    }
});
}}),
"[project]/src/lib/services/TokenRefreshService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Token Refresh Service for automatic JWT token management
 * @module lib/services/TokenRefreshService
 */ __turbopack_context__.s({
    "TokenRefreshService": (()=>TokenRefreshService),
    "getTokenRefreshService": (()=>getTokenRefreshService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
;
;
;
/**
 * Default configuration for token refresh service
 */ const DEFAULT_CONFIG = {
    baseRetryDelay: 1000,
    enableDebugLogging: ("TURBOPACK compile-time value", "development") !== 'production',
    maxRetryAttempts: 3,
    refreshBeforeExpiryMinutes: 5
};
class TokenRefreshService {
    static instance = null;
    callbacks = new Set();
    config;
    currentSession = null;
    isRefreshing = false;
    isTabVisible = true;
    refreshTimeout = null;
    retryAttempts = 0;
    retryTimeout = null;
    constructor(config = {}){
        this.config = {
            ...DEFAULT_CONFIG,
            ...config
        };
        this.setupVisibilityHandling();
        this.log('TokenRefreshService initialized');
    }
    /**
   * Get singleton instance
   */ static getInstance(config) {
        if (!TokenRefreshService.instance) {
            TokenRefreshService.instance = new TokenRefreshService(config);
        }
        return TokenRefreshService.instance;
    }
    /**
   * Get current session information for debugging and validation
   */ async getSessionInfo() {
        try {
            const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (error) {
                this.log('Error getting session info', {
                    error
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleSessionValidation(false, {
                    error: error.message
                });
                return {
                    error: error.message,
                    isValid: false
                };
            }
            if (!session) {
                this.log('No active session found');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleSessionValidation(false, {
                    error: 'No active session'
                });
                return {
                    error: 'No active session',
                    isValid: false
                };
            }
            const now = Math.floor(Date.now() / 1000);
            const isExpired = session.expires_at ? session.expires_at < now : false;
            const result = {
                isExpired,
                isValid: !isExpired,
                user: {
                    id: session.user.id
                }
            };
            if (session.expires_at) {
                result.expiresAt = session.expires_at;
            }
            if (session.user.email) {
                result.user.email = session.user.email;
            }
            if (session.user.user_metadata?.role) {
                result.user.role = session.user.user_metadata.role;
            }
            return result;
        } catch (error) {
            this.log('Exception in getSessionInfo', {
                error
            });
            return {
                error: error instanceof Error ? error.message : 'Unknown error',
                isValid: false
            };
        }
    }
    /**
   * Manually trigger token refresh
   */ async refreshNow() {
        if (this.isRefreshing) {
            this.log('Refresh already in progress, skipping');
            return false;
        }
        return this.performRefresh();
    }
    /**
   * Stop the service and cleanup
   */ stop() {
        this.clearScheduledRefresh();
        this.clearRetryTimeout();
        this.callbacks.clear();
        this.currentSession = null;
        this.isRefreshing = false;
        this.retryAttempts = 0;
        this.log('TokenRefreshService stopped');
    }
    /**
   * Subscribe to token refresh events
   */ subscribe(callback) {
        this.callbacks.add(callback);
        return ()=>this.callbacks.delete(callback);
    }
    /**
   * Update session and schedule refresh
   */ updateSession(session) {
        this.currentSession = session;
        if (session) {
            this.scheduleRefresh(session);
            this.log('Session updated, refresh scheduled', {
                expiresAt: new Date(session.expires_at * 1000).toISOString()
            });
        } else {
            this.clearScheduledRefresh();
            this.log('Session cleared, refresh cancelled');
        }
    }
    /**
   * Clear retry timeout
   */ clearRetryTimeout() {
        if (this.retryTimeout) {
            clearTimeout(this.retryTimeout);
            this.retryTimeout = null;
        }
    }
    /**
   * Clear scheduled refresh timeout
   */ clearScheduledRefresh() {
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
            this.refreshTimeout = null;
        }
    }
    /**
   * Emit event to all subscribers
   */ emitEvent(event, data) {
        for (const callback of this.callbacks){
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in token refresh callback:', error);
            }
        }
    }
    /**
   * Handle refresh failure with retry logic
   */ handleRefreshFailure(errorData) {
        this.retryAttempts++;
        if (this.retryAttempts >= this.config.maxRetryAttempts) {
            this.log('Max retry attempts reached, giving up and signaling critical failure');
            this.emitEvent('critical_refresh_failed', {
                attempts: this.retryAttempts,
                error: 'Max retry attempts exceeded, session unrecoverable',
                ...errorData
            });
            // Optionally, you might want to clear the session here or trigger a logout
            // this.updateSession(null);
            return;
        }
        // Exponential backoff
        const delay = this.config.baseRetryDelay * Math.pow(2, this.retryAttempts - 1);
        this.log('Scheduling retry', {
            attempt: this.retryAttempts,
            delayMs: delay
        });
        this.retryTimeout = setTimeout(()=>{
            this.performRefresh();
        }, delay);
    }
    /**
   * Debug logging
   */ log(message, data) {
        if (this.config.enableDebugLogging) {
            console.log(`🔄 TokenRefreshService: ${message}`, data || '');
        }
    }
    /**
   * Perform the actual token refresh
   */ async performRefresh() {
        if (this.isRefreshing) {
            return false;
        }
        this.isRefreshing = true;
        this.log('Starting token refresh');
        try {
            const headers = {
                'Content-Type': 'application/json'
            };
            if (this.currentSession?.access_token) {
                headers.Authorization = `Bearer ${this.currentSession.access_token}`;
            }
            // Prepare request body with refresh token (Supabase OAuth2 standard)
            const requestBody = {};
            // Include refresh token in body if available from current session
            if (this.currentSession?.refresh_token) {
                requestBody.refresh_token = this.currentSession.refresh_token;
                this.log('Including refresh token in request body');
            } else {
                this.log('Warning: No refresh token available in current session');
            }
            // Use environment-aware backend URL for token refresh
            const backendUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])().apiBaseUrl;
            const response = await fetch(`${backendUrl}/auth/refresh`, {
                credentials: 'include',
                headers,
                method: 'POST',
                body: JSON.stringify(requestBody)
            });
            if (response.ok) {
                const data = await response.json();
                this.log('Token refresh successful', {
                    expiresIn: data.expiresIn
                });
                // Reset retry attempts on success
                this.retryAttempts = 0;
                this.clearRetryTimeout();
                // Explicitly update Supabase session with the new tokens received from backend
                try {
                    // The 'data' object from the backend response should contain 'newTokens'
                    const { newTokens } = data;
                    if (newTokens?.session && newTokens?.user) {
                        // Use setSession to explicitly update Supabase's internal state
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.setSession({
                            access_token: newTokens.session.access_token,
                            refresh_token: newTokens.session.refresh_token
                        });
                        this.currentSession = newTokens.session; // Update internal currentSession
                        this.log('Supabase session explicitly updated with new tokens');
                    } else {
                        this.log('Warning: New tokens from backend did not contain full session/user data', {
                            data
                        } // Log the entire data object for debugging
                        );
                    }
                } catch (sessionUpdateError) {
                    this.log('Error explicitly updating Supabase session after refresh', {
                        sessionUpdateError
                    });
                }
                this.emitEvent('refresh_success', data);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleTokenRefresh(true, data);
                return true;
            } else {
                const errorData = await response.json().catch(()=>({}));
                this.log('Token refresh failed', {
                    error: errorData,
                    status: response.status
                });
                this.handleRefreshFailure(errorData);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleTokenRefresh(false, errorData);
                return false;
            }
        } catch (error) {
            this.log('Token refresh error', {
                error: error instanceof Error ? error.message : String(error)
            });
            this.handleRefreshFailure({
                error: 'Network error'
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleTokenRefresh(false, {
                error: 'Network error'
            });
            return false;
        } finally{
            this.isRefreshing = false;
        }
    }
    /**
   * Schedule token refresh based on expiration time
   */ scheduleRefresh(session) {
        this.clearScheduledRefresh();
        if (!session.expires_at) {
            this.log('No expiration time in session, cannot schedule refresh');
            return;
        }
        const expiresAt = session.expires_at * 1000; // Convert to milliseconds
        const refreshAt = expiresAt - this.config.refreshBeforeExpiryMinutes * 60 * 1000;
        const now = Date.now();
        const delay = Math.max(0, refreshAt - now);
        if (delay === 0) {
            // Token is already expired or about to expire, refresh immediately
            this.log('Token expired or about to expire, refreshing immediately');
            this.performRefresh();
            return;
        }
        this.refreshTimeout = setTimeout(()=>{
            this.performRefresh();
        }, delay);
        this.log('Refresh scheduled', {
            delayMinutes: Math.round(delay / 60_000),
            refreshAt: new Date(refreshAt).toISOString()
        });
        this.emitEvent('refresh_scheduled', {
            delay,
            refreshAt
        });
    }
    /**
   * Setup browser tab visibility handling
   */ setupVisibilityHandling() {
        if (typeof document === 'undefined') return;
        document.addEventListener('visibilitychange', ()=>{
            this.isTabVisible = !document.hidden;
            if (this.isTabVisible && this.currentSession) {
                // Tab became visible, check if we need to refresh
                const now = Date.now();
                const expiresAt = (this.currentSession.expires_at || 0) * 1000;
                const timeUntilExpiry = expiresAt - now;
                if (timeUntilExpiry <= this.config.refreshBeforeExpiryMinutes * 60 * 1000) {
                    this.log('Tab visible and token needs refresh');
                    this.performRefresh();
                }
            }
        });
    }
}
const getTokenRefreshService = (config)=>TokenRefreshService.getInstance(config);
}}),
"[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Token Management Hook - Single Responsibility Principle (SRP)
 * @module hooks/useTokenManagement
 *
 * This hook handles ONLY token lifecycle operations following SRP principles.
 * It provides token validation, refresh, and management functionality.
 *
 * SECURITY NOTE: Works with httpOnly cookies and provides token utility functions.
 */ __turbopack_context__.s({
    "useTokenManagement": (()=>useTokenManagement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/TokenManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function useTokenManagement(currentToken) {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isTokenExpired: false,
        isTokenValid: false,
        lastValidation: null,
        tokenError: null,
        willExpireSoon: false
    });
    const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])(), []);
    /**
   * Validate current token with circuit breaker coordination
   * Single responsibility: Token validation only
   */ const validateCurrentToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!currentToken) {
            return null;
        }
        // Circuit breaker check for validation
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
            console.debug('🔒 Token validation blocked by circuit breaker');
            return null;
        }
        const operationId = 'token-validation';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
            console.debug('🔄 Token validation already in progress');
            return null;
        }
        try {
            const validation = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].validateToken(currentToken);
            if (validation.isValid) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                // Notify SessionManager of valid session
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleSessionValidation(true);
            } else {
                console.warn('❌ Token validation failed:', validation.error);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                // Notify SessionManager of invalid session
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleSessionValidation(false, {
                    error: validation.error
                });
            }
            setState((prev)=>({
                    ...prev,
                    isTokenExpired: validation.isExpired,
                    isTokenValid: validation.isValid,
                    lastValidation: new Date(),
                    tokenError: validation.error || null,
                    willExpireSoon: validation.isValid ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].willExpireSoon(currentToken) : false
                }));
            return validation;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Token validation failed';
            console.error('❌ Token validation error:', errorMessage);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            setState((prev)=>({
                    ...prev,
                    isTokenValid: false,
                    tokenError: errorMessage,
                    lastValidation: new Date()
                }));
            return null;
        } finally{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
        }
    }, [
        currentToken
    ]);
    /**
   * Refresh token using token refresh service with circuit breaker coordination
   * Single responsibility: Token refresh only
   */ const refreshToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        // Circuit breaker check - prevent verification loops
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
            console.debug('🔒 Token refresh blocked by circuit breaker');
            return false;
        }
        const operationId = 'token-refresh';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
            console.debug('🔄 Token refresh already in progress');
            return false;
        }
        try {
            setState((prev)=>({
                    ...prev,
                    tokenError: null
                }));
            console.log('🔄 Starting token refresh...');
            const success = await tokenRefreshService.refreshNow();
            if (success) {
                console.log('✅ Token refresh successful');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                // Notify SessionManager of successful token refresh
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleTokenRefresh(true);
                setState((prev)=>({
                        ...prev,
                        isTokenValid: true,
                        isTokenExpired: false,
                        tokenError: null,
                        lastValidation: new Date()
                    }));
            } else {
                console.warn('❌ Token refresh failed');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                // Notify SessionManager of failed token refresh
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleTokenRefresh(false);
                setState((prev)=>({
                        ...prev,
                        isTokenValid: false,
                        tokenError: 'Token refresh failed'
                    }));
            }
            return success;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Token refresh failed';
            console.error('❌ Token refresh error:', errorMessage);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            // Notify SessionManager of failed token refresh
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleTokenRefresh(false, {
                error: errorMessage
            });
            setState((prev)=>({
                    ...prev,
                    isTokenValid: false,
                    tokenError: errorMessage
                }));
            return false;
        } finally{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
        }
    }, [
        tokenRefreshService
    ]);
    /**
   * Clear token state
   * Single responsibility: Token clearing only
   */ const clearToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState({
            isTokenExpired: false,
            isTokenValid: false,
            lastValidation: null,
            tokenError: null,
            willExpireSoon: false
        });
    }, []);
    /**
   * Check if token will expire soon
   * Single responsibility: Expiry check only
   */ const checkTokenExpiry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!currentToken) return true;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].willExpireSoon(currentToken, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_CONSTANTS"].TOKEN_EXPIRY_THRESHOLD_MINUTES);
    }, [
        currentToken
    ]);
    /**
   * Get token expiration date
   * Single responsibility: Expiration date retrieval only
   */ const getTokenExpiration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!currentToken) return null;
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].getTokenExpiration(currentToken);
    }, [
        currentToken
    ]);
    // Auto-validate token when it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (currentToken) {
            validateCurrentToken();
        } else {
            clearToken();
        }
    }, [
        currentToken,
        validateCurrentToken,
        clearToken
    ]);
    // Auto-refresh token when it's about to expire with circuit breaker coordination
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!currentToken || !state.isTokenValid) return;
        const checkAndRefresh = ()=>{
            // Circuit breaker check before auto-refresh
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                console.debug('🔒 Auto token refresh blocked by circuit breaker');
                return;
            }
            const operationId = 'auto-token-refresh-check';
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                console.debug('🔄 Auto token refresh check already in progress');
                return;
            }
            try {
                if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$TokenManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TokenManager"].willExpireSoon(currentToken, 2)) {
                    // 2 minutes before expiry
                    console.log('⏰ Token will expire soon, triggering auto-refresh');
                    refreshToken();
                }
            } catch (error) {
                console.error('❌ Auto token refresh check failed:', error);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            } finally{
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
            }
        };
        // Reduced frequency to prevent excessive checking (every 2 minutes instead of 1)
        const interval = setInterval(checkAndRefresh, 120 * 1000);
        // Initial check with delay to allow other security operations to complete
        setTimeout(checkAndRefresh, 5000);
        return ()=>clearInterval(interval);
    }, [
        currentToken,
        state.isTokenValid,
        refreshToken
    ]);
    return {
        checkTokenExpiry,
        clearToken,
        getTokenExpiration,
        isTokenExpired: state.isTokenExpired,
        // State
        isTokenValid: state.isTokenValid,
        lastValidation: state.lastValidation,
        refreshToken,
        tokenError: state.tokenError,
        // Actions
        validateCurrentToken,
        willExpireSoon: state.willExpireSoon
    };
}
}}),
"[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Session Security Hook - Single Responsibility Principle (SRP)
 * @module hooks/useSessionSecurity
 *
 * This hook handles ONLY session security state and actions following SRP principles.
 * It provides session timeout detection, cross-tab logout, and concurrent session management.
 *
 * SECURITY NOTE: This manages session security state without handling authentication logic.
 */ __turbopack_context__.s({
    "useSessionSecurity": (()=>useSessionSecurity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function useSessionSecurity() {
    const { signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        concurrentSessions: [],
        isSessionActive: true,
        isSessionExpired: false,
        lastActivity: null,
        sessionId: '',
        sessionWarning: false
    });
    /**
   * Update activity timestamp
   * Single responsibility: Activity update only
   */ const updateActivity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].updateActivity();
        setState((prev)=>({
                ...prev,
                isSessionActive: true,
                lastActivity: new Date(),
                sessionWarning: false
            }));
    }, []);
    /**
   * Handle cross-tab logout
   * Single responsibility: Cross-tab logout handling only
   */ const handleCrossTabLogout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].handleCrossTabLogout();
        setState((prev)=>({
                ...prev,
                isSessionActive: false,
                isSessionExpired: true
            }));
    }, []);
    /**
   * Clear session state
   * Single responsibility: Session clearing only
   */ const clearSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].clearSessionState();
        setState({
            concurrentSessions: [],
            isSessionActive: false,
            isSessionExpired: true,
            lastActivity: null,
            sessionId: '',
            sessionWarning: false
        });
    }, []);
    /**
   * Refresh session state
   * Single responsibility: Session refresh only
   */ const refreshSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const sessionState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getSessionState();
        const sessionId = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getCurrentSessionId();
        if (sessionState) {
            setState((prev)=>({
                    ...prev,
                    isSessionActive: sessionState.isActive,
                    isSessionExpired: false,
                    lastActivity: sessionState.lastActivity,
                    sessionId,
                    sessionWarning: false
                }));
        }
        // Update concurrent sessions
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].manageConcurrentSessions();
    }, []);
    // Initialize session management and circuit breaker
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize circuit breaker first
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].initializeCircuitBreaker();
        // Initialize session management
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].initialize();
        // Perform initial integrity check
        const performInitialCheck = async ()=>{
            try {
                const integrityCheck = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].performIntegrityCheck();
                if (!integrityCheck) {
                    console.warn('📊 Initial session integrity check failed, attempting recovery...');
                    const recovered = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].recoverFromCorruptedState();
                    if (!recovered) {
                        console.error('❌ Initial session recovery failed');
                        setState((prev)=>({
                                ...prev,
                                isSessionActive: false,
                                isSessionExpired: true
                            }));
                        return;
                    }
                }
                // Set initial state after validation
                const sessionState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getSessionState();
                const sessionId = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getCurrentSessionId();
                setState((prev)=>({
                        ...prev,
                        isSessionActive: sessionState?.isActive ?? true,
                        lastActivity: sessionState?.lastActivity ?? new Date(),
                        sessionId
                    }));
                console.log('✅ Session security initialized successfully');
            } catch (error) {
                console.error('❌ Session security initialization failed:', error);
                setState((prev)=>({
                        ...prev,
                        isSessionActive: false,
                        isSessionExpired: true
                    }));
            }
        };
        performInitialCheck();
        // Cleanup on unmount
        return ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].cleanup();
        };
    }, []);
    // Listen for session events with circuit breaker protection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleSessionEvent = (event)=>{
            // Circuit breaker check for event handling
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                console.debug('🔒 Session event handling blocked by circuit breaker');
                return;
            }
            const operationId = `session-event-${event.type}`;
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                console.debug(`🔄 Session event ${event.type} already being handled`);
                return;
            }
            try {
                switch(event.type){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_EVENTS"].CROSS_TAB_LOGOUT:
                        {
                            console.log('🔄 Cross-tab logout event received');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                            setState((prev)=>({
                                    ...prev,
                                    isSessionActive: false,
                                    isSessionExpired: true
                                }));
                            // Debounced signOut to prevent multiple calls
                            setTimeout(()=>signOut(), 100);
                            break;
                        }
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_EVENTS"].SESSION_TIMEOUT:
                        {
                            console.log('⏰ Session timeout event received');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                            setState((prev)=>({
                                    ...prev,
                                    isSessionActive: false,
                                    isSessionExpired: true
                                }));
                            // Debounced signOut to prevent multiple calls
                            setTimeout(()=>signOut(), 100);
                            break;
                        }
                    case 'session_validated':
                        {
                            console.log('✅ Session validated event received');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                            setState((prev)=>({
                                    ...prev,
                                    isSessionActive: true,
                                    isSessionExpired: false,
                                    sessionWarning: false
                                }));
                            break;
                        }
                    case 'token_refresh_success':
                        {
                            console.log('🔄 Token refresh success event received');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                            // Update session state to reflect successful refresh
                            refreshSession();
                            break;
                        }
                    case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SECURITY_EVENTS"].TOKEN_REFRESH_FAILED:
                        {
                            console.warn('❌ Token refresh failed event received');
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                            setState((prev)=>({
                                    ...prev,
                                    sessionWarning: true
                                }));
                            break;
                        }
                    default:
                        {
                            console.debug(`🔍 Unknown session event: ${event.type}`);
                            break;
                        }
                }
            } catch (error) {
                console.error(`❌ Error handling session event ${event.type}:`, error);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            } finally{
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
            }
        };
        const cleanup = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].addSessionEventListener(handleSessionEvent);
        return cleanup;
    }, [
        signOut,
        refreshSession
    ]);
    // Check for session timeout periodically with circuit breaker protection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const checkSessionTimeout = ()=>{
            // Circuit breaker check - prevent verification loops
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                console.debug('🔒 Session timeout check blocked by circuit breaker');
                return;
            }
            const operationId = 'session-timeout-check';
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                console.debug('🔄 Session timeout check already in progress');
                return;
            }
            try {
                // Perform session integrity check first
                const isConsistent = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].validateSessionConsistency();
                if (!isConsistent) {
                    console.warn('📊 Session state inconsistent, attempting recovery...');
                    const recovered = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].recoverFromCorruptedState();
                    if (!recovered) {
                        console.error('❌ Session recovery failed, forcing logout');
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                        setState((prev)=>({
                                ...prev,
                                isSessionActive: false,
                                isSessionExpired: true
                            }));
                        signOut();
                        return;
                    }
                }
                // Check for timeout
                const isTimeout = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].detectTimeout();
                if (isTimeout) {
                    console.log('⏰ Session timeout detected');
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                    setState((prev)=>({
                            ...prev,
                            isSessionActive: false,
                            isSessionExpired: true
                        }));
                    // Debounced signOut to prevent multiple calls
                    setTimeout(()=>signOut(), 100);
                    return;
                }
                // Check for session warning (5 minutes before timeout)
                const sessionState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getSessionState();
                if (sessionState?.lastActivity) {
                    const timeSinceActivity = Date.now() - sessionState.lastActivity.getTime();
                    const warningThreshold = (30 - 5) * 60 * 1000; // 25 minutes (5 min warning)
                    if (timeSinceActivity > warningThreshold) {
                        setState((prev)=>({
                                ...prev,
                                sessionWarning: true
                            }));
                    }
                }
                // Record successful check
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
            } catch (error) {
                console.error('❌ Session timeout check failed:', error);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            } finally{
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
            }
        };
        // Reduced frequency to prevent excessive checking (every 2 minutes instead of 1)
        const interval = setInterval(checkSessionTimeout, 120_000);
        // Initial check with delay to allow other security operations to complete
        setTimeout(checkSessionTimeout, 1000);
        return ()=>clearInterval(interval);
    }, [
        signOut
    ]);
    // Update concurrent sessions periodically
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateConcurrentSessions = ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].manageConcurrentSessions();
        // Note: We could fetch and update concurrent sessions state here if needed
        };
        const interval = setInterval(updateConcurrentSessions, 5 * 60 * 1000); // Every 5 minutes
        return ()=>clearInterval(interval);
    }, []);
    return {
        clearSession,
        concurrentSessions: state.concurrentSessions,
        handleCrossTabLogout,
        // State
        isSessionActive: state.isSessionActive,
        isSessionExpired: state.isSessionExpired,
        lastActivity: state.lastActivity,
        refreshSession,
        sessionId: state.sessionId,
        sessionWarning: state.sessionWarning,
        // Actions
        updateActivity
    };
}
}}),
"[project]/src/lib/api/security/hooks/useSecureHttpClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useSecureHttpClient Hook - Phase 3 React Integration Layer
 * @module api/security/hooks/useSecureHttpClient
 *
 * This hook replaces useSecureApi with proper separation of concerns and dependency injection.
 * It uses the enhanced SecureApiClient with SecurityComposer integration.
 *
 * Phase 3: React Integration Layer
 * - Uses moved security hooks directly
 * - Integrates with SecurityConfigProvider
 * - Provides better separation of concerns
 * - Eliminates HTTP duplication from useSecureApi
 */ __turbopack_context__.s({
    "useSecureHttpClient": (()=>useSecureHttpClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/secureApiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-ssr] (ecmascript)");
// Import the moved security hooks
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useInputValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useInputValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function useSecureHttpClient(config = {}) {
    const { autoInitialize = true, enableLogging = true, onSecurityError, onSecurityStatusChange, ...clientConfig } = config;
    // Get auth context and security configuration
    const { session, user, loading: authLoading, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    const { config: securityConfig, isConfigValid } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecurityConfig"])();
    // Initialize security hooks (moved from hooks/security/)
    // SECURITY NOTE: HttpOnly Cookie Compliance
    // Token passed to useTokenManagement is for client-side validation only
    // Actual API authentication relies on HttpOnly cookies via credentials: 'include'
    const csrfProtection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCSRFProtection"])();
    const tokenManagement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTokenManagement"])(session?.access_token);
    const inputValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useInputValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInputValidation"])();
    const sessionSecurity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSessionSecurity"])();
    // Refs for stable references
    const clientRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const errorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Compute authentication status
    const isAuthenticated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return !!user && !!session?.access_token && !authLoading;
    }, [
        user,
        session?.access_token,
        authLoading
    ]);
    const hasValidToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return isAuthenticated && tokenManagement.isTokenValid && !tokenManagement.isTokenExpired;
    }, [
        isAuthenticated,
        tokenManagement.isTokenValid,
        tokenManagement.isTokenExpired
    ]);
    // Create security features object
    const securityFeatures = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            csrfProtection,
            tokenManagement,
            inputValidation,
            sessionSecurity
        }), [
        csrfProtection,
        tokenManagement,
        inputValidation,
        sessionSecurity
    ]);
    // Create SecureApiClient configuration
    const apiClientConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            baseURL: securityConfig.http.baseURL,
            timeout: securityConfig.http.timeout,
            retryAttempts: securityConfig.http.retryAttempts,
            getAuthToken: ()=>session?.access_token || null,
            enableCSRF: securityConfig.csrf.enabled,
            enableInputSanitization: securityConfig.inputSanitization.enabled,
            enableTokenValidation: securityConfig.tokenValidation.enabled,
            enableAutoLogout: securityConfig.authentication.autoLogout,
            securityConfig,
            validateSecurityFeatures: true,
            ...clientConfig
        }), [
        securityConfig,
        session?.access_token,
        clientConfig
    ]);
    // Initialize SecureApiClient
    const client = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        try {
            if (!isConfigValid) {
                throw new Error('Invalid security configuration');
            }
            const newClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSecureApiClient"])(apiClientConfig, securityFeatures);
            // Initialize security features if auto-initialize is enabled
            if (autoInitialize && securityFeatures) {
                newClient.initializeSecurity(securityFeatures);
            }
            clientRef.current = newClient;
            errorRef.current = null;
            if (enableLogging) {
                console.log('🔐 useSecureHttpClient: Client initialized successfully', {
                    isAuthenticated,
                    hasValidToken,
                    securityFeatures: Object.keys(securityFeatures).filter((key)=>securityFeatures[key])
                });
            }
            return newClient;
        } catch (error) {
            const clientError = error instanceof Error ? error : new Error('Failed to initialize secure client');
            errorRef.current = clientError;
            onSecurityError?.(clientError);
            console.error('useSecureHttpClient: Failed to initialize client:', clientError);
            throw clientError;
        }
    }, [
        apiClientConfig,
        securityFeatures,
        autoInitialize,
        isConfigValid,
        isAuthenticated,
        hasValidToken,
        enableLogging,
        onSecurityError
    ]);
    // Get security status
    const securityStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        try {
            return client?.getSecurityStatus() || null;
        } catch (error) {
            console.warn('useSecureHttpClient: Failed to get security status:', error);
            return null;
        }
    }, [
        client
    ]);
    // Security status change effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (securityStatus && onSecurityStatusChange) {
            onSecurityStatusChange(securityStatus);
        }
    }, [
        securityStatus,
        onSecurityStatusChange
    ]);
    // Refresh security features
    const refreshSecurityFeatures = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        try {
            if (client && securityFeatures) {
                client.refreshSecurityFeatures();
                if (enableLogging) {
                    console.log('🔄 useSecureHttpClient: Security features refreshed');
                }
            }
        } catch (error) {
            console.error('useSecureHttpClient: Failed to refresh security features:', error);
            onSecurityError?.(error instanceof Error ? error : new Error('Refresh failed'));
        }
    }, [
        client,
        securityFeatures,
        enableLogging,
        onSecurityError
    ]);
    // Update security configuration
    const updateSecurityConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((newConfig)=>{
        try {
            if (client) {
                client.updateSecurityConfig(newConfig);
                if (enableLogging) {
                    console.log('🔧 useSecureHttpClient: Security configuration updated', newConfig);
                }
            }
        } catch (error) {
            console.error('useSecureHttpClient: Failed to update security config:', error);
            onSecurityError?.(error instanceof Error ? error : new Error('Config update failed'));
        }
    }, [
        client,
        enableLogging,
        onSecurityError
    ]);
    // Sanitize input function
    const sanitizeInput = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((input)=>{
        return inputValidation.sanitizeInput(input);
    }, [
        inputValidation
    ]);
    // Refresh token function
    const refreshToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            return await tokenManagement.refreshToken();
        } catch (error) {
            console.error('useSecureHttpClient: Token refresh failed:', error);
            onSecurityError?.(error instanceof Error ? error : new Error('Token refresh failed'));
            return false;
        }
    }, [
        tokenManagement,
        onSecurityError
    ]);
    return {
        // HTTP Client Interface
        client,
        // Security Status
        isAuthenticated,
        hasValidToken,
        securityStatus,
        // Security Actions
        refreshToken,
        refreshSecurityFeatures,
        updateSecurityConfig,
        // Utility Functions
        sanitizeInput,
        // Status Flags
        isInitialized: !!client && !errorRef.current,
        isLoading: authLoading,
        error: errorRef.current
    };
}
}}),
"[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useSecureApiClient Hook - Enhanced Secure API Client for React
 * @module api/security/hooks/useSecureApiClient
 *
 * Phase 3: React Integration Layer
 * This hook provides a complete replacement for useSecureApi with enhanced architecture.
 * It combines the enhanced SecureApiClient with React-specific functionality.
 */ __turbopack_context__.s({
    "useSecureApiClient": (()=>useSecureApiClient),
    "useSecureApiClientFeatures": (()=>useSecureApiClientFeatures),
    "useSecureApiReplacement": (()=>useSecureApiReplacement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureHttpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureHttpClient.ts [app-ssr] (ecmascript)");
'use client';
;
;
function useSecureApiClient(config = {}) {
    // Use the enhanced secure HTTP client
    const { client, isAuthenticated, hasValidToken, securityStatus, refreshToken, refreshSecurityFeatures, updateSecurityConfig, sanitizeInput, isInitialized, isLoading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureHttpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureHttpClient"])(config);
    /**
   * Secure request function (compatible with useSecureApi interface)
   * This replaces the HTTP logic that was duplicated in useSecureApi
   */ const secureRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (requestConfig)=>{
        const { data, headers = {}, method = 'GET', timeout, url } = requestConfig;
        try {
            // Convert to RequestConfig format for the HTTP client
            const clientConfig = {
                headers,
                timeout: timeout || 10000
            };
            // Use the appropriate HTTP client method based on the request method
            let response;
            switch(method.toUpperCase()){
                case 'GET':
                    response = await client.get(url, clientConfig);
                    break;
                case 'POST':
                    response = await client.post(url, data, clientConfig);
                    break;
                case 'PUT':
                    response = await client.put(url, data, clientConfig);
                    break;
                case 'PATCH':
                    response = await client.patch(url, data, clientConfig);
                    break;
                case 'DELETE':
                    response = await client.delete(url, clientConfig);
                    break;
                default:
                    throw new Error(`Unsupported HTTP method: ${method}`);
            }
            // Return in the format expected by useSecureApi consumers
            return {
                data: response,
                headers: {},
                status: 200,
                statusText: 'OK'
            };
        } catch (error) {
            // Re-throw errors in a format compatible with useSecureApi
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Request failed');
        }
    }, [
        client
    ]);
    // Return interface compatible with useSecureApi plus enhanced functionality
    return {
        // Core API functionality (compatible with useSecureApi)
        hasValidToken,
        isAuthenticated,
        refreshToken,
        sanitizeInput,
        secureRequest,
        // Enhanced functionality from new architecture
        client,
        securityStatus,
        refreshSecurityFeatures,
        updateSecurityConfig,
        // Status and error handling
        isInitialized,
        isLoading,
        error
    };
}
function useSecureApiReplacement(config = {}) {
    const apiClient = useSecureApiClient(config);
    return {
        hasValidToken: apiClient.hasValidToken,
        isAuthenticated: apiClient.isAuthenticated,
        refreshToken: apiClient.refreshToken,
        sanitizeInput: apiClient.sanitizeInput,
        secureRequest: apiClient.secureRequest
    };
}
function useSecureApiClientFeatures(config = {}) {
    const apiClient = useSecureApiClient(config);
    return {
        client: apiClient.client,
        securityStatus: apiClient.securityStatus,
        refreshSecurityFeatures: apiClient.refreshSecurityFeatures,
        updateSecurityConfig: apiClient.updateSecurityConfig,
        isInitialized: apiClient.isInitialized,
        isLoading: apiClient.isLoading,
        error: apiClient.error
    };
}
}}),
"[project]/src/lib/api/security/hooks/useSecureApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Secure API Hook - UPDATED to use new architecture
 * @module hooks/useSecureApi
 *
 * Phase 4 Migration: This hook now uses the new secure API architecture
 * instead of duplicating HTTP logic. It provides the same interface but
 * leverages the enhanced SecureApiClient under the hood.
 *
 * MIGRATION NOTE: This hook maintains backward compatibility while using
 * the new architecture. HTTP duplication has been eliminated.
 */ __turbopack_context__.s({
    "ApiError": (()=>ApiError),
    "useSecureApi": (()=>useSecureApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
// Import from new architecture to eliminate HTTP duplication
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-ssr] (ecmascript)");
'use client';
;
;
class ApiError extends Error {
    code;
    details;
    status;
    constructor({ code, details, message, status }){
        super(message);
        this.name = 'ApiError';
        this.status = status || 0;
        this.code = code || '';
        this.details = details;
    }
}
function useSecureApi() {
    // Use the new architecture under the hood (eliminates HTTP duplication)
    const newApiClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiReplacement"])({
        enableLogging: true
    });
    // Convert secureRequest to match the old interface
    const secureRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (config)=>{
        try {
            // Use the new architecture's secureRequest method
            return await newApiClient.secureRequest({
                url: config.url,
                method: config.method || 'GET',
                data: config.data,
                headers: config.headers || {},
                timeout: config.timeout || 10000
            });
        } catch (error) {
            // Convert errors to ApiError for backward compatibility
            if (error instanceof Error) {
                throw new ApiError({
                    code: 'REQUEST_FAILED',
                    message: error.message,
                    details: error
                });
            }
            throw error;
        }
    }, [
        newApiClient
    ]);
    return {
        hasValidToken: newApiClient.hasValidToken,
        isAuthenticated: newApiClient.isAuthenticated,
        refreshToken: newApiClient.refreshToken,
        sanitizeInput: newApiClient.sanitizeInput,
        secureRequest
    };
}
}}),
"[project]/src/lib/api/security/hooks/useSecurityMonitoring.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Monitoring Hook - 2025 Standards
 * @module hooks/useSecurityMonitoring
 *
 * This hook provides real-time security monitoring capabilities following 2025 best practices.
 * It monitors for security events, violations, and suspicious activities.
 */ __turbopack_context__.s({
    "useSecurityMonitoring": (()=>useSecurityMonitoring)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function useSecurityMonitoring() {
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    const [events, setEvents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isMonitoring, setIsMonitoring] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    /**
   * Generate unique event ID
   */ const generateEventId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }, []);
    /**
   * Calculate threat level based on recent events with enhanced verification loop detection
   */ const calculateThreatLevel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((events)=>{
        const recentEvents = events.filter((event)=>Date.now() - new Date(event.timestamp).getTime() < 5 * 60 * 1000 // Last 5 minutes
        );
        const criticalCount = recentEvents.filter((e)=>e.severity === 'critical').length;
        const highCount = recentEvents.filter((e)=>e.severity === 'high').length;
        const mediumCount = recentEvents.filter((e)=>e.severity === 'medium').length;
        // Enhanced threat detection for verification loops
        const verificationLoopEvents = recentEvents.filter((e)=>e.type === 'verification_loop' || e.type === 'circuit_breaker_triggered').length;
        const sessionIntegrityEvents = recentEvents.filter((e)=>e.type === 'session_integrity_failure').length;
        const concurrentOpEvents = recentEvents.filter((e)=>e.type === 'concurrent_security_operation').length;
        // Critical threat conditions
        if (criticalCount > 0 || verificationLoopEvents > 0) return 'critical';
        // High threat conditions
        if (highCount >= 3 || highCount >= 1 && sessionIntegrityEvents > 2) return 'high';
        // Medium threat conditions
        if (highCount >= 1 || mediumCount >= 5 || sessionIntegrityEvents > 1 || concurrentOpEvents > 3) return 'medium';
        return 'low';
    }, []);
    /**
   * Report a security event
   */ const reportSecurityEvent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((eventData)=>{
        const event = {
            ...eventData,
            id: generateEventId(),
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        };
        setEvents((prev)=>{
            const updated = [
                event,
                ...prev
            ].slice(0, 100); // Keep last 100 events
            // Log critical events immediately
            if (event.severity === 'critical') {
                console.error('🚨 CRITICAL SECURITY EVENT:', event);
            } else if (event.severity === 'high') {
                console.warn('⚠️ HIGH SECURITY EVENT:', event);
            }
            return updated;
        });
        // Send to backend in production
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, [
        generateEventId
    ]);
    /**
   * Monitor for CSP violations
   */ const monitorCSPViolations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const handleCSPViolation = (event)=>{
            reportSecurityEvent({
                type: 'csp_violation',
                severity: event.effectiveDirective === 'script-src' ? 'critical' : 'high',
                details: {
                    violatedDirective: event.violatedDirective,
                    effectiveDirective: event.effectiveDirective,
                    blockedURI: event.blockedURI,
                    sourceFile: event.sourceFile,
                    lineNumber: event.lineNumber,
                    columnNumber: event.columnNumber,
                    originalPolicy: event.originalPolicy
                }
            });
        };
        document.addEventListener('securitypolicyviolation', handleCSPViolation);
        return ()=>{
            document.removeEventListener('securitypolicyviolation', handleCSPViolation);
        };
    }, [
        reportSecurityEvent
    ]);
    /**
   * Monitor for suspicious DOM manipulation
   */ const monitorDOMChanges = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const observer = new MutationObserver((mutations)=>{
            mutations.forEach((mutation)=>{
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node)=>{
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const element = node;
                            // Check for suspicious script injections
                            if (element.tagName === 'SCRIPT') {
                                const src = element.getAttribute('src');
                                const content = element.textContent;
                                if (src && !src.startsWith(window.location.origin) && !src.startsWith('https://')) {
                                    reportSecurityEvent({
                                        type: 'xss_attempt',
                                        severity: 'critical',
                                        details: {
                                            type: 'script_injection',
                                            src,
                                            content: content?.substring(0, 100)
                                        }
                                    });
                                }
                            }
                            // Check for suspicious iframe injections
                            if (element.tagName === 'IFRAME') {
                                const src = element.getAttribute('src');
                                if (src && !src.startsWith(window.location.origin)) {
                                    reportSecurityEvent({
                                        type: 'xss_attempt',
                                        severity: 'high',
                                        details: {
                                            type: 'iframe_injection',
                                            src
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            });
        });
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        return ()=>observer.disconnect();
    }, [
        reportSecurityEvent
    ]);
    /**
   * Monitor for authentication failures
   */ const monitorAuthFailures = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        let failureCount = 0;
        const maxFailures = 5;
        const timeWindow = 5 * 60 * 1000; // 5 minutes
        const handleAuthFailure = ()=>{
            failureCount++;
            if (failureCount >= maxFailures) {
                reportSecurityEvent({
                    type: 'auth_failure',
                    severity: 'high',
                    details: {
                        failureCount,
                        timeWindow: timeWindow / 1000,
                        userId: user?.id
                    }
                });
            }
            // Reset counter after time window
            setTimeout(()=>{
                failureCount = Math.max(0, failureCount - 1);
            }, timeWindow);
        };
        // Listen for auth failure events
        window.addEventListener('auth:failure', handleAuthFailure);
        return ()=>{
            window.removeEventListener('auth:failure', handleAuthFailure);
        };
    }, [
        reportSecurityEvent,
        user?.id
    ]);
    /**
   * Monitor for verification loop detection
   */ const monitorVerificationLoops = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        let lastCircuitBreakerCheck = 0;
        const checkInterval = 30000; // Check every 30 seconds
        const checkVerificationLoops = ()=>{
            const now = Date.now();
            if (now - lastCircuitBreakerCheck < checkInterval) return;
            lastCircuitBreakerCheck = now;
            try {
                // Check circuit breaker state
                const circuitBreakerState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].getCircuitBreakerState();
                if (circuitBreakerState.isOpen) {
                    reportSecurityEvent({
                        type: 'circuit_breaker_triggered',
                        severity: 'high',
                        details: {
                            attemptCount: circuitBreakerState.attemptCount,
                            lastAttempt: circuitBreakerState.lastAttempt,
                            isOpen: circuitBreakerState.isOpen,
                            reason: 'Too many security verification attempts'
                        }
                    });
                }
                // Check for excessive security attempts
                if (circuitBreakerState.attemptCount > 10) {
                    reportSecurityEvent({
                        type: 'verification_loop',
                        severity: 'critical',
                        details: {
                            attemptCount: circuitBreakerState.attemptCount,
                            timeWindow: '5 minutes',
                            pattern: 'Potential verification loop detected'
                        }
                    });
                }
                // Check session integrity
                const sessionState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].getSessionState();
                if (sessionState && !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].validateSessionConsistency()) {
                    reportSecurityEvent({
                        type: 'session_integrity_failure',
                        severity: 'medium',
                        details: {
                            sessionId: sessionState.sessionId,
                            lastActivity: sessionState.lastActivity,
                            isActive: sessionState.isActive
                        }
                    });
                }
            } catch (error) {
                console.error('Error monitoring verification loops:', error);
            }
        };
        // Initial check
        checkVerificationLoops();
        // Set up interval monitoring
        const intervalId = setInterval(checkVerificationLoops, checkInterval);
        return ()=>{
            clearInterval(intervalId);
        };
    }, [
        reportSecurityEvent
    ]);
    /**
   * Monitor for concurrent security operations
   */ const monitorConcurrentOperations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        let operationCount = 0;
        const maxConcurrentOps = 3;
        const handleSecurityOperationStart = ()=>{
            operationCount++;
            if (operationCount > maxConcurrentOps) {
                reportSecurityEvent({
                    type: 'concurrent_security_operation',
                    severity: 'medium',
                    details: {
                        operationCount,
                        maxAllowed: maxConcurrentOps,
                        timestamp: new Date().toISOString()
                    }
                });
            }
        };
        const handleSecurityOperationEnd = ()=>{
            operationCount = Math.max(0, operationCount - 1);
        };
        // Listen for security operation events
        window.addEventListener('security:operation:start', handleSecurityOperationStart);
        window.addEventListener('security:operation:end', handleSecurityOperationEnd);
        return ()=>{
            window.removeEventListener('security:operation:start', handleSecurityOperationStart);
            window.removeEventListener('security:operation:end', handleSecurityOperationEnd);
        };
    }, [
        reportSecurityEvent
    ]);
    /**
   * Start security monitoring with enhanced verification loop detection
   */ const startMonitoring = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (isMonitoring) return;
        setIsMonitoring(true);
        console.log('🔍 Starting enhanced security monitoring with verification loop detection');
        const cleanupFunctions = [
            monitorCSPViolations(),
            monitorDOMChanges(),
            monitorAuthFailures(),
            monitorVerificationLoops(),
            monitorConcurrentOperations()
        ];
        // Store cleanup functions for later use
        window.__securityCleanup = cleanupFunctions;
        // Report monitoring start event
        reportSecurityEvent({
            type: 'suspicious_activity',
            severity: 'low',
            details: {
                action: 'security_monitoring_started',
                features: [
                    'csp_violations',
                    'dom_changes',
                    'auth_failures',
                    'verification_loops',
                    'concurrent_operations'
                ]
            }
        });
    }, [
        isMonitoring,
        monitorCSPViolations,
        monitorDOMChanges,
        monitorAuthFailures,
        monitorVerificationLoops,
        monitorConcurrentOperations,
        reportSecurityEvent
    ]);
    /**
   * Stop security monitoring
   */ const stopMonitoring = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!isMonitoring) return;
        setIsMonitoring(false);
        const cleanupFunctions = window.__securityCleanup;
        if (cleanupFunctions) {
            cleanupFunctions.forEach((cleanup)=>cleanup());
            delete window.__securityCleanup;
        }
    }, [
        isMonitoring
    ]);
    /**
   * Clear all events
   */ const clearEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setEvents([]);
    }, []);
    /**
   * Calculate metrics
   */ const metrics = {
        totalEvents: events.length,
        criticalEvents: events.filter((e)=>e.severity === 'critical').length,
        recentEvents: events.slice(0, 10),
        threatLevel: calculateThreatLevel(events)
    };
    /**
   * Auto-start monitoring on mount
   */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        startMonitoring();
        return ()=>{
            stopMonitoring();
        };
    }, [
        startMonitoring,
        stopMonitoring
    ]);
    return {
        metrics,
        reportSecurityEvent,
        clearEvents,
        isMonitoring,
        startMonitoring,
        stopMonitoring
    };
}
}}),
"[project]/src/lib/api/security/hooks/useTokenRefreshStatus.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Hook for monitoring token refresh status and providing user feedback
 * @module hooks/useTokenRefreshStatus
 */ __turbopack_context__.s({
    "useAuthenticationRequired": (()=>useAuthenticationRequired),
    "useTokenRefreshMessages": (()=>useTokenRefreshMessages),
    "useTokenRefreshStatus": (()=>useTokenRefreshStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-ssr] (ecmascript)");
;
;
function useTokenRefreshStatus() {
    const [statusInfo, setStatusInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        status: 'idle',
        lastRefresh: null,
        nextRefresh: null,
        error: null,
        retryAttempts: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        const unsubscribe = tokenRefreshService.subscribe((event, data)=>{
            switch(event){
                case 'refresh_scheduled':
                    setStatusInfo((prev)=>({
                            ...prev,
                            status: 'scheduled',
                            nextRefresh: data?.refreshAt ? new Date(data.refreshAt) : null,
                            error: null
                        }));
                    break;
                case 'refresh_success':
                    setStatusInfo((prev)=>({
                            ...prev,
                            status: 'success',
                            lastRefresh: new Date(),
                            error: null,
                            retryAttempts: 0
                        }));
                    // Reset to idle after a short delay
                    setTimeout(()=>{
                        setStatusInfo((prev)=>({
                                ...prev,
                                status: 'idle'
                            }));
                    }, 2000);
                    break;
                case 'refresh_failed':
                    setStatusInfo((prev)=>({
                            ...prev,
                            status: 'failed',
                            error: data?.error || 'Token refresh failed',
                            retryAttempts: data?.attempts || 0
                        }));
                    break;
            }
        });
        return unsubscribe;
    }, []);
    return statusInfo;
}
function useTokenRefreshMessages() {
    const statusInfo = useTokenRefreshStatus();
    switch(statusInfo.status){
        case 'scheduled':
            return {
                message: statusInfo.nextRefresh ? `Authentication will refresh at ${statusInfo.nextRefresh.toLocaleTimeString()}` : 'Authentication refresh scheduled',
                type: 'info'
            };
        case 'success':
            return {
                message: 'Authentication refreshed successfully',
                type: 'success'
            };
        case 'failed':
            if (statusInfo.retryAttempts >= 3) {
                return {
                    message: 'Authentication refresh failed. Please log in again.',
                    type: 'error'
                };
            }
            return {
                message: `Authentication refresh failed (attempt ${statusInfo.retryAttempts}/3). Retrying...`,
                type: 'warning'
            };
        default:
            return {
                message: null,
                type: null
            };
    }
}
function useAuthenticationRequired() {
    const statusInfo = useTokenRefreshStatus();
    return statusInfo.status === 'failed' && statusInfo.retryAttempts >= 3;
}
}}),
"[project]/src/lib/api/security/hooks/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Hooks Index
 * @description Centralized exports for all security-related hooks
 *
 * Includes:
 * - Original security hooks (Phase 1)
 * - New Phase 3 React Integration Layer hooks
 */ // Security hooks (Phase 1)
__turbopack_context__.s({
    "SecurityHookUtils": (()=>SecurityHookUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)");
// Input validation and sanitization
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useInputValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useInputValidation.ts [app-ssr] (ecmascript)");
// Permissions Hook (SRP)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$usePermissions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/usePermissions.ts [app-ssr] (ecmascript)");
// Secure API communication
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-ssr] (ecmascript)");
// Phase 3: React Integration Layer hooks
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureHttpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureHttpClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecurityMonitoring$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecurityMonitoring.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)");
// Token refresh monitoring
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenRefreshStatus$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenRefreshStatus.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const SecurityHookUtils = {
    sanitizeInput: (input)=>{
        return input.replaceAll(/[<>]/g, '');
    },
    // Common validation patterns
    validateEmail: (email)=>{
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    validatePassword: (password)=>{
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }
};
}}),
"[project]/src/lib/api/security/hooks/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useInputValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useInputValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$usePermissions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/usePermissions.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureHttpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureHttpClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecurityMonitoring$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecurityMonitoring.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenRefreshStatus$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenRefreshStatus.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/security/providers/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Providers Index
 * @module api/security/providers
 * 
 * Phase 3: React Integration Layer
 * Centralized exports for all security providers and contexts
 */ // Security Configuration Provider
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-ssr] (ecmascript)");
;
}}),
"[project]/src/lib/api/security/providers/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Module Index
 * @module api/security
 * 
 * Centralized exports for all security-related functionality
 * 
 * Phase 3: React Integration Layer
 * - Security hooks (original + new React integration)
 * - Security providers and contexts
 * - Enhanced SecureApiClient with SecurityComposer
 */ // Export security hooks (Phase 1 + Phase 3)
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/index.ts [app-ssr] (ecmascript) <module evaluation>");
// Export security providers (Phase 3)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/index.ts [app-ssr] (ecmascript) <module evaluation>");
// Export security composer (Phase 2)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/composer.ts [app-ssr] (ecmascript)");
// Export secure API client (Phase 2 Enhanced)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/secureApiClient.ts [app-ssr] (ecmascript)");
;
;
;
;
}}),
"[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/composer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/secureApiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/transformers/delegationTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Delegation domain models.
 * @module transformers/delegationTransformer
 */ // API types from the new central location
__turbopack_context__.s({
    "DelegationTransformer": (()=>DelegationTransformer),
    "FlightDetailsTransformer": (()=>FlightDetailsTransformer)
});
// These helpers now produce structures compatible with CreateDelegationRequest sub-properties
const DelegationAssignmentTransformers = {
    // fromApi methods are less critical now as DelegationApiResponse is domain Delegation
    // but if API responses for these are ever separate and different, they'd be useful.
    // For now, their usage in DelegationTransformer.fromApi might be simplified.
    toDelegateApiStructure (domainData) {
        // The structure of the domain data is identical to CreateDelegateRequest
        return {
            name: domainData.name,
            notes: domainData.notes ?? null,
            title: domainData.title
        };
    },
    toDriverApiStructure (domainData) {
        // The structure of the domain data is identical to CreateDriverRequest
        return {
            employeeId: domainData.employeeId,
            notes: domainData.notes ?? null
        };
    },
    toEscortApiStructure (domainData) {
        // The structure of the domain data is identical to CreateEscortRequest
        return {
            employeeId: domainData.employeeId,
            notes: domainData.notes ?? null
        };
    },
    toVehicleAssignmentApiStructure (domainData) {
        // The structure of the domain data is identical to CreateVehicleAssignmentRequest
        return domainData;
    }
};
// Helper transformers for converting API response types to domain types
const EmployeeTransformer = {
    fromApi (apiData) {
        return {
            availability: apiData.availability,
            contactEmail: apiData.contactEmail ?? null,
            contactInfo: apiData.contactInfo,
            contactMobile: apiData.contactMobile ?? null,
            contactPhone: apiData.contactPhone ?? null,
            createdAt: apiData.createdAt,
            currentLocation: apiData.currentLocation ?? null,
            department: apiData.department ?? null,
            employeeId: apiData.employeeId,
            fullName: apiData.fullName ?? null,
            generalAssignments: apiData.generalAssignments,
            hireDate: apiData.hireDate ?? null,
            id: apiData.id,
            name: apiData.name,
            notes: apiData.notes ?? null,
            position: apiData.position ?? null,
            profileImageUrl: apiData.profileImageUrl ?? null,
            role: apiData.role,
            shiftSchedule: apiData.shiftSchedule ?? null,
            skills: apiData.skills,
            status: apiData.status,
            updatedAt: apiData.updatedAt,
            workingHours: apiData.workingHours ?? null
        };
    }
};
const VehicleTransformer = {
    fromApi (apiData) {
        return {
            color: apiData.color,
            createdAt: apiData.createdAt,
            id: apiData.id,
            imageUrl: apiData.imageUrl,
            initialOdometer: apiData.initialOdometer,
            licensePlate: apiData.licensePlate,
            make: apiData.make,
            model: apiData.model,
            ownerContact: apiData.ownerContact,
            ownerName: apiData.ownerName,
            serviceHistory: [],
            updatedAt: apiData.updatedAt,
            vin: apiData.vin,
            year: apiData.year
        };
    }
};
const DelegationTransformer = {
    fromApi (apiData) {
        return {
            // Map nested relations using their respective transformers or direct mapping if structure matches
            arrivalFlight: apiData.flightArrivalDetails ? FlightDetailsTransformer.fromApi(apiData.flightArrivalDetails) : null,
            createdAt: apiData.createdAt,
            delegates: apiData.delegates || [],
            departureFlight: apiData.flightDepartureDetails ? FlightDetailsTransformer.fromApi(apiData.flightDepartureDetails) : null,
            // Transform drivers: API returns Employee objects directly, but domain expects nested structure
            drivers: apiData.drivers?.map((driverEmployee)=>({
                    createdAt: apiData.createdAt,
                    createdBy: null,
                    delegationId: apiData.id,
                    employee: EmployeeTransformer.fromApi(driverEmployee),
                    employeeId: driverEmployee.id,
                    id: `driver-${apiData.id}-${driverEmployee.id}`,
                    updatedAt: apiData.updatedAt
                })) || [],
            durationFrom: apiData.durationFrom,
            durationTo: apiData.durationTo,
            // Transform escorts: API returns Employee objects directly, but domain expects nested structure
            escorts: apiData.escorts?.map((escortEmployee)=>({
                    createdAt: apiData.createdAt,
                    createdBy: null,
                    delegationId: apiData.id,
                    employee: EmployeeTransformer.fromApi(escortEmployee),
                    employeeId: escortEmployee.id,
                    id: `escort-${apiData.id}-${escortEmployee.id}`,
                    updatedAt: apiData.updatedAt
                })) || [],
            eventName: apiData.eventName,
            id: apiData.id,
            imageUrl: apiData.imageUrl ?? null,
            invitationFrom: apiData.invitationFrom ?? null,
            invitationTo: apiData.invitationTo ?? null,
            location: apiData.location,
            notes: apiData.notes ?? null,
            status: apiData.status,
            statusHistory: apiData.statusHistory || [],
            updatedAt: apiData.updatedAt,
            // Transform vehicles: API returns Vehicle objects directly, but domain expects nested structure
            vehicles: apiData.vehicles?.map((vehicleData)=>({
                    createdAt: apiData.createdAt,
                    createdBy: null,
                    delegationId: apiData.id,
                    id: `vehicle-${apiData.id}-${vehicleData.id}`,
                    updatedAt: apiData.updatedAt,
                    vehicle: VehicleTransformer.fromApi(vehicleData),
                    vehicleId: vehicleData.id
                })) || []
        };
    },
    toCreateRequest (domainData) {
        return {
            // ✅ FIXED: Use correct field names that match backend schema
            delegates: domainData.delegates?.map((delegate)=>DelegationAssignmentTransformers.toDelegateApiStructure(delegate)) ?? [],
            driverEmployeeIds: domainData.drivers?.map((driver)=>driver.employeeId) ?? [],
            durationFrom: domainData.durationFrom,
            durationTo: domainData.durationTo,
            escortEmployeeIds: domainData.escorts?.map((escort)=>escort.employeeId) ?? [],
            eventName: domainData.eventName,
            flightArrivalDetails: domainData.flightArrivalDetails ? FlightDetailsTransformer.toApiStructureForCreate(domainData.flightArrivalDetails) : undefined,
            flightDepartureDetails: domainData.flightDepartureDetails ? FlightDetailsTransformer.toApiStructureForCreate(domainData.flightDepartureDetails) : undefined,
            imageUrl: domainData.imageUrl ?? null,
            invitationFrom: domainData.invitationFrom ?? null,
            invitationTo: domainData.invitationTo ?? null,
            location: domainData.location,
            notes: domainData.notes ?? null,
            status: domainData.status,
            vehicleIds: domainData.vehicles?.map((vehicle)=>vehicle.vehicleId) ?? []
        };
    },
    toUpdateRequest (domainData) {
        const request = {};
        // ✅ PRODUCTION FIX: Use correct field names that match backend expectations
        if (domainData.eventName !== undefined) request.eventName = domainData.eventName; // ✅ Direct mapping
        if (domainData.location !== undefined) request.location = domainData.location;
        if (domainData.durationFrom !== undefined) request.durationFrom = domainData.durationFrom; // ✅ Direct mapping
        if (domainData.durationTo !== undefined) request.durationTo = domainData.durationTo; // ✅ Direct mapping
        if (domainData.status !== undefined) request.status = domainData.status; // Type assertion
        if (domainData.notes !== undefined) request.notes = domainData.notes; // ✅ Direct mapping
        if (domainData.imageUrl !== undefined) request.imageUrl = domainData.imageUrl;
        if (domainData.invitationFrom !== undefined) request.invitationFrom = domainData.invitationFrom;
        if (domainData.invitationTo !== undefined) request.invitationTo = domainData.invitationTo;
        if (domainData.flightArrivalDetails !== undefined) {
            request.flightArrivalDetails = domainData.flightArrivalDetails ?? undefined;
        }
        if (domainData.flightDepartureDetails !== undefined) {
            request.flightDepartureDetails = domainData.flightDepartureDetails ?? undefined;
        }
        // ✅ FIX: Include assignment fields in update request
        if (domainData.escorts !== undefined) {
            request.escortEmployeeIds = domainData.escorts.map((e)=>e.employeeId);
        }
        if (domainData.drivers !== undefined) {
            request.driverEmployeeIds = domainData.drivers.map((d)=>d.employeeId);
        }
        if (domainData.vehicles !== undefined) {
            request.vehicleIds = domainData.vehicles.map((v)=>v.vehicleId);
        }
        return request;
    }
};
const FlightDetailsTransformer = {
    fromApi (apiData) {
        if (!apiData) return null;
        return {
            airport: apiData.airport,
            dateTime: apiData.dateTime,
            flightNumber: apiData.flightNumber,
            id: apiData.id,
            notes: apiData.notes || null,
            terminal: apiData.terminal || null
        };
    },
    // This is for creating flight details as part of a new delegation
    toApiStructureForCreate (domainData) {
        // The structure of Omit<FlightDetails, 'id'> is identical to CreateFlightDetailsRequest
        return domainData;
    },
    toCreateRequest (domainData) {
        // This is for managing details on an existing delegation, not used for initial creation
        return {
            airport: domainData.airport,
            dateTime: domainData.dateTime,
            flightNumber: domainData.flightNumber,
            notes: domainData.notes ?? null,
            terminal: domainData.terminal ?? null
        };
    }
};
}}),
"[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DelegationApiService": (()=>DelegationApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/delegationTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
;
const DelegationApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(data),
    toApi: (data)=>data
};
const FlightDetailsTransformer = {
    toCreateRequest: (data)=>data
};
class DelegationApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/delegations';
    transformer = DelegationApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 2 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    async getByStatus(status) {
        const result = await this.getAll({
            status
        });
        return result.data;
    }
    async manageFlightDetails(id, flightDetails) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = FlightDetailsTransformer.toCreateRequest(flightDetails);
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/flight-details`, requestPayload);
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setDelegates(id, delegates) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/delegates`, {
                delegates
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setDrivers(id, driverEmployeeIds) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = driverEmployeeIds.map((employeeId)=>({
                    employeeId
                }));
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/drivers`, {
                drivers: requestPayload
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setEscorts(id, escortEmployeeIds) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = escortEmployeeIds.map((employeeId)=>({
                    employeeId
                }));
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/escorts`, {
                escorts: requestPayload
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setVehicles(id, vehicleIds) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = vehicleIds.map((vehicleId)=>({
                    assignedDate: new Date().toISOString(),
                    vehicleId
                }));
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/vehicles`, {
                vehicleAssignments: requestPayload
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async updateStatus(id, newStatus, statusChangeReason) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.put(`${this.endpoint}/${id}`, {
                status: newStatus,
                statusChangeReason
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
}
}}),
"[project]/src/lib/utils/dateUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_DATE_CONFIG": (()=>DEFAULT_DATE_CONFIG),
    "createDateConfigFromLocale": (()=>createDateConfigFromLocale),
    "dateUtilsExamples": (()=>dateUtilsExamples),
    "detectDateFormat": (()=>detectDateFormat),
    "formatDateForApi": (()=>formatDateForApi),
    "formatDateForDisplay": (()=>formatDateForDisplay),
    "formatDateForInput": (()=>formatDateForInput),
    "formatFlightDateTimeForApi": (()=>formatFlightDateTimeForApi),
    "getDateFormatInfo": (()=>getDateFormatInfo),
    "isDateAfter": (()=>isDateAfter),
    "isValidDateString": (()=>isValidDateString),
    "isValidIsoDateString": (()=>isValidIsoDateString),
    "parseDateFromApi": (()=>parseDateFromApi),
    "parseSmartDate": (()=>parseSmartDate),
    "safeFormatDateForInput": (()=>safeFormatDateForInput),
    "testEuDateFormat": (()=>testEuDateFormat),
    "validateDateString": (()=>validateDateString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isDate$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isDate.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isValid.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/parse.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/parseISO.mjs [app-ssr] (ecmascript)");
;
const DEFAULT_DATE_CONFIG = {
    fallbackToBothFormats: true,
    locale: 'en-GB',
    primaryFormat: 'EU'
};
const parseSmartDate = (dateString, config = DEFAULT_DATE_CONFIG)=>{
    if (!dateString || dateString.trim() === '') return null;
    const trimmed = dateString.trim();
    try {
        // 1. Try ISO format first (most reliable)
        if (trimmed.includes('T') || trimmed.includes('Z') || /^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
            const isoDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(trimmed);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(isoDate)) return isoDate;
        }
        // 2. Try YYYY-MM-DD format (HTML date input)
        if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
            const htmlDate = new Date(trimmed + 'T00:00:00');
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(htmlDate)) return htmlDate;
        }
        // 3. Handle DD/MM/YYYY vs MM/DD/YYYY ambiguity
        const slashMatch = trimmed.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);
        if (slashMatch) {
            const [, first, second] = slashMatch;
            // Determine which format to try first
            const formats = config.primaryFormat === 'EU' ? [
                'dd/MM/yyyy',
                'MM/dd/yyyy'
            ] : [
                'MM/dd/yyyy',
                'dd/MM/yyyy'
            ];
            for (const formatStr of formats){
                try {
                    const parsedDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["parse"])(trimmed, formatStr, new Date());
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(parsedDate)) {
                        // Additional validation: check if the parsed values make sense, accounting for potential year ambiguity in 2-digit years
                        const month = formatStr.startsWith('MM') ? Number.parseInt(first, 10) : Number.parseInt(second, 10);
                        const day = formatStr.startsWith('MM') ? Number.parseInt(second, 10) : Number.parseInt(first, 10);
                        if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                            return parsedDate;
                        }
                    }
                } catch (error) {
                    // Log the error for debugging purposes
                    console.debug('Error occurred while parsing date:', error);
                // Continue to next format
                }
            }
            // If fallback is disabled, only try primary format
            if (!config.fallbackToBothFormats) return null;
        }
        // 4. Handle other common formats
        const commonFormats = [
            'dd-MM-yyyy',
            'MM-dd-yyyy',
            'yyyy/MM/dd',
            'dd.MM.yyyy',
            'MM.dd.yyyy'
        ];
        for (const formatStr of commonFormats){
            try {
                const parsedDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parse$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["parse"])(trimmed, formatStr, new Date());
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(parsedDate)) return parsedDate;
            } catch  {
            // Continue to next format
            }
        }
        // 5. Last resort: try native Date constructor (least reliable)
        const nativeDate = new Date(trimmed);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(nativeDate)) return nativeDate;
        return null;
    } catch  {
        return null;
    }
};
const formatDateForInput = (dateValue, type = 'datetime-local')=>{
    if (!dateValue) return '';
    try {
        // Convert to Date object if it's a string
        const date = typeof dateValue === 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(dateValue) : dateValue;
        // Check if the date is valid
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(date)) {
            return '';
        }
        // Format based on input type
        if (type === 'date') {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'yyyy-MM-dd');
        }
        // For datetime-local, format is 'yyyy-MM-ddTHH:mm'
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "yyyy-MM-dd'T'HH:mm");
    } catch  {
        return '';
    }
};
const formatDateForDisplay = (dateValue, includeTime = false)=>{
    if (!dateValue) return 'N/A';
    try {
        // Convert to Date object if it's a string
        const date = typeof dateValue === 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(dateValue) : dateValue;
        // Check if the date is valid
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(date)) {
            return 'Invalid Date';
        }
        // Format with or without time
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, includeTime ? 'MMM d, yyyy, HH:mm' : 'MMM d, yyyy');
    } catch  {
        return 'Invalid Date';
    }
};
const formatDateForApi = (dateValue, config = DEFAULT_DATE_CONFIG)=>{
    if (!dateValue) return '';
    try {
        // If it's already a Date object, format it directly
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isDate$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(dateValue)) {
            // Use toISOString() to ensure UTC format ending in 'Z'
            return dateValue.toISOString();
        }
        // For string input, use smart parsing to handle multiple formats
        if (typeof dateValue === 'string') {
            const parsedDate = parseSmartDate(dateValue, config);
            if (parsedDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(parsedDate)) {
                // Use toISOString() to ensure UTC format ending in 'Z'
                // This is required by the backend's Zod z.string().datetime() validation
                return parsedDate.toISOString();
            }
        }
        return '';
    } catch  {
        return '';
    }
};
const parseDateFromApi = (dateString)=>{
    if (!dateString) return null;
    try {
        const date = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(dateString);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(date) ? date : null;
    } catch  {
        return null;
    }
};
const isValidIsoDateString = (dateString)=>{
    if (!dateString) return false;
    try {
        const date = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(dateString);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(date);
    } catch  {
        return false;
    }
};
const isValidDateString = (dateString)=>{
    if (!dateString) return false;
    try {
        const date = new Date(dateString);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(date);
    } catch  {
        return false;
    }
};
const isDateAfter = (date1, date2)=>{
    if (!date1 || !date2) return false;
    try {
        const parsedDate1 = typeof date1 === 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(date1) : date1;
        const parsedDate2 = typeof date2 === 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseISO"])(date2) : date2;
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(parsedDate1) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(parsedDate2)) {
            return false;
        }
        return parsedDate1 > parsedDate2;
    } catch  {
        return false;
    }
};
const formatFlightDateTimeForApi = (dateTime)=>{
    if (!dateTime || dateTime.trim() === '') return '';
    // Use the general formatDateForApi function which handles both ISO and datetime-local formats
    return formatDateForApi(dateTime);
};
const safeFormatDateForInput = (dateValue, type = 'datetime-local')=>{
    if (!dateValue) return '';
    return formatDateForInput(dateValue, type);
};
const detectDateFormat = (dateString)=>{
    if (!dateString || dateString.trim() === '') return null;
    const trimmed = dateString.trim();
    // ISO format detection
    if (trimmed.includes('T') || trimmed.includes('Z') || /^\d{4}-\d{2}-\d{2}/.test(trimmed)) {
        return 'ISO';
    }
    // Slash format analysis
    const slashMatch = trimmed.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);
    if (slashMatch) {
        const [, first, second] = slashMatch;
        const firstNum = Number.parseInt(first, 10);
        const secondNum = Number.parseInt(second, 10);
        // If first number > 12, it must be day (EU format)
        if (firstNum > 12) return 'EU';
        // If second number > 12, it must be day (US format)
        if (secondNum > 12) return 'US';
        // If both are <= 12, it's ambiguous
        return null;
    }
    return null;
};
const createDateConfigFromLocale = (locale)=>{
    if (!locale) return DEFAULT_DATE_CONFIG;
    // US-style locales (MM/DD/YY)
    const usLocales = [
        'en-US',
        'en-CA'
    ];
    // EU-style locales (DD/MM/YY)
    const euLocales = [
        'en-GB',
        'en-AU',
        'de-DE',
        'fr-FR',
        'es-ES',
        'it-IT',
        'pt-PT',
        'nl-NL'
    ];
    if (usLocales.includes(locale)) {
        return {
            fallbackToBothFormats: true,
            locale,
            primaryFormat: 'US'
        };
    }
    if (euLocales.some((euLocale)=>locale.startsWith(euLocale.split('-')[0]))) {
        return {
            fallbackToBothFormats: true,
            locale,
            primaryFormat: 'EU'
        };
    }
    // Default to US format for unknown locales
    return {
        ...DEFAULT_DATE_CONFIG,
        locale
    };
};
const validateDateString = (dateString, config = DEFAULT_DATE_CONFIG)=>{
    if (!dateString || dateString.trim() === '') {
        return {
            isValid: false,
            suggestion: 'Date is required'
        };
    }
    const detectedFormat = detectDateFormat(dateString);
    const parsedDate = parseSmartDate(dateString, config);
    if (parsedDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(parsedDate)) {
        return {
            ...detectedFormat && {
                detectedFormat
            },
            isValid: true,
            parsedDate
        };
    }
    // Provide helpful suggestions based on detected issues
    const slashMatch = dateString.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);
    if (slashMatch) {
        const [, first, second] = slashMatch;
        const firstNum = Number.parseInt(first, 10);
        const secondNum = Number.parseInt(second, 10);
        if (firstNum > 12 && secondNum > 12) {
            return {
                isValid: false,
                suggestion: 'Invalid date: both day and month cannot be greater than 12'
            };
        }
        if (firstNum > 31 || secondNum > 31) {
            return {
                isValid: false,
                suggestion: 'Invalid date: day cannot be greater than 31'
            };
        }
        return {
            isValid: false,
            suggestion: `Ambiguous date format. Try using ${config.primaryFormat === 'EU' ? 'DD/MM/YYYY' : 'MM/DD/YYYY'} format or YYYY-MM-DD`
        };
    }
    return {
        isValid: false,
        suggestion: 'Invalid date format. Use DD/MM/YYYY, MM/DD/YYYY, or YYYY-MM-DD'
    };
};
const dateUtilsExamples = ()=>{
    // Example 1: Ambiguous date with EU preference (now default)
    console.log('Default EU Config - "01/02/2025":', formatDateForApi('01/02/2025'));
    // Result: February 1, 2025 (DD/MM/YYYY format)
    // Example 2: Ambiguous date with US preference (explicit config)
    const usConfig = {
        fallbackToBothFormats: true,
        primaryFormat: 'US'
    };
    console.log('US Config - "01/02/2025":', formatDateForApi('01/02/2025', usConfig));
    // Result: January 2, 2025 (MM/DD/YYYY format)
    // Example 3: Unambiguous date (day > 12) - same result regardless of config
    console.log('Unambiguous - "13/02/2025":', formatDateForApi('13/02/2025'));
    // Result: February 13, 2025 (automatically detected as DD/MM format)
    // Example 4: ISO format (always safe and unambiguous)
    console.log('ISO - "2025-02-01":', formatDateForApi('2025-02-01'));
    // Result: February 1, 2025
    // Example 5: Validation with helpful suggestions
    console.log('Validation - "32/13/2025":', validateDateString('32/13/2025'));
    // Result: { isValid: false, suggestion: "Invalid date: day cannot be greater than 31" }
    // Example 6: EU format validation suggestion
    console.log('EU Validation - "01/02/2025":', validateDateString('01/02/2025'));
// Result: { isValid: true, detectedFormat: undefined, parsedDate: Date(2025-02-01) }
};
const getDateFormatInfo = (config = DEFAULT_DATE_CONFIG)=>{
    return {
        description: config.primaryFormat === 'EU' ? 'European format (Day/Month/Year)' : 'US format (Month/Day/Year)',
        exampleDate: config.primaryFormat === 'EU' ? '31/12/2025' : '12/31/2025',
        formatExample: config.primaryFormat === 'EU' ? 'DD/MM/YYYY' : 'MM/DD/YYYY',
        locale: config.locale,
        primaryFormat: config.primaryFormat
    };
};
const testEuDateFormat = ()=>{
    console.log('🇪🇺 Testing EU Date Format Configuration:');
    console.log('=====================================');
    const formatInfo = getDateFormatInfo();
    console.log('Current config:', formatInfo);
    console.log('\n📅 Test Cases:');
    console.log('1. Ambiguous date "01/02/2025":', formatDateForApi('01/02/2025'));
    console.log('   Expected: 2025-02-01T00:00:00.000Z (February 1st - EU format)');
    console.log('\n2. Unambiguous date "13/02/2025":', formatDateForApi('13/02/2025'));
    console.log('   Expected: 2025-02-13T00:00:00.000Z (February 13th - auto-detected)');
    console.log('\n3. ISO date "2025-02-01":', formatDateForApi('2025-02-01'));
    console.log('   Expected: 2025-02-01T00:00:00.000Z (February 1st - ISO format)');
    console.log('\n✅ EU format is now the default!');
    console.log('💡 Tip: Use YYYY-MM-DD format to avoid any ambiguity');
};
}}),
"[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Employee domain models.
 * @module transformers/employeeTransformer
 */ __turbopack_context__.s({
    "EmployeeTransformer": (()=>EmployeeTransformer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/dateUtils.ts [app-ssr] (ecmascript)");
;
const EmployeeTransformer = {
    /**
   * Converts an API Employee response into a frontend Employee domain model.
   * @param apiData - The data received from the API.
   * @returns The Employee domain model.
   */ fromApi (apiData) {
        return {
            availability: apiData.availability,
            contactEmail: apiData.contactEmail ?? null,
            contactInfo: apiData.contactInfo,
            contactMobile: apiData.contactMobile ?? null,
            contactPhone: apiData.contactPhone ?? null,
            createdAt: apiData.createdAt,
            currentLocation: apiData.currentLocation ?? null,
            department: apiData.department ?? null,
            employeeId: apiData.employeeId,
            fullName: apiData.fullName ?? null,
            generalAssignments: apiData.generalAssignments,
            hireDate: apiData.hireDate ?? null,
            id: apiData.id,
            name: apiData.name || '',
            notes: apiData.notes ?? null,
            position: apiData.position ?? null,
            profileImageUrl: apiData.profileImageUrl ?? null,
            role: apiData.role,
            shiftSchedule: apiData.shiftSchedule ?? null,
            skills: apiData.skills,
            status: apiData.status,
            updatedAt: apiData.updatedAt,
            workingHours: apiData.workingHours ?? null
        };
    },
    /**
   * Converts frontend data for creating an employee into an API request payload.
   * @param employeeData - The data from the frontend for creating a new employee.
   * @returns The transformed CreateEmployeeRequest payload.
   */ toCreateRequest (employeeData) {
        // Convert form data to API request format
        const request = {
            availability: employeeData.availability,
            contactEmail: employeeData.contactEmail?.trim() ?? null,
            contactInfo: employeeData.contactInfo.trim(),
            contactMobile: employeeData.contactMobile?.trim() ?? null,
            contactPhone: employeeData.contactPhone?.trim() ?? null,
            currentLocation: employeeData.currentLocation ?? null,
            department: employeeData.department?.trim() ?? null,
            employeeId: employeeData.employeeId,
            fullName: employeeData.fullName?.trim() ?? null,
            generalAssignments: employeeData.generalAssignments,
            hireDate: employeeData.hireDate ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDateForApi"])(employeeData.hireDate) : null,
            name: employeeData.name.trim(),
            notes: employeeData.notes?.trim() ?? null,
            position: employeeData.position?.trim() ?? null,
            profileImageUrl: employeeData.profileImageUrl?.trim() ?? null,
            role: employeeData.role,
            shiftSchedule: employeeData.shiftSchedule ?? null,
            skills: employeeData.skills,
            status: employeeData.status,
            workingHours: employeeData.workingHours ?? null
        };
        return request;
    },
    /**
   * Converts partial frontend employee data into an API request payload for updating.
   * @param employeeData - The partial data from the frontend for updating an employee.
   * @returns The transformed UpdateEmployeeRequest payload.
   */ toUpdateRequest (employeeData) {
        // Convert partial form data to API request format
        const request = {};
        if (employeeData.name !== undefined) request.name = employeeData.name?.trim() ?? null;
        if (employeeData.employeeId !== undefined) request.employeeId = employeeData.employeeId;
        if (employeeData.contactInfo !== undefined) request.contactInfo = employeeData.contactInfo?.trim() ?? null;
        if (employeeData.contactEmail !== undefined) request.contactEmail = employeeData.contactEmail?.trim() ?? null;
        if (employeeData.contactMobile !== undefined) request.contactMobile = employeeData.contactMobile?.trim() ?? null;
        if (employeeData.contactPhone !== undefined) request.contactPhone = employeeData.contactPhone?.trim() ?? null;
        if (employeeData.position !== undefined) request.position = employeeData.position?.trim() ?? null;
        if (employeeData.department !== undefined) request.department = employeeData.department?.trim() ?? null;
        if (employeeData.hireDate !== undefined) request.hireDate = employeeData.hireDate ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDateForApi"])(employeeData.hireDate) : null; // Convert to ISO format
        if (employeeData.fullName !== undefined) request.fullName = employeeData.fullName?.trim() ?? null;
        if (employeeData.role !== undefined) request.role = employeeData.role; // Type assertion
        if (employeeData.status !== undefined) request.status = employeeData.status; // Type assertion
        if (employeeData.availability !== undefined) request.availability = employeeData.availability; // Type assertion
        if (employeeData.currentLocation !== undefined) request.currentLocation = employeeData.currentLocation;
        if (employeeData.workingHours !== undefined) request.workingHours = employeeData.workingHours;
        if (employeeData.generalAssignments !== undefined) request.generalAssignments = employeeData.generalAssignments;
        if (employeeData.notes !== undefined) request.notes = employeeData.notes?.trim() ?? null;
        if (employeeData.profileImageUrl !== undefined) request.profileImageUrl = employeeData.profileImageUrl?.trim() ?? null;
        if (employeeData.shiftSchedule !== undefined) request.shiftSchedule = employeeData.shiftSchedule;
        if (employeeData.skills !== undefined) request.skills = employeeData.skills;
        return request;
    }
};
}}),
"[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmployeeApiService": (()=>EmployeeApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
;
const EmployeeApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(data),
    toApi: (data)=>data
};
class EmployeeApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/employees';
    transformer = EmployeeApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getByRole(role) {
        const result = await this.getAll({
            role
        });
        return result.data;
    }
    async updateAvailabilityStatus(employeeId, status) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${employeeId}/availability`, {
                availability: status
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${employeeId}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(response);
        });
    }
}
}}),
"[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Vehicle domain models.
 * @module transformers/vehicleTransformer
 */ __turbopack_context__.s({
    "VehicleTransformer": (()=>VehicleTransformer)
});
// ServiceRecordTransformer
const ServiceRecordTransformer = {
    fromApi (apiData) {
        return {
            cost: apiData.cost,
            createdAt: apiData.createdAt,
            date: apiData.date,
            employeeId: apiData.employeeId,
            id: apiData.id,
            notes: apiData.notes,
            odometer: apiData.odometer,
            servicePerformed: Array.isArray(apiData.servicePerformed) ? apiData.servicePerformed : [],
            updatedAt: apiData.updatedAt,
            vehicleId: apiData.vehicleId
        };
    }
};
const VehicleTransformer = {
    /**
   * Converts a raw API Vehicle response into a frontend Vehicle domain model.
   * @param apiData - The raw data received from the API.
   * @returns The transformed Vehicle domain model.
   */ fromApi (apiData) {
        return {
            color: apiData.color ?? null,
            createdAt: apiData.createdAt,
            id: apiData.id,
            imageUrl: apiData.imageUrl ?? null,
            initialOdometer: apiData.initialOdometer ?? null,
            licensePlate: apiData.licensePlate,
            make: apiData.make,
            model: apiData.model,
            ownerContact: apiData.ownerContact,
            ownerName: apiData.ownerName,
            serviceHistory: (()=>{
                const records = apiData.serviceHistory || apiData.ServiceRecord;
                return Array.isArray(records) ? records.map(ServiceRecordTransformer.fromApi) : [];
            })(),
            updatedAt: apiData.updatedAt,
            vin: apiData.vin ?? null,
            year: apiData.year
        };
    },
    /**
   * Converts frontend data for creating a vehicle into an API request payload.
   * @param vehicleData - The data from the frontend for creating a new vehicle.
   * @returns The transformed payload, compatible with CreateVehicleRequest.
   */ toCreateRequest (vehicleData) {
        // Generate a default VIN if not provided (for demo purposes)
        const defaultVin = vehicleData.vin?.trim() || this.generateDefaultVin(vehicleData);
        // Provide valid default values that pass backend validation
        const defaultOwnerContact = vehicleData.ownerContact?.trim() || '<EMAIL>';
        const defaultOwnerName = vehicleData.ownerName?.trim() || 'WorkHub Fleet Management';
        const request = {
            color: vehicleData.color ? vehicleData.color.trim() : null,
            imageUrl: vehicleData.imageUrl ? vehicleData.imageUrl.trim() : '',
            initialOdometer: vehicleData.initialOdometer ?? null,
            licensePlate: vehicleData.licensePlate.trim(),
            make: vehicleData.make.trim(),
            model: vehicleData.model.trim(),
            ownerContact: defaultOwnerContact,
            ownerName: defaultOwnerName,
            vin: defaultVin,
            year: vehicleData.year
        };
        if (!request.make || !request.model || !request.year || !request.licensePlate) {
            throw new Error('Missing required fields for creating a vehicle (make, model, year, licensePlate)');
        }
        // Validate VIN format
        if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(request.vin)) {
            throw new Error('VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)');
        }
        return request;
    },
    /**
   * Generates a default VIN for demo purposes
   * In production, this should be handled differently
   */ generateDefaultVin (vehicleData) {
        // Valid VIN characters (excluding I, O, Q)
        const validChars = 'ABCDEFGHJKLMNPRSTUVWXYZ0123456789';
        // Generate a valid 17-character VIN
        const makeCode = vehicleData.make.substring(0, 3).toUpperCase().replace(/[IOQ]/g, 'X').padEnd(3, 'X');
        const modelCode = vehicleData.model.substring(0, 2).toUpperCase().replace(/[IOQ]/g, 'X').padEnd(2, 'X');
        // Year code (last 2 digits)
        const yearCode = vehicleData.year.toString().substring(2);
        // Generate remaining 10 characters using valid VIN characters
        let randomCode = '';
        for(let i = 0; i < 10; i++){
            randomCode += validChars.charAt(Math.floor(Math.random() * validChars.length));
        }
        const vin = `${makeCode}${modelCode}${yearCode}${randomCode}`;
        // Ensure exactly 17 characters
        return vin.substring(0, 17).padEnd(17, 'X');
    },
    /**
   * Converts partial frontend vehicle data into an API request payload for updating.
   * @param vehicleData - The partial data from the frontend for updating a vehicle.
   * @returns The transformed payload, compatible with UpdateVehicleRequest.
   */ toUpdateRequest (vehicleData) {
        const request = {};
        if (vehicleData.make !== undefined) request.make = vehicleData.make.trim();
        if (vehicleData.model !== undefined) request.model = vehicleData.model.trim();
        if (vehicleData.year !== undefined) request.year = vehicleData.year;
        if (vehicleData.vin !== undefined) request.vin = vehicleData.vin.trim();
        if (vehicleData.licensePlate !== undefined) request.licensePlate = vehicleData.licensePlate.trim();
        if (vehicleData.ownerName !== undefined) request.ownerName = vehicleData.ownerName.trim();
        if (vehicleData.ownerContact !== undefined) request.ownerContact = vehicleData.ownerContact.trim();
        if (vehicleData.color !== undefined) request.color = vehicleData.color ? vehicleData.color.trim() : null;
        if (vehicleData.initialOdometer !== undefined) request.initialOdometer = vehicleData.initialOdometer;
        if (vehicleData.imageUrl !== undefined) {
            request.imageUrl = vehicleData.imageUrl ? vehicleData.imageUrl.trim() : '';
        }
        return request;
    }
};
}}),
"[project]/src/lib/transformers/taskTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Task domain models.
 * @module transformers/taskTransformer
 */ __turbopack_context__.s({
    "SubtaskTransformer": (()=>SubtaskTransformer),
    "TaskTransformer": (()=>TaskTransformer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)");
;
;
const SubtaskTransformer = {
    /**
   * Transforms a subtask API response into a domain.Subtask model.
   * @param apiData - The subtask data as received from the API.
   * @returns A domain.Subtask object.
   */ fromApi (apiData) {
        return {
            completed: apiData.completed,
            id: apiData.id,
            taskId: apiData.taskId,
            title: apiData.title
        };
    },
    /**
   * Transforms a domain.CreateSubtaskData into an API CreateSubtaskRequest.
   * @param domainData - The subtask data from the domain.
   * @returns An API CreateSubtaskRequest object.
   */ toApiRequest (domainData) {
        return {
            completed: domainData.completed === undefined ? false : domainData.completed,
            taskId: domainData.taskId,
            title: domainData.title.trim()
        };
    }
};
const TaskTransformer = {
    fromApi (apiData) {
        return {
            createdAt: apiData.createdAt,
            dateTime: apiData.dateTime,
            deadline: apiData.deadline ?? null,
            description: apiData.description,
            driverEmployee: apiData.Employee_Task_driverEmployeeIdToEmployee ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(apiData.Employee_Task_driverEmployeeIdToEmployee) : null,
            driverEmployeeId: apiData.driverEmployeeId ?? null,
            estimatedDuration: apiData.estimatedDuration,
            id: apiData.id,
            location: apiData.location,
            notes: apiData.notes ?? null,
            priority: apiData.priority,
            requiredSkills: apiData.requiredSkills,
            // Transform relation objects from backend Prisma includes
            staffEmployee: apiData.Employee_Task_staffEmployeeIdToEmployee ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(apiData.Employee_Task_staffEmployeeIdToEmployee) : null,
            staffEmployeeId: apiData.staffEmployeeId,
            status: apiData.status,
            subtasks: Array.isArray(apiData.SubTask) ? apiData.SubTask.map((st)=>SubtaskTransformer.fromApi(st)) : [],
            updatedAt: apiData.updatedAt,
            vehicle: apiData.Vehicle ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].fromApi(apiData.Vehicle) : null,
            vehicleId: apiData.vehicleId ?? null
        };
    },
    toCreateRequest (taskData) {
        return {
            dateTime: taskData.dateTime,
            deadline: taskData.deadline ?? null,
            description: taskData.description,
            driverEmployeeId: taskData.driverEmployeeId ?? null,
            estimatedDuration: taskData.estimatedDuration,
            location: taskData.location,
            notes: taskData.notes ?? null,
            priority: taskData.priority,
            requiredSkills: taskData.requiredSkills,
            staffEmployeeId: taskData.staffEmployeeId,
            status: taskData.status,
            subTasks: taskData.subtasks?.map(SubtaskTransformer.toApiRequest) ?? [],
            vehicleId: taskData.vehicleId ?? null
        };
    },
    toUpdateRequest (taskData) {
        const request = {};
        if (taskData.description !== undefined) {
            request.description = taskData.description; // Direct mapping - domain description to API description
        }
        if (taskData.notes !== undefined) request.notes = taskData.notes; // Direct mapping - domain notes to API notes
        if (taskData.location !== undefined) request.location = taskData.location;
        if (taskData.dateTime !== undefined) request.dateTime = taskData.dateTime;
        if (taskData.estimatedDuration !== undefined) request.estimatedDuration = taskData.estimatedDuration;
        if (taskData.priority !== undefined) request.priority = taskData.priority;
        if (taskData.status !== undefined) request.status = taskData.status;
        if (taskData.deadline !== undefined) request.deadline = taskData.deadline; // Backend expects 'deadline'
        if (taskData.requiredSkills !== undefined) request.requiredSkills = taskData.requiredSkills;
        if (taskData.vehicleId !== undefined) request.vehicleId = taskData.vehicleId;
        if (taskData.staffEmployeeId !== undefined) request.staffEmployeeId = taskData.staffEmployeeId;
        if (taskData.driverEmployeeId !== undefined) request.driverEmployeeId = taskData.driverEmployeeId;
        if (taskData.subtasks !== undefined) {
            request.subTasks = taskData.subtasks.map(SubtaskTransformer.toApiRequest); // Cast to any to allow subTasks
        }
        return request;
    }
};
}}),
"[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TaskApiService": (()=>TaskApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
;
const TaskApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].fromApi(data),
    toApi: (data)=>data
};
class TaskApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/tasks';
    transformer = TaskApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 3 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getByStatus(status) {
        const result = await this.getAll({
            status
        });
        return result.data;
    }
    async updateTaskStatus(taskId, newStatus) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${taskId}/status`, {
                status: newStatus
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${taskId}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].fromApi(response);
        });
    }
}
}}),
"[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Enhanced Vehicle API service using BaseApiService patterns.
 * @module api/services/vehicleApi
 */ __turbopack_context__.s({
    "VehicleApiService": (()=>VehicleApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)");
;
;
const VehicleTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].fromApi(data),
    toApi: (data)=>data
};
class VehicleApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/vehicles';
    transformer = VehicleTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 10 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    async getAvailableVehicles(startDate, endDate) {
        const result = await this.getAll({
            available: true,
            endDate: endDate.toISOString(),
            startDate: startDate.toISOString()
        });
        return result.data;
    }
    // Vehicle-specific methods
    async getByStatus(status) {
        const result = await this.getAll({
            status
        });
        return result.data;
    }
}
}}),
"[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Flight API Service
 *
 * This service provides functions to interact with the flight-related API endpoints.
 * Refactored to use BaseApiService pattern for consistency.
 */ __turbopack_context__.s({
    "FlightApiService": (()=>FlightApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const FlightTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class FlightApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/flights';
    transformer = FlightTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    /**
   * Search for flights by callsign and date
   */ async searchFlightsByCallsignAndDate(callsign, date// Expected format: "YYYY-MM-DD"
    ) {
        if (!date) {
            console.error('Search date is required for historical flight search.');
            throw new Error('Search date is required');
        }
        // Check if date is in the future
        const searchDate = new Date(`${date}T00:00:00.000Z`);
        const currentDate = new Date();
        if (searchDate > currentDate) {
            console.warn(`Search for future date rejected: ${date}`);
            throw new Error(`OpenSky API does not provide data for future dates. The date ${date} is in the future.`);
        }
        return this.executeWithInfrastructure(`search:${callsign}:${date}`, async ()=>{
            const response = await this.apiClient.get(`/flights/search?callsign=${encodeURIComponent(callsign)}&date=${date}`);
            // Handle the enhanced response format
            if (Array.isArray(response)) {
                return response; // Original array response
            } else if (response.flights) {
                return response.flights; // New format with flights array
            } else {
                // If we have an error message but no flights, throw an error with the details
                if (response.message) {
                    const error = new Error(response.message);
                    // @ts-ignore - Add details to the error object
                    error.details = response.details;
                    throw error;
                }
                return []; // Fallback to empty array
            }
        });
    }
    /**
   * Get flights by airport (arrivals or departures)
   */ async getFlightsByAirport(airport, begin, end, type = 'arrival') {
        return this.executeWithInfrastructure(`airport:${airport}:${begin}:${end}:${type}`, async ()=>{
            return this.apiClient.get(`/flights/airport?airport=${encodeURIComponent(airport)}&begin=${begin}&end=${end}&type=${type}`);
        });
    }
    /**
   * Get flights by time interval
   */ async getFlightsByTimeInterval(begin, end) {
        return this.executeWithInfrastructure(`interval:${begin}:${end}`, async ()=>{
            return this.apiClient.get(`/flights/interval?begin=${begin}&end=${end}`);
        });
    }
}
}}),
"[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file API service for FlightDetails-related operations.
 * @module api/services/flightDetailsApi
 * Refactored to use BaseApiService pattern for consistency.
 */ __turbopack_context__.s({
    "FlightDetailsApiService": (()=>FlightDetailsApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const FlightDetailsTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class FlightDetailsApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/flights';
    transformer = FlightDetailsTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    /**
   * Creates or updates flight details.
   * If flightDetails.id is provided, it attempts to update. Otherwise, it creates a new one.
   */ async createOrUpdateFlight(flightDetails) {
        return this.executeWithInfrastructure(null, async ()=>{
            if (flightDetails.id) {
                // Use the inherited update method
                return this.update(flightDetails.id, flightDetails);
            } else {
                // Use the inherited create method
                return this.create(flightDetails);
            }
        });
    }
}
}}),
"[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Centralized exports for the API service layer.
 * @module api
 */ // Core infrastructure
__turbopack_context__.s({
    "apiClient": (()=>apiClient),
    "getGlobalAuthTokenProvider": (()=>getGlobalAuthTokenProvider),
    "getSecureAuthTokenProvider": (()=>getSecureAuthTokenProvider),
    "getUnifiedAuthTokenProvider": (()=>getUnifiedAuthTokenProvider),
    "setGlobalAuthTokenProvider": (()=>setGlobalAuthTokenProvider),
    "setSecureAuthTokenProvider": (()=>setSecureAuthTokenProvider),
    "setUnifiedAuthTokenProvider": (()=>setUnifiedAuthTokenProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$interfaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
// Security architecture (selective exports to avoid conflicts)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
// Domain-specific API services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
// External API services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightDetailsApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
/**
 * Secure Authentication Token Provider
 * Single source of truth for authentication tokens across the entire application
 * Consistent naming convention: "Secure" prefix for all authentication functions
 */ let secureAuthTokenProvider = null;
function setSecureAuthTokenProvider(provider) {
    secureAuthTokenProvider = provider;
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('🔐 Secure Auth Token Provider initialized');
    }
}
/**
 * Get the current authentication token from the secure provider
 * This is used by ALL API clients throughout the application
 */ function getSecureAuthToken() {
    if (!secureAuthTokenProvider) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('⚠️ Secure Auth Token Provider not initialized');
        }
        return null;
    }
    try {
        return secureAuthTokenProvider();
    } catch (error) {
        console.error('❌ Error getting auth token from secure provider:', error);
        return null;
    }
}
function getSecureAuthTokenProvider() {
    return secureAuthTokenProvider;
}
function setGlobalAuthTokenProvider(provider) {
    console.warn('⚠️ setGlobalAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider instead.');
    setSecureAuthTokenProvider(provider);
}
function setUnifiedAuthTokenProvider(provider) {
    console.warn('⚠️ setUnifiedAuthTokenProvider is deprecated. Use setSecureAuthTokenProvider instead.');
    setSecureAuthTokenProvider(provider);
}
function getGlobalAuthTokenProvider() {
    console.warn('⚠️ getGlobalAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead.');
    return getSecureAuthTokenProvider();
}
function getUnifiedAuthTokenProvider() {
    console.warn('⚠️ getUnifiedAuthTokenProvider is deprecated. Use getSecureAuthTokenProvider instead.');
    return getSecureAuthTokenProvider();
}
// Get environment-aware configuration
const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"]({
    baseURL: envConfig.apiBaseUrl,
    getAuthToken: getSecureAuthToken,
    headers: {
        'Content-Type': 'application/json'
    },
    retryAttempts: 3,
    timeout: 10_000
});
;
;
;
;
;
;
;
}}),
"[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$interfaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightDetailsApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/utils/apiUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions for API calls with retry logic
 */ /**
 * Configuration options for retry logic
 */ __turbopack_context__.s({
    "calculateBackoffDelay": (()=>calculateBackoffDelay),
    "withRetry": (()=>withRetry)
});
/**
 * Default retry options
 */ const defaultRetryOptions = {
    backoffFactor: 2,
    initialDelay: 300,
    maxDelay: 5000,
    maxRetries: 3,
    shouldRetry: (error)=>{
        // Retry on network errors or 5xx server errors
        if (!error.response) {
            return true; // Network error
        }
        return error.response.status >= 500 && error.response.status < 600;
    }
};
/**
 * Sleep for a specified duration
 * @param ms Milliseconds to sleep
 */ const sleep = (ms)=>{
    return new Promise((resolve)=>setTimeout(resolve, ms));
};
const calculateBackoffDelay = (attempt, options = defaultRetryOptions)=>{
    const { backoffFactor = 2, initialDelay = 300, maxDelay = 5000 } = options;
    // Calculate exponential backoff: initialDelay * (backoffFactor ^ attempt)
    const delay = initialDelay * Math.pow(backoffFactor, attempt);
    // Add some randomness to prevent all clients retrying simultaneously
    const jitter = Math.random() * 100;
    // Ensure the delay doesn't exceed the maximum
    return Math.min(delay + jitter, maxDelay);
};
async function withRetry(fn, options = defaultRetryOptions) {
    const { maxRetries = 3, shouldRetry = defaultRetryOptions.shouldRetry } = options;
    let attempt = 0;
    while(true){
        try {
            return await fn();
        } catch (error) {
            attempt++;
            // If we've reached the maximum number of retries or shouldn't retry this error, throw
            if (attempt >= maxRetries || !(shouldRetry?.(error) ?? defaultRetryOptions.shouldRetry(error))) {
                throw error;
            }
            // Calculate delay for this attempt
            const delay = calculateBackoffDelay(attempt, options);
            // Log retry attempt
            console.warn(`API call failed, retrying (${attempt}/${maxRetries}) after ${delay}ms`, error);
            // Wait before retrying
            await sleep(delay);
        }
    }
}
}}),
"[project]/src/lib/utils/errorHandling.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Error Handling Utilities for Phase 3
 * Handles structured error responses from the backend API
 */ __turbopack_context__.s({
    "ERROR_CODE_MESSAGES": (()=>ERROR_CODE_MESSAGES),
    "FIELD_ERROR_MESSAGES": (()=>FIELD_ERROR_MESSAGES),
    "formatErrorForUser": (()=>formatErrorForUser),
    "getErrorMessage": (()=>getErrorMessage),
    "isAssignmentError": (()=>isAssignmentError),
    "isRetryableError": (()=>isRetryableError),
    "isValidationError": (()=>isValidationError),
    "logAndFormatError": (()=>logAndFormatError),
    "mapErrorToFormField": (()=>mapErrorToFormField),
    "retryRequest": (()=>retryRequest),
    "retryWithValidationErrorHandling": (()=>retryWithValidationErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>"); // Import ApiError
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/api.ts [app-ssr] (ecmascript)");
;
const ERROR_CODE_MESSAGES = {
    DATABASE_ERROR: 'A database error occurred. Please try again or contact support.',
    EMPLOYEE_NOT_ACTIVE: 'The selected employee is not currently active. Please select an active employee.',
    EMPLOYEE_NOT_FOUND: 'The selected employee could not be found. Please refresh and try again.',
    // Assignment validation errors
    INVALID_DRIVER_ROLE: 'The selected employee does not have driver role. Please select a different employee.',
    INVALID_FORMAT: 'Please check the format of your input.',
    // Network errors
    NETWORK_ERROR: 'Network error. Please check your connection and try again.',
    REQUIRED_FIELD: 'This field is required.',
    SERVER_ERROR: 'Server error. Please try again later.',
    TIMEOUT_ERROR: 'Request timed out. Please try again.',
    // General validation errors
    VALIDATION_ERROR: 'Please check your input and try again.',
    VEHICLE_NOT_FOUND: 'The selected vehicle could not be found. Please refresh and try again.',
    VEHICLE_WITHOUT_DRIVER: 'A vehicle cannot be assigned without selecting a driver first.'
};
const FIELD_ERROR_MESSAGES = {
    capacity: 'Capacity',
    delegates: 'Delegates',
    driverEmployeeId: 'Driver selection',
    durationFrom: 'Start date',
    durationTo: 'End date',
    email: 'Email address',
    escortEmployeeId: 'Escort selection',
    // Common fields
    eventName: 'Event name',
    flightArrivalDetails: 'Arrival flight details',
    flightDepartureDetails: 'Departure flight details',
    licensePlate: 'License plate',
    location: 'Location',
    // Vehicle fields
    make: 'Vehicle make',
    model: 'Vehicle model',
    // Employee fields
    name: 'Name',
    phone: 'Phone number',
    role: 'Role',
    // Assignment fields
    staffEmployeeId: 'Staff member selection',
    status: 'Status',
    vehicleId: 'Vehicle selection',
    year: 'Year'
};
function formatErrorForUser(error, context) {
    const message = getErrorMessage(error) || 'An unexpected error occurred. Please try again.';
    // Determine appropriate title based on error type
    let title = 'Error';
    if (isAssignmentError(error)) {
        title = 'Assignment Error';
    } else if (isValidationError(error)) {
        title = 'Validation Error';
    } else if (context) {
        title = `${context} Error`;
    }
    return {
        code: error?.code,
        field: error?.field || error?.path?.[0],
        message,
        title
    };
}
function getErrorMessage(error) {
    // Return null for falsy values (null, undefined, false, 0, '')
    if (!error) {
        return null;
    }
    // Handle structured API error response
    if (error && typeof error === 'object') {
        // Check for specific error code
        if (error.code && ERROR_CODE_MESSAGES[error.code]) {
            return ERROR_CODE_MESSAGES[error.code] ?? null;
        }
        // Check for error message
        if (error.error && typeof error.error === 'string') {
            return error.error;
        }
        // Check for general message
        if (error.message && typeof error.message === 'string') {
            return error.message;
        }
        // Handle validation errors array
        if (error.errors && Array.isArray(error.errors) && error.errors.length > 0) {
            return error.errors.map((err)=>err.message || err).join(', ');
        }
    }
    // Handle string errors
    if (typeof error === 'string') {
        return error;
    }
    // Handle Error objects (including custom ApiError instances)
    if (error instanceof Error) {
        // If it's a custom ApiError, it might have a 'details' property
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] && error.details) {
            // Try to extract more specific messages from error.details,
            // especially for validation errors (e.g., from BadRequestError)
            if (typeof error.details === 'object') {
                const details = error.details;
                // Common pattern: array of errors
                if (Array.isArray(details.errors) && details.errors.length > 0) {
                    return details.errors.map((err)=>err.message || err).join(', ');
                }
                // Common pattern: object with field-specific errors
                if (details.fieldErrors && typeof details.fieldErrors === 'object') {
                    const fieldErrors = Object.values(details.fieldErrors).flat().map((msg)=>msg.message || msg);
                    if (fieldErrors.length > 0) {
                        return fieldErrors.join(', ');
                    }
                }
                // Prioritize a 'message' or 'error' field within the details
                if (details.message) {
                    return details.message;
                }
                if (details.error) {
                    return details.error;
                }
            }
            // If details contain a string, use that
            if (typeof error.details === 'string') {
                return error.details;
            }
        }
        return error.message; // For standard Errors or ApiErrors where details weren't useful
    }
    // Fallback
    return 'An unexpected error occurred. Please try again.';
}
function isAssignmentError(error) {
    if (!isValidationError(error) || typeof error.code !== 'string') return false;
    const assignmentErrorCodes = [
        'INVALID_DRIVER_ROLE',
        'EMPLOYEE_NOT_FOUND',
        'EMPLOYEE_NOT_ACTIVE',
        'VEHICLE_NOT_FOUND',
        'VEHICLE_WITHOUT_DRIVER'
    ];
    return assignmentErrorCodes.includes(error.code);
}
function isRetryableError(error) {
    // Don't retry validation errors
    if (isValidationError(error)) {
        return false;
    }
    // Don't retry client errors (4xx) except 429 (rate limit)
    if (error?.status >= 400 && error?.status < 500) {
        return error.status === 429; // Only retry rate limit errors
    }
    // Retry server errors (5xx) and network errors
    if (error?.status >= 500 || !error?.status) {
        return true;
    }
    // Check for network-related errors
    if (error instanceof TypeError && error.message.includes('network')) {
        return true;
    }
    // Check for timeout errors
    if (error instanceof DOMException && error.name === 'AbortError') {
        return true;
    }
    return false;
}
function isValidationError(error) {
    return error && typeof error === 'object' && 'code' in error && 'message' in error && 'error' in error;
}
function logAndFormatError(error, context) {
    // Log full error for debugging
    console.error(`Error in ${context || 'unknown context'}:`, error);
    // Return user-friendly version
    return formatErrorForUser(error, context);
}
function mapErrorToFormField(error) {
    if (!error) return null;
    // Check for field-specific error
    if (error.field) {
        return {
            field: error.field,
            message: getErrorMessage(error) || 'Invalid field value'
        };
    } else if (error.path?.[0]) {
        return {
            field: String(error.path[0]),
            message: getErrorMessage(error) || 'Invalid field value'
        };
    }
    // Map assignment errors to specific fields
    if (error.code) {
        switch(error.code){
            case 'EMPLOYEE_NOT_ACTIVE':
            case 'INVALID_DRIVER_ROLE':
                {
                    return {
                        field: 'driverEmployeeId',
                        message: getErrorMessage(error) || 'Invalid driver'
                    };
                }
            case 'EMPLOYEE_NOT_FOUND':
                {
                    // Could be staff or driver, need more context
                    return {
                        field: 'staffEmployeeId',
                        message: getErrorMessage(error) || 'Employee not found'
                    };
                }
            case 'VEHICLE_NOT_FOUND':
            case 'VEHICLE_WITHOUT_DRIVER':
                {
                    return {
                        field: 'vehicleId',
                        message: getErrorMessage(error) || 'Invalid vehicle'
                    };
                }
        }
    }
    return null;
}
async function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
    let lastError;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            return await requestFn();
        } catch (error) {
            lastError = error;
            // Don't retry validation errors
            if (isValidationError(error)) {
                throw error;
            }
            // Don't retry on last attempt
            if (attempt === maxRetries) {
                break;
            }
            // Wait before retrying
            await new Promise((resolve)=>setTimeout(resolve, delay * attempt));
        }
    }
    throw lastError;
}
async function retryWithValidationErrorHandling(requestFn, maxRetries = 3, delay = 1000) {
    let lastError;
    for(let attempt = 1; attempt <= maxRetries; attempt++){
        try {
            return await requestFn();
        } catch (error) {
            lastError = error;
            // Don't retry if error is not retryable
            if (!isRetryableError(error)) {
                throw error;
            }
            // Don't retry on last attempt
            if (attempt === maxRetries) {
                break;
            }
            // Calculate exponential backoff delay
            const backoffDelay = delay * Math.pow(2, attempt - 1);
            // Wait before retrying
            await new Promise((resolve)=>setTimeout(resolve, backoffDelay));
        }
    }
    throw lastError;
}
}}),
"[project]/src/lib/utils/fileUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * File utility functions for working with files
 * Used primarily for CSV export functionality
 */ __turbopack_context__.s({
    "downloadFile": (()=>downloadFile),
    "extractTableDataForCsv": (()=>extractTableDataForCsv),
    "generateCsvFromData": (()=>generateCsvFromData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$papaparse$2f$papaparse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/papaparse/papaparse.js [app-ssr] (ecmascript)");
;
const downloadFile = (blob, fileName)=>{
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.append(a); // Required for Firefox
    a.click();
    a.remove();
    URL.revokeObjectURL(url); // Clean up
};
const generateCsvFromData = async (data, headers, fileName)=>{
    if (!fileName.toLowerCase().endsWith('.csv')) {
        fileName += '.csv';
    }
    // Format data with headers
    const csvData = [
        headers,
        ...data
    ];
    // Convert to CSV using PapaParse
    const csv = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$papaparse$2f$papaparse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].unparse(csvData);
    const blob = new Blob([
        csv
    ], {
        type: 'text/csv;charset=utf-8;'
    });
    // Trigger download
    downloadFile(blob, fileName);
};
const extractTableDataForCsv = (tableElementIdOrSelector)=>{
    const table = document.querySelector(tableElementIdOrSelector);
    if (!table) {
        throw new Error(`Table element not found: ${tableElementIdOrSelector}`);
    }
    // Extract headers
    const headerRow = table.querySelector('thead tr');
    if (!headerRow) {
        throw new Error('Table header row not found');
    }
    const headers = [];
    for (const th of headerRow.querySelectorAll('th')){
        // Skip any action/control columns that have data-skip-export attribute
        if (!Object.hasOwn(th.dataset, 'skipExport')) {
            headers.push(th.textContent?.trim() || '');
        }
    }
    // Extract data rows
    const data = [];
    for (const tr of table.querySelectorAll('tbody tr')){
        const rowData = [];
        let colIndex = 0;
        for (const td of tr.querySelectorAll('td')){
            // Skip any cells in columns that should be skipped (based on header)
            const headerTh = headerRow.querySelectorAll('th')[colIndex];
            colIndex++;
            if (headerTh && !Object.hasOwn(headerTh.dataset, 'skipExport')) {
                // Get text content, preferring data-export-value if it exists
                const cellValue = td.dataset.exportValue || td.textContent?.trim() || '';
                rowData.push(cellValue);
            }
        }
        data.push(rowData);
    }
    return {
        data,
        headers
    };
};
}}),
"[project]/src/lib/utils/formattingUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Formatting Utilities for Phase 3
 * Handles display formatting for various data types
 */ __turbopack_context__.s({
    "formatCurrency": (()=>formatCurrency),
    "formatDelegationStatusForDisplay": (()=>formatDelegationStatusForDisplay),
    "formatEmployeeName": (()=>formatEmployeeName),
    "formatEmployeeRole": (()=>formatEmployeeRole),
    "formatEmployeeStatusForDisplay": (()=>formatEmployeeStatusForDisplay),
    "formatFileSize": (()=>formatFileSize),
    "formatNumber": (()=>formatNumber),
    "formatPhoneNumber": (()=>formatPhoneNumber),
    "formatTaskStatusForDisplay": (()=>formatTaskStatusForDisplay),
    "titleCase": (()=>titleCase),
    "truncateText": (()=>truncateText)
});
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        currency: currency,
        style: 'currency'
    }).format(amount);
}
function formatDelegationStatusForDisplay(status) {
    switch(status){
        case 'In_Progress':
            {
                return 'In Progress';
            }
        case 'No_details':
            {
                return 'No Details';
            }
        default:
            {
                // For statuses like 'Planned', 'Confirmed', 'Completed', 'Cancelled'
                return status;
            }
    }
}
function formatEmployeeName(employee) {
    // Priority: fullName > name > fallback
    if (employee.fullName?.trim()) {
        return employee.fullName.trim();
    }
    if (employee.name?.trim()) {
        const name = employee.name.trim();
        // Check if name looks like a role (contains underscore or matches common role patterns)
        const rolePatterns = [
            'office_staff',
            'service_advisor',
            'administrator',
            'mechanic',
            'driver',
            'manager',
            'technician',
            'other'
        ];
        // If name matches a role pattern, format it nicely but indicate it's a role
        if (rolePatterns.includes(name.toLowerCase()) || name.includes('_')) {
            const formattedRole = name.replaceAll('_', ' ').replaceAll(/\b\w/g, (l)=>l.toUpperCase());
            return `${formattedRole} (Role)`;
        }
        // Otherwise, return the name as-is
        return name;
    }
    // Fallback to role if available
    if (employee.role) {
        const formattedRole = employee.role.replaceAll('_', ' ').replaceAll(/\b\w/g, (l)=>l.toUpperCase());
        return `${formattedRole} (Role)`;
    }
    return 'Unknown Employee';
}
function formatEmployeeRole(role) {
    return role.replaceAll('_', ' ').replaceAll(/\b\w/g, (l)=>l.toUpperCase());
}
function formatEmployeeStatusForDisplay(status) {
    return status.replaceAll('_', ' ');
}
function formatFileSize(bytes) {
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB',
        'TB'
    ];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}
function formatNumber(value, decimals = 0) {
    return new Intl.NumberFormat('en-US', {
        maximumFractionDigits: decimals,
        minimumFractionDigits: decimals
    }).format(value);
}
function formatPhoneNumber(phone) {
    // Remove all non-digit characters
    const cleaned = phone.replaceAll(/\D/g, '');
    // Format as (XXX) XXX-XXXX for US numbers
    if (cleaned.length === 10) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    // Return original if not a standard US number
    return phone;
}
function formatTaskStatusForDisplay(status) {
    return status.replaceAll('_', ' ');
}
function titleCase(text) {
    return text.replaceAll(/\w\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase());
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.slice(0, maxLength - 3) + '...';
}
}}),
"[project]/src/lib/utils/responseAdapter.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Response Adapter Utilities
 *
 * This module provides utilities for adapting API responses to a consistent format.
 * It helps handle different response structures from the backend and provides a
 * consistent interface for the frontend components.
 */ // Legacy ApiResponse interface removed - using standardized backend response format
// Import StandardApiResponse from backend types if needed
/**
 * Adapts an API response to a consistent format
 *
 * This function handles different response structures:
 * 1. Standard wrapped responses: { status: 'success', data: {...} }
 * 2. Direct responses: {...} (without a wrapper)
 * 3. Error responses: { status: 'error', message: '...' }
 *
 * @param response The API response to adapt
 * @returns The adapted response data
 */ __turbopack_context__.s({
    "adaptApiResponse": (()=>adaptApiResponse),
    "withResponseAdapter": (()=>withResponseAdapter)
});
function adaptApiResponse(response) {
    // If the response is null or undefined, return it as is
    if (response == null) {
        return response;
    }
    // If the response has a status and data property, it's already in the standard format
    if (response && typeof response === 'object' && 'status' in response) {
        if (response.status === 'success' && 'data' in response) {
            // Return the data property for success responses
            return response.data;
        } else if (response.status === 'error') {
            // For error responses, throw an error with the message
            throw new Error(response.message || 'Unknown error');
        }
    }
    // If the response doesn't match the standard format, return it as is
    // This handles direct responses without a wrapper
    return response;
}
function withResponseAdapter(fn) {
    return async (...args)=>{
        const response = await fn(...args);
        return adaptApiResponse(response);
    };
}
}}),
"[project]/src/lib/utils/circuitBreaker.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Circuit Breaker Pattern Implementation
 *
 * Prevents cascading failures by stopping requests to failing services
 * and allowing them time to recover.
 */ __turbopack_context__.s({
    "CircuitBreaker": (()=>CircuitBreaker),
    "CircuitState": (()=>CircuitState),
    "adminCircuitBreakers": (()=>adminCircuitBreakers),
    "getCircuitBreakerStatus": (()=>getCircuitBreakerStatus),
    "resetAllCircuitBreakers": (()=>resetAllCircuitBreakers)
});
var CircuitState = /*#__PURE__*/ function(CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
    CircuitState["OPEN"] = "OPEN";
    return CircuitState;
}(CircuitState || {});
class CircuitBreaker {
    failureCount = 0;
    lastFailureTime = 0;
    options;
    state = "CLOSED";
    successCount = 0;
    constructor(options = {}){
        this.options = {
            failureThreshold: 5,
            isFailure: (error)=>{
                // Consider 429 (rate limit) and 5xx errors as failures
                return error?.status === 429 || error?.status >= 500 && error?.status < 600;
            },
            recoveryTimeout: 60_000,
            requestTimeout: 10_000,
            ...options
        };
    }
    /**
   * Execute a function with circuit breaker protection
   */ async execute(fn) {
        if (this.state === "OPEN") {
            if (this.shouldAttemptReset()) {
                this.state = "HALF_OPEN";
                this.successCount = 0;
                console.log('🔄 Circuit Breaker: Attempting to reset (HALF_OPEN)');
            } else {
                const timeUntilRetry = Math.round((this.lastFailureTime + this.options.recoveryTimeout - Date.now()) / 1000);
                throw new Error(`Circuit breaker is OPEN. Retry in ${timeUntilRetry} seconds.`);
            }
        }
        try {
            const result = await this.executeWithTimeout(fn);
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure(error);
            throw error;
        }
    }
    /**
   * Get current circuit breaker status
   */ getStatus() {
        const status = {
            failureCount: this.failureCount,
            lastFailureTime: this.lastFailureTime,
            state: this.state
        };
        if (this.state === "OPEN") {
            const timeUntilRetry = Math.max(0, this.lastFailureTime + this.options.recoveryTimeout - Date.now());
            return {
                ...status,
                timeUntilRetry
            };
        }
        return status;
    }
    /**
   * Manually reset the circuit breaker
   */ reset() {
        this.state = "CLOSED";
        this.failureCount = 0;
        this.lastFailureTime = 0;
        this.successCount = 0;
        console.log('🔄 Circuit Breaker: Manually reset (CLOSED)');
    }
    /**
   * Execute function with timeout
   */ async executeWithTimeout(fn) {
        return new Promise((resolve, reject)=>{
            const timeoutId = setTimeout(()=>{
                reject(new Error('Request timeout'));
            }, this.options.requestTimeout);
            fn().then(resolve).catch(reject).finally(()=>clearTimeout(timeoutId));
        });
    }
    /**
   * Handle failed execution
   */ onFailure(error) {
        if (!this.options.isFailure(error)) {
            return; // Don't count this as a failure
        }
        this.failureCount++;
        this.lastFailureTime = Date.now();
        if (this.state === "HALF_OPEN") {
            this.state = "OPEN";
            console.log('🔄 Circuit Breaker: Reset failed, opening circuit (OPEN)');
        } else if (this.failureCount >= this.options.failureThreshold) {
            this.state = "OPEN";
            console.log(`🔄 Circuit Breaker: Failure threshold reached (${this.failureCount}), opening circuit (OPEN)`);
        }
    }
    /**
   * Handle successful execution
   */ onSuccess() {
        this.failureCount = 0;
        if (this.state === "HALF_OPEN") {
            this.successCount++;
            if (this.successCount >= 3) {
                // Require 3 successes to fully close
                this.state = "CLOSED";
                console.log('🔄 Circuit Breaker: Reset successful (CLOSED)');
            }
        }
    }
    /**
   * Check if we should attempt to reset the circuit
   */ shouldAttemptReset() {
        return Date.now() - this.lastFailureTime >= this.options.recoveryTimeout;
    }
}
const adminCircuitBreakers = {
    audit: new CircuitBreaker({
        failureThreshold: 3,
        recoveryTimeout: 60_000,
        requestTimeout: 8000
    }),
    errors: new CircuitBreaker({
        failureThreshold: 5,
        recoveryTimeout: 30_000,
        requestTimeout: 8000
    }),
    health: new CircuitBreaker({
        failureThreshold: 3,
        recoveryTimeout: 30_000,
        requestTimeout: 5000
    }),
    performance: new CircuitBreaker({
        failureThreshold: 3,
        recoveryTimeout: 60_000,
        requestTimeout: 10_000
    }),
    users: new CircuitBreaker({
        failureThreshold: 3,
        recoveryTimeout: 60_000,
        requestTimeout: 10_000
    })
};
const getCircuitBreakerStatus = ()=>{
    return Object.entries(adminCircuitBreakers).reduce((acc, [key, breaker])=>{
        acc[key] = breaker.getStatus();
        return acc;
    }, {});
};
const resetAllCircuitBreakers = ()=>{
    for (const breaker of Object.values(adminCircuitBreakers))breaker.reset();
    console.log('🔄 Circuit Breaker: All breakers reset');
};
;
}}),
"[project]/src/lib/utils/requestCache.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Request Cache Utility
 *
 * Provides intelligent caching for API requests to reduce server load
 * and improve user experience by avoiding unnecessary API calls.
 */ __turbopack_context__.s({
    "CACHE_DURATIONS": (()=>CACHE_DURATIONS),
    "RequestCache": (()=>RequestCache),
    "requestCache": (()=>requestCache)
});
class RequestCache {
    cache = new Map();
    defaultOptions = {
        duration: 30_000,
        maxEntries: 100,
        staleWhileRevalidate: true,
        throttleTime: 1000
    };
    lastRequestTime = new Map();
    /**
   * Clear all cache entries
   */ clear() {
        const size = this.cache.size;
        this.cache.clear();
        console.log(`🔄 Cache: Cleared ${size} entries`);
    }
    /**
   * Get cached data or execute the request function
   */ async get(key, requestFn, options = {}) {
        const opts = {
            ...this.defaultOptions,
            ...options
        };
        const now = Date.now();
        const cached = this.cache.get(key);
        // Return fresh cached data
        if (cached && now < cached.expiresAt) {
            const timeLeft = Math.round((cached.expiresAt - now) / 1000);
            console.log(`🔄 Cache HIT for ${key} (expires in ${timeLeft}s)`);
            return cached.data;
        }
        // Check throttling - if we made a request too recently, return stale data if available
        const lastRequest = this.lastRequestTime.get(key) || 0;
        if (now - lastRequest < opts.throttleTime && cached) {
            const throttleTimeLeft = Math.round((opts.throttleTime - (now - lastRequest)) / 1000);
            console.log(`🔄 Cache THROTTLED for ${key}, returning stale data (throttle ends in ${throttleTimeLeft}s)`);
            return cached.data;
        }
        // Return stale data while revalidating in background
        if (cached && opts.staleWhileRevalidate && now < cached.expiresAt + opts.duration) {
            console.log(`🔄 Cache STALE for ${key}, revalidating in background`);
            // Revalidate in background
            this.revalidateInBackground(key, requestFn, opts);
            return cached.data;
        }
        // Cache miss or expired - fetch fresh data
        console.log(`🔄 Cache MISS for ${key}, fetching fresh data`);
        return this.fetchAndCache(key, requestFn, opts);
    }
    /**
   * Get cache statistics
   */ getStats() {
        const now = Date.now();
        const entries = [
            ...this.cache.entries()
        ].map(([key, entry])=>({
                age: now - entry.timestamp,
                expiresIn: entry.expiresAt - now,
                key
            }));
        return {
            entries,
            size: this.cache.size
        };
    }
    /**
   * Invalidate specific cache entry
   */ invalidate(key) {
        this.cache.delete(key);
        console.log(`🔄 Cache: Invalidated ${key}`);
    }
    /**
   * Invalidate all cache entries matching pattern
   */ invalidatePattern(pattern) {
        const keysToDelete = [];
        for (const key of this.cache.keys()){
            if (pattern.test(key)) {
                keysToDelete.push(key);
            }
        }
        for (const key of keysToDelete)this.cache.delete(key);
        console.log(`🔄 Cache: Invalidated ${keysToDelete.length} entries matching pattern`);
    }
    /**
   * Cleanup old cache entries
   */ cleanup(maxEntries) {
        if (this.cache.size <= maxEntries) return;
        // Sort by timestamp and remove oldest entries
        const entries = [
            ...this.cache.entries()
        ].sort(([, a], [, b])=>a.timestamp - b.timestamp);
        const toRemove = entries.slice(0, this.cache.size - maxEntries);
        for (const [key] of toRemove){
            this.cache.delete(key);
        }
        console.log(`🔄 Cache: Cleaned up ${toRemove.length} old entries`);
    }
    /**
   * Fetch data and store in cache
   */ async fetchAndCache(key, requestFn, options) {
        try {
            const now = Date.now();
            // Track request time for throttling
            this.lastRequestTime.set(key, now);
            const data = await requestFn();
            this.cache.set(key, {
                data,
                expiresAt: now + options.duration,
                timestamp: now
            });
            // Cleanup old entries if cache is too large
            this.cleanup(options.maxEntries);
            return data;
        } catch (error) {
            // If we have stale data, return it on error
            const cached = this.cache.get(key);
            if (cached) {
                console.warn(`🔄 Cache: Returning stale data for ${key} due to error:`, error);
                return cached.data;
            }
            throw error;
        }
    }
    /**
   * Revalidate cache entry in background
   */ async revalidateInBackground(key, requestFn, options) {
        try {
            await this.fetchAndCache(key, requestFn, options);
            console.log(`🔄 Cache: Background revalidation completed for ${key}`);
        } catch (error) {
            console.warn(`🔄 Cache: Background revalidation failed for ${key}:`, error);
        }
    }
}
const requestCache = new RequestCache();
const CACHE_DURATIONS = {
    AUDIT_LOGS: 60_000,
    ERROR_LOGS: 30_000,
    HEALTH_STATUS: 60_000,
    PERFORMANCE_METRICS: 120_000,
    USER_DATA: 300_000
};
;
}}),
"[project]/src/lib/utils/imageUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Image utility functions for handling image URLs and fallbacks
 */ /**
 * Validates if an image URL is valid and non-empty
 * @param imageUrl - The image URL to validate
 * @returns true if the URL is valid and non-empty, false otherwise
 */ __turbopack_context__.s({
    "generatePlaceholderImageUrl": (()=>generatePlaceholderImageUrl),
    "getSafeDelegationImageUrl": (()=>getSafeDelegationImageUrl),
    "getSafeImageUrl": (()=>getSafeImageUrl),
    "getSafeVehicleImageUrl": (()=>getSafeVehicleImageUrl),
    "isValidImageUrl": (()=>isValidImageUrl)
});
function isValidImageUrl(imageUrl) {
    return Boolean(imageUrl && typeof imageUrl === 'string' && imageUrl.trim() !== '');
}
function getSafeImageUrl(imageUrl, fallbackUrl) {
    return isValidImageUrl(imageUrl) ? imageUrl : fallbackUrl;
}
function generatePlaceholderImageUrl(seed, width = 400, height = 250) {
    return `https://picsum.photos/seed/${seed}/${width}/${height}`;
}
function getSafeDelegationImageUrl(imageUrl, delegationId, size = 'card') {
    const dimensions = {
        card: {
            width: 400,
            height: 250
        },
        detail: {
            width: 600,
            height: 375
        },
        report: {
            width: 600,
            height: 375
        }
    };
    const { width, height } = dimensions[size];
    const fallbackUrl = generatePlaceholderImageUrl(delegationId, width, height);
    return getSafeImageUrl(imageUrl, fallbackUrl);
}
function getSafeVehicleImageUrl(imageUrl, vehicleId) {
    const fallbackUrl = generatePlaceholderImageUrl(vehicleId, 600, 375);
    return getSafeImageUrl(imageUrl, fallbackUrl);
}
}}),
"[project]/src/lib/utils/delegationUtils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Delegation utility functions
 * @module lib/utils/delegationUtils
 */ __turbopack_context__.s({
    "formatDelegationDate": (()=>formatDelegationDate),
    "formatDelegationTime": (()=>formatDelegationTime),
    "getDelegationPriority": (()=>getDelegationPriority),
    "getInfoIconColor": (()=>getInfoIconColor),
    "getStatusColor": (()=>getStatusColor),
    "needsAttention": (()=>needsAttention)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-ssr] (ecmascript) <locals>");
;
const getStatusColor = (status)=>{
    switch(status){
        case 'Cancelled':
            {
                return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
            }
        case 'Completed':
            {
                return 'bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20';
            }
        case 'Confirmed':
            {
                return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
            }
        case 'In_Progress':
            {
                return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
            }
        case 'Planned':
            {
                return 'bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20';
            }
        default:
            {
                return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
            }
    }
};
const formatDelegationDate = (dateString)=>{
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) {
        return 'N/A';
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'MMM d, yyyy');
};
const formatDelegationTime = (dateString)=>{
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) {
        return 'N/A';
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'HH:mm');
};
const getInfoIconColor = (type)=>{
    const colorMap = {
        duration: 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400',
        delegates: 'bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400',
        flight: 'bg-purple-50 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400',
        escort: 'bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400',
        driver: 'bg-cyan-50 dark:bg-cyan-900/30 text-cyan-600 dark:text-cyan-400',
        vehicle: 'bg-slate-50 dark:bg-slate-800 text-slate-600 dark:text-slate-400',
        notes: 'bg-indigo-50 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400'
    };
    return colorMap[type] || 'bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400';
};
const needsAttention = (delegation)=>{
    const hasEscort = delegation.escorts && delegation.escorts.length > 0 && delegation.escorts[0]?.employee;
    return !hasEscort && delegation.status !== 'Completed' && delegation.status !== 'Cancelled';
};
const getDelegationPriority = (delegation)=>{
    if (delegation.status === 'In_Progress') return 'high';
    if (delegation.status === 'Confirmed') return 'medium';
    return 'low';
};
}}),
"[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Utils Index
 * @description Centralized exports for all utility functions following DRY principles
 */ // API Utilities
__turbopack_context__.s({
    "UTILS_CONSTANTS": (()=>UTILS_CONSTANTS),
    "cn": (()=>cn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$apiUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/apiUtils.ts [app-ssr] (ecmascript)");
// Date Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/dateUtils.ts [app-ssr] (ecmascript)");
// Error Handling Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$errorHandling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/errorHandling.ts [app-ssr] (ecmascript)");
// File Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$fileUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/fileUtils.ts [app-ssr] (ecmascript)");
// Formatting Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$formattingUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/formattingUtils.ts [app-ssr] (ecmascript)");
// Response Adapter (legacy ApiResponse type removed)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$responseAdapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/responseAdapter.ts [app-ssr] (ecmascript)");
// Circuit Breaker Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$circuitBreaker$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/circuitBreaker.ts [app-ssr] (ecmascript)");
// Request Cache Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$requestCache$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/requestCache.ts [app-ssr] (ecmascript)");
// Image Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$imageUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/imageUtils.ts [app-ssr] (ecmascript)");
// Delegation Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$delegationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/delegationUtils.ts [app-ssr] (ecmascript)");
// Utility function from lib/utils.ts (cn function)
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const UTILS_CONSTANTS = {
    DEFAULT_RETRY_ATTEMPTS: 3,
    DEFAULT_RETRY_DELAY: 1000,
    DEFAULT_CACHE_TTL: 5 * 60 * 1000,
    MAX_FILE_SIZE: 10 * 1024 * 1024,
    SUPPORTED_IMAGE_TYPES: [
        'image/jpeg',
        'image/png',
        'image/webp'
    ]
};
}}),
"[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$apiUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/apiUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/dateUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$errorHandling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/errorHandling.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$fileUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/fileUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$formattingUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/formattingUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$responseAdapter$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/responseAdapter.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$circuitBreaker$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/circuitBreaker.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$requestCache$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/requestCache.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$imageUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/imageUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$delegationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/delegationUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Zustand store for global application UI state.
 * This store manages UI-related states such as sidebar visibility, theme, and notifications.
 * @module stores/zustand/appStore
 */ __turbopack_context__.s({
    "useAppStore": (()=>useAppStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        addNotification: (notification)=>set((state)=>({
                    notifications: [
                        ...state.notifications,
                        {
                            ...notification,
                            id: crypto.randomUUID(),
                            read: false,
                            timestamp: new Date().toISOString()
                        }
                    ]
                })),
        clearAllNotifications: ()=>set({
                notifications: []
            }),
        currentTheme: 'light',
        markNotificationAsRead: (id)=>set((state)=>({
                    notifications: state.notifications.map((n)=>n.id === id ? {
                            ...n,
                            read: true
                        } : n)
                })),
        notifications: [],
        removeNotification: (id)=>set((state)=>({
                    notifications: state.notifications.filter((n)=>n.id !== id)
                })),
        setTheme: (theme)=>{
            set({
                currentTheme: theme
            });
        // Note: localStorage is handled by persist middleware
        },
        // Initial state
        sidebarOpen: false,
        // Actions
        toggleSidebar: ()=>set((state)=>({
                    sidebarOpen: !state.sidebarOpen
                })),
        // Computed values
        unreadNotificationCount: ()=>{
            const { notifications } = get();
            return notifications.filter((n)=>!n.read).length;
        }
    }), {
    name: 'workhub-app-store',
    partialize: (state)=>({
            currentTheme: state.currentTheme
        })
}), {
    name: 'app-store'
}));
}}),
"[project]/src/lib/stores/zustand/uiStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Zustand store for general UI preferences and ephemeral UI states.
 * This store can be used for states that don't fit into appStore (e.g., user-specific UI settings, modal visibility).
 * @module stores/zustand/uiStore
 */ __turbopack_context__.s({
    "useUiStore": (()=>useUiStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const useUiStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        autoRefreshInterval: 30,
        closeModal: ()=>set({
                isModalOpen: false,
                modalContent: null
            }),
        dashboardLayout: 'cards',
        // Initial state
        fontSize: 'medium',
        isModalOpen: false,
        mapViewPreference: 'roadmap',
        modalContent: null,
        notificationsEnabled: true,
        openModal: (content)=>set({
                isModalOpen: true,
                modalContent: content
            }),
        setAutoRefreshInterval: (interval)=>set({
                autoRefreshInterval: interval
            }),
        setDashboardLayout: (layout)=>set({
                dashboardLayout: layout
            }),
        // Actions
        setFontSize: (size)=>set({
                fontSize: size
            }),
        setMapViewPreference: (view)=>set({
                mapViewPreference: view
            }),
        // WorkHub-specific actions
        setTableDensity: (density)=>set({
                tableDensity: density
            }),
        tableDensity: 'comfortable',
        toggleNotifications: ()=>set((state)=>({
                    notificationsEnabled: !state.notificationsEnabled
                }))
    }), {
    name: 'workhub-ui-store',
    partialize: (state)=>({
            autoRefreshInterval: state.autoRefreshInterval,
            dashboardLayout: state.dashboardLayout,
            fontSize: state.fontSize,
            mapViewPreference: state.mapViewPreference,
            notificationsEnabled: state.notificationsEnabled,
            tableDensity: state.tableDensity
        })
}), {
    name: 'ui-store'
}));
}}),
"[project]/src/lib/hooks/useModal.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Re-export useModal from UI hooks
 * @module lib/hooks/useModal
 */ // Re-export from the standardized location
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useModal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useModal.ts [app-ssr] (ecmascript)");
;
}}),
"[project]/src/lib/hooks/useModal.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useModal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useModal.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useModal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/hooks/useModal.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/stores/queryClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Configuration for TanStack Query (React Query) QueryClient.
 * This file sets up the global QueryClient instance with default options
 * for caching, retries, and error handling.
 * @module stores/queryClient
 */ __turbopack_context__.s({
    "performanceUtils": (()=>performanceUtils),
    "prefetchUtils": (()=>prefetchUtils),
    "queryClient": (()=>queryClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$mutationCache$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/mutationCache.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryCache$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryCache.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
;
;
/**
 * Global performance metrics store
 */ const performanceMetrics = [];
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]({
    defaultOptions: {
        mutations: {
            // Default retry attempts for mutations (often 0 or 1 for mutations)
            retry: 0,
            // Retry delay for mutations (if retry > 0)
            retryDelay: 1000
        },
        queries: {
            // Optimized cache time (garbage collection time)
            gcTime: 10 * 60 * 1000,
            // Background refetch interval for critical data
            refetchInterval: false,
            refetchOnMount: true,
            refetchOnReconnect: true,
            // Background refetching configuration
            refetchOnWindowFocus: true,
            // Retry configuration with exponential backoff
            retry: (failureCount, error)=>{
                // Do not retry on specific API errors like 400, 401, 404
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"] || error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NetworkError"]) {
                    return false;
                }
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"] && (error.status === 400 || error.status === 401 || // ✅ CRITICAL: Don't retry 401 auth errors
                error.status === 404)) {
                    return false;
                }
                // Retry up to 3 times by default for other errors
                return failureCount < 3;
            },
            // Exponential backoff for retries
            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
            // Optimized staleTime for different data types
            staleTime: 5 * 60 * 1000
        }
    },
    mutationCache: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$mutationCache$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MutationCache"]({
        onError: (error)=>{
            // Explicitly type error
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"]) {
                console.error('Mutation Authentication Error:', error.message);
            } else if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NetworkError"]) {
                console.error('Mutation Network Error:', error.message);
            } else if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                const apiError = error;
                console.error(`Mutation API Error (${apiError.status}):`, apiError.message, apiError.details);
            } else {
                console.error('An unexpected mutation error occurred:', error);
            }
        // You might use a global toast/notification system here
        }
    }),
    queryCache: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryCache$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryCache"]({
        onError: (error)=>{
            // Explicitly type error
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"]) {
                console.error('Authentication Error:', error.message);
            // Potentially redirect to login or show a global auth error notification
            } else if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NetworkError"]) {
                console.error('Network Error:', error.message);
            // Show a global network error message
            } else if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                const apiError = error;
                console.error(`API Error (${apiError.status}):`, apiError.message, apiError.details);
            // Show a generic API error message
            } else {
                console.error('An unexpected error occurred:', error);
            // Fallback for unknown errors
            }
        // You might use a global toast/notification system here
        },
        // Performance monitoring for queries
        onSuccess: (_data, query)=>{
            const queryKey = JSON.stringify(query.queryKey);
            const duration = Date.now() - (query.state.dataUpdatedAt || Date.now());
            const cacheHit = query.state.fetchStatus !== 'fetching' && query.state.data !== undefined;
            // Track performance metrics
            performanceMetrics.push({
                cacheHit,
                duration,
                queryKey,
                timestamp: Date.now()
            });
            // Keep only last 1000 metrics to prevent memory leaks
            if (performanceMetrics.length > 1000) {
                performanceMetrics.splice(0, performanceMetrics.length - 1000);
            }
            // Log slow queries in development
            if (("TURBOPACK compile-time value", "development") === 'development' && duration > 2000) {
                console.warn(`Slow query detected: ${queryKey} took ${duration}ms`);
            }
        }
    })
});
const performanceUtils = {
    /**
   * Clear performance metrics
   */ clearMetrics: ()=>{
        performanceMetrics.length = 0;
    },
    /**
   * Get average query duration
   */ getAverageQueryDuration: ()=>{
        if (performanceMetrics.length === 0) return 0;
        const totalDuration = performanceMetrics.reduce((sum, m)=>sum + m.duration, 0);
        return totalDuration / performanceMetrics.length;
    },
    /**
   * Get cache hit rate percentage
   */ getCacheHitRate: ()=>{
        if (performanceMetrics.length === 0) return 0;
        const cacheHits = performanceMetrics.filter((m)=>m.cacheHit).length;
        return cacheHits / performanceMetrics.length * 100;
    },
    /**
   * Get performance metrics for analysis
   */ getMetrics: ()=>[
            ...performanceMetrics
        ],
    /**
   * Get performance summary
   */ getPerformanceSummary: ()=>{
        const metrics = performanceMetrics;
        const cacheHitRate = performanceUtils.getCacheHitRate();
        const avgDuration = performanceUtils.getAverageQueryDuration();
        const slowQueries = performanceUtils.getSlowQueries();
        return {
            averageDuration: Math.round(avgDuration * 100) / 100,
            cacheHitRate: Math.round(cacheHitRate * 100) / 100,
            slowQueriesCount: slowQueries.length,
            slowQueriesPercentage: metrics.length > 0 ? Math.round(slowQueries.length / metrics.length * 10_000) / 100 : 0,
            totalQueries: metrics.length
        };
    },
    /**
   * Get slow queries (> 2 seconds)
   */ getSlowQueries: ()=>{
        return performanceMetrics.filter((m)=>m.duration > 2000);
    }
};
const prefetchUtils = {
    /**
   * Prefetch all critical data for dashboard
   * @param isAuthReady - Whether authentication system is ready for API calls
   */ prefetchDashboardData: async (isAuthReady = false)=>{
        // Guard against premature API calls during auth initialization
        if (!isAuthReady) {
            console.warn('Authentication not ready, deferring dashboard data prefetch.');
            return;
        }
        // Additional safety check: Verify auth token provider exists
        const { getGlobalAuthTokenProvider } = await __turbopack_context__.r("[project]/src/lib/api/index.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        const authTokenProvider = getGlobalAuthTokenProvider();
        if (!authTokenProvider || !authTokenProvider()) {
            console.warn('No auth token available, skipping dashboard data prefetch.');
            return;
        }
        // Import API services
        const { delegationApiService, employeeApiService, taskApiService, vehicleApiService } = await __turbopack_context__.r("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        const promises = [
            queryClient.prefetchQuery({
                queryFn: ()=>vehicleApiService.getAll(),
                queryKey: [
                    'vehicles'
                ],
                staleTime: 10 * 60 * 1000
            }),
            queryClient.prefetchQuery({
                queryFn: ()=>taskApiService.getAll(),
                queryKey: [
                    'tasks'
                ],
                staleTime: 5 * 60 * 1000
            }),
            queryClient.prefetchQuery({
                queryFn: ()=>employeeApiService.getAll(),
                queryKey: [
                    'employees'
                ],
                staleTime: 10 * 60 * 1000
            }),
            queryClient.prefetchQuery({
                queryFn: ()=>delegationApiService.getAll(),
                queryKey: [
                    'delegations'
                ],
                staleTime: 5 * 60 * 1000
            })
        ];
        await Promise.allSettled(promises);
    },
    /**
   * Prefetch data for task management
   * @param isAuthReady - Whether authentication system is ready for API calls
   */ prefetchTaskManagementData: async (isAuthReady = true)=>{
        // Guard against premature API calls during auth initialization
        if (!isAuthReady) {
            console.warn('Authentication not ready, deferring task management data prefetch.');
            return;
        }
        const { employeeApiService, taskApiService, vehicleApiService } = await __turbopack_context__.r("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        const promises = [
            queryClient.prefetchQuery({
                queryFn: ()=>taskApiService.getAll(),
                queryKey: [
                    'tasks'
                ],
                staleTime: 5 * 60 * 1000
            }),
            queryClient.prefetchQuery({
                queryFn: ()=>employeeApiService.getAll(),
                queryKey: [
                    'employees'
                ],
                staleTime: 10 * 60 * 1000
            }),
            queryClient.prefetchQuery({
                queryFn: ()=>vehicleApiService.getAll(),
                queryKey: [
                    'vehicles'
                ],
                staleTime: 10 * 60 * 1000
            })
        ];
        await Promise.allSettled(promises);
    },
    /**
   * Prefetch data for a specific vehicle detail page
   * @param vehicleId - The vehicle ID to prefetch details for
   * @param isAuthReady - Whether authentication system is ready for API calls
   */ prefetchVehicleDetails: async (vehicleId, isAuthReady = true)=>{
        // Guard against premature API calls during auth initialization
        if (!isAuthReady) {
            console.warn('Authentication not ready, deferring vehicle details prefetch.');
            return;
        }
        const { vehicleApiService } = await __turbopack_context__.r("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        await queryClient.prefetchQuery({
            queryFn: ()=>vehicleApiService.getById(vehicleId),
            queryKey: [
                'vehicles',
                vehicleId
            ],
            staleTime: 10 * 60 * 1000
        });
    }
}; // Query Key Factory (Conceptual):
 // A query key factory is a pattern to centralize and standardize query keys.
 // This helps prevent typos and ensures consistency across your application.
 // Example:
 /*
export const queryKeys = {
    vehicles: {
        all: ['vehicles'] as const,
        detail: (id: string) => ['vehicles', id] as const,
        lists: (filters: Record<string, any>) => ['vehicles', 'list', filters] as const,
    },
    delegations: {
        all: ['delegations'] as const,
        detail: (id: string) => ['delegations', id] as const,
    },
    // ... other domains
};
*/  // Devtools Integration:
 // To integrate React Query Devtools, you would typically add it to your root component (e.g., _app.tsx in Next.js):
 /*
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function MyApp({ Component, pageProps }: AppProps) {
    return (
        <QueryClientProvider client={queryClient}>
            <Component {...pageProps} />
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}
*/ 
}}),

};

//# sourceMappingURL=src_lib_87df9c35._.js.map