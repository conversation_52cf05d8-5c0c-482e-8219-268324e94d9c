/**
 * @file Unified Authentication Token Provider
 * @module api/auth/unifiedTokenProvider
 * 
 * Single authoritative source for authentication tokens across the entire application.
 * Replaces the dual token provider system to ensure consistent authentication.
 */

/**
 * Global unified token provider function
 * This is the single source of truth for authentication tokens
 */
let unifiedTokenProvider: (() => string | null) | null = null;

/**
 * Authentication state tracking
 */
interface AuthState {
  isInitialized: boolean;
  lastTokenUpdate: number;
  tokenProvider: (() => string | null) | null;
}

const authState: AuthState = {
  isInitialized: false,
  lastTokenUpdate: 0,
  tokenProvider: null,
};

/**
 * Set the unified authentication token provider
 * This should ONLY be called by the AuthContext
 * 
 * @param provider - Function that returns the current authentication token
 */
export function setUnifiedAuthTokenProvider(provider: () => string | null): void {
  unifiedTokenProvider = provider;
  authState.tokenProvider = provider;
  authState.isInitialized = true;
  authState.lastTokenUpdate = Date.now();
  
  // Debug logging in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Unified Auth Token Provider initialized');
  }
}

/**
 * Get the current authentication token from the unified provider
 * This is used by ALL API clients throughout the application
 * 
 * @returns Current authentication token or null if not available
 */
export function getUnifiedAuthToken(): string | null {
  if (!unifiedTokenProvider) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Unified Auth Token Provider not initialized');
    }
    return null;
  }
  
  try {
    return unifiedTokenProvider();
  } catch (error) {
    console.error('❌ Error getting auth token from unified provider:', error);
    return null;
  }
}

/**
 * Check if the unified token provider is initialized
 * 
 * @returns True if the provider is set up and ready
 */
export function isUnifiedAuthInitialized(): boolean {
  return authState.isInitialized && unifiedTokenProvider !== null;
}

/**
 * Get the unified token provider function (for debugging/testing)
 * 
 * @returns The current token provider function or null
 */
export function getUnifiedTokenProvider(): (() => string | null) | null {
  return unifiedTokenProvider;
}

/**
 * Clear the unified token provider (for logout/cleanup)
 * This should ONLY be called by the AuthContext during logout
 */
export function clearUnifiedAuthTokenProvider(): void {
  unifiedTokenProvider = null;
  authState.isInitialized = false;
  authState.tokenProvider = null;
  authState.lastTokenUpdate = 0;
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Unified Auth Token Provider cleared');
  }
}

/**
 * Get authentication state information (for debugging)
 * 
 * @returns Current authentication state
 */
export function getAuthState(): Readonly<AuthState> {
  return { ...authState };
}

/**
 * Legacy compatibility functions
 * These maintain backward compatibility while using the unified provider
 */

/**
 * @deprecated Use setUnifiedAuthTokenProvider instead
 */
export function setGlobalAuthTokenProvider(provider: () => string | null): void {
  console.warn('⚠️ setGlobalAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider instead.');
  setUnifiedAuthTokenProvider(provider);
}

/**
 * @deprecated Use setUnifiedAuthTokenProvider instead
 */
export function setFactoryAuthTokenProvider(provider: () => string | null): void {
  console.warn('⚠️ setFactoryAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider instead.');
  setUnifiedAuthTokenProvider(provider);
}

/**
 * @deprecated Use getUnifiedAuthToken instead
 */
export function getGlobalAuthTokenProvider(): (() => string | null) | null {
  console.warn('⚠️ getGlobalAuthTokenProvider is deprecated. Use getUnifiedTokenProvider instead.');
  return getUnifiedTokenProvider();
}

/**
 * Export the unified token getter for API clients
 * This is the function that API clients should use
 */
export { getUnifiedAuthToken as getAuthToken };
