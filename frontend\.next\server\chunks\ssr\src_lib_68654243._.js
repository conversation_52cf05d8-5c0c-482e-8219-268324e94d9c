module.exports = {

"[project]/src/lib/transformers/delegationTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Delegation domain models.
 * @module transformers/delegationTransformer
 */ // API types from the new central location
__turbopack_context__.s({
    "DelegationTransformer": (()=>DelegationTransformer),
    "FlightDetailsTransformer": (()=>FlightDetailsTransformer)
});
// These helpers now produce structures compatible with CreateDelegationRequest sub-properties
const DelegationAssignmentTransformers = {
    // fromApi methods are less critical now as DelegationApiResponse is domain Delegation
    // but if API responses for these are ever separate and different, they'd be useful.
    // For now, their usage in DelegationTransformer.fromApi might be simplified.
    toDelegateApiStructure (domainData) {
        // The structure of the domain data is identical to CreateDelegateRequest
        return {
            name: domainData.name,
            notes: domainData.notes ?? null,
            title: domainData.title
        };
    },
    toDriverApiStructure (domainData) {
        // The structure of the domain data is identical to CreateDriverRequest
        return {
            employeeId: domainData.employeeId,
            notes: domainData.notes ?? null
        };
    },
    toEscortApiStructure (domainData) {
        // The structure of the domain data is identical to CreateEscortRequest
        return {
            employeeId: domainData.employeeId,
            notes: domainData.notes ?? null
        };
    },
    toVehicleAssignmentApiStructure (domainData) {
        // The structure of the domain data is identical to CreateVehicleAssignmentRequest
        return domainData;
    }
};
// Helper transformers for converting API response types to domain types
const EmployeeTransformer = {
    fromApi (apiData) {
        return {
            availability: apiData.availability,
            contactEmail: apiData.contactEmail ?? null,
            contactInfo: apiData.contactInfo,
            contactMobile: apiData.contactMobile ?? null,
            contactPhone: apiData.contactPhone ?? null,
            createdAt: apiData.createdAt,
            currentLocation: apiData.currentLocation ?? null,
            department: apiData.department ?? null,
            employeeId: apiData.employeeId,
            fullName: apiData.fullName ?? null,
            generalAssignments: apiData.generalAssignments,
            hireDate: apiData.hireDate ?? null,
            id: apiData.id,
            name: apiData.name,
            notes: apiData.notes ?? null,
            position: apiData.position ?? null,
            profileImageUrl: apiData.profileImageUrl ?? null,
            role: apiData.role,
            shiftSchedule: apiData.shiftSchedule ?? null,
            skills: apiData.skills,
            status: apiData.status,
            updatedAt: apiData.updatedAt,
            workingHours: apiData.workingHours ?? null
        };
    }
};
const VehicleTransformer = {
    fromApi (apiData) {
        return {
            color: apiData.color,
            createdAt: apiData.createdAt,
            id: apiData.id,
            imageUrl: apiData.imageUrl,
            initialOdometer: apiData.initialOdometer,
            licensePlate: apiData.licensePlate,
            make: apiData.make,
            model: apiData.model,
            ownerContact: apiData.ownerContact,
            ownerName: apiData.ownerName,
            serviceHistory: [],
            updatedAt: apiData.updatedAt,
            vin: apiData.vin,
            year: apiData.year
        };
    }
};
const DelegationTransformer = {
    fromApi (apiData) {
        return {
            // Map nested relations using their respective transformers or direct mapping if structure matches
            arrivalFlight: apiData.flightArrivalDetails ? FlightDetailsTransformer.fromApi(apiData.flightArrivalDetails) : null,
            createdAt: apiData.createdAt,
            delegates: apiData.delegates || [],
            departureFlight: apiData.flightDepartureDetails ? FlightDetailsTransformer.fromApi(apiData.flightDepartureDetails) : null,
            // Transform drivers: API returns Employee objects directly, but domain expects nested structure
            drivers: apiData.drivers?.map((driverEmployee)=>({
                    createdAt: apiData.createdAt,
                    createdBy: null,
                    delegationId: apiData.id,
                    employee: EmployeeTransformer.fromApi(driverEmployee),
                    employeeId: driverEmployee.id,
                    id: `driver-${apiData.id}-${driverEmployee.id}`,
                    updatedAt: apiData.updatedAt
                })) || [],
            durationFrom: apiData.durationFrom,
            durationTo: apiData.durationTo,
            // Transform escorts: API returns Employee objects directly, but domain expects nested structure
            escorts: apiData.escorts?.map((escortEmployee)=>({
                    createdAt: apiData.createdAt,
                    createdBy: null,
                    delegationId: apiData.id,
                    employee: EmployeeTransformer.fromApi(escortEmployee),
                    employeeId: escortEmployee.id,
                    id: `escort-${apiData.id}-${escortEmployee.id}`,
                    updatedAt: apiData.updatedAt
                })) || [],
            eventName: apiData.eventName,
            id: apiData.id,
            imageUrl: apiData.imageUrl ?? null,
            invitationFrom: apiData.invitationFrom ?? null,
            invitationTo: apiData.invitationTo ?? null,
            location: apiData.location,
            notes: apiData.notes ?? null,
            status: apiData.status,
            statusHistory: apiData.statusHistory || [],
            updatedAt: apiData.updatedAt,
            // Transform vehicles: API returns Vehicle objects directly, but domain expects nested structure
            vehicles: apiData.vehicles?.map((vehicleData)=>({
                    createdAt: apiData.createdAt,
                    createdBy: null,
                    delegationId: apiData.id,
                    id: `vehicle-${apiData.id}-${vehicleData.id}`,
                    updatedAt: apiData.updatedAt,
                    vehicle: VehicleTransformer.fromApi(vehicleData),
                    vehicleId: vehicleData.id
                })) || []
        };
    },
    toCreateRequest (domainData) {
        return {
            // ✅ FIXED: Use correct field names that match backend schema
            delegates: domainData.delegates?.map((delegate)=>DelegationAssignmentTransformers.toDelegateApiStructure(delegate)) ?? [],
            driverEmployeeIds: domainData.drivers?.map((driver)=>driver.employeeId) ?? [],
            durationFrom: domainData.durationFrom,
            durationTo: domainData.durationTo,
            escortEmployeeIds: domainData.escorts?.map((escort)=>escort.employeeId) ?? [],
            eventName: domainData.eventName,
            flightArrivalDetails: domainData.flightArrivalDetails ? FlightDetailsTransformer.toApiStructureForCreate(domainData.flightArrivalDetails) : undefined,
            flightDepartureDetails: domainData.flightDepartureDetails ? FlightDetailsTransformer.toApiStructureForCreate(domainData.flightDepartureDetails) : undefined,
            imageUrl: domainData.imageUrl ?? null,
            invitationFrom: domainData.invitationFrom ?? null,
            invitationTo: domainData.invitationTo ?? null,
            location: domainData.location,
            notes: domainData.notes ?? null,
            status: domainData.status,
            vehicleIds: domainData.vehicles?.map((vehicle)=>vehicle.vehicleId) ?? []
        };
    },
    toUpdateRequest (domainData) {
        const request = {};
        // ✅ PRODUCTION FIX: Use correct field names that match backend expectations
        if (domainData.eventName !== undefined) request.eventName = domainData.eventName; // ✅ Direct mapping
        if (domainData.location !== undefined) request.location = domainData.location;
        if (domainData.durationFrom !== undefined) request.durationFrom = domainData.durationFrom; // ✅ Direct mapping
        if (domainData.durationTo !== undefined) request.durationTo = domainData.durationTo; // ✅ Direct mapping
        if (domainData.status !== undefined) request.status = domainData.status; // Type assertion
        if (domainData.notes !== undefined) request.notes = domainData.notes; // ✅ Direct mapping
        if (domainData.imageUrl !== undefined) request.imageUrl = domainData.imageUrl;
        if (domainData.invitationFrom !== undefined) request.invitationFrom = domainData.invitationFrom;
        if (domainData.invitationTo !== undefined) request.invitationTo = domainData.invitationTo;
        if (domainData.flightArrivalDetails !== undefined) {
            request.flightArrivalDetails = domainData.flightArrivalDetails ?? undefined;
        }
        if (domainData.flightDepartureDetails !== undefined) {
            request.flightDepartureDetails = domainData.flightDepartureDetails ?? undefined;
        }
        // ✅ FIX: Include assignment fields in update request
        if (domainData.escorts !== undefined) {
            request.escortEmployeeIds = domainData.escorts.map((e)=>e.employeeId);
        }
        if (domainData.drivers !== undefined) {
            request.driverEmployeeIds = domainData.drivers.map((d)=>d.employeeId);
        }
        if (domainData.vehicles !== undefined) {
            request.vehicleIds = domainData.vehicles.map((v)=>v.vehicleId);
        }
        return request;
    }
};
const FlightDetailsTransformer = {
    fromApi (apiData) {
        if (!apiData) return null;
        return {
            airport: apiData.airport,
            dateTime: apiData.dateTime,
            flightNumber: apiData.flightNumber,
            id: apiData.id,
            notes: apiData.notes || null,
            terminal: apiData.terminal || null
        };
    },
    // This is for creating flight details as part of a new delegation
    toApiStructureForCreate (domainData) {
        // The structure of Omit<FlightDetails, 'id'> is identical to CreateFlightDetailsRequest
        return domainData;
    },
    toCreateRequest (domainData) {
        // This is for managing details on an existing delegation, not used for initial creation
        return {
            airport: domainData.airport,
            dateTime: domainData.dateTime,
            flightNumber: domainData.flightNumber,
            notes: domainData.notes ?? null,
            terminal: domainData.terminal ?? null
        };
    }
};
}}),
"[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced BaseApiService - Production-Ready Service Foundation
 *
 * This service provides a standardized foundation for all API services with:
 * - Circuit breaker protection (from AdminService patterns)
 * - Request caching and deduplication
 * - Comprehensive error handling with retry logic
 * - Performance monitoring and metrics
 * - Data transformation and validation
 *
 * Built upon proven patterns from the consolidated AdminService.
 */ __turbopack_context__.s({
    "BaseApiService": (()=>BaseApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
;
/**
 * Simple circuit breaker implementation
 */ class CircuitBreaker {
    name;
    threshold;
    timeout;
    failures;
    lastFailureTime;
    state;
    constructor(name, threshold = 5, timeout = 60_000){
        this.name = name;
        this.threshold = threshold;
        this.timeout = timeout;
        this.failures = 0;
        this.lastFailureTime = 0;
        this.state = 'CLOSED';
    }
    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'HALF_OPEN';
            } else {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]('Circuit breaker is OPEN', 'CIRCUIT_BREAKER_OPEN');
            }
        }
        try {
            const result = await operation();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }
    getState() {
        return {
            failures: this.failures,
            lastFailureTime: this.lastFailureTime,
            name: this.name,
            state: this.state
        };
    }
    onFailure() {
        this.failures++;
        this.lastFailureTime = Date.now();
        if (this.failures >= this.threshold) {
            this.state = 'OPEN';
        }
    }
    onSuccess() {
        this.failures = 0;
        this.state = 'CLOSED';
    }
}
/**
 * Simple request cache implementation
 */ class RequestCache {
    cache = new Map();
    clear() {
        this.cache.clear();
    }
    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        return item.data;
    }
    getStats() {
        return {
            keys: [
                ...this.cache.keys()
            ],
            size: this.cache.size
        };
    }
    invalidate(key) {
        this.cache.delete(key);
    }
    invalidatePattern(pattern) {
        for (const key of this.cache.keys()){
            if (pattern.test(key)) {
                this.cache.delete(key);
            }
        }
    }
    set(key, data, duration = 300_000) {
        this.cache.set(key, {
            data,
            expiry: Date.now() + duration
        });
    }
}
class BaseApiService {
    apiClient;
    cache;
    // Service infrastructure (enhanced from AdminService patterns)
    circuitBreaker;
    config;
    metrics;
    constructor(apiClient, config = {}){
        this.apiClient = apiClient;
        this.config = {
            cacheDuration: 5 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        };
        // Initialize service infrastructure
        this.circuitBreaker = new CircuitBreaker(`${this.constructor.name}`, this.config.circuitBreakerThreshold);
        this.cache = new RequestCache();
        this.metrics = {
            averageResponseTime: 0,
            cacheHitRatio: 0,
            errorCount: 0,
            requestCount: 0
        };
    }
    /**
   * Clear service cache
   */ clearCache() {
        this.cache.clear();
    }
    /**
   * Create new entity
   */ async create(data) {
        return this.executeWithInfrastructure(null, async ()=>{
            const transformedData = this.transformer.toApi ? this.transformer.toApi(data) : data;
            const response = await this.apiClient.post(this.endpoint, transformedData);
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    /**
   * Delete entity
   */ async delete(id) {
        return this.executeWithInfrastructure(null, async ()=>{
            await this.apiClient.delete(`${this.endpoint}/${id}`);
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
        });
    }
    /**
   * Get all entities with pagination and filtering
   */ async getAll(filters) {
        const cacheKey = `${this.endpoint}:getAll:${JSON.stringify(filters || {})}`;
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            const params = new URLSearchParams();
            if (filters) {
                for (const [key, value] of Object.entries(filters)){
                    if (value !== undefined && value !== null) {
                        params.append(key, String(value));
                    }
                }
            }
            const queryString = params.toString();
            const url = queryString ? `${this.endpoint}?${queryString}` : this.endpoint;
            const response = await this.apiClient.get(url);
            // Handle different response formats from backend
            let responseData;
            let paginationInfo = {};
            // Handle wrapped response format from responseWrapper middleware
            if (response && response.status === 'success' && response.data) {
                // Backend returns wrapped response: {status: 'success', data: {...}, pagination?: {...}}
                const wrappedData = response.data;
                if (Array.isArray(wrappedData)) {
                    // Direct array in wrapped response
                    responseData = wrappedData;
                    // Check for pagination at the top level of wrapped response
                    if (response.pagination) {
                        paginationInfo = {
                            pagination: {
                                hasNext: response.pagination.hasNext ?? false,
                                hasPrevious: response.pagination.hasPrevious ?? false,
                                limit: response.pagination.limit,
                                page: response.pagination.page,
                                total: response.pagination.total,
                                totalPages: response.pagination.totalPages ?? Math.ceil(response.pagination.total / response.pagination.limit)
                            }
                        };
                    }
                } else if (wrappedData && Array.isArray(wrappedData.data)) {
                    // Nested data structure: {status: 'success', data: {data: [], pagination: {}}}
                    responseData = wrappedData.data;
                    // Handle nested pagination object
                    if (wrappedData.pagination) {
                        paginationInfo = {
                            pagination: {
                                hasNext: wrappedData.pagination.hasNext ?? false,
                                hasPrevious: wrappedData.pagination.hasPrevious ?? false,
                                limit: wrappedData.pagination.limit,
                                page: wrappedData.pagination.page,
                                total: wrappedData.pagination.total,
                                totalPages: wrappedData.pagination.totalPages ?? Math.ceil(wrappedData.pagination.total / wrappedData.pagination.limit)
                            }
                        };
                    } else if (response.pagination) {
                        // Pagination at wrapper level
                        paginationInfo = {
                            pagination: {
                                hasNext: response.pagination.hasNext ?? false,
                                hasPrevious: response.pagination.hasPrevious ?? false,
                                limit: response.pagination.limit,
                                page: response.pagination.page,
                                total: response.pagination.total,
                                totalPages: response.pagination.totalPages ?? Math.ceil(response.pagination.total / response.pagination.limit)
                            }
                        };
                    }
                } else {
                    // Single item wrapped response - convert to array for consistency
                    responseData = [
                        wrappedData
                    ];
                }
            } else if (Array.isArray(response)) {
                // Direct array response (common backend pattern)
                responseData = response;
            } else if (response && (response.error || response.status === 'error')) {
                // Backend returned an error response
                throw new Error(response.message || response.error || 'API request failed');
            } else if (response && typeof response === 'object') {
                // Single object response - convert to array for consistency
                responseData = [
                    response
                ];
            } else {
                // Unexpected response format
                throw new Error(`Invalid response format from API: ${JSON.stringify(response)}`);
            }
            // Transform data using the service's transformer
            const transformedData = responseData.map((item)=>this.transformer.fromApi ? this.transformer.fromApi(item) : item);
            return {
                data: transformedData,
                ...paginationInfo
            };
        });
    }
    /**
   * Get entity by ID
   */ async getById(id) {
        const cacheKey = `${this.endpoint}:getById:${id}`;
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/${id}`);
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    /**
   * Get service health status
   */ getHealthStatus() {
        return {
            cacheStats: this.cache.getStats(),
            circuitBreakerState: this.circuitBreaker.getState(),
            endpoint: this.endpoint,
            metrics: this.metrics,
            service: this.constructor.name
        };
    }
    /**
   * Reset service metrics
   */ resetMetrics() {
        this.metrics = {
            averageResponseTime: 0,
            cacheHitRatio: 0,
            errorCount: 0,
            requestCount: 0
        };
    }
    /**
   * Update existing entity
   */ async update(id, data) {
        return this.executeWithInfrastructure(null, async ()=>{
            const transformedData = this.transformer.toApi ? this.transformer.toApi(data) : data;
            const response = await this.apiClient.put(`${this.endpoint}/${id}`, transformedData);
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    /**
   * Execute operation with full service infrastructure
   * (circuit breaker, caching, error handling, metrics)
   */ async executeWithInfrastructure(cacheKey, operation) {
        const startTime = Date.now();
        try {
            this.metrics.requestCount++;
            // Try cache first if cacheKey provided
            if (cacheKey) {
                const cached = this.cache.get(cacheKey);
                if (cached) {
                    this.metrics.cacheHitRatio = (this.metrics.cacheHitRatio * (this.metrics.requestCount - 1) + 1) / this.metrics.requestCount;
                    return cached;
                }
            }
            // Execute with circuit breaker and retry logic
            const result = await this.circuitBreaker.execute(async ()=>{
                return withRetry(operation, this.config.retryAttempts);
            });
            // Cache result if cacheKey provided
            if (cacheKey && result) {
                this.cache.set(cacheKey, result, this.config.cacheDuration);
            }
            // Update metrics
            const responseTime = Date.now() - startTime;
            this.metrics.averageResponseTime = (this.metrics.averageResponseTime * (this.metrics.requestCount - 1) + responseTime) / this.metrics.requestCount;
            return result;
        } catch (error) {
            this.metrics.errorCount++;
            // Log the error for debugging with better error serialization
            console.error(`Service error in ${this.constructor.name}:`, {
                endpoint: this.endpoint,
                errorDetails: error instanceof Error ? {
                    message: error.message,
                    name: error.name,
                    stack: error.stack
                } : error,
                errorMessage: error instanceof Error ? error.message : String(error),
                errorType: error?.constructor?.name || typeof error,
                timestamp: new Date().toISOString()
            });
            // Transform error to ServiceError
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]) {
                throw error;
            }
            // Handle specific API errors
            if (error instanceof Error) {
                // Check if it's a network connectivity issue
                if (error.message.includes('fetch') || error.message.includes('network')) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]('Network connection failed. Please check your internet connection and try again.', 'NETWORK_ERROR', undefined, {
                        endpoint: this.endpoint,
                        service: this.constructor.name
                    });
                }
                // Check if it's a backend server error
                if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"]('Server error occurred. Please try again later.', 'SERVER_ERROR', undefined, {
                        endpoint: this.endpoint,
                        service: this.constructor.name
                    });
                }
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"](error instanceof Error ? error.message : 'Unknown service error', 'SERVICE_ERROR', undefined, {
                endpoint: this.endpoint,
                service: this.constructor.name
            });
        }
    }
}
/**
 * Simple retry utility
 */ async function withRetry(operation, maxAttempts = 3, delay = 1000) {
    let lastError;
    for(let attempt = 1; attempt <= maxAttempts; attempt++){
        try {
            return await operation();
        } catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
            if (attempt === maxAttempts) {
                throw lastError;
            }
            // Wait before retry
            await new Promise((resolve)=>setTimeout(resolve, delay * attempt));
        }
    }
    throw lastError;
}
}}),
"[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DelegationApiService": (()=>DelegationApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/delegationTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
;
const DelegationApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(data),
    toApi: (data)=>data
};
const FlightDetailsTransformer = {
    toCreateRequest: (data)=>data
};
class DelegationApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/delegations';
    transformer = DelegationApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 2 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    async getByStatus(status) {
        const result = await this.getAll({
            status
        });
        return result.data;
    }
    async manageFlightDetails(id, flightDetails) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = FlightDetailsTransformer.toCreateRequest(flightDetails);
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/flight-details`, requestPayload);
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setDelegates(id, delegates) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/delegates`, {
                delegates
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setDrivers(id, driverEmployeeIds) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = driverEmployeeIds.map((employeeId)=>({
                    employeeId
                }));
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/drivers`, {
                drivers: requestPayload
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setEscorts(id, escortEmployeeIds) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = escortEmployeeIds.map((employeeId)=>({
                    employeeId
                }));
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/escorts`, {
                escorts: requestPayload
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async setVehicles(id, vehicleIds) {
        return this.executeWithInfrastructure(null, async ()=>{
            const requestPayload = vehicleIds.map((vehicleId)=>({
                    assignedDate: new Date().toISOString(),
                    vehicleId
                }));
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/vehicles`, {
                vehicleAssignments: requestPayload
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
    async updateStatus(id, newStatus, statusChangeReason) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.put(`${this.endpoint}/${id}`, {
                status: newStatus,
                statusChangeReason
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].fromApi(response);
        });
    }
}
}}),
"[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Employee domain models.
 * @module transformers/employeeTransformer
 */ __turbopack_context__.s({
    "EmployeeTransformer": (()=>EmployeeTransformer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/dateUtils.ts [app-ssr] (ecmascript)");
;
const EmployeeTransformer = {
    /**
   * Converts an API Employee response into a frontend Employee domain model.
   * @param apiData - The data received from the API.
   * @returns The Employee domain model.
   */ fromApi (apiData) {
        return {
            availability: apiData.availability,
            contactEmail: apiData.contactEmail ?? null,
            contactInfo: apiData.contactInfo,
            contactMobile: apiData.contactMobile ?? null,
            contactPhone: apiData.contactPhone ?? null,
            createdAt: apiData.createdAt,
            currentLocation: apiData.currentLocation ?? null,
            department: apiData.department ?? null,
            employeeId: apiData.employeeId,
            fullName: apiData.fullName ?? null,
            generalAssignments: apiData.generalAssignments,
            hireDate: apiData.hireDate ?? null,
            id: apiData.id,
            name: apiData.name || '',
            notes: apiData.notes ?? null,
            position: apiData.position ?? null,
            profileImageUrl: apiData.profileImageUrl ?? null,
            role: apiData.role,
            shiftSchedule: apiData.shiftSchedule ?? null,
            skills: apiData.skills,
            status: apiData.status,
            updatedAt: apiData.updatedAt,
            workingHours: apiData.workingHours ?? null
        };
    },
    /**
   * Converts frontend data for creating an employee into an API request payload.
   * @param employeeData - The data from the frontend for creating a new employee.
   * @returns The transformed CreateEmployeeRequest payload.
   */ toCreateRequest (employeeData) {
        // Convert form data to API request format
        const request = {
            availability: employeeData.availability,
            contactEmail: employeeData.contactEmail?.trim() ?? null,
            contactInfo: employeeData.contactInfo.trim(),
            contactMobile: employeeData.contactMobile?.trim() ?? null,
            contactPhone: employeeData.contactPhone?.trim() ?? null,
            currentLocation: employeeData.currentLocation ?? null,
            department: employeeData.department?.trim() ?? null,
            employeeId: employeeData.employeeId,
            fullName: employeeData.fullName?.trim() ?? null,
            generalAssignments: employeeData.generalAssignments,
            hireDate: employeeData.hireDate ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDateForApi"])(employeeData.hireDate) : null,
            name: employeeData.name.trim(),
            notes: employeeData.notes?.trim() ?? null,
            position: employeeData.position?.trim() ?? null,
            profileImageUrl: employeeData.profileImageUrl?.trim() ?? null,
            role: employeeData.role,
            shiftSchedule: employeeData.shiftSchedule ?? null,
            skills: employeeData.skills,
            status: employeeData.status,
            workingHours: employeeData.workingHours ?? null
        };
        return request;
    },
    /**
   * Converts partial frontend employee data into an API request payload for updating.
   * @param employeeData - The partial data from the frontend for updating an employee.
   * @returns The transformed UpdateEmployeeRequest payload.
   */ toUpdateRequest (employeeData) {
        // Convert partial form data to API request format
        const request = {};
        if (employeeData.name !== undefined) request.name = employeeData.name?.trim() ?? null;
        if (employeeData.employeeId !== undefined) request.employeeId = employeeData.employeeId;
        if (employeeData.contactInfo !== undefined) request.contactInfo = employeeData.contactInfo?.trim() ?? null;
        if (employeeData.contactEmail !== undefined) request.contactEmail = employeeData.contactEmail?.trim() ?? null;
        if (employeeData.contactMobile !== undefined) request.contactMobile = employeeData.contactMobile?.trim() ?? null;
        if (employeeData.contactPhone !== undefined) request.contactPhone = employeeData.contactPhone?.trim() ?? null;
        if (employeeData.position !== undefined) request.position = employeeData.position?.trim() ?? null;
        if (employeeData.department !== undefined) request.department = employeeData.department?.trim() ?? null;
        if (employeeData.hireDate !== undefined) request.hireDate = employeeData.hireDate ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDateForApi"])(employeeData.hireDate) : null; // Convert to ISO format
        if (employeeData.fullName !== undefined) request.fullName = employeeData.fullName?.trim() ?? null;
        if (employeeData.role !== undefined) request.role = employeeData.role; // Type assertion
        if (employeeData.status !== undefined) request.status = employeeData.status; // Type assertion
        if (employeeData.availability !== undefined) request.availability = employeeData.availability; // Type assertion
        if (employeeData.currentLocation !== undefined) request.currentLocation = employeeData.currentLocation;
        if (employeeData.workingHours !== undefined) request.workingHours = employeeData.workingHours;
        if (employeeData.generalAssignments !== undefined) request.generalAssignments = employeeData.generalAssignments;
        if (employeeData.notes !== undefined) request.notes = employeeData.notes?.trim() ?? null;
        if (employeeData.profileImageUrl !== undefined) request.profileImageUrl = employeeData.profileImageUrl?.trim() ?? null;
        if (employeeData.shiftSchedule !== undefined) request.shiftSchedule = employeeData.shiftSchedule;
        if (employeeData.skills !== undefined) request.skills = employeeData.skills;
        return request;
    }
};
}}),
"[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmployeeApiService": (()=>EmployeeApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
;
const EmployeeApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(data),
    toApi: (data)=>data
};
class EmployeeApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/employees';
    transformer = EmployeeApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getByRole(role) {
        const result = await this.getAll({
            role
        });
        return result.data;
    }
    async updateAvailabilityStatus(employeeId, status) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${employeeId}/availability`, {
                availability: status
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${employeeId}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(response);
        });
    }
}
}}),
"[project]/src/lib/api/services/domain/reliabilityApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReliabilityApiService": (()=>ReliabilityApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const ReliabilityTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class ReliabilityApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/reliability';
    transformer = ReliabilityTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 1 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getSystemHealth() {
        return this.executeWithInfrastructure('health:system', async ()=>{
            const response = await this.apiClient.get('/health');
            return response;
        });
    }
    async getDetailedHealth() {
        return this.executeWithInfrastructure('health:detailed', async ()=>{
            const response = await this.apiClient.get('/health/detailed');
            return response;
        });
    }
    async getDependencyHealth() {
        return this.executeWithInfrastructure('health:dependencies', async ()=>{
            const response = await this.apiClient.get('/health/dependencies');
            return response;
        });
    }
    async getCircuitBreakerStatus() {
        return this.executeWithInfrastructure('monitoring:circuit-breakers', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/monitoring/circuit-breakers');
                const circuitBreakers = apiResponse?.circuitBreakers || [];
                return {
                    circuitBreakers: circuitBreakers || [],
                    summary: {
                        total: circuitBreakers?.length || 0,
                        closed: circuitBreakers?.filter((cb)=>cb.state === 'CLOSED').length || 0,
                        open: circuitBreakers?.filter((cb)=>cb.state === 'OPEN').length || 0,
                        halfOpen: circuitBreakers?.filter((cb)=>cb.state === 'HALF_OPEN').length || 0
                    }
                };
            } catch (error) {
                console.error('Failed to get circuit breaker status:', error);
                return {
                    circuitBreakers: [],
                    summary: {
                        total: 0,
                        closed: 0,
                        open: 0,
                        halfOpen: 0
                    }
                };
            }
        });
    }
    async getDeduplicationMetrics() {
        return this.executeWithInfrastructure('monitoring:deduplication', async ()=>{
            const response = await this.apiClient.get('/monitoring/deduplication');
            return response;
        });
    }
    async getMetrics() {
        return this.executeWithInfrastructure('metrics:system', async ()=>{
            const response = await this.apiClient.get('/metrics', {
                headers: {
                    Accept: 'application/json'
                }
            });
            return response;
        });
    }
    async getActiveAlerts() {
        return this.executeWithInfrastructure('alerts:active', async ()=>{
            try {
                const apiResponse = await this.apiClient.get('/alerts');
                return apiResponse?.alerts || [];
            } catch (error) {
                console.error('Failed to get active alerts:', error);
                return [];
            }
        });
    }
    async getAlertHistory(page = 1, limit = 50) {
        return this.executeWithInfrastructure(`alerts:history:${page}:${limit}`, async ()=>{
            const queryParams = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });
            const response = await this.apiClient.get(`/alerts/history?${queryParams.toString()}`);
            return response;
        });
    }
    async getAlertStatistics() {
        return this.executeWithInfrastructure('alerts:statistics', async ()=>{
            try {
                const response = await this.apiClient.get('/alerts/statistics');
                return response;
            } catch (error) {
                console.error('Failed to get alert statistics:', error);
                return {
                    total: 0,
                    active: 0,
                    acknowledged: 0,
                    resolved: 0,
                    bySeverity: {
                        low: 0,
                        medium: 0,
                        high: 0,
                        critical: 0
                    },
                    averageResolutionTime: 0,
                    recentTrends: {
                        last24Hours: 0,
                        last7Days: 0,
                        last30Days: 0
                    }
                };
            }
        });
    }
    async resolveAlert(alertId, reason, resolvedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/resolve`, {
                reason,
                resolvedBy
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async acknowledgeAlert(alertId, note, acknowledgedBy) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/alerts/${alertId}/acknowledge`, {
                note,
                acknowledgedBy
            });
            this.cache.invalidatePattern(new RegExp('^alerts:'));
            return response;
        });
    }
    async testAlerts() {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post('/alerts/test');
            return {
                success: response?.status === 'success',
                message: response?.message || 'Test alert triggered',
                testAlertId: response?.data?.id
            };
        });
    }
    async getReliabilityDashboardData() {
        const [systemHealth, detailedHealth, circuitBreakers, metrics, activeAlerts, alertStatistics] = await Promise.all([
            this.getSystemHealth(),
            this.getDetailedHealth(),
            this.getCircuitBreakerStatus(),
            this.getMetrics(),
            this.getActiveAlerts(),
            this.getAlertStatistics()
        ]);
        return {
            systemHealth,
            detailedHealth,
            circuitBreakers,
            metrics,
            activeAlerts,
            alertStatistics
        };
    }
    async isSystemHealthy() {
        try {
            const health = await this.getSystemHealth();
            return health.status === 'healthy';
        } catch (error) {
            return false;
        }
    }
    async getCriticalAlertCount() {
        try {
            const statistics = await this.getAlertStatistics();
            return statistics.bySeverity.critical;
        } catch (error) {
            return 0;
        }
    }
    async getHealthTrends(timeframe = '24h') {
        return this.executeWithInfrastructure(`health:trends:${timeframe}`, async ()=>{
            const response = await this.apiClient.get(`/health/trends?timeframe=${timeframe}`);
            return response;
        });
    }
    async getCircuitBreakerHistory(timeframe = '24h', breakerName) {
        return this.executeWithInfrastructure(`circuit-breakers:history:${timeframe}:${breakerName || 'all'}`, async ()=>{
            const params = new URLSearchParams({
                timeframe
            });
            if (breakerName) {
                params.append('breakerName', breakerName);
            }
            const response = await this.apiClient.get(`/monitoring/circuit-breakers/history?${params.toString()}`);
            return response;
        });
    }
    async getHttpRequestMetrics() {
        return this.executeWithInfrastructure('http:metrics', async ()=>{
            const response = await this.apiClient.get('/monitoring/http-request-metrics');
            return response;
        });
    }
}
}}),
"[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Vehicle domain models.
 * @module transformers/vehicleTransformer
 */ __turbopack_context__.s({
    "VehicleTransformer": (()=>VehicleTransformer)
});
// ServiceRecordTransformer
const ServiceRecordTransformer = {
    fromApi (apiData) {
        return {
            cost: apiData.cost,
            createdAt: apiData.createdAt,
            date: apiData.date,
            employeeId: apiData.employeeId,
            id: apiData.id,
            notes: apiData.notes,
            odometer: apiData.odometer,
            servicePerformed: Array.isArray(apiData.servicePerformed) ? apiData.servicePerformed : [],
            updatedAt: apiData.updatedAt,
            vehicleId: apiData.vehicleId
        };
    }
};
const VehicleTransformer = {
    /**
   * Converts a raw API Vehicle response into a frontend Vehicle domain model.
   * @param apiData - The raw data received from the API.
   * @returns The transformed Vehicle domain model.
   */ fromApi (apiData) {
        return {
            color: apiData.color ?? null,
            createdAt: apiData.createdAt,
            id: apiData.id,
            imageUrl: apiData.imageUrl ?? null,
            initialOdometer: apiData.initialOdometer ?? null,
            licensePlate: apiData.licensePlate,
            make: apiData.make,
            model: apiData.model,
            ownerContact: apiData.ownerContact,
            ownerName: apiData.ownerName,
            serviceHistory: (()=>{
                const records = apiData.serviceHistory || apiData.ServiceRecord;
                return Array.isArray(records) ? records.map(ServiceRecordTransformer.fromApi) : [];
            })(),
            updatedAt: apiData.updatedAt,
            vin: apiData.vin ?? null,
            year: apiData.year
        };
    },
    /**
   * Converts frontend data for creating a vehicle into an API request payload.
   * @param vehicleData - The data from the frontend for creating a new vehicle.
   * @returns The transformed payload, compatible with CreateVehicleRequest.
   */ toCreateRequest (vehicleData) {
        // Generate a default VIN if not provided (for demo purposes)
        const defaultVin = vehicleData.vin?.trim() || this.generateDefaultVin(vehicleData);
        // Provide valid default values that pass backend validation
        const defaultOwnerContact = vehicleData.ownerContact?.trim() || '<EMAIL>';
        const defaultOwnerName = vehicleData.ownerName?.trim() || 'WorkHub Fleet Management';
        const request = {
            color: vehicleData.color ? vehicleData.color.trim() : null,
            imageUrl: vehicleData.imageUrl ? vehicleData.imageUrl.trim() : '',
            initialOdometer: vehicleData.initialOdometer ?? null,
            licensePlate: vehicleData.licensePlate.trim(),
            make: vehicleData.make.trim(),
            model: vehicleData.model.trim(),
            ownerContact: defaultOwnerContact,
            ownerName: defaultOwnerName,
            vin: defaultVin,
            year: vehicleData.year
        };
        if (!request.make || !request.model || !request.year || !request.licensePlate) {
            throw new Error('Missing required fields for creating a vehicle (make, model, year, licensePlate)');
        }
        // Validate VIN format
        if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(request.vin)) {
            throw new Error('VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)');
        }
        return request;
    },
    /**
   * Generates a default VIN for demo purposes
   * In production, this should be handled differently
   */ generateDefaultVin (vehicleData) {
        // Valid VIN characters (excluding I, O, Q)
        const validChars = 'ABCDEFGHJKLMNPRSTUVWXYZ0123456789';
        // Generate a valid 17-character VIN
        const makeCode = vehicleData.make.substring(0, 3).toUpperCase().replace(/[IOQ]/g, 'X').padEnd(3, 'X');
        const modelCode = vehicleData.model.substring(0, 2).toUpperCase().replace(/[IOQ]/g, 'X').padEnd(2, 'X');
        // Year code (last 2 digits)
        const yearCode = vehicleData.year.toString().substring(2);
        // Generate remaining 10 characters using valid VIN characters
        let randomCode = '';
        for(let i = 0; i < 10; i++){
            randomCode += validChars.charAt(Math.floor(Math.random() * validChars.length));
        }
        const vin = `${makeCode}${modelCode}${yearCode}${randomCode}`;
        // Ensure exactly 17 characters
        return vin.substring(0, 17).padEnd(17, 'X');
    },
    /**
   * Converts partial frontend vehicle data into an API request payload for updating.
   * @param vehicleData - The partial data from the frontend for updating a vehicle.
   * @returns The transformed payload, compatible with UpdateVehicleRequest.
   */ toUpdateRequest (vehicleData) {
        const request = {};
        if (vehicleData.make !== undefined) request.make = vehicleData.make.trim();
        if (vehicleData.model !== undefined) request.model = vehicleData.model.trim();
        if (vehicleData.year !== undefined) request.year = vehicleData.year;
        if (vehicleData.vin !== undefined) request.vin = vehicleData.vin.trim();
        if (vehicleData.licensePlate !== undefined) request.licensePlate = vehicleData.licensePlate.trim();
        if (vehicleData.ownerName !== undefined) request.ownerName = vehicleData.ownerName.trim();
        if (vehicleData.ownerContact !== undefined) request.ownerContact = vehicleData.ownerContact.trim();
        if (vehicleData.color !== undefined) request.color = vehicleData.color ? vehicleData.color.trim() : null;
        if (vehicleData.initialOdometer !== undefined) request.initialOdometer = vehicleData.initialOdometer;
        if (vehicleData.imageUrl !== undefined) {
            request.imageUrl = vehicleData.imageUrl ? vehicleData.imageUrl.trim() : '';
        }
        return request;
    }
};
}}),
"[project]/src/lib/transformers/taskTransformer.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Data transformer for Task domain models.
 * @module transformers/taskTransformer
 */ __turbopack_context__.s({
    "SubtaskTransformer": (()=>SubtaskTransformer),
    "TaskTransformer": (()=>TaskTransformer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)");
;
;
const SubtaskTransformer = {
    /**
   * Transforms a subtask API response into a domain.Subtask model.
   * @param apiData - The subtask data as received from the API.
   * @returns A domain.Subtask object.
   */ fromApi (apiData) {
        return {
            completed: apiData.completed,
            id: apiData.id,
            taskId: apiData.taskId,
            title: apiData.title
        };
    },
    /**
   * Transforms a domain.CreateSubtaskData into an API CreateSubtaskRequest.
   * @param domainData - The subtask data from the domain.
   * @returns An API CreateSubtaskRequest object.
   */ toApiRequest (domainData) {
        return {
            completed: domainData.completed === undefined ? false : domainData.completed,
            taskId: domainData.taskId,
            title: domainData.title.trim()
        };
    }
};
const TaskTransformer = {
    fromApi (apiData) {
        return {
            createdAt: apiData.createdAt,
            dateTime: apiData.dateTime,
            deadline: apiData.deadline ?? null,
            description: apiData.description,
            driverEmployee: apiData.Employee_Task_driverEmployeeIdToEmployee ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(apiData.Employee_Task_driverEmployeeIdToEmployee) : null,
            driverEmployeeId: apiData.driverEmployeeId ?? null,
            estimatedDuration: apiData.estimatedDuration,
            id: apiData.id,
            location: apiData.location,
            notes: apiData.notes ?? null,
            priority: apiData.priority,
            requiredSkills: apiData.requiredSkills,
            // Transform relation objects from backend Prisma includes
            staffEmployee: apiData.Employee_Task_staffEmployeeIdToEmployee ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].fromApi(apiData.Employee_Task_staffEmployeeIdToEmployee) : null,
            staffEmployeeId: apiData.staffEmployeeId,
            status: apiData.status,
            subtasks: Array.isArray(apiData.SubTask) ? apiData.SubTask.map((st)=>SubtaskTransformer.fromApi(st)) : [],
            updatedAt: apiData.updatedAt,
            vehicle: apiData.Vehicle ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].fromApi(apiData.Vehicle) : null,
            vehicleId: apiData.vehicleId ?? null
        };
    },
    toCreateRequest (taskData) {
        return {
            dateTime: taskData.dateTime,
            deadline: taskData.deadline ?? null,
            description: taskData.description,
            driverEmployeeId: taskData.driverEmployeeId ?? null,
            estimatedDuration: taskData.estimatedDuration,
            location: taskData.location,
            notes: taskData.notes ?? null,
            priority: taskData.priority,
            requiredSkills: taskData.requiredSkills,
            staffEmployeeId: taskData.staffEmployeeId,
            status: taskData.status,
            subTasks: taskData.subtasks?.map(SubtaskTransformer.toApiRequest) ?? [],
            vehicleId: taskData.vehicleId ?? null
        };
    },
    toUpdateRequest (taskData) {
        const request = {};
        if (taskData.description !== undefined) {
            request.description = taskData.description; // Direct mapping - domain description to API description
        }
        if (taskData.notes !== undefined) request.notes = taskData.notes; // Direct mapping - domain notes to API notes
        if (taskData.location !== undefined) request.location = taskData.location;
        if (taskData.dateTime !== undefined) request.dateTime = taskData.dateTime;
        if (taskData.estimatedDuration !== undefined) request.estimatedDuration = taskData.estimatedDuration;
        if (taskData.priority !== undefined) request.priority = taskData.priority;
        if (taskData.status !== undefined) request.status = taskData.status;
        if (taskData.deadline !== undefined) request.deadline = taskData.deadline; // Backend expects 'deadline'
        if (taskData.requiredSkills !== undefined) request.requiredSkills = taskData.requiredSkills;
        if (taskData.vehicleId !== undefined) request.vehicleId = taskData.vehicleId;
        if (taskData.staffEmployeeId !== undefined) request.staffEmployeeId = taskData.staffEmployeeId;
        if (taskData.driverEmployeeId !== undefined) request.driverEmployeeId = taskData.driverEmployeeId;
        if (taskData.subtasks !== undefined) {
            request.subTasks = taskData.subtasks.map(SubtaskTransformer.toApiRequest); // Cast to any to allow subTasks
        }
        return request;
    }
};
}}),
"[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TaskApiService": (()=>TaskApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
;
const TaskApiTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].fromApi(data),
    toApi: (data)=>data
};
class TaskApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/tasks';
    transformer = TaskApiTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 3 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getByStatus(status) {
        const result = await this.getAll({
            status
        });
        return result.data;
    }
    async updateTaskStatus(taskId, newStatus) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${taskId}/status`, {
                status: newStatus
            });
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${taskId}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].fromApi(response);
        });
    }
}
}}),
"[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Enhanced Vehicle API service using BaseApiService patterns.
 * @module api/services/vehicleApi
 */ __turbopack_context__.s({
    "VehicleApiService": (()=>VehicleApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)");
;
;
const VehicleTransformer = {
    fromApi: (data)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].fromApi(data),
    toApi: (data)=>data
};
class VehicleApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/vehicles';
    transformer = VehicleTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 10 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    async getAvailableVehicles(startDate, endDate) {
        const result = await this.getAll({
            available: true,
            endDate: endDate.toISOString(),
            startDate: startDate.toISOString()
        });
        return result.data;
    }
    // Vehicle-specific methods
    async getByStatus(status) {
        const result = await this.getAll({
            status
        });
        return result.data;
    }
}
}}),
"[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Common API types and interfaces.
 * @module api/base/types
 */ /**
 * Configuration interface for the ApiClient.
 * @property baseURL - The base URL for API requests.
 * @property timeout - Optional. The request timeout in milliseconds. Defaults to 10000 (10 seconds).
 * @property retryAttempts - Optional. The number of retry attempts for failed requests. Defaults to 3.
 * @property headers - Optional. Default headers to be sent with every request.
 * @property authToken - Optional. Authentication token to be included in requests.
 * @property getAuthToken - Optional. Function to get the current authentication token dynamically.
 */ __turbopack_context__.s({});
;
}}),
"[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Core interfaces for the API layer
 * @module api/core/interfaces
 */ __turbopack_context__.s({});
;
}}),
"[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Flight API Service
 *
 * This service provides functions to interact with the flight-related API endpoints.
 * Refactored to use BaseApiService pattern for consistency.
 */ __turbopack_context__.s({
    "FlightApiService": (()=>FlightApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const FlightTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class FlightApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/flights';
    transformer = FlightTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    /**
   * Search for flights by callsign and date
   */ async searchFlightsByCallsignAndDate(callsign, date// Expected format: "YYYY-MM-DD"
    ) {
        if (!date) {
            console.error('Search date is required for historical flight search.');
            throw new Error('Search date is required');
        }
        // Check if date is in the future
        const searchDate = new Date(`${date}T00:00:00.000Z`);
        const currentDate = new Date();
        if (searchDate > currentDate) {
            console.warn(`Search for future date rejected: ${date}`);
            throw new Error(`OpenSky API does not provide data for future dates. The date ${date} is in the future.`);
        }
        return this.executeWithInfrastructure(`search:${callsign}:${date}`, async ()=>{
            const response = await this.apiClient.get(`/flights/search?callsign=${encodeURIComponent(callsign)}&date=${date}`);
            // Handle the enhanced response format
            if (Array.isArray(response)) {
                return response; // Original array response
            } else if (response.flights) {
                return response.flights; // New format with flights array
            } else {
                // If we have an error message but no flights, throw an error with the details
                if (response.message) {
                    const error = new Error(response.message);
                    // @ts-ignore - Add details to the error object
                    error.details = response.details;
                    throw error;
                }
                return []; // Fallback to empty array
            }
        });
    }
    /**
   * Get flights by airport (arrivals or departures)
   */ async getFlightsByAirport(airport, begin, end, type = 'arrival') {
        return this.executeWithInfrastructure(`airport:${airport}:${begin}:${end}:${type}`, async ()=>{
            return this.apiClient.get(`/flights/airport?airport=${encodeURIComponent(airport)}&begin=${begin}&end=${end}&type=${type}`);
        });
    }
    /**
   * Get flights by time interval
   */ async getFlightsByTimeInterval(begin, end) {
        return this.executeWithInfrastructure(`interval:${begin}:${end}`, async ()=>{
            return this.apiClient.get(`/flights/interval?begin=${begin}&end=${end}`);
        });
    }
}
}}),
"[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file API service for FlightDetails-related operations.
 * @module api/services/flightDetailsApi
 * Refactored to use BaseApiService pattern for consistency.
 */ __turbopack_context__.s({
    "FlightDetailsApiService": (()=>FlightDetailsApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
;
const FlightDetailsTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class FlightDetailsApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/flights';
    transformer = FlightDetailsTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    /**
   * Creates or updates flight details.
   * If flightDetails.id is provided, it attempts to update. Otherwise, it creates a new one.
   */ async createOrUpdateFlight(flightDetails) {
        return this.executeWithInfrastructure(null, async ()=>{
            if (flightDetails.id) {
                // Use the inherited update method
                return this.update(flightDetails.id, flightDetails);
            } else {
                // Use the inherited create method
                return this.create(flightDetails);
            }
        });
    }
}
}}),
"[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Centralized exports for the API service layer.
 * @module api
 */ // Core infrastructure
__turbopack_context__.s({
    "apiClient": (()=>apiClient),
    "getGlobalAuthTokenProvider": (()=>getGlobalAuthTokenProvider),
    "getUnifiedAuthTokenProvider": (()=>getUnifiedAuthTokenProvider),
    "setGlobalAuthTokenProvider": (()=>setGlobalAuthTokenProvider),
    "setUnifiedAuthTokenProvider": (()=>setUnifiedAuthTokenProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$interfaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
// Security architecture (selective exports to avoid conflicts)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
// Domain-specific API services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
// External API services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightDetailsApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
/**
 * Unified Authentication Token Provider
 * Single source of truth for authentication tokens across the entire application
 */ let unifiedAuthTokenProvider = null;
function setUnifiedAuthTokenProvider(provider) {
    unifiedAuthTokenProvider = provider;
    if ("TURBOPACK compile-time truthy", 1) {
        console.log('🔐 Unified Auth Token Provider initialized');
    }
}
/**
 * Get the current authentication token from the unified provider
 * This is used by ALL API clients throughout the application
 */ function getAuthToken() {
    if (!unifiedAuthTokenProvider) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('⚠️ Unified Auth Token Provider not initialized');
        }
        return null;
    }
    try {
        return unifiedAuthTokenProvider();
    } catch (error) {
        console.error('❌ Error getting auth token from unified provider:', error);
        return null;
    }
}
function getUnifiedAuthTokenProvider() {
    return unifiedAuthTokenProvider;
}
function setGlobalAuthTokenProvider(provider) {
    console.warn('⚠️ setGlobalAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider instead.');
    setUnifiedAuthTokenProvider(provider);
}
function getGlobalAuthTokenProvider() {
    console.warn('⚠️ getGlobalAuthTokenProvider is deprecated. Use getUnifiedAuthTokenProvider instead.');
    return getUnifiedAuthTokenProvider();
}
// Get environment-aware configuration
const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"]({
    baseURL: envConfig.apiBaseUrl,
    getAuthToken,
    headers: {
        'Content-Type': 'application/json'
    },
    retryAttempts: 3,
    timeout: 10_000
});
;
;
;
;
;
;
;
}}),
"[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$interfaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightDetailsApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Factory for creating and managing API service instances.
 * @module api/services/apiServiceFactory
 */ __turbopack_context__.s({
    "ApiServiceFactory": (()=>ApiServiceFactory),
    "apiServiceFactory": (()=>apiServiceFactory),
    "delegationApiService": (()=>delegationApiService),
    "employeeApiService": (()=>employeeApiService),
    "reliabilityApiService": (()=>reliabilityApiService),
    "setFactoryAuthTokenProvider": (()=>setFactoryAuthTokenProvider),
    "taskApiService": (()=>taskApiService),
    "vehicleApiService": (()=>vehicleApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/reliabilityApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
// Import unified auth token provider
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
/**
 * Get the current auth token from the unified provider
 * Uses the single source of truth for authentication tokens
 */ function getAuthToken() {
    const provider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUnifiedAuthTokenProvider"])();
    if (!provider) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('⚠️ Factory: Unified Auth Token Provider not initialized');
        }
        return null;
    }
    try {
        return provider();
    } catch (error) {
        console.error('❌ Factory: Error getting auth token from unified provider:', error);
        return null;
    }
}
function setFactoryAuthTokenProvider(provider) {
    console.warn('⚠️ setFactoryAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider from @/lib/api instead.');
// This function is now a no-op since we use the unified provider
// The warning guides developers to use the correct function
}
class ApiServiceFactory {
    apiClient;
    delegationService;
    employeeService;
    reliabilityService;
    taskService;
    vehicleService;
    /**
   * Creates an instance of ApiServiceFactory.
   * @param config - Configuration for the API services.
   */ constructor(config){
        this.apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"]({
            ...config,
            getAuthToken
        });
    }
    /**
   * Gets the underlying ApiClient instance.
   * @returns The ApiClient instance.
   */ getApiClient() {
        return this.apiClient;
    }
    /**
   * Gets the Delegation API service instance.
   * @returns The DelegationApiService instance.
   */ getDelegationService() {
        if (!this.delegationService) {
            this.delegationService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationApiService"](this.apiClient);
        }
        return this.delegationService;
    }
    /**
   * Gets the Employee API service instance.
   * @returns The EmployeeApiService instance.
   */ getEmployeeService() {
        if (!this.employeeService) {
            this.employeeService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeApiService"](this.apiClient);
        }
        return this.employeeService;
    }
    /**
   * Gets the Reliability API service instance.
   * @returns The ReliabilityApiService instance.
   */ getReliabilityService() {
        if (!this.reliabilityService) {
            this.reliabilityService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$reliabilityApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReliabilityApiService"](this.apiClient);
        }
        return this.reliabilityService;
    }
    /**
   * Gets the Task API service instance.
   * @returns The TaskApiService instance.
   */ getTaskService() {
        if (!this.taskService) {
            this.taskService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskApiService"](this.apiClient);
        }
        return this.taskService;
    }
    /**
   * Gets the Vehicle API service instance.
   * @returns The VehicleApiService instance.
   */ getVehicleService() {
        if (!this.vehicleService) {
            this.vehicleService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleApiService"](this.apiClient);
        }
        return this.vehicleService;
    }
}
// Create a default factory instance for the application with environment-aware configuration
const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
const defaultConfig = {
    baseURL: envConfig.apiBaseUrl,
    headers: {
        'Content-Type': 'application/json'
    },
    retryAttempts: 3,
    timeout: 10_000
};
const apiServiceFactory = new ApiServiceFactory(defaultConfig);
const vehicleApiService = apiServiceFactory.getVehicleService();
const delegationApiService = apiServiceFactory.getDelegationService();
const taskApiService = apiServiceFactory.getTaskService();
const employeeApiService = apiServiceFactory.getEmployeeService();
const reliabilityApiService = apiServiceFactory.getReliabilityService();
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file API Service Factory - Backward Compatibility Export
 * @module api/services/apiServiceFactory
 * 
 * This file provides backward compatibility for imports that expect
 * apiServiceFactory.ts instead of factory.ts
 */ // Re-export everything from the factory module
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
;
;
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setFactoryAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setFactoryAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=src_lib_68654243._.js.map