{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/transformers/delegationTransformer.ts"], "sourcesContent": ["/**\r\n * @file Data transformer for Delegation domain models.\r\n * @module transformers/delegationTransformer\r\n */\r\n\r\n// API types from the new central location\r\nimport type {\r\n  CreateDelegateRequest,\r\n  CreateDelegationRequest,\r\n  CreateDriverRequest,\r\n  CreateEscortRequest,\r\n  CreateFlightDetailsRequest,\r\n  CreateVehicleAssignmentRequest,\r\n  DelegationApiResponse,\r\n  EmployeeApiResponse,\r\n  FlightDetailsApiResponse,\r\n  UpdateDelegationRequest,\r\n  VehicleApiResponse,\r\n} from '../types/apiContracts';\r\nimport type {\r\n  CreateDelegationData,\r\n  Delegation,\r\n  DelegationStatusPrisma,\r\n  DriverAvailabilityPrisma,\r\n  Employee,\r\n  EmployeeRolePrisma,\r\n  EmployeeStatusPrisma,\r\n  FlightDetails,\r\n  Vehicle,\r\n} from '../types/domain';\r\nimport { undefinedToNull } from '../utils/typeHelpers';\r\n\r\n// These helpers now produce structures compatible with CreateDelegationRequest sub-properties\r\nconst DelegationAssignmentTransformers = {\r\n  // fromApi methods are less critical now as DelegationApiResponse is domain Delegation\r\n  // but if API responses for these are ever separate and different, they'd be useful.\r\n  // For now, their usage in DelegationTransformer.fromApi might be simplified.\r\n\r\n  toDelegateApiStructure(domainData: {\r\n    name: string;\r\n    notes?: string | undefined;\r\n    title: string;\r\n  }): CreateDelegateRequest {\r\n    // The structure of the domain data is identical to CreateDelegateRequest\r\n    return {\r\n      name: domainData.name,\r\n      notes: domainData.notes ?? null,\r\n      title: domainData.title,\r\n    };\r\n  },\r\n\r\n  toDriverApiStructure(domainData: {\r\n    employeeId: number;\r\n    notes?: string;\r\n  }): CreateDriverRequest {\r\n    // The structure of the domain data is identical to CreateDriverRequest\r\n    return {\r\n      employeeId: domainData.employeeId,\r\n      notes: domainData.notes ?? null,\r\n    };\r\n  },\r\n\r\n  toEscortApiStructure(domainData: {\r\n    employeeId: number;\r\n    notes?: string;\r\n  }): CreateEscortRequest {\r\n    // The structure of the domain data is identical to CreateEscortRequest\r\n    return {\r\n      employeeId: domainData.employeeId,\r\n      notes: domainData.notes ?? null,\r\n    };\r\n  },\r\n\r\n  toVehicleAssignmentApiStructure(domainData: {\r\n    assignedDate: string;\r\n    notes?: null | string;\r\n    returnDate?: null | string;\r\n    vehicleId: number;\r\n  }): CreateVehicleAssignmentRequest {\r\n    // The structure of the domain data is identical to CreateVehicleAssignmentRequest\r\n    return domainData;\r\n  },\r\n};\r\n\r\n// Helper transformers for converting API response types to domain types\r\nconst EmployeeTransformer = {\r\n  fromApi(apiData: EmployeeApiResponse): Employee {\r\n    return {\r\n      availability: apiData.availability as DriverAvailabilityPrisma | null, // Cast string to enum\r\n      contactEmail: apiData.contactEmail ?? null,\r\n      contactInfo: apiData.contactInfo,\r\n      contactMobile: apiData.contactMobile ?? null,\r\n      contactPhone: apiData.contactPhone ?? null,\r\n      createdAt: apiData.createdAt,\r\n      currentLocation: apiData.currentLocation ?? null,\r\n      department: apiData.department ?? null,\r\n      employeeId: apiData.employeeId,\r\n      fullName: apiData.fullName ?? null,\r\n      generalAssignments: apiData.generalAssignments,\r\n      hireDate: apiData.hireDate ?? null,\r\n      id: apiData.id,\r\n      name: apiData.name,\r\n      notes: apiData.notes ?? null,\r\n      position: apiData.position ?? null,\r\n      profileImageUrl: apiData.profileImageUrl ?? null,\r\n      role: apiData.role as EmployeeRolePrisma, // Cast string to enum\r\n      shiftSchedule: apiData.shiftSchedule ?? null,\r\n      skills: apiData.skills,\r\n      status: apiData.status as EmployeeStatusPrisma | null, // Cast string to enum\r\n      updatedAt: apiData.updatedAt,\r\n      workingHours: apiData.workingHours ?? null,\r\n    };\r\n  },\r\n};\r\n\r\nconst VehicleTransformer = {\r\n  fromApi(apiData: VehicleApiResponse): Vehicle {\r\n    return {\r\n      color: apiData.color,\r\n      createdAt: apiData.createdAt,\r\n      id: apiData.id,\r\n      imageUrl: apiData.imageUrl,\r\n      initialOdometer: apiData.initialOdometer,\r\n      licensePlate: apiData.licensePlate,\r\n      make: apiData.make,\r\n      model: apiData.model,\r\n      ownerContact: apiData.ownerContact,\r\n      ownerName: apiData.ownerName,\r\n      serviceHistory: [], // Not included in API response for delegation context\r\n      updatedAt: apiData.updatedAt,\r\n      vin: apiData.vin,\r\n      year: apiData.year,\r\n    };\r\n  },\r\n};\r\n\r\n/**\r\n * Transforms delegation data between API response formats and frontend domain models.\r\n */\r\nexport const DelegationTransformer = {\r\n  fromApi(apiData: DelegationApiResponse): Delegation {\r\n    return {\r\n      // Map nested relations using their respective transformers or direct mapping if structure matches\r\n      arrivalFlight: apiData.flightArrivalDetails\r\n        ? FlightDetailsTransformer.fromApi(apiData.flightArrivalDetails)\r\n        : null,\r\n      createdAt: apiData.createdAt, // Direct mapping\r\n      delegates: apiData.delegates || [], // Direct mapping, handle undefined\r\n      departureFlight: apiData.flightDepartureDetails\r\n        ? FlightDetailsTransformer.fromApi(apiData.flightDepartureDetails)\r\n        : null,\r\n      // Transform drivers: API returns Employee objects directly, but domain expects nested structure\r\n      drivers:\r\n        apiData.drivers?.map(driverEmployee => ({\r\n          createdAt: apiData.createdAt, // Use delegation timestamps as fallback\r\n          createdBy: null,\r\n          delegationId: apiData.id,\r\n          employee: EmployeeTransformer.fromApi(driverEmployee), // Transform and nest the employee data\r\n          employeeId: driverEmployee.id, // Keep as number\r\n          id: `driver-${apiData.id}-${driverEmployee.id}`, // Generate ID for join table\r\n          updatedAt: apiData.updatedAt,\r\n        })) || [],\r\n      durationFrom: apiData.durationFrom, // Direct mapping\r\n      durationTo: apiData.durationTo, // Direct mapping\r\n      // Transform escorts: API returns Employee objects directly, but domain expects nested structure\r\n      escorts:\r\n        apiData.escorts?.map(escortEmployee => ({\r\n          createdAt: apiData.createdAt, // Use delegation timestamps as fallback\r\n          createdBy: null,\r\n          delegationId: apiData.id,\r\n          employee: EmployeeTransformer.fromApi(escortEmployee), // Transform and nest the employee data\r\n          employeeId: escortEmployee.id, // Keep as number\r\n          id: `escort-${apiData.id}-${escortEmployee.id}`, // Generate ID for join table\r\n          updatedAt: apiData.updatedAt,\r\n        })) || [],\r\n      eventName: apiData.eventName, // Direct mapping\r\n      id: apiData.id, // ID is now string in API response\r\n      imageUrl: apiData.imageUrl ?? null, // Direct mapping, handle null/undefined\r\n      invitationFrom: apiData.invitationFrom ?? null, // Direct mapping, handle null/undefined\r\n\r\n      invitationTo: apiData.invitationTo ?? null, // Direct mapping, handle null/undefined\r\n      location: apiData.location, // Direct mapping\r\n      notes: apiData.notes ?? null, // Direct mapping, handle null/undefined\r\n\r\n      status: apiData.status as DelegationStatusPrisma, // Direct mapping with type assertion\r\n\r\n      statusHistory: apiData.statusHistory || [], // Direct mapping, handle undefined\r\n\r\n      updatedAt: apiData.updatedAt, // Direct mapping\r\n\r\n      // Transform vehicles: API returns Vehicle objects directly, but domain expects nested structure\r\n      vehicles:\r\n        apiData.vehicles?.map(vehicleData => ({\r\n          createdAt: apiData.createdAt, // Use delegation timestamps as fallback\r\n          createdBy: null,\r\n          delegationId: apiData.id,\r\n          id: `vehicle-${apiData.id}-${vehicleData.id}`, // Generate ID for join table\r\n          updatedAt: apiData.updatedAt,\r\n          vehicle: VehicleTransformer.fromApi(vehicleData), // Transform and nest the vehicle data\r\n          vehicleId: vehicleData.id,\r\n        })) || [],\r\n    };\r\n  },\r\n\r\n  toCreateRequest(domainData: CreateDelegationData): CreateDelegationRequest {\r\n    return {\r\n      // ✅ FIXED: Use correct field names that match backend schema\r\n      delegates:\r\n        domainData.delegates?.map((delegate: any) =>\r\n          DelegationAssignmentTransformers.toDelegateApiStructure(delegate)\r\n        ) ?? [],\r\n      driverEmployeeIds:\r\n        domainData.drivers?.map(driver => driver.employeeId) ?? [], // ✅ Extract IDs from nested objects\r\n      durationFrom: domainData.durationFrom, // ✅ Direct mapping (not startDate)\r\n      durationTo: domainData.durationTo, // ✅ Direct mapping (not endDate)\r\n      escortEmployeeIds:\r\n        domainData.escorts?.map(escort => escort.employeeId) ?? [], // ✅ Extract IDs from nested objects\r\n      eventName: domainData.eventName, // ✅ Direct mapping (not title)\r\n      flightArrivalDetails: domainData.flightArrivalDetails\r\n        ? FlightDetailsTransformer.toApiStructureForCreate(\r\n            domainData.flightArrivalDetails\r\n          )\r\n        : undefined,\r\n      flightDepartureDetails: domainData.flightDepartureDetails\r\n        ? FlightDetailsTransformer.toApiStructureForCreate(\r\n            domainData.flightDepartureDetails\r\n          )\r\n        : undefined,\r\n      imageUrl: domainData.imageUrl ?? null,\r\n      invitationFrom: domainData.invitationFrom ?? null,\r\n      invitationTo: domainData.invitationTo ?? null,\r\n      location: domainData.location,\r\n      notes: domainData.notes ?? null,\r\n      status: domainData.status as string,\r\n      vehicleIds: domainData.vehicles?.map(vehicle => vehicle.vehicleId) ?? [], // ✅ Extract IDs from nested objects\r\n    };\r\n  },\r\n\r\n  toUpdateRequest(\r\n    domainData: Partial<CreateDelegationData>\r\n  ): UpdateDelegationRequest {\r\n    const request: UpdateDelegationRequest = {};\r\n\r\n    // ✅ PRODUCTION FIX: Use correct field names that match backend expectations\r\n    if (domainData.eventName !== undefined)\r\n      request.eventName = domainData.eventName; // ✅ Direct mapping\r\n    if (domainData.location !== undefined)\r\n      request.location = domainData.location;\r\n    if (domainData.durationFrom !== undefined)\r\n      request.durationFrom = domainData.durationFrom; // ✅ Direct mapping\r\n    if (domainData.durationTo !== undefined)\r\n      request.durationTo = domainData.durationTo; // ✅ Direct mapping\r\n    if (domainData.status !== undefined)\r\n      request.status = domainData.status as string; // Type assertion\r\n    if (domainData.notes !== undefined) request.notes = domainData.notes; // ✅ Direct mapping\r\n    if (domainData.imageUrl !== undefined)\r\n      request.imageUrl = domainData.imageUrl;\r\n    if (domainData.invitationFrom !== undefined)\r\n      request.invitationFrom = domainData.invitationFrom;\r\n    if (domainData.invitationTo !== undefined)\r\n      request.invitationTo = domainData.invitationTo;\r\n\r\n    if (domainData.flightArrivalDetails !== undefined) {\r\n      request.flightArrivalDetails =\r\n        domainData.flightArrivalDetails ?? undefined;\r\n    }\r\n    if (domainData.flightDepartureDetails !== undefined) {\r\n      request.flightDepartureDetails =\r\n        domainData.flightDepartureDetails ?? undefined;\r\n    }\r\n\r\n    // ✅ FIX: Include assignment fields in update request\r\n    if (domainData.escorts !== undefined) {\r\n      request.escortEmployeeIds = domainData.escorts.map(e => e.employeeId);\r\n    }\r\n    if (domainData.drivers !== undefined) {\r\n      request.driverEmployeeIds = domainData.drivers.map(d => d.employeeId);\r\n    }\r\n    if (domainData.vehicles !== undefined) {\r\n      request.vehicleIds = domainData.vehicles.map(v => v.vehicleId);\r\n    }\r\n\r\n    return request;\r\n  },\r\n};\r\n\r\nexport const FlightDetailsTransformer = {\r\n  fromApi(apiData: FlightDetailsApiResponse): FlightDetails | null {\r\n    if (!apiData) return null;\r\n    return {\r\n      airport: apiData.airport,\r\n      dateTime: apiData.dateTime,\r\n      flightNumber: apiData.flightNumber,\r\n      id: apiData.id,\r\n      notes: apiData.notes || null,\r\n      terminal: apiData.terminal || null,\r\n    };\r\n  },\r\n\r\n  // This is for creating flight details as part of a new delegation\r\n  toApiStructureForCreate(\r\n    domainData: Omit<FlightDetails, 'id'>\r\n  ): CreateFlightDetailsRequest {\r\n    // The structure of Omit<FlightDetails, 'id'> is identical to CreateFlightDetailsRequest\r\n    return domainData;\r\n  },\r\n\r\n  toCreateRequest(domainData: FlightDetails): CreateFlightDetailsRequest {\r\n    // This is for managing details on an existing delegation, not used for initial creation\r\n    return {\r\n      airport: domainData.airport,\r\n      dateTime: domainData.dateTime,\r\n      flightNumber: domainData.flightNumber,\r\n      notes: domainData.notes ?? null,\r\n      terminal: domainData.terminal ?? null,\r\n    };\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0CAA0C;;;;;AA2B1C,8FAA8F;AAC9F,MAAM,mCAAmC;IACvC,sFAAsF;IACtF,oFAAoF;IACpF,6EAA6E;IAE7E,wBAAuB,UAItB;QACC,yEAAyE;QACzE,OAAO;YACL,MAAM,WAAW,IAAI;YACrB,OAAO,WAAW,KAAK,IAAI;YAC3B,OAAO,WAAW,KAAK;QACzB;IACF;IAEA,sBAAqB,UAGpB;QACC,uEAAuE;QACvE,OAAO;YACL,YAAY,WAAW,UAAU;YACjC,OAAO,WAAW,KAAK,IAAI;QAC7B;IACF;IAEA,sBAAqB,UAGpB;QACC,uEAAuE;QACvE,OAAO;YACL,YAAY,WAAW,UAAU;YACjC,OAAO,WAAW,KAAK,IAAI;QAC7B;IACF;IAEA,iCAAgC,UAK/B;QACC,kFAAkF;QAClF,OAAO;IACT;AACF;AAEA,wEAAwE;AACxE,MAAM,sBAAsB;IAC1B,SAAQ,OAA4B;QAClC,OAAO;YACL,cAAc,QAAQ,YAAY;YAClC,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,WAAW;YAChC,eAAe,QAAQ,aAAa,IAAI;YACxC,cAAc,QAAQ,YAAY,IAAI;YACtC,WAAW,QAAQ,SAAS;YAC5B,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,YAAY,QAAQ,UAAU,IAAI;YAClC,YAAY,QAAQ,UAAU;YAC9B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,oBAAoB,QAAQ,kBAAkB;YAC9C,UAAU,QAAQ,QAAQ,IAAI;YAC9B,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU,QAAQ,QAAQ,IAAI;YAC9B,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,MAAM,QAAQ,IAAI;YAClB,eAAe,QAAQ,aAAa,IAAI;YACxC,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,WAAW,QAAQ,SAAS;YAC5B,cAAc,QAAQ,YAAY,IAAI;QACxC;IACF;AACF;AAEA,MAAM,qBAAqB;IACzB,SAAQ,OAA2B;QACjC,OAAO;YACL,OAAO,QAAQ,KAAK;YACpB,WAAW,QAAQ,SAAS;YAC5B,IAAI,QAAQ,EAAE;YACd,UAAU,QAAQ,QAAQ;YAC1B,iBAAiB,QAAQ,eAAe;YACxC,cAAc,QAAQ,YAAY;YAClC,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK;YACpB,cAAc,QAAQ,YAAY;YAClC,WAAW,QAAQ,SAAS;YAC5B,gBAAgB,EAAE;YAClB,WAAW,QAAQ,SAAS;YAC5B,KAAK,QAAQ,GAAG;YAChB,MAAM,QAAQ,IAAI;QACpB;IACF;AACF;AAKO,MAAM,wBAAwB;IACnC,SAAQ,OAA8B;QACpC,OAAO;YACL,kGAAkG;YAClG,eAAe,QAAQ,oBAAoB,GACvC,yBAAyB,OAAO,CAAC,QAAQ,oBAAoB,IAC7D;YACJ,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS,IAAI,EAAE;YAClC,iBAAiB,QAAQ,sBAAsB,GAC3C,yBAAyB,OAAO,CAAC,QAAQ,sBAAsB,IAC/D;YACJ,gGAAgG;YAChG,SACE,QAAQ,OAAO,EAAE,IAAI,CAAA,iBAAkB,CAAC;oBACtC,WAAW,QAAQ,SAAS;oBAC5B,WAAW;oBACX,cAAc,QAAQ,EAAE;oBACxB,UAAU,oBAAoB,OAAO,CAAC;oBACtC,YAAY,eAAe,EAAE;oBAC7B,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,eAAe,EAAE,EAAE;oBAC/C,WAAW,QAAQ,SAAS;gBAC9B,CAAC,MAAM,EAAE;YACX,cAAc,QAAQ,YAAY;YAClC,YAAY,QAAQ,UAAU;YAC9B,gGAAgG;YAChG,SACE,QAAQ,OAAO,EAAE,IAAI,CAAA,iBAAkB,CAAC;oBACtC,WAAW,QAAQ,SAAS;oBAC5B,WAAW;oBACX,cAAc,QAAQ,EAAE;oBACxB,UAAU,oBAAoB,OAAO,CAAC;oBACtC,YAAY,eAAe,EAAE;oBAC7B,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,eAAe,EAAE,EAAE;oBAC/C,WAAW,QAAQ,SAAS;gBAC9B,CAAC,MAAM,EAAE;YACX,WAAW,QAAQ,SAAS;YAC5B,IAAI,QAAQ,EAAE;YACd,UAAU,QAAQ,QAAQ,IAAI;YAC9B,gBAAgB,QAAQ,cAAc,IAAI;YAE1C,cAAc,QAAQ,YAAY,IAAI;YACtC,UAAU,QAAQ,QAAQ;YAC1B,OAAO,QAAQ,KAAK,IAAI;YAExB,QAAQ,QAAQ,MAAM;YAEtB,eAAe,QAAQ,aAAa,IAAI,EAAE;YAE1C,WAAW,QAAQ,SAAS;YAE5B,gGAAgG;YAChG,UACE,QAAQ,QAAQ,EAAE,IAAI,CAAA,cAAe,CAAC;oBACpC,WAAW,QAAQ,SAAS;oBAC5B,WAAW;oBACX,cAAc,QAAQ,EAAE;oBACxB,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE;oBAC7C,WAAW,QAAQ,SAAS;oBAC5B,SAAS,mBAAmB,OAAO,CAAC;oBACpC,WAAW,YAAY,EAAE;gBAC3B,CAAC,MAAM,EAAE;QACb;IACF;IAEA,iBAAgB,UAAgC;QAC9C,OAAO;YACL,6DAA6D;YAC7D,WACE,WAAW,SAAS,EAAE,IAAI,CAAC,WACzB,iCAAiC,sBAAsB,CAAC,cACrD,EAAE;YACT,mBACE,WAAW,OAAO,EAAE,IAAI,CAAA,SAAU,OAAO,UAAU,KAAK,EAAE;YAC5D,cAAc,WAAW,YAAY;YACrC,YAAY,WAAW,UAAU;YACjC,mBACE,WAAW,OAAO,EAAE,IAAI,CAAA,SAAU,OAAO,UAAU,KAAK,EAAE;YAC5D,WAAW,WAAW,SAAS;YAC/B,sBAAsB,WAAW,oBAAoB,GACjD,yBAAyB,uBAAuB,CAC9C,WAAW,oBAAoB,IAEjC;YACJ,wBAAwB,WAAW,sBAAsB,GACrD,yBAAyB,uBAAuB,CAC9C,WAAW,sBAAsB,IAEnC;YACJ,UAAU,WAAW,QAAQ,IAAI;YACjC,gBAAgB,WAAW,cAAc,IAAI;YAC7C,cAAc,WAAW,YAAY,IAAI;YACzC,UAAU,WAAW,QAAQ;YAC7B,OAAO,WAAW,KAAK,IAAI;YAC3B,QAAQ,WAAW,MAAM;YACzB,YAAY,WAAW,QAAQ,EAAE,IAAI,CAAA,UAAW,QAAQ,SAAS,KAAK,EAAE;QAC1E;IACF;IAEA,iBACE,UAAyC;QAEzC,MAAM,UAAmC,CAAC;QAE1C,4EAA4E;QAC5E,IAAI,WAAW,SAAS,KAAK,WAC3B,QAAQ,SAAS,GAAG,WAAW,SAAS,EAAE,mBAAmB;QAC/D,IAAI,WAAW,QAAQ,KAAK,WAC1B,QAAQ,QAAQ,GAAG,WAAW,QAAQ;QACxC,IAAI,WAAW,YAAY,KAAK,WAC9B,QAAQ,YAAY,GAAG,WAAW,YAAY,EAAE,mBAAmB;QACrE,IAAI,WAAW,UAAU,KAAK,WAC5B,QAAQ,UAAU,GAAG,WAAW,UAAU,EAAE,mBAAmB;QACjE,IAAI,WAAW,MAAM,KAAK,WACxB,QAAQ,MAAM,GAAG,WAAW,MAAM,EAAY,iBAAiB;QACjE,IAAI,WAAW,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG,WAAW,KAAK,EAAE,mBAAmB;QACzF,IAAI,WAAW,QAAQ,KAAK,WAC1B,QAAQ,QAAQ,GAAG,WAAW,QAAQ;QACxC,IAAI,WAAW,cAAc,KAAK,WAChC,QAAQ,cAAc,GAAG,WAAW,cAAc;QACpD,IAAI,WAAW,YAAY,KAAK,WAC9B,QAAQ,YAAY,GAAG,WAAW,YAAY;QAEhD,IAAI,WAAW,oBAAoB,KAAK,WAAW;YACjD,QAAQ,oBAAoB,GAC1B,WAAW,oBAAoB,IAAI;QACvC;QACA,IAAI,WAAW,sBAAsB,KAAK,WAAW;YACnD,QAAQ,sBAAsB,GAC5B,WAAW,sBAAsB,IAAI;QACzC;QAEA,qDAAqD;QACrD,IAAI,WAAW,OAAO,KAAK,WAAW;YACpC,QAAQ,iBAAiB,GAAG,WAAW,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;QACtE;QACA,IAAI,WAAW,OAAO,KAAK,WAAW;YACpC,QAAQ,iBAAiB,GAAG,WAAW,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;QACtE;QACA,IAAI,WAAW,QAAQ,KAAK,WAAW;YACrC,QAAQ,UAAU,GAAG,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS;QAC/D;QAEA,OAAO;IACT;AACF;AAEO,MAAM,2BAA2B;IACtC,SAAQ,OAAiC;QACvC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO;YACL,SAAS,QAAQ,OAAO;YACxB,UAAU,QAAQ,QAAQ;YAC1B,cAAc,QAAQ,YAAY;YAClC,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU,QAAQ,QAAQ,IAAI;QAChC;IACF;IAEA,kEAAkE;IAClE,yBACE,UAAqC;QAErC,wFAAwF;QACxF,OAAO;IACT;IAEA,iBAAgB,UAAyB;QACvC,wFAAwF;QACxF,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,UAAU,WAAW,QAAQ;YAC7B,cAAc,WAAW,YAAY;YACrC,OAAO,WAAW,KAAK,IAAI;YAC3B,UAAU,WAAW,QAAQ,IAAI;QACnC;IACF;AACF", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/core/baseApiService.ts"], "sourcesContent": ["/**\r\n * Enhanced BaseApiService - Production-Ready Service Foundation\r\n *\r\n * This service provides a standardized foundation for all API services with:\r\n * - Circuit breaker protection (from AdminService patterns)\r\n * - Request caching and deduplication\r\n * - Comprehensive error handling with retry logic\r\n * - Performance monitoring and metrics\r\n * - Data transformation and validation\r\n *\r\n * Built upon proven patterns from the consolidated AdminService.\r\n */\r\n\r\nimport type { PaginatedResponse } from '@/types';\r\n\r\nimport type { ApiClient } from './apiClient';\r\n\r\nimport { ApiError, ServiceError } from './errors';\r\n\r\n/**\r\n * Data transformer interface for consistent data transformation\r\n */\r\nexport interface DataTransformer<T> {\r\n  fromApi?: (data: any) => T;\r\n  toApi?: (data: any) => any;\r\n}\r\n\r\n// PaginatedResponse interface now imported from @/types for consistency\r\n\r\n/**\r\n * Service configuration interface\r\n */\r\nexport interface ServiceConfig {\r\n  cacheDuration?: number;\r\n  circuitBreakerThreshold?: number;\r\n  enableMetrics?: boolean;\r\n  retryAttempts?: number;\r\n}\r\n\r\n/**\r\n * Service metrics interface\r\n */\r\nexport interface ServiceMetrics {\r\n  averageResponseTime: number;\r\n  cacheHitRatio: number;\r\n  errorCount: number;\r\n  requestCount: number;\r\n}\r\n\r\n/**\r\n * Simple circuit breaker implementation\r\n */\r\nclass CircuitBreaker {\r\n  private failures = 0;\r\n  private lastFailureTime = 0;\r\n  private state: 'CLOSED' | 'HALF_OPEN' | 'OPEN' = 'CLOSED';\r\n\r\n  constructor(\r\n    private readonly name: string,\r\n    private readonly threshold = 5,\r\n    private readonly timeout = 60_000\r\n  ) {}\r\n\r\n  async execute<T>(operation: () => Promise<T>): Promise<T> {\r\n    if (this.state === 'OPEN') {\r\n      if (Date.now() - this.lastFailureTime > this.timeout) {\r\n        this.state = 'HALF_OPEN';\r\n      } else {\r\n        throw new ServiceError(\r\n          'Circuit breaker is OPEN',\r\n          'CIRCUIT_BREAKER_OPEN'\r\n        );\r\n      }\r\n    }\r\n\r\n    try {\r\n      const result = await operation();\r\n      this.onSuccess();\r\n      return result;\r\n    } catch (error) {\r\n      this.onFailure();\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  getState() {\r\n    return {\r\n      failures: this.failures,\r\n      lastFailureTime: this.lastFailureTime,\r\n      name: this.name,\r\n      state: this.state,\r\n    };\r\n  }\r\n\r\n  private onFailure() {\r\n    this.failures++;\r\n    this.lastFailureTime = Date.now();\r\n    if (this.failures >= this.threshold) {\r\n      this.state = 'OPEN';\r\n    }\r\n  }\r\n\r\n  private onSuccess() {\r\n    this.failures = 0;\r\n    this.state = 'CLOSED';\r\n  }\r\n}\r\n\r\n/**\r\n * Simple request cache implementation\r\n */\r\nclass RequestCache {\r\n  private readonly cache = new Map<string, { data: any; expiry: number }>();\r\n\r\n  clear(): void {\r\n    this.cache.clear();\r\n  }\r\n\r\n  get<T>(key: string): null | T {\r\n    const item = this.cache.get(key);\r\n    if (!item) return null;\r\n\r\n    if (Date.now() > item.expiry) {\r\n      this.cache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    return item.data as T;\r\n  }\r\n\r\n  getStats() {\r\n    return {\r\n      keys: [...this.cache.keys()],\r\n      size: this.cache.size,\r\n    };\r\n  }\r\n\r\n  invalidate(key: string): void {\r\n    this.cache.delete(key);\r\n  }\r\n\r\n  invalidatePattern(pattern: RegExp): void {\r\n    for (const key of this.cache.keys()) {\r\n      if (pattern.test(key)) {\r\n        this.cache.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  set<T>(key: string, data: T, duration = 300_000): void {\r\n    this.cache.set(key, {\r\n      data,\r\n      expiry: Date.now() + duration,\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Enhanced BaseApiService with production-grade patterns\r\n */\r\nexport abstract class BaseApiService<\r\n  T,\r\n  CreateT = Partial<T>,\r\n  UpdateT = Partial<T>,\r\n> {\r\n  protected cache: RequestCache;\r\n  // Service infrastructure (enhanced from AdminService patterns)\r\n  protected circuitBreaker: CircuitBreaker;\r\n\r\n  protected config: ServiceConfig;\r\n  protected abstract endpoint: string;\r\n  protected metrics: ServiceMetrics;\r\n  protected abstract transformer: DataTransformer<T>;\r\n\r\n  constructor(\r\n    protected apiClient: ApiClient,\r\n    config: ServiceConfig = {}\r\n  ) {\r\n    this.config = {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes default\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    };\r\n\r\n    // Initialize service infrastructure\r\n    this.circuitBreaker = new CircuitBreaker(\r\n      `${this.constructor.name}`,\r\n      this.config.circuitBreakerThreshold\r\n    );\r\n\r\n    this.cache = new RequestCache();\r\n\r\n    this.metrics = {\r\n      averageResponseTime: 0,\r\n      cacheHitRatio: 0,\r\n      errorCount: 0,\r\n      requestCount: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clear service cache\r\n   */\r\n  clearCache() {\r\n    this.cache.clear();\r\n  }\r\n\r\n  /**\r\n   * Create new entity\r\n   */\r\n  async create(data: CreateT): Promise<T> {\r\n    return this.executeWithInfrastructure(\r\n      null, // No caching for create operations\r\n      async () => {\r\n        const transformedData = this.transformer.toApi\r\n          ? this.transformer.toApi(data)\r\n          : data;\r\n        const response = await this.apiClient.post<any>(\r\n          this.endpoint,\r\n          transformedData\r\n        );\r\n\r\n        // Invalidate related caches\r\n        this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n\r\n        return this.transformer.fromApi\r\n          ? this.transformer.fromApi(response)\r\n          : response;\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Delete entity\r\n   */\r\n  async delete(id: number | string): Promise<void> {\r\n    return this.executeWithInfrastructure(\r\n      null, // No caching for delete operations\r\n      async () => {\r\n        await this.apiClient.delete(`${this.endpoint}/${id}`);\r\n\r\n        // Invalidate related caches\r\n        this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n        this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get all entities with pagination and filtering\r\n   */\r\n  async getAll(filters?: Record<string, any>): Promise<PaginatedResponse<T>> {\r\n    const cacheKey = `${this.endpoint}:getAll:${JSON.stringify(filters || {})}`;\r\n\r\n    return this.executeWithInfrastructure(cacheKey, async () => {\r\n      const params = new URLSearchParams();\r\n      if (filters) {\r\n        for (const [key, value] of Object.entries(filters)) {\r\n          if (value !== undefined && value !== null) {\r\n            params.append(key, String(value));\r\n          }\r\n        }\r\n      }\r\n\r\n      const queryString = params.toString();\r\n      const url = queryString\r\n        ? `${this.endpoint}?${queryString}`\r\n        : this.endpoint;\r\n\r\n      const response = await this.apiClient.get<any>(url);\r\n\r\n      // Handle different response formats from backend\r\n      let responseData: any[];\r\n      let paginationInfo: any = {};\r\n\r\n      // Handle wrapped response format from responseWrapper middleware\r\n      if (response && response.status === 'success' && response.data) {\r\n        // Backend returns wrapped response: {status: 'success', data: {...}, pagination?: {...}}\r\n        const wrappedData = response.data;\r\n\r\n        if (Array.isArray(wrappedData)) {\r\n          // Direct array in wrapped response\r\n          responseData = wrappedData;\r\n          // Check for pagination at the top level of wrapped response\r\n          if (response.pagination) {\r\n            paginationInfo = {\r\n              pagination: {\r\n                hasNext: response.pagination.hasNext ?? false,\r\n                hasPrevious: response.pagination.hasPrevious ?? false,\r\n                limit: response.pagination.limit,\r\n                page: response.pagination.page,\r\n                total: response.pagination.total,\r\n                totalPages:\r\n                  response.pagination.totalPages ??\r\n                  Math.ceil(\r\n                    response.pagination.total / response.pagination.limit\r\n                  ),\r\n              },\r\n            };\r\n          }\r\n        } else if (wrappedData && Array.isArray(wrappedData.data)) {\r\n          // Nested data structure: {status: 'success', data: {data: [], pagination: {}}}\r\n          responseData = wrappedData.data;\r\n\r\n          // Handle nested pagination object\r\n          if (wrappedData.pagination) {\r\n            paginationInfo = {\r\n              pagination: {\r\n                hasNext: wrappedData.pagination.hasNext ?? false,\r\n                hasPrevious: wrappedData.pagination.hasPrevious ?? false,\r\n                limit: wrappedData.pagination.limit,\r\n                page: wrappedData.pagination.page,\r\n                total: wrappedData.pagination.total,\r\n                totalPages:\r\n                  wrappedData.pagination.totalPages ??\r\n                  Math.ceil(\r\n                    wrappedData.pagination.total / wrappedData.pagination.limit\r\n                  ),\r\n              },\r\n            };\r\n          } else if (response.pagination) {\r\n            // Pagination at wrapper level\r\n            paginationInfo = {\r\n              pagination: {\r\n                hasNext: response.pagination.hasNext ?? false,\r\n                hasPrevious: response.pagination.hasPrevious ?? false,\r\n                limit: response.pagination.limit,\r\n                page: response.pagination.page,\r\n                total: response.pagination.total,\r\n                totalPages:\r\n                  response.pagination.totalPages ??\r\n                  Math.ceil(\r\n                    response.pagination.total / response.pagination.limit\r\n                  ),\r\n              },\r\n            };\r\n          }\r\n        } else {\r\n          // Single item wrapped response - convert to array for consistency\r\n          responseData = [wrappedData];\r\n        }\r\n      } else if (Array.isArray(response)) {\r\n        // Direct array response (common backend pattern)\r\n        responseData = response;\r\n      } else if (response && (response.error || response.status === 'error')) {\r\n        // Backend returned an error response\r\n        throw new Error(\r\n          response.message || response.error || 'API request failed'\r\n        );\r\n      } else if (response && typeof response === 'object') {\r\n        // Single object response - convert to array for consistency\r\n        responseData = [response];\r\n      } else {\r\n        // Unexpected response format\r\n        throw new Error(\r\n          `Invalid response format from API: ${JSON.stringify(response)}`\r\n        );\r\n      }\r\n\r\n      // Transform data using the service's transformer\r\n      const transformedData = responseData.map(item =>\r\n        this.transformer.fromApi ? this.transformer.fromApi(item) : item\r\n      );\r\n\r\n      return {\r\n        data: transformedData,\r\n        ...paginationInfo,\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get entity by ID\r\n   */\r\n  async getById(id: number | string): Promise<T> {\r\n    const cacheKey = `${this.endpoint}:getById:${id}`;\r\n\r\n    return this.executeWithInfrastructure(cacheKey, async () => {\r\n      const response = await this.apiClient.get<any>(`${this.endpoint}/${id}`);\r\n      return this.transformer.fromApi\r\n        ? this.transformer.fromApi(response)\r\n        : response;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get service health status\r\n   */\r\n  getHealthStatus() {\r\n    return {\r\n      cacheStats: this.cache.getStats(),\r\n      circuitBreakerState: this.circuitBreaker.getState(),\r\n      endpoint: this.endpoint,\r\n      metrics: this.metrics,\r\n      service: this.constructor.name,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Reset service metrics\r\n   */\r\n  resetMetrics() {\r\n    this.metrics = {\r\n      averageResponseTime: 0,\r\n      cacheHitRatio: 0,\r\n      errorCount: 0,\r\n      requestCount: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update existing entity\r\n   */\r\n  async update(id: number | string, data: UpdateT): Promise<T> {\r\n    return this.executeWithInfrastructure(\r\n      null, // No caching for update operations\r\n      async () => {\r\n        const transformedData = this.transformer.toApi\r\n          ? this.transformer.toApi(data)\r\n          : data;\r\n        const response = await this.apiClient.put<any>(\r\n          `${this.endpoint}/${id}`,\r\n          transformedData\r\n        );\r\n\r\n        // Invalidate related caches\r\n        this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n        this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n        return this.transformer.fromApi\r\n          ? this.transformer.fromApi(response)\r\n          : response;\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Execute operation with full service infrastructure\r\n   * (circuit breaker, caching, error handling, metrics)\r\n   */\r\n  protected async executeWithInfrastructure<R>(\r\n    cacheKey: null | string,\r\n    operation: () => Promise<R>\r\n  ): Promise<R> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      this.metrics.requestCount++;\r\n\r\n      // Try cache first if cacheKey provided\r\n      if (cacheKey) {\r\n        const cached = this.cache.get(cacheKey);\r\n        if (cached) {\r\n          this.metrics.cacheHitRatio =\r\n            (this.metrics.cacheHitRatio * (this.metrics.requestCount - 1) + 1) /\r\n            this.metrics.requestCount;\r\n          return cached as R;\r\n        }\r\n      }\r\n\r\n      // Execute with circuit breaker and retry logic\r\n      const result = await this.circuitBreaker.execute(async () => {\r\n        return withRetry(operation, this.config.retryAttempts);\r\n      });\r\n\r\n      // Cache result if cacheKey provided\r\n      if (cacheKey && result) {\r\n        this.cache.set(cacheKey, result, this.config.cacheDuration);\r\n      }\r\n\r\n      // Update metrics\r\n      const responseTime = Date.now() - startTime;\r\n      this.metrics.averageResponseTime =\r\n        (this.metrics.averageResponseTime * (this.metrics.requestCount - 1) +\r\n          responseTime) /\r\n        this.metrics.requestCount;\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.metrics.errorCount++;\r\n\r\n      // Log the error for debugging with better error serialization\r\n      console.error(`Service error in ${this.constructor.name}:`, {\r\n        endpoint: this.endpoint,\r\n        errorDetails:\r\n          error instanceof Error\r\n            ? {\r\n                message: error.message,\r\n                name: error.name,\r\n                stack: error.stack,\r\n              }\r\n            : error,\r\n        errorMessage: error instanceof Error ? error.message : String(error),\r\n        errorType: error?.constructor?.name || typeof error,\r\n        timestamp: new Date().toISOString(),\r\n      });\r\n\r\n      // Transform error to ServiceError\r\n      if (error instanceof ServiceError) {\r\n        throw error;\r\n      }\r\n\r\n      // Handle specific API errors\r\n      if (error instanceof Error) {\r\n        // Check if it's a network connectivity issue\r\n        if (\r\n          error.message.includes('fetch') ||\r\n          error.message.includes('network')\r\n        ) {\r\n          throw new ServiceError(\r\n            'Network connection failed. Please check your internet connection and try again.',\r\n            'NETWORK_ERROR',\r\n            undefined,\r\n            { endpoint: this.endpoint, service: this.constructor.name }\r\n          );\r\n        }\r\n\r\n        // Check if it's a backend server error\r\n        if (\r\n          error.message.includes('500') ||\r\n          error.message.includes('Internal Server Error')\r\n        ) {\r\n          throw new ServiceError(\r\n            'Server error occurred. Please try again later.',\r\n            'SERVER_ERROR',\r\n            undefined,\r\n            { endpoint: this.endpoint, service: this.constructor.name }\r\n          );\r\n        }\r\n      }\r\n\r\n      throw new ServiceError(\r\n        error instanceof Error ? error.message : 'Unknown service error',\r\n        'SERVICE_ERROR',\r\n        undefined,\r\n        { endpoint: this.endpoint, service: this.constructor.name }\r\n      );\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Simple retry utility\r\n */\r\nasync function withRetry<T>(\r\n  operation: () => Promise<T>,\r\n  maxAttempts = 3,\r\n  delay = 1000\r\n): Promise<T> {\r\n  let lastError: Error;\r\n\r\n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\r\n    try {\r\n      return await operation();\r\n    } catch (error) {\r\n      lastError = error instanceof Error ? error : new Error('Unknown error');\r\n\r\n      if (attempt === maxAttempts) {\r\n        throw lastError;\r\n      }\r\n\r\n      // Wait before retry\r\n      await new Promise(resolve => setTimeout(resolve, delay * attempt));\r\n    }\r\n  }\r\n\r\n  throw lastError!;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAMD;AAAA;;AAgCA;;CAEC,GACD,MAAM;;;;IACI,SAAa;IACb,gBAAoB;IACpB,MAAkD;IAE1D,YACE,AAAiB,IAAY,EAC7B,AAAiB,YAAY,CAAC,EAC9B,AAAiB,UAAU,MAAM,CACjC;aAHiB,OAAA;aACA,YAAA;aACA,UAAA;aAPX,WAAW;aACX,kBAAkB;aAClB,QAAyC;IAM9C;IAEH,MAAM,QAAW,SAA2B,EAAc;QACxD,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ;YACzB,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,EAAE;gBACpD,IAAI,CAAC,KAAK,GAAG;YACf,OAAO;gBACL,MAAM,IAAI,mJAAA,CAAA,eAAY,CACpB,2BACA;YAEJ;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,IAAI,CAAC,SAAS;YACd,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,SAAS;YACd,MAAM;QACR;IACF;IAEA,WAAW;QACT,OAAO;YACL,UAAU,IAAI,CAAC,QAAQ;YACvB,iBAAiB,IAAI,CAAC,eAAe;YACrC,MAAM,IAAI,CAAC,IAAI;YACf,OAAO,IAAI,CAAC,KAAK;QACnB;IACF;IAEQ,YAAY;QAClB,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,CAAC,KAAK,GAAG;QACf;IACF;IAEQ,YAAY;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;IACf;AACF;AAEA;;CAEC,GACD,MAAM;IACa,QAAQ,IAAI,MAA6C;IAE1E,QAAc;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,MAAM,OAAO;QAElB,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO,KAAK,IAAI;IAClB;IAEA,WAAW;QACT,OAAO;YACL,MAAM;mBAAI,IAAI,CAAC,KAAK,CAAC,IAAI;aAAG;YAC5B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;QACvB;IACF;IAEA,WAAW,GAAW,EAAQ;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IACpB;IAEA,kBAAkB,OAAe,EAAQ;QACvC,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,QAAQ,IAAI,CAAC,MAAM;gBACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA,IAAO,GAAW,EAAE,IAAO,EAAE,WAAW,OAAO,EAAQ;QACrD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,QAAQ,KAAK,GAAG,KAAK;QACvB;IACF;AACF;AAKO,MAAe;;IAKV,MAAoB;IAC9B,+DAA+D;IACrD,eAA+B;IAE/B,OAAsB;IAEtB,QAAwB;IAGlC,YACE,AAAU,SAAoB,EAC9B,SAAwB,CAAC,CAAC,CAC1B;aAFU,YAAA;QAGV,IAAI,CAAC,MAAM,GAAG;YACZ,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;QAEA,oCAAoC;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,eACxB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB;QAGrC,IAAI,CAAC,KAAK,GAAG,IAAI;QAEjB,IAAI,CAAC,OAAO,GAAG;YACb,qBAAqB;YACrB,eAAe;YACf,YAAY;YACZ,cAAc;QAChB;IACF;IAEA;;GAEC,GACD,aAAa;QACX,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,MAAM,OAAO,IAAa,EAAc;QACtC,OAAO,IAAI,CAAC,yBAAyB,CACnC,MACA;YACE,MAAM,kBAAkB,IAAI,CAAC,WAAW,CAAC,KAAK,GAC1C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QACvB;YACJ,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,IAAI,CAAC,QAAQ,EACb;YAGF,4BAA4B;YAC5B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAE5D,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IAEJ;IAEA;;GAEC,GACD,MAAM,OAAO,EAAmB,EAAiB;QAC/C,OAAO,IAAI,CAAC,yBAAyB,CACnC,MACA;YACE,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAEpD,4BAA4B;YAC5B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;QACxD;IAEJ;IAEA;;GAEC,GACD,MAAM,OAAO,OAA6B,EAAiC;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,WAAW,CAAC,IAAI;QAE3E,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC9C,MAAM,SAAS,IAAI;YACnB,IAAI,SAAS;gBACX,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;oBAClD,IAAI,UAAU,aAAa,UAAU,MAAM;wBACzC,OAAO,MAAM,CAAC,KAAK,OAAO;oBAC5B;gBACF;YACF;YAEA,MAAM,cAAc,OAAO,QAAQ;YACnC,MAAM,MAAM,cACR,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,GACjC,IAAI,CAAC,QAAQ;YAEjB,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;YAE/C,iDAAiD;YACjD,IAAI;YACJ,IAAI,iBAAsB,CAAC;YAE3B,iEAAiE;YACjE,IAAI,YAAY,SAAS,MAAM,KAAK,aAAa,SAAS,IAAI,EAAE;gBAC9D,yFAAyF;gBACzF,MAAM,cAAc,SAAS,IAAI;gBAEjC,IAAI,MAAM,OAAO,CAAC,cAAc;oBAC9B,mCAAmC;oBACnC,eAAe;oBACf,4DAA4D;oBAC5D,IAAI,SAAS,UAAU,EAAE;wBACvB,iBAAiB;4BACf,YAAY;gCACV,SAAS,SAAS,UAAU,CAAC,OAAO,IAAI;gCACxC,aAAa,SAAS,UAAU,CAAC,WAAW,IAAI;gCAChD,OAAO,SAAS,UAAU,CAAC,KAAK;gCAChC,MAAM,SAAS,UAAU,CAAC,IAAI;gCAC9B,OAAO,SAAS,UAAU,CAAC,KAAK;gCAChC,YACE,SAAS,UAAU,CAAC,UAAU,IAC9B,KAAK,IAAI,CACP,SAAS,UAAU,CAAC,KAAK,GAAG,SAAS,UAAU,CAAC,KAAK;4BAE3D;wBACF;oBACF;gBACF,OAAO,IAAI,eAAe,MAAM,OAAO,CAAC,YAAY,IAAI,GAAG;oBACzD,+EAA+E;oBAC/E,eAAe,YAAY,IAAI;oBAE/B,kCAAkC;oBAClC,IAAI,YAAY,UAAU,EAAE;wBAC1B,iBAAiB;4BACf,YAAY;gCACV,SAAS,YAAY,UAAU,CAAC,OAAO,IAAI;gCAC3C,aAAa,YAAY,UAAU,CAAC,WAAW,IAAI;gCACnD,OAAO,YAAY,UAAU,CAAC,KAAK;gCACnC,MAAM,YAAY,UAAU,CAAC,IAAI;gCACjC,OAAO,YAAY,UAAU,CAAC,KAAK;gCACnC,YACE,YAAY,UAAU,CAAC,UAAU,IACjC,KAAK,IAAI,CACP,YAAY,UAAU,CAAC,KAAK,GAAG,YAAY,UAAU,CAAC,KAAK;4BAEjE;wBACF;oBACF,OAAO,IAAI,SAAS,UAAU,EAAE;wBAC9B,8BAA8B;wBAC9B,iBAAiB;4BACf,YAAY;gCACV,SAAS,SAAS,UAAU,CAAC,OAAO,IAAI;gCACxC,aAAa,SAAS,UAAU,CAAC,WAAW,IAAI;gCAChD,OAAO,SAAS,UAAU,CAAC,KAAK;gCAChC,MAAM,SAAS,UAAU,CAAC,IAAI;gCAC9B,OAAO,SAAS,UAAU,CAAC,KAAK;gCAChC,YACE,SAAS,UAAU,CAAC,UAAU,IAC9B,KAAK,IAAI,CACP,SAAS,UAAU,CAAC,KAAK,GAAG,SAAS,UAAU,CAAC,KAAK;4BAE3D;wBACF;oBACF;gBACF,OAAO;oBACL,kEAAkE;oBAClE,eAAe;wBAAC;qBAAY;gBAC9B;YACF,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;gBAClC,iDAAiD;gBACjD,eAAe;YACjB,OAAO,IAAI,YAAY,CAAC,SAAS,KAAK,IAAI,SAAS,MAAM,KAAK,OAAO,GAAG;gBACtE,qCAAqC;gBACrC,MAAM,IAAI,MACR,SAAS,OAAO,IAAI,SAAS,KAAK,IAAI;YAE1C,OAAO,IAAI,YAAY,OAAO,aAAa,UAAU;gBACnD,4DAA4D;gBAC5D,eAAe;oBAAC;iBAAS;YAC3B,OAAO;gBACL,6BAA6B;gBAC7B,MAAM,IAAI,MACR,CAAC,kCAAkC,EAAE,KAAK,SAAS,CAAC,WAAW;YAEnE;YAEA,iDAAiD;YACjD,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAA,OACvC,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ;YAG9D,OAAO;gBACL,MAAM;gBACN,GAAG,cAAc;YACnB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAmB,EAAc;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;QAEjD,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YACvE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IACF;IAEA;;GAEC,GACD,kBAAkB;QAChB,OAAO;YACL,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC/B,qBAAqB,IAAI,CAAC,cAAc,CAAC,QAAQ;YACjD,UAAU,IAAI,CAAC,QAAQ;YACvB,SAAS,IAAI,CAAC,OAAO;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI;QAChC;IACF;IAEA;;GAEC,GACD,eAAe;QACb,IAAI,CAAC,OAAO,GAAG;YACb,qBAAqB;YACrB,eAAe;YACf,YAAY;YACZ,cAAc;QAChB;IACF;IAEA;;GAEC,GACD,MAAM,OAAO,EAAmB,EAAE,IAAa,EAAc;QAC3D,OAAO,IAAI,CAAC,yBAAyB,CACnC,MACA;YACE,MAAM,kBAAkB,IAAI,CAAC,WAAW,CAAC,KAAK,GAC1C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QACvB;YACJ,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EACxB;YAGF,4BAA4B;YAC5B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IAEJ;IAEA;;;GAGC,GACD,MAAgB,0BACd,QAAuB,EACvB,SAA2B,EACf;QACZ,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,YAAY;YAEzB,uCAAuC;YACvC,IAAI,UAAU;gBACZ,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBAC9B,IAAI,QAAQ;oBACV,IAAI,CAAC,OAAO,CAAC,aAAa,GACxB,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IACjE,IAAI,CAAC,OAAO,CAAC,YAAY;oBAC3B,OAAO;gBACT;YACF;YAEA,+CAA+C;YAC/C,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC/C,OAAO,UAAU,WAAW,IAAI,CAAC,MAAM,CAAC,aAAa;YACvD;YAEA,oCAAoC;YACpC,IAAI,YAAY,QAAQ;gBACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa;YAC5D;YAEA,iBAAiB;YACjB,MAAM,eAAe,KAAK,GAAG,KAAK;YAClC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAC9B,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,IAChE,YAAY,IACd,IAAI,CAAC,OAAO,CAAC,YAAY;YAE3B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,OAAO,CAAC,UAAU;YAEvB,8DAA8D;YAC9D,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;gBAC1D,UAAU,IAAI,CAAC,QAAQ;gBACvB,cACE,iBAAiB,QACb;oBACE,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,OAAO,MAAM,KAAK;gBACpB,IACA;gBACN,cAAc,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAC9D,WAAW,OAAO,aAAa,QAAQ,OAAO;gBAC9C,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kCAAkC;YAClC,IAAI,iBAAiB,mJAAA,CAAA,eAAY,EAAE;gBACjC,MAAM;YACR;YAEA,6BAA6B;YAC7B,IAAI,iBAAiB,OAAO;gBAC1B,6CAA6C;gBAC7C,IACE,MAAM,OAAO,CAAC,QAAQ,CAAC,YACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,YACvB;oBACA,MAAM,IAAI,mJAAA,CAAA,eAAY,CACpB,mFACA,iBACA,WACA;wBAAE,UAAU,IAAI,CAAC,QAAQ;wBAAE,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI;oBAAC;gBAE9D;gBAEA,uCAAuC;gBACvC,IACE,MAAM,OAAO,CAAC,QAAQ,CAAC,UACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,0BACvB;oBACA,MAAM,IAAI,mJAAA,CAAA,eAAY,CACpB,kDACA,gBACA,WACA;wBAAE,UAAU,IAAI,CAAC,QAAQ;wBAAE,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI;oBAAC;gBAE9D;YACF;YAEA,MAAM,IAAI,mJAAA,CAAA,eAAY,CACpB,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC,iBACA,WACA;gBAAE,UAAU,IAAI,CAAC,QAAQ;gBAAE,SAAS,IAAI,CAAC,WAAW,CAAC,IAAI;YAAC;QAE9D;IACF;AACF;AAEA;;CAEC,GACD,eAAe,UACb,SAA2B,EAC3B,cAAc,CAAC,EACf,QAAQ,IAAI;IAEZ,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,aAAa,UAAW;QACvD,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;YAEvD,IAAI,YAAY,aAAa;gBAC3B,MAAM;YACR;YAEA,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ;QAC3D;IACF;IAEA,MAAM;AACR", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/domain/delegationApi.ts"], "sourcesContent": ["import type {\r\n  CreateDelegationRequest,\r\n  CreateFlightDetailsRequest,\r\n  DelegationApiResponse,\r\n  UpdateDelegationRequest,\r\n} from '../../../types/apiContracts';\r\nimport type {\r\n  Delegation,\r\n  DelegationStatusPrisma,\r\n  FlightDetails,\r\n} from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport { DelegationTransformer } from '../../../transformers/delegationTransformer';\r\n\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst DelegationApiTransformer: DataTransformer<Delegation> = {\r\n  fromApi: (data: DelegationApiResponse) => DelegationTransformer.fromApi(data),\r\n  toApi: (data: CreateDelegationRequest | UpdateDelegationRequest) => data,\r\n};\r\n\r\nconst FlightDetailsTransformer = {\r\n  toCreateRequest: (data: CreateFlightDetailsRequest) => data,\r\n};\r\n\r\nexport class DelegationApiService extends BaseApiService<\r\n  Delegation,\r\n  CreateDelegationRequest,\r\n  UpdateDelegationRequest\r\n> {\r\n  protected endpoint = '/delegations';\r\n  protected transformer: DataTransformer<Delegation> = DelegationApiTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 2 * 60 * 1000, // 2 minutes for delegations\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getByStatus(status: DelegationStatusPrisma): Promise<Delegation[]> {\r\n    const result = await this.getAll({ status });\r\n    return result.data;\r\n  }\r\n\r\n  async manageFlightDetails(\r\n    id: string,\r\n    flightDetails: FlightDetails\r\n  ): Promise<Delegation> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const requestPayload =\r\n        FlightDetailsTransformer.toCreateRequest(flightDetails);\r\n      const response = await this.apiClient.patch<DelegationApiResponse>(\r\n        `${this.endpoint}/${id}/flight-details`,\r\n        requestPayload\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return DelegationTransformer.fromApi(response);\r\n    });\r\n  }\r\n\r\n  async setDelegates(\r\n    id: string,\r\n    delegates: { employeeId: number; notes?: string; role: string }[]\r\n  ): Promise<Delegation> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.patch<DelegationApiResponse>(\r\n        `${this.endpoint}/${id}/delegates`,\r\n        { delegates }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return DelegationTransformer.fromApi(response);\r\n    });\r\n  }\r\n\r\n  async setDrivers(\r\n    id: string,\r\n    driverEmployeeIds: number[]\r\n  ): Promise<Delegation> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const requestPayload = driverEmployeeIds.map(employeeId => ({\r\n        employeeId,\r\n      }));\r\n      const response = await this.apiClient.patch<DelegationApiResponse>(\r\n        `${this.endpoint}/${id}/drivers`,\r\n        { drivers: requestPayload }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return DelegationTransformer.fromApi(response);\r\n    });\r\n  }\r\n\r\n  async setEscorts(\r\n    id: string,\r\n    escortEmployeeIds: number[]\r\n  ): Promise<Delegation> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const requestPayload = escortEmployeeIds.map(employeeId => ({\r\n        employeeId,\r\n      }));\r\n      const response = await this.apiClient.patch<DelegationApiResponse>(\r\n        `${this.endpoint}/${id}/escorts`,\r\n        { escorts: requestPayload }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return DelegationTransformer.fromApi(response);\r\n    });\r\n  }\r\n\r\n  async setVehicles(id: string, vehicleIds: number[]): Promise<Delegation> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const requestPayload = vehicleIds.map(vehicleId => ({\r\n        assignedDate: new Date().toISOString(),\r\n        vehicleId,\r\n      }));\r\n      const response = await this.apiClient.patch<DelegationApiResponse>(\r\n        `${this.endpoint}/${id}/vehicles`,\r\n        { vehicleAssignments: requestPayload }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return DelegationTransformer.fromApi(response);\r\n    });\r\n  }\r\n\r\n  async updateStatus(\r\n    id: string,\r\n    newStatus: DelegationStatusPrisma,\r\n    statusChangeReason?: string\r\n  ): Promise<Delegation> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.put<DelegationApiResponse>(\r\n        `${this.endpoint}/${id}`,\r\n        { status: newStatus, statusChangeReason }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${id}`);\r\n\r\n      return DelegationTransformer.fromApi(response);\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAYA;AAEA;;;AAMA,MAAM,2BAAwD;IAC5D,SAAS,CAAC,OAAgC,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;IACxE,OAAO,CAAC,OAA4D;AACtE;AAEA,MAAM,2BAA2B;IAC/B,iBAAiB,CAAC,OAAqC;AACzD;AAEO,MAAM,6BAA6B,2IAAA,CAAA,iBAAc;IAK5C,WAAW,eAAe;IAC1B,cAA2C,yBAAyB;IAE9E,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,YAAY,MAA8B,EAAyB;QACvE,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAO;QAC1C,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,oBACJ,EAAU,EACV,aAA4B,EACP;QACrB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,iBACJ,yBAAyB,eAAe,CAAC;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,eAAe,CAAC,EACvC;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;QACvC;IACF;IAEA,MAAM,aACJ,EAAU,EACV,SAAiE,EAC5C;QACrB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,EAClC;gBAAE;YAAU;YAGd,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;QACvC;IACF;IAEA,MAAM,WACJ,EAAU,EACV,iBAA2B,EACN;QACrB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,iBAAiB,kBAAkB,GAAG,CAAC,CAAA,aAAc,CAAC;oBAC1D;gBACF,CAAC;YACD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,EAChC;gBAAE,SAAS;YAAe;YAG5B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;QACvC;IACF;IAEA,MAAM,WACJ,EAAU,EACV,iBAA2B,EACN;QACrB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,iBAAiB,kBAAkB,GAAG,CAAC,CAAA,aAAc,CAAC;oBAC1D;gBACF,CAAC;YACD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,EAChC;gBAAE,SAAS;YAAe;YAG5B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;QACvC;IACF;IAEA,MAAM,YAAY,EAAU,EAAE,UAAoB,EAAuB;QACvE,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,iBAAiB,WAAW,GAAG,CAAC,CAAA,YAAa,CAAC;oBAClD,cAAc,IAAI,OAAO,WAAW;oBACpC;gBACF,CAAC;YACD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,EACjC;gBAAE,oBAAoB;YAAe;YAGvC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;QACvC;IACF;IAEA,MAAM,aACJ,EAAU,EACV,SAAiC,EACjC,kBAA2B,EACN;QACrB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EACxB;gBAAE,QAAQ;gBAAW;YAAmB;YAG1C,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI;YAEtD,OAAO,mJAAA,CAAA,wBAAqB,CAAC,OAAO,CAAC;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/transformers/employeeTransformer.ts"], "sourcesContent": ["/**\r\n * @file Data transformer for Employee domain models.\r\n * @module transformers/employeeTransformer\r\n */\r\n\r\nimport type {\r\n  CreateEmployeeRequest,\r\n  EmployeeApiResponse,\r\n  UpdateEmployeeRequest,\r\n} from '../types/apiContracts'; // Changed import path\r\nimport type {\r\n  CreateEmployeeData,\r\n  DriverAvailabilityPrisma,\r\n  Employee,\r\n  EmployeeRolePrisma,\r\n  EmployeeStatusPrisma,\r\n} from '../types/domain';\r\n\r\nimport { formatDateForApi } from '../utils/dateUtils';\r\n\r\n/**\r\n * Transforms employee data between API response formats and frontend domain models.\r\n */\r\nexport const EmployeeTransformer = {\r\n  /**\r\n   * Converts an API Employee response into a frontend Employee domain model.\r\n   * @param apiData - The data received from the API.\r\n   * @returns The Employee domain model.\r\n   */\r\n  fromApi(apiData: EmployeeApiResponse): Employee {\r\n    return {\r\n      availability: apiData.availability as DriverAvailabilityPrisma,\r\n      contactEmail: apiData.contactEmail ?? null,\r\n      contactInfo: apiData.contactInfo,\r\n      contactMobile: apiData.contactMobile ?? null,\r\n      contactPhone: apiData.contactPhone ?? null,\r\n      createdAt: apiData.createdAt,\r\n      currentLocation: apiData.currentLocation ?? null,\r\n      department: apiData.department ?? null,\r\n      employeeId: apiData.employeeId,\r\n      fullName: apiData.fullName ?? null, // Provide fallback for fullName\r\n      generalAssignments: apiData.generalAssignments,\r\n      hireDate: apiData.hireDate ?? null,\r\n      id: apiData.id,\r\n      name: apiData.name || '', // Provide fallback for name\r\n      notes: apiData.notes ?? null,\r\n      position: apiData.position ?? null,\r\n      profileImageUrl: apiData.profileImageUrl ?? null,\r\n      role: apiData.role as EmployeeRolePrisma,\r\n      shiftSchedule: apiData.shiftSchedule ?? null,\r\n      skills: apiData.skills,\r\n      status: apiData.status as EmployeeStatusPrisma,\r\n      updatedAt: apiData.updatedAt,\r\n      workingHours: apiData.workingHours ?? null,\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Converts frontend data for creating an employee into an API request payload.\r\n   * @param employeeData - The data from the frontend for creating a new employee.\r\n   * @returns The transformed CreateEmployeeRequest payload.\r\n   */\r\n  toCreateRequest(employeeData: CreateEmployeeData): CreateEmployeeRequest {\r\n    // Convert form data to API request format\r\n\r\n    const request: CreateEmployeeRequest = {\r\n      availability: employeeData.availability as string, // Type assertion\r\n      contactEmail: employeeData.contactEmail?.trim() ?? null,\r\n      contactInfo: employeeData.contactInfo.trim(),\r\n      contactMobile: employeeData.contactMobile?.trim() ?? null,\r\n      contactPhone: employeeData.contactPhone?.trim() ?? null,\r\n      currentLocation: employeeData.currentLocation ?? null,\r\n      department: employeeData.department?.trim() ?? null,\r\n      employeeId: employeeData.employeeId,\r\n      fullName: employeeData.fullName?.trim() ?? null,\r\n      generalAssignments: employeeData.generalAssignments,\r\n      hireDate: employeeData.hireDate\r\n        ? formatDateForApi(employeeData.hireDate)\r\n        : null, // Convert to ISO format\r\n      name: employeeData.name.trim(),\r\n\r\n      notes: employeeData.notes?.trim() ?? null,\r\n      position: employeeData.position?.trim() ?? null,\r\n      profileImageUrl: employeeData.profileImageUrl?.trim() ?? null,\r\n      role: employeeData.role as string, // Type assertion\r\n      shiftSchedule: employeeData.shiftSchedule ?? null,\r\n      skills: employeeData.skills,\r\n      status: employeeData.status as string, // Type assertion\r\n      workingHours: employeeData.workingHours ?? null,\r\n    };\r\n\r\n    return request;\r\n  },\r\n\r\n  /**\r\n   * Converts partial frontend employee data into an API request payload for updating.\r\n   * @param employeeData - The partial data from the frontend for updating an employee.\r\n   * @returns The transformed UpdateEmployeeRequest payload.\r\n   */\r\n  toUpdateRequest(\r\n    employeeData: Partial<CreateEmployeeData>\r\n  ): UpdateEmployeeRequest {\r\n    // Convert partial form data to API request format\r\n\r\n    const request: UpdateEmployeeRequest = {};\r\n\r\n    if (employeeData.name !== undefined)\r\n      request.name = employeeData.name?.trim() ?? null;\r\n    if (employeeData.employeeId !== undefined)\r\n      request.employeeId = employeeData.employeeId;\r\n    if (employeeData.contactInfo !== undefined)\r\n      request.contactInfo = employeeData.contactInfo?.trim() ?? null;\r\n    if (employeeData.contactEmail !== undefined)\r\n      request.contactEmail = employeeData.contactEmail?.trim() ?? null;\r\n    if (employeeData.contactMobile !== undefined)\r\n      request.contactMobile = employeeData.contactMobile?.trim() ?? null;\r\n    if (employeeData.contactPhone !== undefined)\r\n      request.contactPhone = employeeData.contactPhone?.trim() ?? null;\r\n    if (employeeData.position !== undefined)\r\n      request.position = employeeData.position?.trim() ?? null;\r\n    if (employeeData.department !== undefined)\r\n      request.department = employeeData.department?.trim() ?? null;\r\n    if (employeeData.hireDate !== undefined)\r\n      request.hireDate = employeeData.hireDate\r\n        ? formatDateForApi(employeeData.hireDate)\r\n        : null; // Convert to ISO format\r\n    if (employeeData.fullName !== undefined)\r\n      request.fullName = employeeData.fullName?.trim() ?? null;\r\n    if (employeeData.role !== undefined)\r\n      request.role = employeeData.role as string; // Type assertion\r\n    if (employeeData.status !== undefined)\r\n      request.status = employeeData.status as string; // Type assertion\r\n\r\n    if (employeeData.availability !== undefined)\r\n      request.availability = employeeData.availability as string; // Type assertion\r\n    if (employeeData.currentLocation !== undefined)\r\n      request.currentLocation = employeeData.currentLocation;\r\n    if (employeeData.workingHours !== undefined)\r\n      request.workingHours = employeeData.workingHours;\r\n    if (employeeData.generalAssignments !== undefined)\r\n      request.generalAssignments = employeeData.generalAssignments;\r\n    if (employeeData.notes !== undefined)\r\n      request.notes = employeeData.notes?.trim() ?? null;\r\n    if (employeeData.profileImageUrl !== undefined)\r\n      request.profileImageUrl = employeeData.profileImageUrl?.trim() ?? null;\r\n    if (employeeData.shiftSchedule !== undefined)\r\n      request.shiftSchedule = employeeData.shiftSchedule;\r\n    if (employeeData.skills !== undefined) request.skills = employeeData.skills;\r\n\r\n    return request;\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAeD;;AAKO,MAAM,sBAAsB;IACjC;;;;GAIC,GACD,SAAQ,OAA4B;QAClC,OAAO;YACL,cAAc,QAAQ,YAAY;YAClC,cAAc,QAAQ,YAAY,IAAI;YACtC,aAAa,QAAQ,WAAW;YAChC,eAAe,QAAQ,aAAa,IAAI;YACxC,cAAc,QAAQ,YAAY,IAAI;YACtC,WAAW,QAAQ,SAAS;YAC5B,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,YAAY,QAAQ,UAAU,IAAI;YAClC,YAAY,QAAQ,UAAU;YAC9B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,oBAAoB,QAAQ,kBAAkB;YAC9C,UAAU,QAAQ,QAAQ,IAAI;YAC9B,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI,IAAI;YACtB,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU,QAAQ,QAAQ,IAAI;YAC9B,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,MAAM,QAAQ,IAAI;YAClB,eAAe,QAAQ,aAAa,IAAI;YACxC,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,MAAM;YACtB,WAAW,QAAQ,SAAS;YAC5B,cAAc,QAAQ,YAAY,IAAI;QACxC;IACF;IAEA;;;;GAIC,GACD,iBAAgB,YAAgC;QAC9C,0CAA0C;QAE1C,MAAM,UAAiC;YACrC,cAAc,aAAa,YAAY;YACvC,cAAc,aAAa,YAAY,EAAE,UAAU;YACnD,aAAa,aAAa,WAAW,CAAC,IAAI;YAC1C,eAAe,aAAa,aAAa,EAAE,UAAU;YACrD,cAAc,aAAa,YAAY,EAAE,UAAU;YACnD,iBAAiB,aAAa,eAAe,IAAI;YACjD,YAAY,aAAa,UAAU,EAAE,UAAU;YAC/C,YAAY,aAAa,UAAU;YACnC,UAAU,aAAa,QAAQ,EAAE,UAAU;YAC3C,oBAAoB,aAAa,kBAAkB;YACnD,UAAU,aAAa,QAAQ,GAC3B,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,QAAQ,IACtC;YACJ,MAAM,aAAa,IAAI,CAAC,IAAI;YAE5B,OAAO,aAAa,KAAK,EAAE,UAAU;YACrC,UAAU,aAAa,QAAQ,EAAE,UAAU;YAC3C,iBAAiB,aAAa,eAAe,EAAE,UAAU;YACzD,MAAM,aAAa,IAAI;YACvB,eAAe,aAAa,aAAa,IAAI;YAC7C,QAAQ,aAAa,MAAM;YAC3B,QAAQ,aAAa,MAAM;YAC3B,cAAc,aAAa,YAAY,IAAI;QAC7C;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,iBACE,YAAyC;QAEzC,kDAAkD;QAElD,MAAM,UAAiC,CAAC;QAExC,IAAI,aAAa,IAAI,KAAK,WACxB,QAAQ,IAAI,GAAG,aAAa,IAAI,EAAE,UAAU;QAC9C,IAAI,aAAa,UAAU,KAAK,WAC9B,QAAQ,UAAU,GAAG,aAAa,UAAU;QAC9C,IAAI,aAAa,WAAW,KAAK,WAC/B,QAAQ,WAAW,GAAG,aAAa,WAAW,EAAE,UAAU;QAC5D,IAAI,aAAa,YAAY,KAAK,WAChC,QAAQ,YAAY,GAAG,aAAa,YAAY,EAAE,UAAU;QAC9D,IAAI,aAAa,aAAa,KAAK,WACjC,QAAQ,aAAa,GAAG,aAAa,aAAa,EAAE,UAAU;QAChE,IAAI,aAAa,YAAY,KAAK,WAChC,QAAQ,YAAY,GAAG,aAAa,YAAY,EAAE,UAAU;QAC9D,IAAI,aAAa,QAAQ,KAAK,WAC5B,QAAQ,QAAQ,GAAG,aAAa,QAAQ,EAAE,UAAU;QACtD,IAAI,aAAa,UAAU,KAAK,WAC9B,QAAQ,UAAU,GAAG,aAAa,UAAU,EAAE,UAAU;QAC1D,IAAI,aAAa,QAAQ,KAAK,WAC5B,QAAQ,QAAQ,GAAG,aAAa,QAAQ,GACpC,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,QAAQ,IACtC,MAAM,wBAAwB;QACpC,IAAI,aAAa,QAAQ,KAAK,WAC5B,QAAQ,QAAQ,GAAG,aAAa,QAAQ,EAAE,UAAU;QACtD,IAAI,aAAa,IAAI,KAAK,WACxB,QAAQ,IAAI,GAAG,aAAa,IAAI,EAAY,iBAAiB;QAC/D,IAAI,aAAa,MAAM,KAAK,WAC1B,QAAQ,MAAM,GAAG,aAAa,MAAM,EAAY,iBAAiB;QAEnE,IAAI,aAAa,YAAY,KAAK,WAChC,QAAQ,YAAY,GAAG,aAAa,YAAY,EAAY,iBAAiB;QAC/E,IAAI,aAAa,eAAe,KAAK,WACnC,QAAQ,eAAe,GAAG,aAAa,eAAe;QACxD,IAAI,aAAa,YAAY,KAAK,WAChC,QAAQ,YAAY,GAAG,aAAa,YAAY;QAClD,IAAI,aAAa,kBAAkB,KAAK,WACtC,QAAQ,kBAAkB,GAAG,aAAa,kBAAkB;QAC9D,IAAI,aAAa,KAAK,KAAK,WACzB,QAAQ,KAAK,GAAG,aAAa,KAAK,EAAE,UAAU;QAChD,IAAI,aAAa,eAAe,KAAK,WACnC,QAAQ,eAAe,GAAG,aAAa,eAAe,EAAE,UAAU;QACpE,IAAI,aAAa,aAAa,KAAK,WACjC,QAAQ,aAAa,GAAG,aAAa,aAAa;QACpD,IAAI,aAAa,MAAM,KAAK,WAAW,QAAQ,MAAM,GAAG,aAAa,MAAM;QAE3E,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/domain/employeeApi.ts"], "sourcesContent": ["import type {\r\n  CreateEmployeeRequest,\r\n  EmployeeApiResponse,\r\n  UpdateEmployeeRequest,\r\n} from '../../../types/apiContracts';\r\nimport type { Employee, DriverAvailabilityPrisma } from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport { EmployeeTransformer } from '../../../transformers/employeeTransformer';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst EmployeeApiTransformer: DataTransformer<Employee> = {\r\n  fromApi: (data: EmployeeApiResponse) => EmployeeTransformer.fromApi(data),\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class EmployeeApiService extends BaseApiService<\r\n  Employee,\r\n  CreateEmployeeRequest,\r\n  UpdateEmployeeRequest\r\n> {\r\n  protected endpoint = '/employees';\r\n  protected transformer: DataTransformer<Employee> = EmployeeApiTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for employees\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getByRole(role: string): Promise<Employee[]> {\r\n    const result = await this.getAll({ role });\r\n    return result.data;\r\n  }\r\n\r\n  async updateAvailabilityStatus(\r\n    employeeId: string,\r\n    status: DriverAvailabilityPrisma\r\n  ): Promise<Employee> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.patch<EmployeeApiResponse>(\r\n        `${this.endpoint}/${employeeId}/availability`,\r\n        { availability: status }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${employeeId}`);\r\n\r\n      return EmployeeTransformer.fromApi(response);\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAOA;AACA;;;AAMA,MAAM,yBAAoD;IACxD,SAAS,CAAC,OAA8B,iJAAA,CAAA,sBAAmB,CAAC,OAAO,CAAC;IACpE,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,2BAA2B,2IAAA,CAAA,iBAAc;IAK1C,WAAW,aAAa;IACxB,cAAyC,uBAAuB;IAE1E,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,UAAU,IAAY,EAAuB;QACjD,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAK;QACxC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,yBACJ,UAAkB,EAClB,MAAgC,EACb;QACnB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,aAAa,CAAC,EAC7C;gBAAE,cAAc;YAAO;YAGzB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,YAAY;YAE9D,OAAO,iJAAA,CAAA,sBAAmB,CAAC,OAAO,CAAC;QACrC;IACF;AACF", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/domain/reliabilityApi.ts"], "sourcesContent": ["import type {\r\n  AlertApiResponse,\r\n  AlertHistoryApiResponse,\r\n  AlertStatisticsApiResponse,\r\n  CircuitBreakerStatusApiResponse,\r\n  DeduplicationMetricsApiResponse,\r\n  DependencyHealthApiResponse,\r\n  DetailedHealthApiResponse,\r\n  HealthCheckApiResponse,\r\n  MetricsApiResponse,\r\n  TestAlertsApiResponse,\r\n} from '../../../types/api';\r\nimport type {\r\n  Alert,\r\n  AlertHistory,\r\n  AlertStatistics,\r\n  CircuitBreakerStatus,\r\n  DeduplicationMetrics,\r\n  DependencyHealth,\r\n  DetailedHealthCheck,\r\n  HealthCheck,\r\n  SystemMetrics,\r\n  TestAlertsResult,\r\n} from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\nimport logger from '../../../utils/logger';\r\n\r\nconst ReliabilityTransformer: DataTransformer<any> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class ReliabilityApiService extends BaseApiService<any, any, any> {\r\n  protected endpoint = '/reliability';\r\n  protected transformer: DataTransformer<any> = ReliabilityTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 1 * 60 * 1000, // 1 minute for reliability data\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getSystemHealth(): Promise<HealthCheck> {\r\n    return this.executeWithInfrastructure('health:system', async () => {\r\n      const response =\r\n        await this.apiClient.get<HealthCheckApiResponse>('/health');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDetailedHealth(): Promise<DetailedHealthCheck> {\r\n    return this.executeWithInfrastructure('health:detailed', async () => {\r\n      const response =\r\n        await this.apiClient.get<DetailedHealthApiResponse>('/health/detailed');\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getDependencyHealth(): Promise<DependencyHealth> {\r\n    return this.executeWithInfrastructure('health:dependencies', async () => {\r\n      const response = await this.apiClient.get<DependencyHealthApiResponse>(\r\n        '/health/dependencies'\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getCircuitBreakerStatus(): Promise<CircuitBreakerStatus> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:circuit-breakers',\r\n      async () => {\r\n        try {\r\n          const apiResponse = await this.apiClient.get<any>(\r\n            '/monitoring/circuit-breakers'\r\n          );\r\n\r\n          const circuitBreakers = apiResponse?.circuitBreakers || [];\r\n\r\n          return {\r\n            circuitBreakers: circuitBreakers || [],\r\n            summary: {\r\n              total: circuitBreakers?.length || 0,\r\n              closed:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'CLOSED')\r\n                  .length || 0,\r\n              open:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'OPEN')\r\n                  .length || 0,\r\n              halfOpen:\r\n                circuitBreakers?.filter((cb: any) => cb.state === 'HALF_OPEN')\r\n                  .length || 0,\r\n            },\r\n          };\r\n        } catch (error) {\r\n          console.error('Failed to get circuit breaker status:', error);\r\n          return {\r\n            circuitBreakers: [],\r\n            summary: { total: 0, closed: 0, open: 0, halfOpen: 0 },\r\n          };\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  async getDeduplicationMetrics(): Promise<DeduplicationMetrics> {\r\n    return this.executeWithInfrastructure(\r\n      'monitoring:deduplication',\r\n      async () => {\r\n        const response =\r\n          await this.apiClient.get<DeduplicationMetricsApiResponse>(\r\n            '/monitoring/deduplication'\r\n          );\r\n        return response as any;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getMetrics(): Promise<SystemMetrics> {\r\n    return this.executeWithInfrastructure('metrics:system', async () => {\r\n      const response = await this.apiClient.get<MetricsApiResponse>(\r\n        '/metrics',\r\n        {\r\n          headers: { Accept: 'application/json' },\r\n        }\r\n      );\r\n      return response as any;\r\n    });\r\n  }\r\n\r\n  async getActiveAlerts(): Promise<Alert[]> {\r\n    return this.executeWithInfrastructure('alerts:active', async () => {\r\n      try {\r\n        const apiResponse = await this.apiClient.get<any>('/alerts');\r\n\r\n        return apiResponse?.alerts || [];\r\n      } catch (error) {\r\n        console.error('Failed to get active alerts:', error);\r\n        return [];\r\n      }\r\n    });\r\n  }\r\n\r\n  async getAlertHistory(\r\n    page: number = 1,\r\n    limit: number = 50\r\n  ): Promise<AlertHistory> {\r\n    return this.executeWithInfrastructure(\r\n      `alerts:history:${page}:${limit}`,\r\n      async () => {\r\n        const queryParams = new URLSearchParams({\r\n          page: page.toString(),\r\n          limit: limit.toString(),\r\n        });\r\n        const response = await this.apiClient.get<AlertHistoryApiResponse>(\r\n          `/alerts/history?${queryParams.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getAlertStatistics(): Promise<AlertStatistics> {\r\n    return this.executeWithInfrastructure('alerts:statistics', async () => {\r\n      try {\r\n        const response =\r\n          await this.apiClient.get<AlertStatisticsApiResponse>(\r\n            '/alerts/statistics'\r\n          );\r\n        return response;\r\n      } catch (error) {\r\n        console.error('Failed to get alert statistics:', error);\r\n        return {\r\n          total: 0,\r\n          active: 0,\r\n          acknowledged: 0,\r\n          resolved: 0,\r\n          bySeverity: { low: 0, medium: 0, high: 0, critical: 0 },\r\n          averageResolutionTime: 0,\r\n          recentTrends: { last24Hours: 0, last7Days: 0, last30Days: 0 },\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  async resolveAlert(\r\n    alertId: string,\r\n    reason?: string,\r\n    resolvedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/resolve`,\r\n        {\r\n          reason,\r\n          resolvedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async acknowledgeAlert(\r\n    alertId: string,\r\n    note?: string,\r\n    acknowledgedBy?: string\r\n  ): Promise<Alert> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<AlertApiResponse>(\r\n        `/alerts/${alertId}/acknowledge`,\r\n        {\r\n          note,\r\n          acknowledgedBy,\r\n        }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp('^alerts:'));\r\n\r\n      return response;\r\n    });\r\n  }\r\n\r\n  async testAlerts(): Promise<TestAlertsResult> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>('/alerts/test');\r\n      return {\r\n        success: response?.status === 'success',\r\n        message: response?.message || 'Test alert triggered',\r\n        testAlertId: response?.data?.id,\r\n      };\r\n    });\r\n  }\r\n\r\n  async getReliabilityDashboardData(): Promise<{\r\n    systemHealth: HealthCheck;\r\n    detailedHealth: DetailedHealthCheck;\r\n    circuitBreakers: CircuitBreakerStatus;\r\n    metrics: SystemMetrics;\r\n    activeAlerts: Alert[];\r\n    alertStatistics: AlertStatistics;\r\n  }> {\r\n    const [\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    ] = await Promise.all([\r\n      this.getSystemHealth(),\r\n      this.getDetailedHealth(),\r\n      this.getCircuitBreakerStatus(),\r\n      this.getMetrics(),\r\n      this.getActiveAlerts(),\r\n      this.getAlertStatistics(),\r\n    ]);\r\n\r\n    return {\r\n      systemHealth,\r\n      detailedHealth,\r\n      circuitBreakers,\r\n      metrics,\r\n      activeAlerts,\r\n      alertStatistics,\r\n    };\r\n  }\r\n\r\n  async isSystemHealthy(): Promise<boolean> {\r\n    try {\r\n      const health = await this.getSystemHealth();\r\n      return health.status === 'healthy';\r\n    } catch (error) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async getCriticalAlertCount(): Promise<number> {\r\n    try {\r\n      const statistics = await this.getAlertStatistics();\r\n      return statistics.bySeverity.critical;\r\n    } catch (error) {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  async getHealthTrends(\r\n    timeframe: '1h' | '6h' | '24h' | '7d' = '24h'\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `health:trends:${timeframe}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<any>(\r\n          `/health/trends?timeframe=${timeframe}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getCircuitBreakerHistory(\r\n    timeframe: '1h' | '6h' | '24h' | '7d' = '24h',\r\n    breakerName?: string\r\n  ): Promise<any> {\r\n    return this.executeWithInfrastructure(\r\n      `circuit-breakers:history:${timeframe}:${breakerName || 'all'}`,\r\n      async () => {\r\n        const params = new URLSearchParams({ timeframe });\r\n        if (breakerName) {\r\n          params.append('breakerName', breakerName);\r\n        }\r\n        const response = await this.apiClient.get<any>(\r\n          `/monitoring/circuit-breakers/history?${params.toString()}`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async getHttpRequestMetrics(): Promise<any> {\r\n    return this.executeWithInfrastructure('http:metrics', async () => {\r\n      const response = await this.apiClient.get<any>(\r\n        '/monitoring/http-request-metrics'\r\n      );\r\n      return response;\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAyBA;;AAOA,MAAM,yBAA+C;IACnD,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,8BAA8B,2IAAA,CAAA,iBAAc;IAC7C,WAAW,eAAe;IAC1B,cAAoC,uBAAuB;IAErE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,kBAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB;YACnD,OAAO;QACT;IACF;IAEA,MAAM,oBAAkD;QACtD,OAAO,IAAI,CAAC,yBAAyB,CAAC,mBAAmB;YACvD,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAA4B;YACtD,OAAO;QACT;IACF;IAEA,MAAM,sBAAiD;QACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,uBAAuB;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,+BACA;YACE,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAC1C;gBAGF,MAAM,kBAAkB,aAAa,mBAAmB,EAAE;gBAE1D,OAAO;oBACL,iBAAiB,mBAAmB,EAAE;oBACtC,SAAS;wBACP,OAAO,iBAAiB,UAAU;wBAClC,QACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,UAC/C,UAAU;wBACf,MACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,QAC/C,UAAU;wBACf,UACE,iBAAiB,OAAO,CAAC,KAAY,GAAG,KAAK,KAAK,aAC/C,UAAU;oBACjB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;oBACL,iBAAiB,EAAE;oBACnB,SAAS;wBAAE,OAAO;wBAAG,QAAQ;wBAAG,MAAM;wBAAG,UAAU;oBAAE;gBACvD;YACF;QACF;IAEJ;IAEA,MAAM,0BAAyD;QAC7D,OAAO,IAAI,CAAC,yBAAyB,CACnC,4BACA;YACE,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;YAEJ,OAAO;QACT;IAEJ;IAEA,MAAM,aAAqC;QACzC,OAAO,IAAI,CAAC,yBAAyB,CAAC,kBAAkB;YACtD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,YACA;gBACE,SAAS;oBAAE,QAAQ;gBAAmB;YACxC;YAEF,OAAO;QACT;IACF;IAEA,MAAM,kBAAoC;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC,iBAAiB;YACrD,IAAI;gBACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAM;gBAElD,OAAO,aAAa,UAAU,EAAE;YAClC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBACJ,OAAe,CAAC,EAChB,QAAgB,EAAE,EACK;QACvB,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE,OAAO,EACjC;YACE,MAAM,cAAc,IAAI,gBAAgB;gBACtC,MAAM,KAAK,QAAQ;gBACnB,OAAO,MAAM,QAAQ;YACvB;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;YAE7C,OAAO;QACT;IAEJ;IAEA,MAAM,qBAA+C;QACnD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB;YACzD,IAAI;gBACF,MAAM,WACJ,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACtB;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,YAAY;wBAAE,KAAK;wBAAG,QAAQ;wBAAG,MAAM;wBAAG,UAAU;oBAAE;oBACtD,uBAAuB;oBACvB,cAAc;wBAAE,aAAa;wBAAG,WAAW;wBAAG,YAAY;oBAAE;gBAC9D;YACF;QACF;IACF;IAEA,MAAM,aACJ,OAAe,EACf,MAAe,EACf,UAAmB,EACH;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,EAC5B;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,iBACJ,OAAe,EACf,IAAa,EACb,cAAuB,EACP;QAChB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,QAAQ,YAAY,CAAC,EAChC;gBACE;gBACA;YACF;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO;YAExC,OAAO;QACT;IACF;IAEA,MAAM,aAAwC;QAC5C,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAM;YAChD,OAAO;gBACL,SAAS,UAAU,WAAW;gBAC9B,SAAS,UAAU,WAAW;gBAC9B,aAAa,UAAU,MAAM;YAC/B;QACF;IACF;IAEA,MAAM,8BAOH;QACD,MAAM,CACJ,cACA,gBACA,iBACA,SACA,cACA,gBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,kBAAkB;SACxB;QAED,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe;YACzC,OAAO,OAAO,MAAM,KAAK;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,wBAAyC;QAC7C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,kBAAkB;YAChD,OAAO,WAAW,UAAU,CAAC,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,gBACJ,YAAwC,KAAK,EAC/B;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,cAAc,EAAE,WAAW,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,yBAAyB,EAAE,WAAW;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,yBACJ,YAAwC,KAAK,EAC7C,WAAoB,EACN;QACd,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,yBAAyB,EAAE,UAAU,CAAC,EAAE,eAAe,OAAO,EAC/D;YACE,MAAM,SAAS,IAAI,gBAAgB;gBAAE;YAAU;YAC/C,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,eAAe;YAC/B;YACA,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,qCAAqC,EAAE,OAAO,QAAQ,IAAI;YAE7D,OAAO;QACT;IAEJ;IAEA,MAAM,wBAAsC;QAC1C,OAAO,IAAI,CAAC,yBAAyB,CAAC,gBAAgB;YACpD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC;YAEF,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/transformers/vehicleTransformer.ts"], "sourcesContent": ["/**\r\n * @file Data transformer for Vehicle domain models.\r\n * @module transformers/vehicleTransformer\r\n */\r\n\r\nimport type {\r\n  CreateVehicleRequest,\r\n  ServiceRecordApiResponse,\r\n  UpdateVehicleRequest,\r\n  VehicleApiResponse,\r\n} from '../types/api'; // Corrected import path\r\nimport type {\r\n  CreateVehicleData,\r\n  ServiceRecord,\r\n  Vehicle,\r\n} from '../types/domain';\r\n\r\n// ServiceRecordTransformer\r\nconst ServiceRecordTransformer = {\r\n  fromApi(apiData: ServiceRecordApiResponse): ServiceRecord {\r\n    return {\r\n      cost: apiData.cost,\r\n      createdAt: apiData.createdAt,\r\n      date: apiData.date,\r\n      employeeId: apiData.employeeId,\r\n      id: apiData.id,\r\n      notes: apiData.notes,\r\n      odometer: apiData.odometer,\r\n      servicePerformed: Array.isArray(apiData.servicePerformed)\r\n        ? apiData.servicePerformed\r\n        : [],\r\n      updatedAt: apiData.updatedAt,\r\n      vehicleId: apiData.vehicleId,\r\n    };\r\n  },\r\n  // toApi for ServiceRecord might be needed if creating/updating service records through vehicle endpoint\r\n  // For now, assuming service records are managed via their own endpoints.\r\n};\r\n\r\n/**\r\n * Transforms vehicle data between API response formats and frontend domain models.\r\n */\r\nexport const VehicleTransformer = {\r\n  /**\r\n   * Converts a raw API Vehicle response into a frontend Vehicle domain model.\r\n   * @param apiData - The raw data received from the API.\r\n   * @returns The transformed Vehicle domain model.\r\n   */\r\n  fromApi(apiData: VehicleApiResponse): Vehicle {\r\n    return {\r\n      color: apiData.color ?? null,\r\n      createdAt: apiData.createdAt,\r\n      id: apiData.id,\r\n      imageUrl: apiData.imageUrl ?? null,\r\n      initialOdometer: apiData.initialOdometer ?? null,\r\n      licensePlate: apiData.licensePlate,\r\n      make: apiData.make,\r\n      model: apiData.model,\r\n      ownerContact: apiData.ownerContact,\r\n      ownerName: apiData.ownerName,\r\n      serviceHistory: (() => {\r\n        const records = apiData.serviceHistory || apiData.ServiceRecord;\r\n        return Array.isArray(records)\r\n          ? records.map(ServiceRecordTransformer.fromApi)\r\n          : [];\r\n      })(),\r\n      updatedAt: apiData.updatedAt,\r\n      vin: apiData.vin ?? null,\r\n      year: apiData.year,\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Converts frontend data for creating a vehicle into an API request payload.\r\n   * @param vehicleData - The data from the frontend for creating a new vehicle.\r\n   * @returns The transformed payload, compatible with CreateVehicleRequest.\r\n   */\r\n  toCreateRequest(vehicleData: CreateVehicleData): CreateVehicleRequest {\r\n    // Generate a default VIN if not provided (for demo purposes)\r\n    const defaultVin =\r\n      vehicleData.vin?.trim() || this.generateDefaultVin(vehicleData);\r\n\r\n    // Provide valid default values that pass backend validation\r\n    const defaultOwnerContact =\r\n      vehicleData.ownerContact?.trim() || '<EMAIL>';\r\n    const defaultOwnerName =\r\n      vehicleData.ownerName?.trim() || 'WorkHub Fleet Management';\r\n\r\n    const request: CreateVehicleRequest = {\r\n      color: vehicleData.color ? vehicleData.color.trim() : null,\r\n      imageUrl: vehicleData.imageUrl ? vehicleData.imageUrl.trim() : '',\r\n      initialOdometer: vehicleData.initialOdometer ?? null,\r\n      licensePlate: vehicleData.licensePlate.trim(),\r\n      make: vehicleData.make.trim(),\r\n      model: vehicleData.model.trim(),\r\n      ownerContact: defaultOwnerContact,\r\n      ownerName: defaultOwnerName,\r\n      vin: defaultVin,\r\n      year: vehicleData.year,\r\n    };\r\n\r\n    if (\r\n      !request.make ||\r\n      !request.model ||\r\n      !request.year ||\r\n      !request.licensePlate\r\n    ) {\r\n      throw new Error(\r\n        'Missing required fields for creating a vehicle (make, model, year, licensePlate)'\r\n      );\r\n    }\r\n\r\n    // Validate VIN format\r\n    if (!/^[A-HJ-NPR-Z0-9]{17}$/.test(request.vin!)) {\r\n      throw new Error(\r\n        'VIN must be exactly 17 characters and contain only valid characters (A-H, J-N, P-R, Z, 0-9)'\r\n      );\r\n    }\r\n\r\n    return request;\r\n  },\r\n\r\n  /**\r\n   * Generates a default VIN for demo purposes\r\n   * In production, this should be handled differently\r\n   */\r\n  generateDefaultVin(vehicleData: CreateVehicleData): string {\r\n    // Valid VIN characters (excluding I, O, Q)\r\n    const validChars = 'ABCDEFGHJKLMNPRSTUVWXYZ0123456789';\r\n\r\n    // Generate a valid 17-character VIN\r\n    const makeCode = vehicleData.make\r\n      .substring(0, 3)\r\n      .toUpperCase()\r\n      .replace(/[IOQ]/g, 'X')\r\n      .padEnd(3, 'X');\r\n\r\n    const modelCode = vehicleData.model\r\n      .substring(0, 2)\r\n      .toUpperCase()\r\n      .replace(/[IOQ]/g, 'X')\r\n      .padEnd(2, 'X');\r\n\r\n    // Year code (last 2 digits)\r\n    const yearCode = vehicleData.year.toString().substring(2);\r\n\r\n    // Generate remaining 10 characters using valid VIN characters\r\n    let randomCode = '';\r\n    for (let i = 0; i < 10; i++) {\r\n      randomCode += validChars.charAt(\r\n        Math.floor(Math.random() * validChars.length)\r\n      );\r\n    }\r\n\r\n    const vin = `${makeCode}${modelCode}${yearCode}${randomCode}`;\r\n\r\n    // Ensure exactly 17 characters\r\n    return vin.substring(0, 17).padEnd(17, 'X');\r\n  },\r\n\r\n  /**\r\n   * Converts partial frontend vehicle data into an API request payload for updating.\r\n   * @param vehicleData - The partial data from the frontend for updating a vehicle.\r\n   * @returns The transformed payload, compatible with UpdateVehicleRequest.\r\n   */\r\n  toUpdateRequest(\r\n    vehicleData: Partial<CreateVehicleData>\r\n  ): UpdateVehicleRequest {\r\n    const request: UpdateVehicleRequest = {};\r\n\r\n    if (vehicleData.make !== undefined) request.make = vehicleData.make.trim();\r\n    if (vehicleData.model !== undefined)\r\n      request.model = vehicleData.model.trim();\r\n    if (vehicleData.year !== undefined) request.year = vehicleData.year;\r\n    if (vehicleData.vin !== undefined) request.vin = vehicleData.vin.trim();\r\n    if (vehicleData.licensePlate !== undefined)\r\n      request.licensePlate = vehicleData.licensePlate.trim();\r\n    if (vehicleData.ownerName !== undefined)\r\n      request.ownerName = vehicleData.ownerName.trim();\r\n    if (vehicleData.ownerContact !== undefined)\r\n      request.ownerContact = vehicleData.ownerContact.trim();\r\n    if (vehicleData.color !== undefined)\r\n      request.color = vehicleData.color ? vehicleData.color.trim() : null;\r\n    if (vehicleData.initialOdometer !== undefined)\r\n      request.initialOdometer = vehicleData.initialOdometer;\r\n    if (vehicleData.imageUrl !== undefined) {\r\n      request.imageUrl = vehicleData.imageUrl\r\n        ? vehicleData.imageUrl.trim()\r\n        : '';\r\n    }\r\n\r\n    return request;\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAcD,2BAA2B;AAC3B,MAAM,2BAA2B;IAC/B,SAAQ,OAAiC;QACvC,OAAO;YACL,MAAM,QAAQ,IAAI;YAClB,WAAW,QAAQ,SAAS;YAC5B,MAAM,QAAQ,IAAI;YAClB,YAAY,QAAQ,UAAU;YAC9B,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,UAAU,QAAQ,QAAQ;YAC1B,kBAAkB,MAAM,OAAO,CAAC,QAAQ,gBAAgB,IACpD,QAAQ,gBAAgB,GACxB,EAAE;YACN,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;IACF;AAGF;AAKO,MAAM,qBAAqB;IAChC;;;;GAIC,GACD,SAAQ,OAA2B;QACjC,OAAO;YACL,OAAO,QAAQ,KAAK,IAAI;YACxB,WAAW,QAAQ,SAAS;YAC5B,IAAI,QAAQ,EAAE;YACd,UAAU,QAAQ,QAAQ,IAAI;YAC9B,iBAAiB,QAAQ,eAAe,IAAI;YAC5C,cAAc,QAAQ,YAAY;YAClC,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK;YACpB,cAAc,QAAQ,YAAY;YAClC,WAAW,QAAQ,SAAS;YAC5B,gBAAgB,CAAC;gBACf,MAAM,UAAU,QAAQ,cAAc,IAAI,QAAQ,aAAa;gBAC/D,OAAO,MAAM,OAAO,CAAC,WACjB,QAAQ,GAAG,CAAC,yBAAyB,OAAO,IAC5C,EAAE;YACR,CAAC;YACD,WAAW,QAAQ,SAAS;YAC5B,KAAK,QAAQ,GAAG,IAAI;YACpB,MAAM,QAAQ,IAAI;QACpB;IACF;IAEA;;;;GAIC,GACD,iBAAgB,WAA8B;QAC5C,6DAA6D;QAC7D,MAAM,aACJ,YAAY,GAAG,EAAE,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAErD,4DAA4D;QAC5D,MAAM,sBACJ,YAAY,YAAY,EAAE,UAAU;QACtC,MAAM,mBACJ,YAAY,SAAS,EAAE,UAAU;QAEnC,MAAM,UAAgC;YACpC,OAAO,YAAY,KAAK,GAAG,YAAY,KAAK,CAAC,IAAI,KAAK;YACtD,UAAU,YAAY,QAAQ,GAAG,YAAY,QAAQ,CAAC,IAAI,KAAK;YAC/D,iBAAiB,YAAY,eAAe,IAAI;YAChD,cAAc,YAAY,YAAY,CAAC,IAAI;YAC3C,MAAM,YAAY,IAAI,CAAC,IAAI;YAC3B,OAAO,YAAY,KAAK,CAAC,IAAI;YAC7B,cAAc;YACd,WAAW;YACX,KAAK;YACL,MAAM,YAAY,IAAI;QACxB;QAEA,IACE,CAAC,QAAQ,IAAI,IACb,CAAC,QAAQ,KAAK,IACd,CAAC,QAAQ,IAAI,IACb,CAAC,QAAQ,YAAY,EACrB;YACA,MAAM,IAAI,MACR;QAEJ;QAEA,sBAAsB;QACtB,IAAI,CAAC,wBAAwB,IAAI,CAAC,QAAQ,GAAG,GAAI;YAC/C,MAAM,IAAI,MACR;QAEJ;QAEA,OAAO;IACT;IAEA;;;GAGC,GACD,oBAAmB,WAA8B;QAC/C,2CAA2C;QAC3C,MAAM,aAAa;QAEnB,oCAAoC;QACpC,MAAM,WAAW,YAAY,IAAI,CAC9B,SAAS,CAAC,GAAG,GACb,WAAW,GACX,OAAO,CAAC,UAAU,KAClB,MAAM,CAAC,GAAG;QAEb,MAAM,YAAY,YAAY,KAAK,CAChC,SAAS,CAAC,GAAG,GACb,WAAW,GACX,OAAO,CAAC,UAAU,KAClB,MAAM,CAAC,GAAG;QAEb,4BAA4B;QAC5B,MAAM,WAAW,YAAY,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAEvD,8DAA8D;QAC9D,IAAI,aAAa;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,cAAc,WAAW,MAAM,CAC7B,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM;QAEhD;QAEA,MAAM,MAAM,GAAG,WAAW,YAAY,WAAW,YAAY;QAE7D,+BAA+B;QAC/B,OAAO,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI;IACzC;IAEA;;;;GAIC,GACD,iBACE,WAAuC;QAEvC,MAAM,UAAgC,CAAC;QAEvC,IAAI,YAAY,IAAI,KAAK,WAAW,QAAQ,IAAI,GAAG,YAAY,IAAI,CAAC,IAAI;QACxE,IAAI,YAAY,KAAK,KAAK,WACxB,QAAQ,KAAK,GAAG,YAAY,KAAK,CAAC,IAAI;QACxC,IAAI,YAAY,IAAI,KAAK,WAAW,QAAQ,IAAI,GAAG,YAAY,IAAI;QACnE,IAAI,YAAY,GAAG,KAAK,WAAW,QAAQ,GAAG,GAAG,YAAY,GAAG,CAAC,IAAI;QACrE,IAAI,YAAY,YAAY,KAAK,WAC/B,QAAQ,YAAY,GAAG,YAAY,YAAY,CAAC,IAAI;QACtD,IAAI,YAAY,SAAS,KAAK,WAC5B,QAAQ,SAAS,GAAG,YAAY,SAAS,CAAC,IAAI;QAChD,IAAI,YAAY,YAAY,KAAK,WAC/B,QAAQ,YAAY,GAAG,YAAY,YAAY,CAAC,IAAI;QACtD,IAAI,YAAY,KAAK,KAAK,WACxB,QAAQ,KAAK,GAAG,YAAY,KAAK,GAAG,YAAY,KAAK,CAAC,IAAI,KAAK;QACjE,IAAI,YAAY,eAAe,KAAK,WAClC,QAAQ,eAAe,GAAG,YAAY,eAAe;QACvD,IAAI,YAAY,QAAQ,KAAK,WAAW;YACtC,QAAQ,QAAQ,GAAG,YAAY,QAAQ,GACnC,YAAY,QAAQ,CAAC,IAAI,KACzB;QACN;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/transformers/taskTransformer.ts"], "sourcesContent": ["/**\r\n * @file Data transformer for Task domain models.\r\n * @module transformers/taskTransformer\r\n */\r\n\r\nimport type {\r\n  CreateSubtaskRequest,\r\n  CreateTaskRequest,\r\n  SubtaskApiResponse,\r\n  TaskApiResponse,\r\n  UpdateTaskRequest,\r\n} from '../types/apiContracts';\r\nimport type {\r\n  CreateSubtaskData,\r\n  CreateTaskData,\r\n  Subtask,\r\n  Task,\r\n  TaskPriorityPrisma,\r\n  TaskStatusPrisma,\r\n} from '../types/domain';\r\n\r\nimport {\r\n  // Added SubtaskApiResponse\r\n  EmployeeApiResponse,\r\n  VehicleApiResponse,\r\n} from '../types/apiContracts'; // Changed import path to apiContracts\r\nimport { Employee, Vehicle } from '../types/domain';\r\nimport { EmployeeTransformer } from './employeeTransformer';\r\nimport { VehicleTransformer } from './vehicleTransformer';\r\n\r\nexport const SubtaskTransformer = {\r\n  /**\r\n   * Transforms a subtask API response into a domain.Subtask model.\r\n   * @param apiData - The subtask data as received from the API.\r\n   * @returns A domain.Subtask object.\r\n   */\r\n  fromApi(apiData: SubtaskApiResponse): Subtask {\r\n    return {\r\n      completed: apiData.completed,\r\n      id: apiData.id,\r\n      taskId: apiData.taskId, // Added taskId\r\n      title: apiData.title,\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Transforms a domain.CreateSubtaskData into an API CreateSubtaskRequest.\r\n   * @param domainData - The subtask data from the domain.\r\n   * @returns An API CreateSubtaskRequest object.\r\n   */\r\n  toApiRequest(domainData: CreateSubtaskData): CreateSubtaskRequest {\r\n    return {\r\n      completed:\r\n        domainData.completed === undefined ? false : domainData.completed,\r\n      taskId: domainData.taskId, // Added taskId\r\n      title: domainData.title.trim(),\r\n    };\r\n  },\r\n};\r\n\r\n/**\r\n * Transforms task data between API response formats and frontend domain models.\r\n */\r\nexport const TaskTransformer = {\r\n  fromApi(apiData: TaskApiResponse): Task {\r\n    return {\r\n      createdAt: apiData.createdAt,\r\n      dateTime: apiData.dateTime, // REQUIRED - no null fallback\r\n      deadline: apiData.deadline ?? null, // Optional field\r\n      description: apiData.description, // REQUIRED - no null fallback\r\n      driverEmployee: apiData.Employee_Task_driverEmployeeIdToEmployee\r\n        ? EmployeeTransformer.fromApi(\r\n            apiData.Employee_Task_driverEmployeeIdToEmployee\r\n          )\r\n        : null,\r\n      driverEmployeeId: apiData.driverEmployeeId ?? null,\r\n      estimatedDuration: apiData.estimatedDuration, // REQUIRED - no null fallback\r\n      id: apiData.id,\r\n      location: apiData.location, // REQUIRED - no null fallback\r\n      notes: apiData.notes ?? null, // Optional field\r\n      priority: apiData.priority as TaskPriorityPrisma,\r\n      requiredSkills: apiData.requiredSkills, // REQUIRED - no null fallback\r\n      // Transform relation objects from backend Prisma includes\r\n      staffEmployee: apiData.Employee_Task_staffEmployeeIdToEmployee\r\n        ? EmployeeTransformer.fromApi(\r\n            apiData.Employee_Task_staffEmployeeIdToEmployee\r\n          )\r\n        : null,\r\n      staffEmployeeId: apiData.staffEmployeeId, // REQUIRED - no null fallback\r\n      status: apiData.status as TaskStatusPrisma,\r\n      subtasks: Array.isArray(apiData.SubTask)\r\n        ? apiData.SubTask.map((st: SubtaskApiResponse) =>\r\n            SubtaskTransformer.fromApi(st)\r\n          )\r\n        : [],\r\n\r\n      updatedAt: apiData.updatedAt,\r\n      vehicle: apiData.Vehicle\r\n        ? VehicleTransformer.fromApi(apiData.Vehicle)\r\n        : null,\r\n      vehicleId: apiData.vehicleId ?? null,\r\n    };\r\n  },\r\n\r\n  toCreateRequest(taskData: CreateTaskData): CreateTaskRequest {\r\n    return {\r\n      dateTime: taskData.dateTime, // REQUIRED\r\n      deadline: taskData.deadline ?? null, // Optional\r\n      description: taskData.description, // REQUIRED\r\n      driverEmployeeId: taskData.driverEmployeeId ?? null, // Optional\r\n      estimatedDuration: taskData.estimatedDuration, // REQUIRED\r\n      location: taskData.location, // REQUIRED\r\n      notes: taskData.notes ?? null, // Optional\r\n      priority: taskData.priority as string, // REQUIRED\r\n      requiredSkills: taskData.requiredSkills, // REQUIRED\r\n      staffEmployeeId: taskData.staffEmployeeId, // REQUIRED\r\n      status: taskData.status as string, // REQUIRED\r\n      subTasks: taskData.subtasks?.map(SubtaskTransformer.toApiRequest) ?? [], // Backend expects 'subTasks'\r\n      vehicleId: taskData.vehicleId ?? null, // Optional\r\n    };\r\n  },\r\n\r\n  toUpdateRequest(taskData: Partial<CreateTaskData>): UpdateTaskRequest {\r\n    const request: UpdateTaskRequest = {};\r\n\r\n    if (taskData.description !== undefined) {\r\n      request.description = taskData.description; // Direct mapping - domain description to API description\r\n    }\r\n    if (taskData.notes !== undefined) request.notes = taskData.notes; // Direct mapping - domain notes to API notes\r\n    if (taskData.location !== undefined) request.location = taskData.location;\r\n    if (taskData.dateTime !== undefined) request.dateTime = taskData.dateTime;\r\n    if (taskData.estimatedDuration !== undefined)\r\n      request.estimatedDuration = taskData.estimatedDuration;\r\n    if (taskData.priority !== undefined)\r\n      request.priority = taskData.priority as string;\r\n    if (taskData.status !== undefined)\r\n      request.status = taskData.status as string;\r\n    if (taskData.deadline !== undefined) request.deadline = taskData.deadline; // Backend expects 'deadline'\r\n    if (taskData.requiredSkills !== undefined)\r\n      request.requiredSkills = taskData.requiredSkills;\r\n    if (taskData.vehicleId !== undefined)\r\n      request.vehicleId = taskData.vehicleId;\r\n    if (taskData.staffEmployeeId !== undefined)\r\n      request.staffEmployeeId = taskData.staffEmployeeId;\r\n    if (taskData.driverEmployeeId !== undefined)\r\n      request.driverEmployeeId = taskData.driverEmployeeId;\r\n    if (taskData.subtasks !== undefined) {\r\n      (request as any).subTasks = taskData.subtasks.map(\r\n        SubtaskTransformer.toApiRequest\r\n      ); // Cast to any to allow subTasks\r\n    }\r\n\r\n    return request;\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAwBD;AACA;;;AAEO,MAAM,qBAAqB;IAChC;;;;GAIC,GACD,SAAQ,OAA2B;QACjC,OAAO;YACL,WAAW,QAAQ,SAAS;YAC5B,IAAI,QAAQ,EAAE;YACd,QAAQ,QAAQ,MAAM;YACtB,OAAO,QAAQ,KAAK;QACtB;IACF;IAEA;;;;GAIC,GACD,cAAa,UAA6B;QACxC,OAAO;YACL,WACE,WAAW,SAAS,KAAK,YAAY,QAAQ,WAAW,SAAS;YACnE,QAAQ,WAAW,MAAM;YACzB,OAAO,WAAW,KAAK,CAAC,IAAI;QAC9B;IACF;AACF;AAKO,MAAM,kBAAkB;IAC7B,SAAQ,OAAwB;QAC9B,OAAO;YACL,WAAW,QAAQ,SAAS;YAC5B,UAAU,QAAQ,QAAQ;YAC1B,UAAU,QAAQ,QAAQ,IAAI;YAC9B,aAAa,QAAQ,WAAW;YAChC,gBAAgB,QAAQ,wCAAwC,GAC5D,iJAAA,CAAA,sBAAmB,CAAC,OAAO,CACzB,QAAQ,wCAAwC,IAElD;YACJ,kBAAkB,QAAQ,gBAAgB,IAAI;YAC9C,mBAAmB,QAAQ,iBAAiB;YAC5C,IAAI,QAAQ,EAAE;YACd,UAAU,QAAQ,QAAQ;YAC1B,OAAO,QAAQ,KAAK,IAAI;YACxB,UAAU,QAAQ,QAAQ;YAC1B,gBAAgB,QAAQ,cAAc;YACtC,0DAA0D;YAC1D,eAAe,QAAQ,uCAAuC,GAC1D,iJAAA,CAAA,sBAAmB,CAAC,OAAO,CACzB,QAAQ,uCAAuC,IAEjD;YACJ,iBAAiB,QAAQ,eAAe;YACxC,QAAQ,QAAQ,MAAM;YACtB,UAAU,MAAM,OAAO,CAAC,QAAQ,OAAO,IACnC,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,KACnB,mBAAmB,OAAO,CAAC,OAE7B,EAAE;YAEN,WAAW,QAAQ,SAAS;YAC5B,SAAS,QAAQ,OAAO,GACpB,gJAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC,QAAQ,OAAO,IAC1C;YACJ,WAAW,QAAQ,SAAS,IAAI;QAClC;IACF;IAEA,iBAAgB,QAAwB;QACtC,OAAO;YACL,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ,IAAI;YAC/B,aAAa,SAAS,WAAW;YACjC,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,mBAAmB,SAAS,iBAAiB;YAC7C,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK,IAAI;YACzB,UAAU,SAAS,QAAQ;YAC3B,gBAAgB,SAAS,cAAc;YACvC,iBAAiB,SAAS,eAAe;YACzC,QAAQ,SAAS,MAAM;YACvB,UAAU,SAAS,QAAQ,EAAE,IAAI,mBAAmB,YAAY,KAAK,EAAE;YACvE,WAAW,SAAS,SAAS,IAAI;QACnC;IACF;IAEA,iBAAgB,QAAiC;QAC/C,MAAM,UAA6B,CAAC;QAEpC,IAAI,SAAS,WAAW,KAAK,WAAW;YACtC,QAAQ,WAAW,GAAG,SAAS,WAAW,EAAE,yDAAyD;QACvG;QACA,IAAI,SAAS,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG,SAAS,KAAK,EAAE,6CAA6C;QAC/G,IAAI,SAAS,QAAQ,KAAK,WAAW,QAAQ,QAAQ,GAAG,SAAS,QAAQ;QACzE,IAAI,SAAS,QAAQ,KAAK,WAAW,QAAQ,QAAQ,GAAG,SAAS,QAAQ;QACzE,IAAI,SAAS,iBAAiB,KAAK,WACjC,QAAQ,iBAAiB,GAAG,SAAS,iBAAiB;QACxD,IAAI,SAAS,QAAQ,KAAK,WACxB,QAAQ,QAAQ,GAAG,SAAS,QAAQ;QACtC,IAAI,SAAS,MAAM,KAAK,WACtB,QAAQ,MAAM,GAAG,SAAS,MAAM;QAClC,IAAI,SAAS,QAAQ,KAAK,WAAW,QAAQ,QAAQ,GAAG,SAAS,QAAQ,EAAE,6BAA6B;QACxG,IAAI,SAAS,cAAc,KAAK,WAC9B,QAAQ,cAAc,GAAG,SAAS,cAAc;QAClD,IAAI,SAAS,SAAS,KAAK,WACzB,QAAQ,SAAS,GAAG,SAAS,SAAS;QACxC,IAAI,SAAS,eAAe,KAAK,WAC/B,QAAQ,eAAe,GAAG,SAAS,eAAe;QACpD,IAAI,SAAS,gBAAgB,KAAK,WAChC,QAAQ,gBAAgB,GAAG,SAAS,gBAAgB;QACtD,IAAI,SAAS,QAAQ,KAAK,WAAW;YAClC,QAAgB,QAAQ,GAAG,SAAS,QAAQ,CAAC,GAAG,CAC/C,mBAAmB,YAAY,GAC9B,gCAAgC;QACrC;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/domain/taskApi.ts"], "sourcesContent": ["import type {\r\n  CreateTaskRequest,\r\n  TaskApiResponse,\r\n  UpdateTaskRequest,\r\n} from '../../../types/apiContracts';\r\nimport type { Task, TaskStatusPrisma } from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport { TaskTransformer } from '../../../transformers/taskTransformer';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst TaskApiTransformer: DataTransformer<Task> = {\r\n  fromApi: (data: TaskApiResponse) => TaskTransformer.fromApi(data),\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nexport class TaskApiService extends BaseApiService<\r\n  Task,\r\n  CreateTaskRequest,\r\n  UpdateTaskRequest\r\n> {\r\n  protected endpoint = '/tasks';\r\n  protected transformer: DataTransformer<Task> = TaskApiTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 3 * 60 * 1000, // 3 minutes for tasks\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getByStatus(status: TaskStatusPrisma): Promise<Task[]> {\r\n    const result = await this.getAll({ status });\r\n    return result.data;\r\n  }\r\n\r\n  async updateTaskStatus(\r\n    taskId: string,\r\n    newStatus: TaskStatusPrisma\r\n  ): Promise<Task> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.patch<TaskApiResponse>(\r\n        `${this.endpoint}/${taskId}/status`,\r\n        { status: newStatus }\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidate(`${this.endpoint}:getById:${taskId}`);\r\n\r\n      return TaskTransformer.fromApi(response);\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAOA;AACA;;;AAMA,MAAM,qBAA4C;IAChD,SAAS,CAAC,OAA0B,6IAAA,CAAA,kBAAe,CAAC,OAAO,CAAC;IAC5D,OAAO,CAAC,OAAc;AACxB;AAEO,MAAM,uBAAuB,2IAAA,CAAA,iBAAc;IAKtC,WAAW,SAAS;IACpB,cAAqC,mBAAmB;IAElE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,YAAY,MAAwB,EAAmB;QAC3D,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAO;QAC1C,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,iBACJ,MAAc,EACd,SAA2B,EACZ;QACf,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CACzC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,EACnC;gBAAE,QAAQ;YAAU;YAGtB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ;YAE1D,OAAO,6IAAA,CAAA,kBAAe,CAAC,OAAO,CAAC;QACjC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/domain/vehicleApi.ts"], "sourcesContent": ["/**\r\n * @file Enhanced Vehicle API service using BaseApiService patterns.\r\n * @module api/services/vehicleApi\r\n */\r\n\r\nimport type {\r\n  CreateVehicleRequest,\r\n  UpdateVehicleRequest,\r\n  VehicleApiResponse,\r\n} from '../../../types/api';\r\nimport type { Vehicle } from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\n\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nimport { VehicleTransformer as VehicleApiTransformer } from '../../../transformers/vehicleTransformer';\r\n\r\nconst VehicleTransformer: DataTransformer<Vehicle> = {\r\n  fromApi: (data: VehicleApiResponse) => VehicleApiTransformer.fromApi(data),\r\n  toApi: (data: CreateVehicleRequest | UpdateVehicleRequest) => data,\r\n};\r\n\r\n/**\r\n * Enhanced Vehicle API Service with production-grade patterns\r\n */\r\nexport class VehicleApiService extends BaseApiService<\r\n  Vehicle,\r\n  CreateVehicleRequest,\r\n  UpdateVehicleRequest\r\n> {\r\n  protected endpoint = '/vehicles';\r\n  protected transformer: DataTransformer<Vehicle> = VehicleTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 10 * 60 * 1000, // 10 minutes for vehicles\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      retryAttempts: 3,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getAvailableVehicles(\r\n    startDate: Date,\r\n    endDate: Date\r\n  ): Promise<Vehicle[]> {\r\n    const result = await this.getAll({\r\n      available: true,\r\n      endDate: endDate.toISOString(),\r\n      startDate: startDate.toISOString(),\r\n    });\r\n    return result.data;\r\n  }\r\n\r\n  // Vehicle-specific methods\r\n  async getByStatus(status: string): Promise<Vehicle[]> {\r\n    const result = await this.getAll({ status });\r\n    return result.data;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAUD;AAMA;;;AAEA,MAAM,qBAA+C;IACnD,SAAS,CAAC,OAA6B,gJAAA,CAAA,qBAAqB,CAAC,OAAO,CAAC;IACrE,OAAO,CAAC,OAAsD;AAChE;AAKO,MAAM,0BAA0B,2IAAA,CAAA,iBAAc;IAKzC,WAAW,YAAY;IACvB,cAAwC,mBAAmB;IAErE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,KAAK,KAAK;YACzB,yBAAyB;YACzB,eAAe;YACf,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,qBACJ,SAAe,EACf,OAAa,EACO;QACpB,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAC/B,WAAW;YACX,SAAS,QAAQ,WAAW;YAC5B,WAAW,UAAU,WAAW;QAClC;QACA,OAAO,OAAO,IAAI;IACpB;IAEA,2BAA2B;IAC3B,MAAM,YAAY,MAAc,EAAsB;QACpD,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,CAAC;YAAE;QAAO;QAC1C,OAAO,OAAO,IAAI;IACpB;AACF", "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/core/types.ts"], "sourcesContent": ["/**\r\n * @file Common API types and interfaces.\r\n * @module api/base/types\r\n */\r\n\r\n/**\r\n * Configuration interface for the ApiClient.\r\n * @property baseURL - The base URL for API requests.\r\n * @property timeout - Optional. The request timeout in milliseconds. Defaults to 10000 (10 seconds).\r\n * @property retryAttempts - Optional. The number of retry attempts for failed requests. Defaults to 3.\r\n * @property headers - Optional. Default headers to be sent with every request.\r\n * @property authToken - Optional. Authentication token to be included in requests.\r\n * @property getAuthToken - Optional. Function to get the current authentication token dynamically.\r\n */\r\nexport interface ApiClientConfig {\r\n  authToken?: string;\r\n  baseURL: string;\r\n  getAuthToken?: () => string | null;\r\n  headers?: Record<string, string>;\r\n  retryAttempts?: number;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Represents a generic API error response structure.\r\n * This can be extended or specialized for specific API error formats.\r\n */\r\nexport interface ApiErrorResponse {\r\n  code?: string;\r\n  details?: any;\r\n  message: string;\r\n}\r\n\r\n/**\r\n * Represents a generic request payload for creation operations.\r\n * @template T - The type of the data to be created.\r\n */\r\nexport interface CreateRequest<T> {\r\n  data: T;\r\n}\r\n\r\n/**\r\n * Defines the supported HTTP methods.\r\n */\r\nexport type HttpMethod = 'DELETE' | 'GET' | 'PATCH' | 'POST' | 'PUT';\r\n\r\n// Legacy PaginatedApiResponse interface removed - use PaginatedResponse from types/index.ts instead\r\n\r\n/**\r\n * Represents a raw HTTP response structure from fetch operations.\r\n * This represents the low-level HTTP response details.\r\n * @template T - The type of the data payload in the response.\r\n */\r\nexport interface RawHttpResponse<T = any> {\r\n  data: T;\r\n  headers: Headers;\r\n  status: number;\r\n  statusText: string;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Configuration interface for individual API requests.\r\n * @property headers - Optional. Headers specific to this request, overriding default client headers.\r\n * @property timeout - Optional. Timeout specific to this request, overriding client default.\r\n * @property signal - Optional. An AbortSignal to cancel the request.\r\n */\r\nexport interface RequestConfig {\r\n  headers?: Record<string, string>;\r\n  retryDelay?: number; // Added for custom retry delay per request\r\n  signal?: AbortSignal;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Represents a generic request payload for update operations.\r\n * @template T - The type of the data to be updated.\r\n */\r\nexport interface UpdateRequest<T> {\r\n  data: Partial<T>;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;CAQC", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/core/interfaces.ts"], "sourcesContent": ["/**\n * @file Core interfaces for the API layer\n * @module api/core/interfaces\n */\n\nimport type { RequestConfig } from './types';\n\n/**\n * Core HTTP client interface for making API requests\n * This interface defines the contract for all HTTP clients in the system\n */\nexport interface IHttpClient {\n  /**\n   * Performs a GET request\n   * @template T - The expected response data type\n   * @param endpoint - The API endpoint (e.g., '/users')\n   * @param config - Optional request configuration\n   * @returns A Promise that resolves with the response data\n   */\n  get<T>(endpoint: string, config?: RequestConfig): Promise<T>;\n\n  /**\n   * Performs a POST request\n   * @template T - The expected response data type\n   * @param endpoint - The API endpoint\n   * @param data - The request body\n   * @param config - Optional request configuration\n   * @returns A Promise that resolves with the response data\n   */\n  post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T>;\n\n  /**\n   * Performs a PUT request\n   * @template T - The expected response data type\n   * @param endpoint - The API endpoint\n   * @param data - The request body\n   * @param config - Optional request configuration\n   * @returns A Promise that resolves with the response data\n   */\n  put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T>;\n\n  /**\n   * Performs a PATCH request\n   * @template T - The expected response data type\n   * @param endpoint - The API endpoint\n   * @param data - The request body\n   * @param config - Optional request configuration\n   * @returns A Promise that resolves with the response data\n   */\n  patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T>;\n\n  /**\n   * Performs a DELETE request\n   * @template T - The expected response data type (can be void if no content)\n   * @param endpoint - The API endpoint\n   * @param config - Optional request configuration\n   * @returns A Promise that resolves when the request is complete\n   */\n  delete<T = void>(endpoint: string, config?: RequestConfig): Promise<T>;\n}\n\n/**\n * Security context interface for middleware\n */\nexport interface SecurityContext {\n  isAuthenticated: boolean;\n  hasValidToken: boolean;\n  user?: any;\n  session?: any;\n  timestamp: Date;\n}\n\n/**\n * Security middleware interface for composable security features\n */\nexport interface ISecurityMiddleware {\n  readonly name: string;\n  readonly priority: number;\n  \n  /**\n   * Process a request configuration with security enhancements\n   * @param config - The request configuration to process\n   * @param context - The current security context\n   * @returns The processed request configuration\n   */\n  process(config: RequestConfig, context: SecurityContext): Promise<RequestConfig>;\n  \n  /**\n   * Handle errors that occur during request processing\n   * @param error - The error that occurred\n   * @param context - The current security context\n   */\n  handleError?(error: unknown, context: SecurityContext): Promise<void>;\n}\n\n/**\n * Security configuration interface\n */\nexport interface SecurityConfig {\n  csrf: {\n    enabled: boolean;\n    tokenHeader: string;\n    excludePaths: string[];\n  };\n  tokenValidation: {\n    enabled: boolean;\n    refreshThreshold: number;\n    autoRefresh: boolean;\n  };\n  inputSanitization: {\n    enabled: boolean;\n    sanitizers: string[];\n  };\n  authentication: {\n    enabled: boolean;\n    autoLogout: boolean;\n    redirectOnFailure: boolean;\n  };\n  http: {\n    baseURL: string;\n    timeout: number;\n    retryAttempts: number;\n  };\n}\n\n/**\n * Security status interface\n */\nexport interface SecurityStatus {\n  isAuthenticated: boolean;\n  hasValidToken: boolean;\n  securityFeaturesEnabled: Partial<SecurityConfig>;\n  lastSecurityCheck: Date;\n  threatLevel: 'low' | 'medium' | 'high' | 'critical';\n}\n"], "names": [], "mappings": "AAAA;;;CAGC", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/external/flightApi.ts"], "sourcesContent": ["/**\r\n * Flight API Service\r\n *\r\n * This service provides functions to interact with the flight-related API endpoints.\r\n * Refactored to use BaseApiService pattern for consistency.\r\n */\r\n\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\n// Define the flight data interface\r\nexport interface FlightData {\r\n  altitude?: number;\r\n  arrivalAirport?: string;\r\n  arrivalTime?: number;\r\n  callsign: string;\r\n  departureAirport?: string;\r\n  departureTime?: number;\r\n  heading?: number;\r\n  icao24: string;\r\n  lastSeen?: number;\r\n  latitude?: number;\r\n  longitude?: number;\r\n  onGround?: boolean;\r\n  velocity?: number;\r\n}\r\n\r\n// Enhanced response type for flight search\r\nexport interface FlightSearchResponse {\r\n  details?: {\r\n    apiInfo?: string;\r\n    possibleReasons?: string[];\r\n    searchParams?: {\r\n      callsign: string;\r\n      date: string;\r\n    };\r\n  };\r\n  error?: string;\r\n  flights?: FlightData[];\r\n  message?: string;\r\n  timestamp?: string;\r\n}\r\n\r\nconst FlightTransformer: DataTransformer<FlightData> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\n/**\r\n * Flight API Service using BaseApiService pattern\r\n */\r\nexport class FlightApiService extends BaseApiService<FlightData, any, any> {\r\n  protected endpoint = '/flights';\r\n  protected transformer: DataTransformer<FlightData> = FlightTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for flight data\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Search for flights by callsign and date\r\n   */\r\n  async searchFlightsByCallsignAndDate(\r\n    callsign: string,\r\n    date: string // Expected format: \"YYYY-MM-DD\"\r\n  ): Promise<FlightData[]> {\r\n    if (!date) {\r\n      console.error('Search date is required for historical flight search.');\r\n      throw new Error('Search date is required');\r\n    }\r\n\r\n    // Check if date is in the future\r\n    const searchDate = new Date(`${date}T00:00:00.000Z`);\r\n    const currentDate = new Date();\r\n    if (searchDate > currentDate) {\r\n      console.warn(`Search for future date rejected: ${date}`);\r\n      throw new Error(\r\n        `OpenSky API does not provide data for future dates. The date ${date} is in the future.`\r\n      );\r\n    }\r\n\r\n    return this.executeWithInfrastructure(\r\n      `search:${callsign}:${date}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<FlightSearchResponse>(\r\n          `/flights/search?callsign=${encodeURIComponent(callsign)}&date=${date}`\r\n        );\r\n\r\n        // Handle the enhanced response format\r\n        if (Array.isArray(response)) {\r\n          return response; // Original array response\r\n        } else if (response.flights) {\r\n          return response.flights; // New format with flights array\r\n        } else {\r\n          // If we have an error message but no flights, throw an error with the details\r\n          if (response.message) {\r\n            const error = new Error(response.message);\r\n            // @ts-ignore - Add details to the error object\r\n            error.details = response.details;\r\n            throw error;\r\n          }\r\n          return []; // Fallback to empty array\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get flights by airport (arrivals or departures)\r\n   */\r\n  async getFlightsByAirport(\r\n    airport: string,\r\n    begin: number,\r\n    end: number,\r\n    type: 'arrival' | 'departure' = 'arrival'\r\n  ): Promise<FlightData[]> {\r\n    return this.executeWithInfrastructure(\r\n      `airport:${airport}:${begin}:${end}:${type}`,\r\n      async () => {\r\n        return this.apiClient.get<FlightData[]>(\r\n          `/flights/airport?airport=${encodeURIComponent(\r\n            airport\r\n          )}&begin=${begin}&end=${end}&type=${type}`\r\n        );\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get flights by time interval\r\n   */\r\n  async getFlightsByTimeInterval(\r\n    begin: number,\r\n    end: number\r\n  ): Promise<FlightData[]> {\r\n    return this.executeWithInfrastructure(\r\n      `interval:${begin}:${end}`,\r\n      async () => {\r\n        return this.apiClient.get<FlightData[]>(\r\n          `/flights/interval?begin=${begin}&end=${end}`\r\n        );\r\n      }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAGD;;AAuCA,MAAM,oBAAiD;IACrD,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAKO,MAAM,yBAAyB,2IAAA,CAAA,iBAAc;IACxC,WAAW,WAAW;IACtB,cAA2C,kBAAkB;IAEvE,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA;;GAEC,GACD,MAAM,+BACJ,QAAgB,EAChB,IAAY,AAAC,gCAAgC;MACtB;QACvB,IAAI,CAAC,MAAM;YACT,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,iCAAiC;QACjC,MAAM,aAAa,IAAI,KAAK,GAAG,KAAK,cAAc,CAAC;QACnD,MAAM,cAAc,IAAI;QACxB,IAAI,aAAa,aAAa;YAC5B,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,MAAM;YACvD,MAAM,IAAI,MACR,CAAC,6DAA6D,EAAE,KAAK,kBAAkB,CAAC;QAE5F;QAEA,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,MAAM,EAC5B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,yBAAyB,EAAE,mBAAmB,UAAU,MAAM,EAAE,MAAM;YAGzE,sCAAsC;YACtC,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,OAAO,UAAU,0BAA0B;YAC7C,OAAO,IAAI,SAAS,OAAO,EAAE;gBAC3B,OAAO,SAAS,OAAO,EAAE,gCAAgC;YAC3D,OAAO;gBACL,8EAA8E;gBAC9E,IAAI,SAAS,OAAO,EAAE;oBACpB,MAAM,QAAQ,IAAI,MAAM,SAAS,OAAO;oBACxC,+CAA+C;oBAC/C,MAAM,OAAO,GAAG,SAAS,OAAO;oBAChC,MAAM;gBACR;gBACA,OAAO,EAAE,EAAE,0BAA0B;YACvC;QACF;IAEJ;IAEA;;GAEC,GACD,MAAM,oBACJ,OAAe,EACf,KAAa,EACb,GAAW,EACX,OAAgC,SAAS,EAClB;QACvB,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,EAC5C;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CACvB,CAAC,yBAAyB,EAAE,mBAC1B,SACA,OAAO,EAAE,MAAM,KAAK,EAAE,IAAI,MAAM,EAAE,MAAM;QAE9C;IAEJ;IAEA;;GAEC,GACD,MAAM,yBACJ,KAAa,EACb,GAAW,EACY;QACvB,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK,EAC1B;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CACvB,CAAC,wBAAwB,EAAE,MAAM,KAAK,EAAE,KAAK;QAEjD;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/external/flightDetailsApi.ts"], "sourcesContent": ["/**\r\n * @file API service for FlightDetails-related operations.\r\n * @module api/services/flightDetailsApi\r\n * Refactored to use BaseApiService pattern for consistency.\r\n */\r\n\r\nimport type { FlightDetails } from '../../../types/domain';\r\nimport type { ApiClient } from '../../core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '../../core/baseApiService';\r\n\r\nconst FlightDetailsTransformer: DataTransformer<FlightDetails> = {\r\n  fromApi: (data: any) => data,\r\n  toApi: (data: any) => data,\r\n};\r\n\r\n/**\r\n * Service class for interacting with the Flight Details API endpoints.\r\n * Now extends BaseApiService for consistency and enhanced features.\r\n */\r\nexport class FlightDetailsApiService extends BaseApiService<\r\n  FlightDetails,\r\n  Partial<FlightDetails>,\r\n  Partial<FlightDetails>\r\n> {\r\n  protected endpoint = '/flights';\r\n  protected transformer: DataTransformer<FlightDetails> =\r\n    FlightDetailsTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for flight details\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Creates or updates flight details.\r\n   * If flightDetails.id is provided, it attempts to update. Otherwise, it creates a new one.\r\n   */\r\n  public async createOrUpdateFlight(\r\n    flightDetails: Partial<FlightDetails>\r\n  ): Promise<FlightDetails> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      if (flightDetails.id) {\r\n        // Use the inherited update method\r\n        return this.update(flightDetails.id, flightDetails);\r\n      } else {\r\n        // Use the inherited create method\r\n        return this.create(flightDetails);\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAID;;AAMA,MAAM,2BAA2D;IAC/D,SAAS,CAAC,OAAc;IACxB,OAAO,CAAC,OAAc;AACxB;AAMO,MAAM,gCAAgC,2IAAA,CAAA,iBAAc;IAK/C,WAAW,WAAW;IACtB,cACR,yBAAyB;IAE3B,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA;;;GAGC,GACD,MAAa,qBACX,aAAqC,EACb;QACxB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,IAAI,cAAc,EAAE,EAAE;gBACpB,kCAAkC;gBAClC,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE;YACvC,OAAO;gBACL,kCAAkC;gBAClC,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/index.ts"], "sourcesContent": ["/**\r\n * @file Centralized exports for the API service layer.\r\n * @module api\r\n */\r\n\r\n// Core infrastructure\r\nexport * from './core/apiClient';\r\nexport * from './core/baseApiService';\r\nexport * from './core/errors';\r\nexport * from './core/types';\r\nexport * from './core/interfaces';\r\n\r\n// Create and export a configured API client instance\r\nimport { ApiClient } from './core/apiClient';\r\nimport { getEnvironmentConfig } from '../config/environment';\r\n\r\n/**\r\n * Unified Authentication Token Provider\r\n * Single source of truth for authentication tokens across the entire application\r\n */\r\nlet unifiedAuthTokenProvider: (() => string | null) | null = null;\r\n\r\n/**\r\n * Set the unified authentication token provider\r\n * This should ONLY be called by the AuthContext\r\n * Replaces both setGlobalAuthTokenProvider and setFactoryAuthTokenProvider\r\n */\r\nexport function setUnifiedAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  unifiedAuthTokenProvider = provider;\r\n\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.log('🔐 Unified Auth Token Provider initialized');\r\n  }\r\n}\r\n\r\n/**\r\n * Get the current authentication token from the unified provider\r\n * This is used by ALL API clients throughout the application\r\n */\r\nfunction getAuthToken(): string | null {\r\n  if (!unifiedAuthTokenProvider) {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('⚠️ Unified Auth Token Provider not initialized');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return unifiedAuthTokenProvider();\r\n  } catch (error) {\r\n    console.error('❌ Error getting auth token from unified provider:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Get the unified token provider function (for debugging/testing)\r\n */\r\nexport function getUnifiedAuthTokenProvider(): (() => string | null) | null {\r\n  return unifiedAuthTokenProvider;\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use setUnifiedAuthTokenProvider instead\r\n */\r\nexport function setGlobalAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  console.warn(\r\n    '⚠️ setGlobalAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider instead.'\r\n  );\r\n  setUnifiedAuthTokenProvider(provider);\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use getUnifiedAuthTokenProvider instead\r\n */\r\nexport function getGlobalAuthTokenProvider(): (() => string | null) | null {\r\n  console.warn(\r\n    '⚠️ getGlobalAuthTokenProvider is deprecated. Use getUnifiedAuthTokenProvider instead.'\r\n  );\r\n  return getUnifiedAuthTokenProvider();\r\n}\r\n\r\n// Get environment-aware configuration\r\nconst envConfig = getEnvironmentConfig();\r\n\r\nexport const apiClient = new ApiClient({\r\n  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration\r\n  getAuthToken, // Provide the auth token getter\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  retryAttempts: 3,\r\n  timeout: 10_000,\r\n});\r\n\r\n// Security architecture (selective exports to avoid conflicts)\r\nexport {\r\n  // Security hooks\r\n  useSecureApiClient,\r\n  useSecureApiReplacement,\r\n  useSecureHttpClient,\r\n  useCSRFProtection,\r\n  useInputValidation,\r\n  useSecurityMonitoring,\r\n  useSessionSecurity,\r\n  useTokenManagement,\r\n  // Security providers\r\n  SecurityConfigProvider,\r\n  useSecurityConfig,\r\n  useSecurityConfigValue,\r\n  // Security composer\r\n  SecurityComposer,\r\n  createSecurityComposer,\r\n  // Secure API client\r\n  SecureApiClient,\r\n  createSecureApiClient,\r\n} from './security';\r\n\r\nexport type {\r\n  // Security types (avoid RequestConfig conflict)\r\n  UseSecureApiClientReturn,\r\n  SecureApiRequestConfig,\r\n  UseSecureHttpClientReturn,\r\n  SecurityFeatures,\r\n  SecureApiClientConfig,\r\n} from './security';\r\n\r\n// Domain-specific API services\r\nexport * from './services/domain/delegationApi';\r\nexport * from './services/domain/employeeApi';\r\nexport * from './services/domain/taskApi';\r\nexport * from './services/domain/vehicleApi';\r\n\r\n// External API services\r\nexport * from './services/external/flightApi';\r\nexport * from './services/external/flightDetailsApi';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,sBAAsB;;;;;;;;AACtB;AACA;AACA;AACA;AACA;AAIA;AAuFA,+DAA+D;AAC/D;AA+BA,+BAA+B;AAC/B;AACA;AACA;AACA;AAEA,wBAAwB;AACxB;AACA;;;;;;;;AA7HA;;;CAGC,GACD,IAAI,2BAAyD;AAOtD,SAAS,4BACd,QAA6B;IAE7B,2BAA2B;IAE3B,wCAA4C;QAC1C,QAAQ,GAAG,CAAC;IACd;AACF;AAEA;;;CAGC,GACD,SAAS;IACP,IAAI,CAAC,0BAA0B;QAC7B,wCAA4C;YAC1C,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;IACT;AACF;AAKO,SAAS;IACd,OAAO;AACT;AAMO,SAAS,2BACd,QAA6B;IAE7B,QAAQ,IAAI,CACV;IAEF,4BAA4B;AAC9B;AAMO,SAAS;IACd,QAAQ,IAAI,CACV;IAEF,OAAO;AACT;AAEA,sCAAsC;AACtC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD;AAE9B,MAAM,YAAY,IAAI,sIAAA,CAAA,YAAS,CAAC;IACrC,SAAS,UAAU,UAAU;IAC7B;IACA,SAAS;QACP,gBAAgB;IAClB;IACA,eAAe;IACf,SAAS;AACX", "debugId": null}}, {"offset": {"line": 1718, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/factory.ts"], "sourcesContent": ["/**\r\n * @file Factory for creating and managing API service instances.\r\n * @module api/services/apiServiceFactory\r\n */\r\n\r\nimport { ApiClient } from '../core/apiClient';\r\nimport { DelegationApiService } from './domain/delegationApi';\r\nimport { EmployeeApiService } from './domain/employeeApi';\r\nimport { ReliabilityApiService } from './domain/reliabilityApi';\r\nimport { TaskApiService } from './domain/taskApi';\r\nimport { VehicleApiService } from './domain/vehicleApi';\r\nimport { getEnvironmentConfig } from '../../config/environment';\r\n// Import unified auth token provider\r\nimport { getUnifiedAuthTokenProvider } from '../index';\r\n\r\n/**\r\n * Get the current auth token from the unified provider\r\n * Uses the single source of truth for authentication tokens\r\n */\r\nfunction getAuthToken(): string | null {\r\n  const provider = getUnifiedAuthTokenProvider();\r\n  if (!provider) {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.warn('⚠️ Factory: Unified Auth Token Provider not initialized');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return provider();\r\n  } catch (error) {\r\n    console.error(\r\n      '❌ Factory: Error getting auth token from unified provider:',\r\n      error\r\n    );\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Legacy compatibility - maintains backward compatibility\r\n * @deprecated Use setUnifiedAuthTokenProvider from main API module instead\r\n */\r\nexport function setFactoryAuthTokenProvider(\r\n  provider: () => string | null\r\n): void {\r\n  console.warn(\r\n    '⚠️ setFactoryAuthTokenProvider is deprecated. Use setUnifiedAuthTokenProvider from @/lib/api instead.'\r\n  );\r\n  // This function is now a no-op since we use the unified provider\r\n  // The warning guides developers to use the correct function\r\n}\r\n\r\n/**\r\n * Configuration for the API service factory.\r\n */\r\nexport interface ApiServiceFactoryConfig {\r\n  authToken?: string;\r\n  baseURL: string;\r\n  headers?: Record<string, string>;\r\n  retryAttempts?: number;\r\n  timeout?: number;\r\n}\r\n\r\n/**\r\n * Factory class for creating and managing API service instances.\r\n * Provides a centralized way to configure and access all API services.\r\n */\r\nexport class ApiServiceFactory {\r\n  private readonly apiClient: ApiClient;\r\n  private delegationService?: DelegationApiService;\r\n  private employeeService?: EmployeeApiService;\r\n  private reliabilityService?: ReliabilityApiService;\r\n  private taskService?: TaskApiService;\r\n  private vehicleService?: VehicleApiService;\r\n\r\n  /**\r\n   * Creates an instance of ApiServiceFactory.\r\n   * @param config - Configuration for the API services.\r\n   */\r\n  constructor(config: ApiServiceFactoryConfig) {\r\n    this.apiClient = new ApiClient({\r\n      ...config,\r\n      getAuthToken, // Provide the auth token getter\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the underlying ApiClient instance.\r\n   * @returns The ApiClient instance.\r\n   */\r\n  public getApiClient(): ApiClient {\r\n    return this.apiClient;\r\n  }\r\n\r\n  /**\r\n   * Gets the Delegation API service instance.\r\n   * @returns The DelegationApiService instance.\r\n   */\r\n  public getDelegationService(): DelegationApiService {\r\n    if (!this.delegationService) {\r\n      this.delegationService = new DelegationApiService(this.apiClient);\r\n    }\r\n    return this.delegationService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Employee API service instance.\r\n   * @returns The EmployeeApiService instance.\r\n   */\r\n  public getEmployeeService(): EmployeeApiService {\r\n    if (!this.employeeService) {\r\n      this.employeeService = new EmployeeApiService(this.apiClient);\r\n    }\r\n    return this.employeeService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Reliability API service instance.\r\n   * @returns The ReliabilityApiService instance.\r\n   */\r\n  public getReliabilityService(): ReliabilityApiService {\r\n    if (!this.reliabilityService) {\r\n      this.reliabilityService = new ReliabilityApiService(this.apiClient);\r\n    }\r\n    return this.reliabilityService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Task API service instance.\r\n   * @returns The TaskApiService instance.\r\n   */\r\n  public getTaskService(): TaskApiService {\r\n    if (!this.taskService) {\r\n      this.taskService = new TaskApiService(this.apiClient);\r\n    }\r\n    return this.taskService;\r\n  }\r\n\r\n  /**\r\n   * Gets the Vehicle API service instance.\r\n   * @returns The VehicleApiService instance.\r\n   */\r\n  public getVehicleService(): VehicleApiService {\r\n    if (!this.vehicleService) {\r\n      this.vehicleService = new VehicleApiService(this.apiClient);\r\n    }\r\n    return this.vehicleService;\r\n  }\r\n}\r\n\r\n// Create a default factory instance for the application with environment-aware configuration\r\nconst envConfig = getEnvironmentConfig();\r\nconst defaultConfig: ApiServiceFactoryConfig = {\r\n  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  retryAttempts: 3,\r\n  timeout: 10_000,\r\n};\r\n\r\nexport const apiServiceFactory = new ApiServiceFactory(defaultConfig);\r\n\r\n// Export individual service instances for convenience\r\nexport const vehicleApiService = apiServiceFactory.getVehicleService();\r\nexport const delegationApiService = apiServiceFactory.getDelegationService();\r\nexport const taskApiService = apiServiceFactory.getTaskService();\r\nexport const employeeApiService = apiServiceFactory.getEmployeeService();\r\nexport const reliabilityApiService = apiServiceFactory.getReliabilityService();\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AAAA;;;;;;;;;AAEA;;;CAGC,GACD,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,0IAAA,CAAA,8BAA2B,AAAD;IAC3C,IAAI,CAAC,UAAU;QACb,wCAA4C;YAC1C,QAAQ,IAAI,CAAC;QACf;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,8DACA;QAEF,OAAO;IACT;AACF;AAMO,SAAS,4BACd,QAA6B;IAE7B,QAAQ,IAAI,CACV;AAEF,iEAAiE;AACjE,4DAA4D;AAC9D;AAiBO,MAAM;IACM,UAAqB;IAC9B,kBAAyC;IACzC,gBAAqC;IACrC,mBAA2C;IAC3C,YAA6B;IAC7B,eAAmC;IAE3C;;;GAGC,GACD,YAAY,MAA+B,CAAE;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,sIAAA,CAAA,YAAS,CAAC;YAC7B,GAAG,MAAM;YACT;QACF;IACF;IAEA;;;GAGC,GACD,AAAO,eAA0B;QAC/B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;GAGC,GACD,AAAO,uBAA6C;QAClD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,IAAI,CAAC,iBAAiB,GAAG,IAAI,wJAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,SAAS;QAClE;QACA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;;GAGC,GACD,AAAO,qBAAyC;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACzB,IAAI,CAAC,eAAe,GAAG,IAAI,sJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,SAAS;QAC9D;QACA,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;;GAGC,GACD,AAAO,wBAA+C;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,yJAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,SAAS;QACpE;QACA,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA;;;GAGC,GACD,AAAO,iBAAiC;QACtC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,kJAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,SAAS;QACtD;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA;;;GAGC,GACD,AAAO,oBAAuC;QAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,cAAc,GAAG,IAAI,qJAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,SAAS;QAC5D;QACA,OAAO,IAAI,CAAC,cAAc;IAC5B;AACF;AAEA,6FAA6F;AAC7F,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD;AACrC,MAAM,gBAAyC;IAC7C,SAAS,UAAU,UAAU;IAC7B,SAAS;QACP,gBAAgB;IAClB;IACA,eAAe;IACf,SAAS;AACX;AAEO,MAAM,oBAAoB,IAAI,kBAAkB;AAGhD,MAAM,oBAAoB,kBAAkB,iBAAiB;AAC7D,MAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,MAAM,iBAAiB,kBAAkB,cAAc;AACvD,MAAM,qBAAqB,kBAAkB,kBAAkB;AAC/D,MAAM,wBAAwB,kBAAkB,qBAAqB", "debugId": null}}, {"offset": {"line": 1862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/WorkHub/frontend/src/lib/api/services/apiServiceFactory.ts"], "sourcesContent": ["/**\n * @file API Service Factory - Backward Compatibility Export\n * @module api/services/apiServiceFactory\n * \n * This file provides backward compatibility for imports that expect\n * apiServiceFactory.ts instead of factory.ts\n */\n\n// Re-export everything from the factory module\nexport * from './factory';\n\n// Ensure all the commonly used exports are available\nexport {\n  ApiServiceFactory,\n  apiServiceFactory,\n  setFactoryAuthTokenProvider,\n  vehicleApiService,\n  delegationApiService,\n  taskApiService,\n  employeeApiService,\n  reliabilityApiService,\n} from './factory';\n\nexport type {\n  ApiServiceFactoryConfig,\n} from './factory';\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED,+CAA+C;;AAC/C", "debugId": null}}]}