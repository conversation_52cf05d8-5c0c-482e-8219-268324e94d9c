(()=>{var e={};e.id=6841,e.ids=[6841],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,r,t)=>{"use strict";t.d(r,{O_:()=>i,t6:()=>n});var s=t(43210),o=t(49278);function n(){let e=(0,s.useCallback)((e,r)=>o.JP.success(e,r),[]),r=(0,s.useCallback)((e,r)=>o.JP.error(e,r),[]),t=(0,s.useCallback)((e,r)=>o.JP.info(e,r),[]),n=(0,s.useCallback)(r=>e(r?.successTitle||"Success",r?.successDescription||"Operation completed successfully"),[e]),i=(0,s.useCallback)((e,t)=>{let s=e instanceof Error?e.message:e;return r(t?.errorTitle||"Error",t?.errorDescription||s||"An unexpected error occurred")},[r]);return{showSuccess:e,showError:r,showInfo:t,showFormSuccess:n,showFormError:i}}function i(e){let r;switch(e){case"employee":r=t(49278).Ok;break;case"vehicle":r=t(49278).G7;break;case"task":r=t(49278).z0;break;case"delegation":r=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,r){let{showFormSuccess:t,showFormError:i}=n(),a=r||(e?(0,o.iw)(e):null),l=(0,s.useCallback)(e=>a?a.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[a,t]),c=(0,s.useCallback)(e=>a?a.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[a,t]),u=(0,s.useCallback)(e=>a?a.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[a,t]),d=(0,s.useCallback)(e=>{if(a){let r=e instanceof Error?e.message:e;return a.entityCreationError(r)}return i(e,{errorTitle:"Creation Failed"})},[a,i]);return{showEntityCreated:l,showEntityUpdated:c,showEntityDeleted:u,showEntityCreationError:d,showEntityUpdateError:(0,s.useCallback)(e=>{if(a){let r=e instanceof Error?e.message:e;return a.entityUpdateError(r)}return i(e,{errorTitle:"Update Failed"})},[a,i]),showEntityDeletionError:(0,s.useCallback)(e=>{if(a){let r=e instanceof Error?e.message:e;return a.entityDeletionError(r)}return i(e,{errorTitle:"Deletion Failed"})},[a,i]),showFormSuccess:t,showFormError:i}}(void 0,r)}},7707:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\WorkHub\\\\frontend\\\\src\\\\app\\\\employees\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\new\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28399:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35200:(e,r,t)=>{Promise.resolve().then(t.bind(t,47229))},36386:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=t(65239),o=t(48088),n=t(88170),i=t.n(n),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let c={children:["",{children:["employees",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7707)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34595)),"C:\\Projects\\WorkHub\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Projects\\WorkHub\\frontend\\src\\app\\employees\\new\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/employees/new/page",pathname:"/employees/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},47229:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),o=t(28399),n=t(16189);t(43210);var i=t(96342),a=t(48041),l=t(3940),c=t(19599);let u=()=>{let e=(0,n.useRouter)(),{showEntityCreated:r,showEntityCreationError:t}=(0,l.O_)("employee"),u=(0,c.Ar)(),d=async s=>{let o={availability:s.availability??null,contactEmail:s.contactEmail??null,contactInfo:s.contactInfo,contactMobile:s.contactMobile??null,contactPhone:s.contactPhone??null,department:s.department??null,employeeId:s.employeeId,fullName:s.fullName??null,generalAssignments:s.generalAssignments??[],hireDate:s.hireDate??null,name:s.name,notes:s.notes??null,position:s.position??null,profileImageUrl:s.profileImageUrl??null,role:s.role,shiftSchedule:s.shiftSchedule??null,skills:s.skills??[],status:s.status.replace(" ","_")};try{let t=await u.mutateAsync(o);r(t),e.push("/employees")}catch(e){console.error("Failed to add employee:",e),t(e.response?.data?.error||e.message||"An unexpected error occurred.")}};return(0,s.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,s.jsx)(a.z,{description:"Enter the details for the new employee.",icon:o.A,title:"Add New Employee"}),u.error&&(0,s.jsxs)("div",{className:"relative mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700",role:"alert",children:[(0,s.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,s.jsx)("span",{className:"block sm:inline",children:u.error?.message||"Failed to add employee."})]}),(0,s.jsx)(i.N,{isEditing:!1,isLoading:u.isPending,onSubmit:d})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71648:(e,r,t)=>{Promise.resolve().then(t.bind(t,7707))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,211,1658,8390,2670,9275,6013,101,7055,9599,5785,6342],()=>t(36386));module.exports=s})();